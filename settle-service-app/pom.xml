<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.hp.cmcc.bboss</groupId>
		<artifactId>settle-service</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>settle-service-app</artifactId>
	<name>settle-service-app</name>
	<version>1.0.0</version>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.hp.cmcc.bboss</groupId>
			<artifactId>settle-service-pub</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<id>settle-apps-jar</id>
						<configuration>
							<descriptors>
							<!--
								<descriptor>src/assembly/asm-app.xml</descriptor>
							  -->
								<descriptor></descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<configuration>
							<artifactSet>
								<includes>
									<include>com.hp.cmcc.bboss:settle-pub</include>
								</includes>
							</artifactSet>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<!--<finalName>settle-apps</finalName>-->
	</build>
</project>
