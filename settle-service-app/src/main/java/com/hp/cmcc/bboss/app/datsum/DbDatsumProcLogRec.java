package com.hp.cmcc.bboss.app.datsum;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbDatsumProcLogRec extends GsonObj {
	private Integer log_mm;
	private String sum_file_nm;
	private String raw_file_nm;
	private Long raw_lines;
	private Long aud_val;
	private Long spare_num1; // opt count

	public Object[] asInsertObjArray() {
		Object[] o = new Object[6];
		o[0] = log_mm;
		o[1] = sum_file_nm;
		o[2] = raw_file_nm;
		o[3] = raw_lines;
		o[4] = aud_val;
		o[5] = spare_num1;
		return o;
	}

	public Integer getLog_mm() {
		return log_mm;
	}

	public void setLog_mm(Integer log_mm) {
		this.log_mm = log_mm;
	}

	public String getSum_file_nm() {
		return sum_file_nm;
	}

	public void setSum_file_nm(String sum_file_nm) {
		this.sum_file_nm = sum_file_nm;
	}

	public String getRaw_file_nm() {
		return raw_file_nm;
	}

	public void setRaw_file_nm(String raw_file_nm) {
		this.raw_file_nm = raw_file_nm;
	}

	public Long getRaw_lines() {
		return raw_lines;
	}

	public void setRaw_lines(Long raw_lines) {
		this.raw_lines = raw_lines;
	}

	public Long getAud_val() {
		return aud_val;
	}

	public void setAud_val(Long aud_val) {
		this.aud_val = aud_val;
	}

	public Long getSpare_num1() {
		return spare_num1;
	}

	public void setSpare_num1(Long spare_num1) {
		this.spare_num1 = spare_num1;
	}
}
