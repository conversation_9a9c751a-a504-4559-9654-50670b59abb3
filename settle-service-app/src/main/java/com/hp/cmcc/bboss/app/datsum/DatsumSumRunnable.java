package com.hp.cmcc.bboss.app.datsum;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.script.Bindings;
import javax.script.CompiledScript;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.SimpleBindings;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DatsumSumRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(DatsumSumRunnable.class);
	private int _threadId;
	private BlockingQueue<DatsumQElem> _queue;
	private AtomicBoolean _eobFlag;
	private File _dbFile;
	private Connection _sqlite;
	private Statement _stmt;
	private AppTicker _ticker;
	private int _recCnt;
	private int _idleCnt;
	private String[] _insertSqls;
	private PreparedStatement _pstmts[];
	private Bindings _bindings;
	private ScriptEngine _luaj;
	private Map<String, CompiledScript> _csMap;
	private boolean _terminateFlag = false;

	public DatsumSumRunnable(int thread_id, BlockingQueue<DatsumQElem> queue, AtomicBoolean eob_flag) throws Exception {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_threadId = thread_id;
		_queue = queue;
		_eobFlag = eob_flag;
		_ticker = new AppTicker();
		_initSqlite();
		_bindings = new SimpleBindings();
		_luaj = new ScriptEngineManager().getEngineByName("luaj");
		_csMap = DatsumCfg.PreCompileEvalScripts(_luaj);
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started, _threadId={}", _threadId);
		_recCnt = 0;
		DatsumQElem qelem_ = null;
		while (!_terminateFlag) {
			try {
				qelem_ = _queue.poll(1, TimeUnit.SECONDS);
				if (qelem_ == null) {
					++_idleCnt;
					if (_idleCnt >= 3600) {
						L.debug("idling {} cycles", _idleCnt);
						_idleCnt = 0;
					}
					continue;
				}
				_idleCnt = 0;
				_procElem(qelem_);
			} catch (Exception e) {
				L.warn("encounter exception, sleep 3 seconds", e);
				PubMethod.Sleep(3000);
			}
		}
	}

	private void _procElem(DatsumQElem qelem) throws SQLException {
		switch (qelem._cmdEnum) {
		case DatsumQElem.CMD_ENUM_REC:
			++_recCnt;
			_sumOneLine(qelem);
			break;
		case DatsumQElem.CMD_ENUM_BOB:
			L.info("BOB");
			_ticker.tickStart();
			_recCnt = 0;
			for (int i = 0; i < DatsumCfg._WorkerHashNum; i++)
				_stmt.executeUpdate(String.format("DELETE FROM WRK_%02d", i)); // truncate table
			_stmt.executeUpdate("BEGIN TRANSACTION");
			break;
		case DatsumQElem.CMD_ENUM_EOB:
			L.info("EOB");
			_stmt.executeUpdate("COMMIT TRANSACTION");
			_ticker.tickEnd(_recCnt);
			L.info("cnt:dur:pfm {}:{}:{}", _ticker._recNum, _ticker._term, _ticker._pfm);
			_eobFlag.set(true);
			synchronized (_eobFlag) {
				_eobFlag.notifyAll();
			}
			break;
		default:
			L.warn("unsupported cmd enum discatd {}", qelem.toGsonStr());
			break;
		}
	}

	private void _sumOneLine(DatsumQElem qelem) throws SQLException {
		//L.trace("_lineCnt={}, {}", _lineCnt, elem.toGsonStr());
		int hash_id_ = qelem.calcHashRemainder(DatsumCfg._WorkerHashNum);
		PreparedStatement pstmt_ = _pstmts[hash_id_];
		int idx_ = 0;
		pstmt_.setString(++idx_, qelem._keyStr);
		for (int i = 0; i < DatsumCfg._SumCubeCfg.length; i++) {
			DbDatsumCubeCfgRec cfg_ = DatsumCfg._SumCubeCfg[i];
			switch (cfg_.getSum_attr()) {
			case DbDatsumCubeCfgRec.ATTR_1_INCR:
				pstmt_.setInt(++idx_, _evalIncrField(cfg_, qelem));
				break;
			case DbDatsumCubeCfgRec.ATTR_2_SUM:
				pstmt_.setLong(++idx_, _evalSumField(cfg_, qelem));
				break;
			case DbDatsumCubeCfgRec.ATTR_3_DUMMY:
				pstmt_.setString(++idx_, _evalDummyField(cfg_, qelem));
				break;
			}
		}
		pstmt_.executeUpdate();
	}

	private int _evalIncrField(DbDatsumCubeCfgRec cfg, DatsumQElem qelem) {
		int result_ = 0;
		if (qelem._isOptRec && cfg.getOpt_src_idx() != null) {
			result_ = 1;
		} else if (qelem._isOptRec) {
			result_ = 0;
		} else {
			result_ = 1;
		}
		return result_;
	}

	private long _evalSumField(DbDatsumCubeCfgRec cfg, DatsumQElem qelem) {
		long result_ = 0;
		String field_ = null;
		if (qelem._isOptRec && cfg.getOpt_src_idx() != null) {
			if (PubMethod.IsEmpty(cfg.getOpt_eval())) {
				field_ = qelem._fields[cfg.getOpt_src_idx()];
				if (PubMethod.IsBlank(field_)) {
					result_ = 0;
				} else {
					result_ = Long.parseLong(field_);
				}
			} else {
				CompiledScript cs_ = _csMap.get(cfg.getOpt_eval());
				String eval_result_ = cfg.eval(cs_, _bindings, qelem);
				result_ = PubMethod.A2L(eval_result_);
			}
		} else if (qelem._isOptRec) {
			result_ = 0;
		} else {
			if (PubMethod.IsEmpty(cfg.getEval())) {
				field_ = qelem._fields[cfg.getSrc_idx()];
				if (PubMethod.IsBlank(field_)) {
					result_ = 0;
				} else {
					result_ = Long.parseLong(field_);
				}
			} else {
				CompiledScript cs_ = _csMap.get(cfg.getEval());
				String eval_result_ = cfg.eval(cs_, _bindings, qelem);
				result_ = PubMethod.A2L(eval_result_);
			}
		}
		return result_;
	}

	private String _evalDummyField(DbDatsumCubeCfgRec cfg, DatsumQElem qelem) {
		String result_ = "";
		if (qelem._isOptRec && cfg.getOpt_src_idx() != null) {
			if (PubMethod.IsEmpty(cfg.getOpt_eval())) {
				if (!PubMethod.IsEmpty(qelem._fields[cfg.getOpt_src_idx()])) {
					result_ = qelem._fields[cfg.getOpt_src_idx()];
				}
			} else {
				CompiledScript cs_ = _csMap.get(cfg.getOpt_eval());
				result_ = cfg.eval(cs_, _bindings, qelem);
			}
		} else {
			if (PubMethod.IsEmpty(cfg.getEval())) {
				if (!PubMethod.IsEmpty(qelem._fields[cfg.getSrc_idx()])) {
					result_ = qelem._fields[cfg.getSrc_idx()];
				}
			} else {
				CompiledScript cs_ = _csMap.get(cfg.getEval());
				result_ = cfg.eval(cs_, _bindings, qelem);
			}
		}
		return result_;
	}

	private void _initSqlite() throws Exception {
		Class.forName("org.sqlite.JDBC");
		String db_file_path_ = String.format("%s/SUM_%02d.db3", DatsumCfg._WorkingDir, _threadId);
		_dbFile = new File(db_file_path_);
		if (_dbFile.isFile()) {
			L.debug("unlink {} before init", _dbFile.getAbsolutePath());
			_dbFile.delete();
		}
		_sqlite = DriverManager.getConnection("jdbc:sqlite:" + _dbFile.getAbsolutePath());
		L.info("try initialize {}", _dbFile.getAbsolutePath());
		_stmt = _sqlite.createStatement();
		for (int hash_id_ = 0; hash_id_ < DatsumCfg._WorkerHashNum; hash_id_++) {
			String sql_ = _genSqlCreateTbl(hash_id_);
			L.debug(sql_);
			_stmt.executeUpdate(sql_);
		}
		for (int hash_id_ = 0; hash_id_ < DatsumCfg._WorkerHashNum; hash_id_++) {
			String sql_ = _genSqlCreateTrg(hash_id_);
			L.debug(sql_);
			_stmt.executeUpdate(sql_);
		}
		_insertSqls = new String[DatsumCfg._WorkerHashNum];
		for (int hash_id_ = 0; hash_id_ < DatsumCfg._WorkerHashNum; hash_id_++) {
			String sql_ = _genSqlInsert(hash_id_);
			_insertSqls[hash_id_] = sql_;
			L.debug(sql_);
		}
		_pstmts = new PreparedStatement[DatsumCfg._WorkerHashNum];
		for (int hash_id_ = 0; hash_id_ < DatsumCfg._WorkerHashNum; hash_id_++) {
			_pstmts[hash_id_] = _sqlite.prepareStatement(_insertSqls[hash_id_]);
		}
	}

	private String _genSqlCreateTbl(int hash_id) {
		StringBuilder sb_ = new StringBuilder();
		sb_.append(String.format("CREATE TABLE IF NOT EXISTS WRK_%02d (KEY_STR TEXT NOT NULL", hash_id));
		for (int i = 0; i < DatsumCfg._SumCubeCfg.length; i++) {
			int sum_idx_ = DatsumCfg._SumCubeCfg[i].getSum_idx();
			switch (DatsumCfg._SumCubeCfg[i].getSum_attr()) {
			case DbDatsumCubeCfgRec.ATTR_1_INCR:
				sb_.append(String.format(", INCR_%d INTEGER NOT NULL", sum_idx_));
				break;
			case DbDatsumCubeCfgRec.ATTR_2_SUM:
				sb_.append(String.format(", SUM_%d INTEGER NOT NULL", sum_idx_));
				break;
			case DbDatsumCubeCfgRec.ATTR_3_DUMMY:
				sb_.append(String.format(", DUMMY_%d TEXT", sum_idx_));
				break;
			default: // DbSumCubeCfgRec.ATTR_0_KEY: ignore
				break;
			}
		}
		sb_.append(", UNIQUE (KEY_STR) ON CONFLICT IGNORE);");
		return sb_.substring(0);
	}

	private String _genSqlCreateTrg(int hash_id) {
		StringBuilder sb_ = new StringBuilder();
		sb_.append(String.format("CREATE TRIGGER IF NOT EXISTS TRG_%02d BEFORE INSERT ON WRK_%02d ", hash_id, hash_id));
		sb_.append(String.format("WHEN EXISTS (SELECT KEY_STR FROM WRK_%02d WHERE KEY_STR = NEW.KEY_STR) ", hash_id));
		sb_.append(String.format("BEGIN UPDATE WRK_%02d SET", hash_id));
		int comma_cnt_ = 0;
		for (int i = 0; i < DatsumCfg._SumCubeCfg.length; i++) {
			int sum_idx_ = DatsumCfg._SumCubeCfg[i].getSum_idx();
			switch (DatsumCfg._SumCubeCfg[i].getSum_attr()) {
			case DbDatsumCubeCfgRec.ATTR_1_INCR:
				sb_.append(String.format("%s INCR_%d = INCR_%d + 1", comma_cnt_ == 0 ? "" : ",", sum_idx_, sum_idx_));
				++comma_cnt_;
				break;
			case DbDatsumCubeCfgRec.ATTR_2_SUM:
				sb_.append(String.format("%s SUM_%d = SUM_%d + NEW.SUM_%d", comma_cnt_ == 0 ? "" : ",", sum_idx_, sum_idx_,
						sum_idx_));
				++comma_cnt_;
				break;
			default: // DbSumCubeCfgRec.ATTR_0_KEY or ATTR_3_DUMMY: ignore
				break;
			}
		}
		sb_.append(" WHERE KEY_STR = NEW.KEY_STR; END;");
		return sb_.substring(0);
	}

	private String _genSqlInsert(int hash_id) {
		StringBuilder sb_ = new StringBuilder();
		sb_.append(String.format("INSERT INTO WRK_%02d (KEY_STR", hash_id));
		for (int i = 0; i < DatsumCfg._SumCubeCfg.length; i++) {
			int sum_idx_ = DatsumCfg._SumCubeCfg[i].getSum_idx();
			switch (DatsumCfg._SumCubeCfg[i].getSum_attr()) {
			case DbDatsumCubeCfgRec.ATTR_1_INCR:
				sb_.append(String.format(", INCR_%d", sum_idx_));
				break;
			case DbDatsumCubeCfgRec.ATTR_2_SUM:
				sb_.append(String.format(", SUM_%d", sum_idx_));
				break;
			case DbDatsumCubeCfgRec.ATTR_3_DUMMY:
				sb_.append(String.format(", DUMMY_%d", sum_idx_));
				break;
			}
		}
		sb_.append(") VALUES (?");
		for (int i = 0; i < DatsumCfg._SumCubeCfg.length; i++) {
			switch (DatsumCfg._SumCubeCfg[i].getSum_attr()) {
			case DbDatsumCubeCfgRec.ATTR_1_INCR:
			case DbDatsumCubeCfgRec.ATTR_2_SUM:
			case DbDatsumCubeCfgRec.ATTR_3_DUMMY:
				sb_.append(", ?");
				break;
			}
		}
		sb_.append(")");
		return sb_.substring(0);
	}
}
