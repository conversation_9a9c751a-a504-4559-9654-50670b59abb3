package com.hp.cmcc.bboss.app.erruld;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrKafkaConsumerRunnable;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ErruldMain {
	private static Logger L = LoggerFactory.getLogger(ErruldMain.class);
	private static BlockingQueue<UdrFmt> _BlockingQueue = new LinkedBlockingQueue<UdrFmt>();
	private static UdrKafkaConsumerRunnable<UdrFmt> _ConsumerRunnable;
	private static ErruldBizRunnable _BizRunnable;
	private static Thread _ConsumerThread;
	private static Thread _BizThread;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("erruld", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 3, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!ErruldCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		MdbAgt.Init(OdbSystemParam.GetInstance(), 0, 3, 0);
		_ConsumerRunnable = new UdrKafkaConsumerRunnable<UdrFmt>(_BlockingQueue, 2000, 100, UdrFmt.class, ErruldCfg._KafkaTopicErr);
		_ConsumerThread = new Thread(_ConsumerRunnable, "CONE");
		_BizRunnable = new ErruldBizRunnable(_BlockingQueue);
		_BizThread = new Thread(_BizRunnable, "ULDE");
		_BizRunnable.recover();
		_ConsumerThread.start();
		_BizThread.start();
	}

	private static void _MainLoop() throws InterruptedException {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}
			L.info("try stop normal");
			_ConsumerRunnable.setTerminateFlag();
			PubMethod.Sleep(10);
			_ConsumerThread.interrupt();
			L.debug("try join thread {}", _ConsumerThread.getName());
			_ConsumerThread.join();
			L.debug("thread {} joined", _ConsumerThread.getName());
			_BizRunnable.setTerminateFlag();
			L.debug("try join thread {}", _BizThread.getName());
			_BizThread.join();
			L.debug("thread {} joined", _BizThread.getName());
			break;
		}
	}
}
