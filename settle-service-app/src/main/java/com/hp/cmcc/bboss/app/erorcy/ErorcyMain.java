package com.hp.cmcc.bboss.app.erorcy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ErorcyMain {
	private static Logger L = LoggerFactory.getLogger(ErorcyMain.class);

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("erorcy", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 2, 0, 2)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!ErorcyCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
	}

	private static void _MainLoop() throws Exception {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		ErorcyMgr mgr_ = new ErorcyMgr();
		long prev_ts_ = 0;
		long curr_ts_ = 0;
		while (true) {
			PubMethod.Sleep(1000);
			if (ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				L.info("stop request detected");
				break;
			}
			curr_ts_ = System.currentTimeMillis();
			if ((curr_ts_ - prev_ts_) < ErorcyCfg._Interval * 1000)
				continue;
			prev_ts_ = curr_ts_;
			mgr_.run();
			prev_ts_ = System.currentTimeMillis();
		}
	}
}
