package com.hp.cmcc.bboss.app.shpmsv;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.app.RestRequestValidator;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmVerRec;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

@Path("rest")
public class ShpmsvRest {
	private static Logger L = LoggerFactory.getLogger(ShpmsvRest.class);

	@POST
	@Path("refresh")
	@Produces(MediaType.TEXT_PLAIN)
	public String refresh(@Context HttpServletRequest request) {
		L.info("request received, {} bytes", request.getContentLength());
		try {
			String rsps_ = RestRequestValidator.ValidateCurrentTime(request, null);
			if ("0".equals(rsps_)) {
				ShpmsvOdb odb_ = new ShpmsvOdb();
				DbShpmVerRec rec_newest_ = odb_.getShpmVerNewest(AppCmdline.GetInstance()._instNm);
				if (rec_newest_ != null) {
					long now_ = System.currentTimeMillis();
					String ver_now_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14).substring(2, 12);
					if (ver_now_.equals(rec_newest_.getShpm_ver())) {
						rsps_ = PubMethod.FmtArgs("newest SHPM_VER too new to refresh, {}", rec_newest_.toGsonStr());
						L.warn("{}", rsps_);
					}
				}
			}
			if ("0".equals(rsps_)) {
				ShpmsvTimerRefreshRunnable._RefreshFlag = true;
			}
			return rsps_;
		} catch (Exception e) {
			L.warn("process refresh request exception", e);
			return e.toString();
		}
	}
}
