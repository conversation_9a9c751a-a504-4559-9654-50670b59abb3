package com.hp.cmcc.bboss.app.shpmsv;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmBlobRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmCtlRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmVerRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmsvEdbMgr {
	private static Logger L = LoggerFactory.getLogger(ShpmsvEdbMgr.class);
	public String _shpmVer;
	private ShpmsvOdb _odb;
	private AppTicker _ticker;
	private DbShpmVerRec _verRec;
	private Map<String, DbShpmCtlRec> _ctlMap;
	private OdbUtils _odbUtils;
	private int _rdbCnt; // Relational DB count
	private int _edbCnt; // Embedded DB count

	public ShpmsvEdbMgr() {
		_odb = new ShpmsvOdb();
		_ticker = new AppTicker();
		_ctlMap = new HashMap<String, DbShpmCtlRec>();
		_odbUtils = new OdbUtils();
	}

	public boolean needInitRefresh() {
		try {
			DbShpmVerRec rec_newest_ = _odb.getShpmVerNewest(AppCmdline.GetInstance()._instNm);
			if (rec_newest_ == null) {
				L.debug("no SHPM_VER rec exists, need init refresh");
				return true;
			}
			long now_ = System.currentTimeMillis();
			String ver_now_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14).substring(2, 12);
			if (ver_now_.equals(rec_newest_.getShpm_ver())) {
				L.info("newest SHPM_VER ver too new, can not init refresh, {}", rec_newest_.toGsonStr());
				return false;
			}
			DbShpmVerRec rec_success_ = _odb.getShpmVerNewest(AppCmdline.GetInstance()._instNm, DbShpmVerRec.VER_FLAG_1_SUCCESS);
			if (rec_success_ == null) {
				L.debug("no SHPM_VER with VER_FLAG {} exists, need init refresh", DbShpmVerRec.VER_FLAG_1_SUCCESS);
				return true;
			}
			long ten_minutes_ago_ = now_ - (600 * 1000);
			if (ten_minutes_ago_ >= rec_success_.getTm_start().getTime()) {
				L.debug("newest ver {} lt {}, need init refresh",
						PubMethod.Timestamp2Str(rec_success_.getTm_start(), PubMethod.TimeStrFmt.Fmt19),
						PubMethod.Long2Str(ten_minutes_ago_, PubMethod.TimeStrFmt.Fmt19));
				return true;
			} else {
				L.debug("newest ver {} ge {}, no need init refresh",
						PubMethod.Timestamp2Str(rec_success_.getTm_start(), PubMethod.TimeStrFmt.Fmt19),
						PubMethod.Long2Str(ten_minutes_ago_, PubMethod.TimeStrFmt.Fmt19));
				return false;
			}
		} catch (Exception e) {
			L.warn("encounter exception", e);
			return false;
		}
	}

	public boolean refresh() {
		_ticker.tickStart();
		boolean rc_ = true;
		boolean need_submit_ = true;
		try {
			_initVerRec();
			File fdb3_ = null;
			ShpmsvEdb edb_ = null;
			if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
				edb_ = new ShpmsvEdbUidd(_odb);
			} else if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
				edb_ = new ShpmsvEdbSidd(_odb);
			} else {
				String alm_ = PubMethod.FmtArgs("ukn SUB_SYSTEM_TYPE %d", OdbSystemParam.GetInstance()._subSystemType);
				throw new RuntimeException(alm_);
			}
			fdb3_ = edb_.encapsulateEdb(_shpmVer);
			_rdbCnt = edb_.getRdbCnt();
			_edbCnt = edb_.getEdbCnt();
			DbShpmBlobRec blob_rec_ = _initBlob(fdb3_);
			if (_needSubmit(fdb3_, blob_rec_)) {
				if (_rdbCnt == _edbCnt) {
					L.info("{} {}, cnt rdb eq edb {}", _shpmVer, fdb3_.getAbsolutePath(), _rdbCnt);
				} else {
					L.warn("{} {}, cnt rdb ne edb {}:{}, pls have a look", _shpmVer, fdb3_.getAbsolutePath(), _rdbCnt, _edbCnt);
				}
				_addBlob(fdb3_, blob_rec_);
				_addCtl();
				_triggerCtl();
				rc_ = _waitCtl();
			} else {
				need_submit_ = false;
				fdb3_.delete();
				L.debug("{} canceled, {} deleted, {} bytes", _shpmVer, fdb3_.getAbsolutePath(), blob_rec_.getShpm_size());
			}
			return rc_;
		} catch (Exception e) {
			_verRec.setVer_flag(DbShpmVerRec.VER_FLAG_2_SHPMSV_ERR);
			_verRec.setErr_msg(e.getMessage());
			L.warn("{} refresh exception", _shpmVer, e);
			return false;
		} finally {
			_verRec.setTs_finish(System.currentTimeMillis());
			_verRec.setTm_finish(new Timestamp(_verRec.getTs_finish()));
			if (need_submit_) {
				_odb.updShpmVerTmFinish(_verRec);
				DbRawAlmRec alm_ = null;
				if (_verRec.getVer_flag() == DbShpmVerRec.VER_FLAG_1_SUCCESS) {
					alm_ = new DbRawAlmRec(AlmConst.ALM_203231000_SHPMSV_REFRESH_OK, PubMethod.FmtArgs("%s refresh ok", _shpmVer),
							_verRec.toGsonStr());
				} else {
					alm_ = new DbRawAlmRec(AlmConst.ALM_203341002_SHPMSV_REFRESH_FAIL,
							PubMethod.FmtArgs("%s refresh fail", _shpmVer), _verRec.toGsonStr());
				}
				_odbUtils.addRawAlm(null, alm_);
			} else {
				_odb.delShpmVerTemp(AppCmdline.GetInstance()._instNm, _shpmVer);
			}
		}
	}

	private boolean _needSubmit(File fdb3, DbShpmBlobRec blob_rec) {
		DbShpmVerRec rec_success_ = _odb.getShpmVerNewest(AppCmdline.GetInstance()._instNm, DbShpmVerRec.VER_FLAG_1_SUCCESS);
		if (rec_success_ == null) {
			L.info("{} no his SHPM_VER with VER_FLAG {} exists, need submit", _shpmVer, DbShpmVerRec.VER_FLAG_1_SUCCESS);
			return true;
		}
		String his_ver_yymmdd_ = rec_success_.getShpm_ver().substring(0, 6);
		String cur_ver_yymmdd_ = _shpmVer.substring(0, 6);
		if (!cur_ver_yymmdd_.equals(his_ver_yymmdd_)) {
			L.info("{} his SHPM_VER {} not today, need submit", _shpmVer, rec_success_.getShpm_ver());
			return true;
		}

		DbShpmBlobRec his_blob_ = _odb.getShpmBlob(rec_success_);
		if (his_blob_ == null) {
			L.info("{} his SHPM_BLOB {} not exists, need submit", _shpmVer, rec_success_.getShpm_ver());
			return true;
		}
		if ((blob_rec.getShpm_size().intValue() != his_blob_.getShpm_size().intValue())
				|| !(blob_rec.getShpm_cksum().equals(his_blob_.getShpm_cksum()))) {
			L.info("{} his SHPM_BLOB {} size or cksum ne, his:cur sz {}:{} cksum {}:{}, need submit", _shpmVer,
					rec_success_.getShpm_ver(), his_blob_.getShpm_size(), blob_rec.getShpm_size(), his_blob_.getShpm_cksum(),
					blob_rec.getShpm_cksum());
			return true;
		}
		L.debug("{} his SHPM_BLOB {} both size and cksum eq, sz {} cksum {}, no need submit", _shpmVer, rec_success_.getShpm_ver(),
				blob_rec.getShpm_size(), blob_rec.getShpm_cksum());
		return false;
	}

	private void _initVerRec() throws SQLException {
		_shpmVer = PubMethod.Long2Str(_ticker._tsStart, PubMethod.TimeStrFmt.Fmt14).substring(2, 12);
		_verRec = new DbShpmVerRec();
		_verRec.setVer_mm(Integer.parseInt(_shpmVer.substring(2, 4)));
		_verRec.setInst_nm(OdbSystemParam.GetInstance()._shpmsvInstNm);
		_verRec.setShpm_ver(_shpmVer);
		_verRec.setVer_flag(0);
		_verRec.setTm_start(new Timestamp(_ticker._tsStart));
		_verRec.setTs_start(_ticker._tsStart);
		_rdbCnt = 0;
		_edbCnt = 0;
		L.trace("{} {}", _shpmVer, _verRec.toGsonStr());
		_odb.addShpmVer(_verRec);
	}

	private boolean _waitCtl() {
		long now_ = System.currentTimeMillis();
		long end_ = now_ + 15 * 1000;
		boolean done_ = false;
		Map<String, DbShpmCtlRec> ctl_map_ = null;
		while (true) {
			now_ = System.currentTimeMillis();
			if (now_ > end_) {
				_verRec.setVer_flag(DbShpmVerRec.VER_FLAG_3_SHPMAG_ERR);
				_verRec.setErr_msg("wait agent done timeout");
				break;
			}
			PubMethod.Sleep(500);
			int init_cnt_ = 0;
			ctl_map_ = _odb.getShpmCtl(_verRec.getVer_mm(), _verRec.getInst_nm(), _verRec.getShpm_ver());
			if (ctl_map_ == null) {
				_verRec.setVer_flag(DbShpmVerRec.VER_FLAG_2_SHPMSV_ERR);
				_verRec.setErr_msg("read SHPM_CTL returns null");
				L.warn("{} {}", _shpmVer, _verRec.getErr_msg());
				break;
			}
			if (_ctlMap.size() != ctl_map_.size()) {
				_verRec.setVer_flag(DbShpmVerRec.VER_FLAG_2_SHPMSV_ERR);
				_verRec.setErr_msg(
						PubMethod.FmtArgs("read SHPM_CTL returns %d elements ne expecting %d", ctl_map_.size(), _ctlMap.size()));
				L.warn("{} {}", _shpmVer, _verRec.getErr_msg());
				break;
			}
			for (DbShpmCtlRec rec_ : ctl_map_.values()) {
				if (rec_.getCtl_flag() == DbShpmVerRec.VER_FLAG_0_INIT)
					++init_cnt_;
			}
			if (init_cnt_ == 0) {
				L.info("{} all {} ctl_flags were set", _shpmVer, ctl_map_.size());
				done_ = true;
				break;
			} else {
				L.info("{} {} of {} ctl_flags not set yet", _shpmVer, init_cnt_, ctl_map_.size());
			}
		}
		if (!done_) {
			return false;
		} else {
			_verRec.setVer_flag(DbShpmVerRec.VER_FLAG_1_SUCCESS);
			L.info("{} refresh done and ok", _shpmVer);
			return true;
		}
	}

	private void _addCtl() throws SQLException {
		long now_ = System.currentTimeMillis();
		_ctlMap.clear();
		for (Entry<String, ShpmsvAgtInfo> ent_ : ShpmsvCfg._AgtInfoMap.entrySet()) {
			DbShpmCtlRec ctl_rec_ = new DbShpmCtlRec();
			ctl_rec_.setVer_mm(_verRec.getVer_mm());
			ctl_rec_.setInst_nm(_verRec.getInst_nm());
			ctl_rec_.setShpm_ver(_verRec.getShpm_ver());
			ctl_rec_.setAgt_inst(ent_.getValue()._agtInstNm);
			ctl_rec_.setCtl_flag(DbShpmVerRec.VER_FLAG_0_INIT);
			ctl_rec_.setTm_rqst(new Timestamp(now_));
			ctl_rec_.setTs_rqst(now_);
			_ctlMap.put(ctl_rec_.getAgt_inst(), ctl_rec_);
		}
		_odb.addShpmCtl(new ArrayList<DbShpmCtlRec>(_ctlMap.values()));
	}

	private void _triggerCtl() throws Exception {
		for (Entry<String, ShpmsvAgtInfo> ent_ : ShpmsvCfg._AgtInfoMap.entrySet()) {
			DbShpmCtlRec ctl_rec_ = _ctlMap.get(ent_.getValue()._agtInstNm);
			_triggerOneAgt(ent_.getValue(), ctl_rec_);
		}
	}

	private void _triggerOneAgt(ShpmsvAgtInfo agt_info, DbShpmCtlRec ctl_rec) {
		L.trace("{} try trigger {}", _shpmVer, agt_info.toGsonStr());
		try {
			URL url_ = new URL(PubMethod.FmtArgs("http://%s:%d/rest/trigger", agt_info._agtHost, agt_info._agtRestPort));
			HttpURLConnection http_ = (HttpURLConnection) url_.openConnection();
			http_.setRequestMethod("POST");
			http_.setRequestProperty("User-Agent", "HttpURLConnection/jdk1.7");
			http_.setRequestProperty("Content-Type", "text/plain");
			http_.setReadTimeout(3000);
			http_.setDoOutput(true);
			DataOutputStream dos_ = new DataOutputStream(http_.getOutputStream());
			dos_.writeBytes(PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt14));
			dos_.flush();
			dos_.close();
			int rsps_code_ = http_.getResponseCode();
			if (rsps_code_ != 200) {
				L.warn("{} pls be noticed rsps_code_ is {}, not 200", _shpmVer, rsps_code_);
			}
			BufferedReader br_ = new BufferedReader(new InputStreamReader(http_.getInputStream()));
			StringBuilder sb_ = new StringBuilder();
			String line_;
			while ((line_ = br_.readLine()) != null) {
				sb_.append(line_);
			}
			br_.close();
			String rsps_ = sb_.substring(0);
			if (!rsps_.equals("0")) {
				L.warn("{} agent {} returns [{}]", _shpmVer, agt_info._agtInstNm, rsps_);
				ctl_rec.setCtl_flag(DbShpmVerRec.VER_FLAG_2_SHPMSV_ERR);
				ctl_rec.setErr_msg(PubMethod.IsBlank(rsps_) ? "no response" : rsps_);
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_203341001_SHPMSV_TRIGGER_EXCEPTION,
					PubMethod.FmtArgs("%s trigger %s exception", _shpmVer, agt_info.toGsonStr()), _verRec.toGsonStr());
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			ctl_rec.setErr_msg(e.getMessage());
			_odbUtils.addRawAlm(null, alm_);
		} finally {
			_odb.updShpmCtlRsps(ctl_rec);
		}
	}

	private void _addBlob(File fdb3, DbShpmBlobRec blob_rec) throws Exception {
		if (!PubMethod.Gzip(OdbSystemParam.GetInstance()._gzipPath, fdb3, true)) {
			throw new RuntimeException(PubMethod.FmtArgs("gzip %s failed", fdb3.getAbsolutePath()));
		}
		File gzipped_db3_file_ = new File(fdb3.getAbsolutePath() + ".gz");
		_odb.addShpmBlob(blob_rec, gzipped_db3_file_);
		long gzipped_len_ = gzipped_db3_file_.length();
		gzipped_db3_file_.delete();
		L.trace("{} {} deleted, {} bytes", _shpmVer, gzipped_db3_file_.getAbsolutePath(), gzipped_len_);
		long now_ = System.currentTimeMillis();
		_verRec.setTs_blob(now_);
		_verRec.setTm_blob(new Timestamp(now_));
		_odb.updShpmVerTmBlob(_verRec);
		_ticker.tickEnd(_rdbCnt);
		L.info("{} load blob done, cnt:dur:pfm {}:{}:{}", _shpmVer, _ticker._recNum, _ticker._term, _ticker._pfm);
	}

	private DbShpmBlobRec _initBlob(File fdb3) {
		DbShpmBlobRec blob_rec_ = new DbShpmBlobRec();
		blob_rec_.setVer_mm(_verRec.getVer_mm());
		blob_rec_.setInst_nm(_verRec.getInst_nm());
		blob_rec_.setShpm_ver(_verRec.getShpm_ver());
		blob_rec_.setShpm_size((int) fdb3.length());
		blob_rec_.setShpm_cksum(Long.toString(PubMethod.FileCksum(fdb3.getAbsolutePath())));
		return blob_rec_;
	}
}
