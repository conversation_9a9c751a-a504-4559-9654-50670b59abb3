package com.hp.cmcc.bboss.app.udruld;

import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class UdruldAdjRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(UdruldAdjRunnable.class);
	private BlockingQueue<UdrFmt> _blockingQueue;
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public UdruldAdjRunnable(BlockingQueue<UdrFmt> blocking_queue) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_blockingQueue = blocking_queue;
		_odbUtils = new OdbUtils();
	}

	public void setTerminalFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		UdrFmt udr_ = null;
		while (!_terminateFlag) {
			try {
				udr_ = _blockingQueue.poll(500, TimeUnit.MILLISECONDS);
				if (udr_ == null)
					continue;
				_adjUdr(udr_);
			} catch (Exception e) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_211341002_ADJ_EXCEPTION, "adjust exception", null);
				alm_.setAlm_kpi(e.toString());
				if (udr_ != null)
					alm_.setAlm_json(udr_.toGsonStr());
				L.warn("{}, sleep 500 millis", alm_.getAlm_msg(), e);
				_odbUtils.addRawAlm(null, alm_);
				PubMethod.Sleep(500);
			}
		}
		L.info("thread end");
	}

	private void _adjUdr(UdrFmt udr) {
		String bizlog_cid_ = udr.getBizlogCid();
		String bizlog_lid_ = udr.getBizlogLid();
		String bizlog_uid_ = udr.getBizlogUid();
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		Transaction tx_ = null;
		try {
			pool_ = cli_.getPool();
			jedis_ = pool_.getResource();
			tx_ = jedis_.multi();
			tx_.hincrBy(bizlog_cid_, udr._eFields[UdrFmt.E_07_A08_RCV_YMDH], -1);
			if (udr.isFeedback()) {
				tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_FBK_NML, 1);
			} else {
				tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_CNT_NML, 1);
			}
			tx_.del(bizlog_uid_);
			List<Object> rsps_ = tx_.exec();
			if (rsps_.size() != 3 || (Long) rsps_.get(2) != 1) {
				L.warn("{}, {}", bizlog_uid_, PubMethod.Collection2Str(rsps_, ":"));
			}
		} catch (Exception e) {
			String alm_ = PubMethod.FmtArgs("[%s] jedis exception, %s", bizlog_uid_, udr.toGsonStr());
			L.warn(alm_, e);
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}
}
