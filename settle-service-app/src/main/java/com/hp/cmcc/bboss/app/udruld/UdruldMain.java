package com.hp.cmcc.bboss.app.udruld;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrKafkaConsumerRunnable;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class UdruldMain {
	private static Logger L = LoggerFactory.getLogger(UdruldMain.class);
	private static BlockingQueue<UdrFmt> _BizBlockingQueue = new LinkedBlockingQueue<UdrFmt>();
	private static BlockingQueue<UdrFmt> _AdjBlockingQueue = new LinkedBlockingQueue<UdrFmt>(2000);
	private static UdrKafkaConsumerRunnable<UdrFmt> _ConsumerRunnable;
	private static UdruldBizRunnable _BizRunnable;
	private static UdruldAdjRunnable[] _AdjRunnables = new UdruldAdjRunnable[4];
	private static Thread[] _AdjThreads = new Thread[4];
	private static Thread _ConsumerThread;
	private static Thread _BizThread;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("udruld", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 3, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!UdruldCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		UdruldCfg.TraceCfg();
		MdbAgt.Init(OdbSystemParam.GetInstance(), 0, 4, 0);
		for (int i = 0; i < _AdjRunnables.length; ++i) {
			_AdjRunnables[i] = new UdruldAdjRunnable(_AdjBlockingQueue);
			_AdjThreads[i] = new Thread(_AdjRunnables[i], "ADJ" + Integer.toString(i));
		}
		_ConsumerRunnable = new UdrKafkaConsumerRunnable<UdrFmt>(_BizBlockingQueue, 4000, 100, UdrFmt.class,
				UdruldCfg._KafkaTopicNml);
		_ConsumerThread = new Thread(_ConsumerRunnable, "CONS");
		_BizRunnable = new UdruldBizRunnable(_BizBlockingQueue, _AdjBlockingQueue);
		_BizThread = new Thread(_BizRunnable, "ULDT");
		_BizRunnable.recover();
		for (int i = 0; i < _AdjThreads.length; ++i) {
			_AdjThreads[i].start();
		}
		_ConsumerThread.start();
		L.debug("*** *** start BizThread *** ***");
		_BizThread.start();
	}

	private static void _MainLoop() throws InterruptedException {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}
			L.info("try stop normal");
			_ConsumerRunnable.setTerminateFlag();
			PubMethod.Sleep(10);
			_ConsumerThread.interrupt();
			L.debug("try join thread {}", _ConsumerThread.getName());
			_ConsumerThread.join();
			L.debug("thread {} joined", _ConsumerThread.getName());

			int sz_ = 0;
			for (int i = 0; i < 1000; ++i) {
				sz_ = _BizBlockingQueue.size();
				if (sz_ == 0) {
					break;
				} else if (i % 10 == 0) {
					L.trace("{} elems still in _BizBlockingQueue, i={}", sz_, i);
				}
				PubMethod.Sleep(100);
			}
			L.debug("_BizBlockingQueue.size()={}", _BizBlockingQueue.size());

			_BizRunnable.setTerminateFlag();
			L.debug("try join thread {}", _BizThread.getName());
			_BizThread.join();
			L.debug("thread {} joined", _BizThread.getName());

			for (int i = 0; i < 1000; ++i) {
				sz_ = _AdjBlockingQueue.size();
				if (sz_ == 0) {
					break;
				} else if (i % 10 == 0) {
					L.trace("{} elems still in _AdjBlockingQueue, i={}", sz_, i);
				}
				PubMethod.Sleep(100);
			}
			L.debug("_AdjBlockingQueue.size()={}", _AdjBlockingQueue.size());

			break;
		}
	}
}
