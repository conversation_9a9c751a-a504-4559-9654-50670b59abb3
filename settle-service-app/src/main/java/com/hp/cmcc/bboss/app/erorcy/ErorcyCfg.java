package com.hp.cmcc.bboss.app.erorcy;

import java.io.File;
import java.util.Map;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbBizTypeDefRec;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class ErorcyCfg {
	public static Logger L = LoggerFactory.getLogger(ErorcyCfg.class);
	public static int _Interval;
	public static String _WrkDir;
	public static String _FailDir;
	public static Map<String, ErorcyBizCfg> _BizCfgMap = new TreeMap<String, ErorcyBizCfg>();
	public static Map<String, DbBizTypeDefRec> _BizTypeMap = new TreeMap<String, DbBizTypeDefRec>();

	public static boolean Init() {
		boolean rc_ = OdbSystemParam.GetInstance().refresh();
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}

		OdbUtils odb_utils_ = new OdbUtils();
		if (!odb_utils_.getBizTypeDef(_BizTypeMap, null)) {
			L.warn("init BIZ_TYPE_DEF error");
			rc_ = false;
		}

		if (!_InitAppParam()) {
			L.warn("call _InitAppParam error");
			rc_ = false;
		}
		return rc_;
	}

	private static boolean _InitAppParam() {
		String inst_nm_ = AppCmdline.GetInstance()._instNm;
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(AppCmdline.GetInstance()._module, inst_nm_);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		Long lval_ = app_param_.chkValNum("COMMON", "INTERVAL", 1L, 3600L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_Interval = lval_.intValue();
		}

		File fval_ = app_param_.chkValFile("COMMON", "WRK_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_WrkDir = fval_.getAbsolutePath();
		}

		fval_ = app_param_.chkValFile("COMMON", "FAIL_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_FailDir = fval_.getAbsolutePath();
		}

		if (!_InitBizCfg(app_param_))
			rc_ = false;

		return rc_;
	}

	private static boolean _InitBizCfg(OdbAppParam app_param) {
		boolean rc_ = true;
		String sval_;
		File fval_;
		ErorcyBizCfg biz_cfg_ = null;
		Map<String, ErorcyBizCfg> biz_cfg_map_ = new TreeMap<String, ErorcyBizCfg>();
		for (String sec_nm_ : app_param._paramMap.keySet()) {
			if (!sec_nm_.startsWith("RCY."))
				continue;
			if (biz_cfg_map_.containsKey(sec_nm_)) {
				L.warn("absurd!, section [{}] already exists", sec_nm_);
				continue;
			}
			biz_cfg_ = new ErorcyBizCfg();
			biz_cfg_._bizNm = sec_nm_;

			sval_ = app_param.chkValStr(sec_nm_, "BIZ_TYPE", null);
			if (sval_ == null) {
				biz_cfg_._bizNm = null;
			} else {
				biz_cfg_._bizType = sval_;
				DbBizTypeDefRec biz_type_def_rec_ = _BizTypeMap.get(sval_);
				if (biz_type_def_rec_ == null) {
					L.warn("{}==>BIZ_TYPE [{}] not exists in BIZ_TYPE_DEF", sec_nm_, sval_);
					biz_cfg_._bizNm = null;
				} else {
					biz_cfg_._typeId = biz_type_def_rec_.getType_id();
				}
			}

			fval_ = app_param.chkValFile(sec_nm_, "DST_DIR", true, true, true);
			if (fval_ == null) {
				biz_cfg_._bizNm = null;
			} else {
				biz_cfg_._dstDir = fval_.getAbsolutePath();
			}

			sval_ = app_param.chkValStr(sec_nm_, "CHARSET", null);
			if (sval_ == null) {
				biz_cfg_._bizNm = null;
			} else {
				biz_cfg_._charset = sval_;
			}

			if (biz_cfg_._bizNm == null) {
				L.warn("section [{}] init error", sec_nm_);
				rc_ = false;
			} else {
				biz_cfg_map_.put(biz_cfg_._bizNm, biz_cfg_);
			}
		}

		if (biz_cfg_map_.isEmpty()) {
			L.warn("no valid 'RCY.*' section found");
			rc_ = false;
		}
		if (rc_) {
			_BizCfgMap = biz_cfg_map_;
			L.debug("{} 'RCY.*' sections initialized", _BizCfgMap.size());
		}
		return rc_;
	}
}
