// THIS CLASS IS OBSOLETED !!!

package com.hp.cmcc.bboss.app.shpmsv;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.ArrayList;
import java.util.List;

public class ShpmsvDustmanRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(ShpmsvDustmanRunnable.class);
	private int _hisVerKeepHours;
	private boolean _terminateFlag = false;

	public ShpmsvDustmanRunnable(int his_ver_keep_hours) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_hisVerKeepHours = his_ver_keep_hours;
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5 * 1000);
			}
		}
		L.info("thread terminated");
	}

	private void _exec() {
		for (int i = 0; i < 90; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				return;
			}
		}
		long now_ = System.currentTimeMillis();
		now_ -= _hisVerKeepHours * 3600 * 1000;
		String oldest_ver_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14).substring(2, 12);

		List<String> expire_ver_list_ = _queryExpireVer(true, oldest_ver_);
		if (expire_ver_list_ != null) {
			for (String ver_ : expire_ver_list_) {
				if (_clearExpireVer(ver_)) {
					_removeExpireVer(true, ver_);
				}
			}
		}

		expire_ver_list_ = _queryExpireVer(false, oldest_ver_);
		if (expire_ver_list_ != null) {
			for (String ver_ : expire_ver_list_) {
				if (_clearExpireVer(ver_)) {
					_removeExpireVer(false, ver_);
				}
			}
		}
	}

	private List<String> _queryExpireVer(boolean is_tmp, String oldest_ver) {
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		String k = is_tmp ? MdbConst.SHPARM_KEY_VER_TMP : MdbConst.SHPARM_KEY_VER_CMT;
		List<String> result_ = new ArrayList<String>();
		try {
			List<String> range_ = jedis_.lrange(k, 0, -1);
			if (range_ != null && !range_.isEmpty()) {
				for (String v : range_) {
					if (oldest_ver.compareTo(v) > 0) {
						L.trace("{} ver {} older than {}, expired", new Object[] { k, v, oldest_ver });
						result_.add(v);
					}
				}
			}
		} catch (Exception e) {
			L.warn("jedis exception", e);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
			result_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return result_;
	}

	private void _removeExpireVer(boolean is_tmp, String expire_ver) {
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		String k = is_tmp ? "shparm:tmp" : "shparm:ver";
		try {
			Long cnt_ = jedis_.lrem(k, 0, expire_ver);
			if (cnt_ != null) {
				L.info("expire ver [{}] removed, ({})", expire_ver, cnt_);
			} else {
				L.warn("lrem({}, 0, {}) returns null", k, expire_ver);
			}
		} catch (Exception e) {
			L.warn("jedis exception", e);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}

	private boolean _clearExpireVer(String expire_ver) {
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		ScanParams scan_params_ = new ScanParams();
		scan_params_.match(expire_ver + ":*");
		int del_cnt_ = 0;
		try {
			String cursor_ = ScanParams.SCAN_POINTER_START;
			while (true) {
				ScanResult<String> scan_result_ = jedis_.scan(cursor_, scan_params_);
				for (String k : scan_result_.getResult()) {
					jedis_.del(k);
					L.trace("key [{}] deleted", k);
					del_cnt_++;
				}
				cursor_ = scan_result_.getCursor();
				if (cursor_.equals(ScanParams.SCAN_POINTER_START)) {
					break;
				}
			}
			L.info("expire_ver [{}] {} keys deleted", expire_ver, del_cnt_);
			return true;
		} catch (Exception e) {
			L.warn("jedis exception", e);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
			return false;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}
}
