package com.hp.cmcc.bboss.app.datsum;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class DatsumOdb {
	private static Logger L = LoggerFactory.getLogger(DatsumOdb.class);
	private static final String _Q_DATSUM_CUBE_CFG = "SELECT FMT_NM, SUM_IDX, SRC_IDX, EVAL, SUM_ATTR, OPT_SRC_IDX, OPT_EVAL, "
			+ "AUD_FLAG FROM DATSUM_CUBE_CFG WHERE FMT_NM = ? ORDER BY SUM_IDX";
	private static final String _I_DATSUM_FILE_LOG = "INSERT INTO DATSUM_FILE_LOG (LOG_MM, SUM_FILE_NM, SUM_FILE_PFX, "
			+ "SUM_FILE_TYPE, ACNT_YM, SUM_YMD, SUM_FILE_SEQ, INST_NM, FMT_NM, SUM_LINES, RAW_LINES, AUD_VAL, TS_START, "
			+ "TS_END) VALUES (?, ? ,? ,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String _I_DATSUM_PROC_LOG = "INSERT INTO DATSUM_PROC_LOG (LOG_MM, SUM_FILE_NM, RAW_FILE_NM, "
			+ "RAW_LINES, AUD_VAL, SPARE_NUM1) VALUES (?, ?, ?, ?, ?, ?)";

	public List<DbDatsumCubeCfgRec> getSumCubeCfg(String fmt_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbDatsumCubeCfgRec> db_result_ = cli_.queryForOList(_Q_DATSUM_CUBE_CFG, DbDatsumCubeCfgRec.class, fmt_nm);
		if (db_result_ == null)
			L.warn("query SUM_CUBE_CFG for [{}] returns null", fmt_nm);
		return db_result_;
	}

	public void addSttlSumFileLog(Connection conn, DbDatsumFileLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		cli_.update(conn, _I_DATSUM_FILE_LOG, rec.asInsertObjArray());
		L.info("add {}, transaction {}", rec.toGsonStr(), conn.toString());
	}

	public void addSttlSumProcLog(Connection conn, DbDatsumProcLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		cli_.update(conn, _I_DATSUM_PROC_LOG, rec.asInsertObjArray());
		L.info("add {}, transaction {}", rec.toGsonStr(), conn.toString());
	}
}
