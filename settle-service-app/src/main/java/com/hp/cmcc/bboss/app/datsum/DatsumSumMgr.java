package com.hp.cmcc.bboss.app.datsum;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

import javax.script.Bindings;
import javax.script.Compilable;
import javax.script.CompiledScript;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.SimpleBindings;

import org.luaj.vm2.LuaTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;
import com.hp.cmcc.bboss.pub.edb.entity.EdbInputFilesRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbSumTrgCtlRec;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.udr.UdrFileHandle;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PatternFileFilter;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DatsumSumMgr {
	private static Logger L = LoggerFactory.getLogger(DatsumSumMgr.class);
	private static final String _C_TBL_SUM_TRG_CTL = "CREATE TABLE IF NOT EXISTS SUM_TRG_CTL (TRG_KEY TEXT NOT NULL, "
			+ "TRG_TM_TS INTEGER NOT NULL, TRG_TM_STR TEXT NOT NULL, SUM_FILE_NM TEXT, UNIQUE (TRG_KEY, TRG_TM_TS) "
			+ "ON CONFLICT REPLACE)";
	private static final String _I_SUM_TRG_CTL = "INSERT INTO SUM_TRG_CTL (TRG_KEY, TRG_TM_TS, TRG_TM_STR, SUM_FILE_NM) "
			+ "VALUES (?, ?, ?, ?)";
	private static final String _D_SUM_TRG_CTL = "DELETE FROM SUM_TRG_CTL WHERE TRG_TM_TS < ?";
	private static final String _Q_SUM_TRG_CTL = "SELECT TRG_KEY, TRG_TM_TS, TRG_TM_STR, SUM_FILE_NM FROM SUM_TRG_CTL "
			+ "WHERE TRG_KEY = ? ORDER BY TRG_TM_TS DESC LIMIT 1";
	private static final String _C_TBL_INPUT_FILES = "CREATE TABLE IF NOT EXISTS INPUT_FILES (INPUT_FNM TEXT NOT NULL, "
			+ "FILE_TYPE INTEGER NOT NULL, ACNT_YM INTEGER NOT NULL, FILE_SZ INTEGER NOT NULL, RAW_LINES INTEGER NOT NULL, "
			+ "FILE_TM_TS INTEGER NOT NULL, FILE_TM_STR TEXT NOT NULL, SCAN_TM_TS INTEGER NOT NULL, SCAN_TM_STR TEXT "
			+ "NOT NULL, DB_TM_STR TEXT, UNIQUE (INPUT_FNM) ON CONFLICT REPLACE)";
	private static final String _C_IX1_INPUT_FILES = "CREATE INDEX IF NOT EXISTS IX1_INPUT_FILES ON INPUT_FILES "
			+ "(FILE_TYPE, ACNT_YM)";
	private static final String _Q_INPUT_FILES_ALL = "SELECT INPUT_FNM, FILE_TYPE, ACNT_YM, FILE_SZ, RAW_LINES, FILE_TM_TS, "
			+ "FILE_TM_STR, SCAN_TM_TS, SCAN_TM_STR, DB_TM_STR FROM INPUT_FILES ORDER BY INPUT_FNM";
	private static final String _D_INPUT_FILES = "DELETE FROM INPUT_FILES WHERE INPUT_FNM = ?";
	private static final String _I_INPUT_FILES = "INSERT INTO INPUT_FILES (INPUT_FNM, FILE_TYPE, ACNT_YM, FILE_SZ, RAW_LINES, "
			+ "FILE_TM_TS, FILE_TM_STR, SCAN_TM_TS, SCAN_TM_STR, DB_TM_STR) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, "
			+ "STRFTIME('%Y-%m-%d %H:%M:%S.%f', 'NOW', 'LOCALTIME'))";
	private BlockingQueue<DatsumQElem>[] _sumQueues;
	private AtomicBoolean[] _eobFlags;
	private PatternFileFilter _filter;
	private long _scanTs;
	private Map<String, List<EdbInputFilesRec>> _triggeredMap;
	private DbDatsumFileLogRec _sumFileLogRec;
	private List<DbDatsumProcLogRec> _sumProcLogRecList;
	private Connection _sqlite;
	private Statement _stmt;
	private AppTicker _batchTicker;
	private UdrFileHandle _sumFh;
	private int _batchRawLines;
	private long _batchSumLines;
	private long _batchAudVal;
	private String[] _dumpFields;
	private StringBuilder _sb;
	private Gson _json;
	private OdbUtils _odbUtils;
	private Bindings _bindings;
	private ScriptEngine _luaj;
	CompiledScript _inputFnmParserCS;
	private Map<String, CompiledScript> _csMap;

	public DatsumSumMgr(BlockingQueue<DatsumQElem>[] sum_queues, AtomicBoolean[] eob_flags) {
		_sumQueues = sum_queues;
		_eobFlags = eob_flags;
		_scanTs = System.currentTimeMillis();
		_triggeredMap = new TreeMap<String, List<EdbInputFilesRec>>();
		_batchTicker = new AppTicker();
		_sumProcLogRecList = new ArrayList<DbDatsumProcLogRec>();
		_sb = new StringBuilder();
		_json = new Gson();
		_odbUtils = new OdbUtils();
		_bindings = new SimpleBindings();
	}

	public boolean init() {
		try {
			_filter = new PatternFileFilter(Pattern.compile(DatsumCfg._InputFnmPattern), PatternFileFilter.TypeFilter.TypeFile);
			_initSqlite();
			_dumpFields = new String[DatsumCfg._SumCubeCfg.length];
			_luaj = new ScriptEngineManager().getEngineByName("luaj");
			_inputFnmParserCS = ((Compilable) _luaj).compile(DatsumCfg._InputFnmParser);
			_csMap = DatsumCfg.PreCompileEvalScripts(_luaj);
			L.trace("{} entries in _csMap", _csMap.size());
			return true;
		} catch (Exception e) {
			L.warn("init exception, pls chk", e);
			return false;
		}
	}

	public void run() throws Exception {
		_scanInputFiles();
		_refreshTriggeredMap();
		if (_triggeredMap.isEmpty())
			return;

		L.trace("{} keys [{}]", _triggeredMap.size(), PubMethod.Collection2Str(_triggeredMap.keySet(), ", "));
		for (Entry<String, List<EdbInputFilesRec>> ent_ : _triggeredMap.entrySet()) {
			_runOneBatch(ent_.getKey(), ent_.getValue());
		}
		_purgeExpiredSumTrgCtl();
	}

	public void _runOneBatch(String sum_grp_key, List<EdbInputFilesRec> input_files) throws Exception {
		_batchTicker.tickStart();
		_batchRawLines = 0;
		_batchSumLines = 0;
		_batchAudVal = 0;
		L.debug("sum_grp_key {}, {} files start", sum_grp_key, input_files.size());
		_bob(sum_grp_key);
		for (EdbInputFilesRec rec_ : input_files) {
			_procOneFile(new File(DatsumCfg._InputDir + "/" + rec_._inputFnm));
		}
		_eob();
		_dumpSumFile();
		_batchTicker.tickEnd(_batchRawLines);
		L.info("{} cnt:dur:pfm {}:{}:{}", _sumFileLogRec.getSum_file_nm(), _batchTicker._recNum, _batchTicker._term,
				_batchTicker._pfm);
		_postProc();
		_bakInputFiles(input_files);
		L.debug("sum_grp_key {}, {} files end", sum_grp_key, input_files.size());
		EdbSumTrgCtlRec sum_trg_ctl_rec_ = new EdbSumTrgCtlRec();
		sum_trg_ctl_rec_._trgKey = sum_grp_key;
		sum_trg_ctl_rec_._sumFileNm = _sumFileLogRec.getSum_file_nm();
		_addSumTrgCtlRec(sum_trg_ctl_rec_);
	}

	private void _procOneFile(File f) throws Exception {
		String raw_line_ = null;
		BufferedReader br_ = null;
		DbDatsumProcLogRec sum_proc_log_ = new DbDatsumProcLogRec();
		sum_proc_log_.setLog_mm(_sumFileLogRec.getLog_mm());
		sum_proc_log_.setRaw_file_nm(f.getName());
		try {
			br_ = new BufferedReader(new InputStreamReader(new FileInputStream(f), DatsumCfg._InputCharset));
			L.trace("[{}] opened", f.getAbsolutePath());
			while ((raw_line_ = br_.readLine()) != null) {
				_procOneLine(raw_line_, sum_proc_log_);
			}
			L.info("[{}] {} lines dispatch done, file_aud_={}", f.getAbsolutePath(), sum_proc_log_.getRaw_lines(),
					sum_proc_log_.getAud_val());
			_sumProcLogRecList.add(sum_proc_log_);
		} finally {
			PubMethod.Close(br_);
		}
	}

	private void _procOneLine(String raw_line, DbDatsumProcLogRec proc_log) {
		++_batchRawLines;
		if (proc_log.getRaw_lines() == null)
			proc_log.setRaw_lines(0L);
		proc_log.setRaw_lines(proc_log.getRaw_lines() + 1L);
		DatsumQElem qelem_ = _initQElem(raw_line);
		int key_field_cnt_ = 0;
		_sb.delete(0, _sb.length());
		for (DbDatsumCubeCfgRec cfg_ : DatsumCfg._SumSrcKeyCfgList) {
			if (key_field_cnt_ > 0)
				_sb.append(DatsumCfg._InputDelimiterRaw);
			_sb.append(_evalKeyField(cfg_, qelem_));
			++key_field_cnt_;
		}
		qelem_.setKeyStr(_sb.substring(0));
		int thread_hash_ = qelem_.calcHashRemainder(DatsumCfg._WorkerThreadNum);
		_sumQueues[thread_hash_].add(qelem_);
		_adjAudVal(qelem_, proc_log);
		if (DatsumCfg._OptSrcKeyIdxList.size() > 0) {
			_procOptElem(raw_line, qelem_, proc_log);
		}
		if (_batchRawLines % 100000 == 0)
			L.trace("_batchRawLines={}", _batchRawLines);
		int queued_ = _sumQueues[thread_hash_].size();
		if (queued_ > 10000)
			PubMethod.Sleep(20);
	}

	private void _procOptElem(String raw_line, DatsumQElem qelem, DbDatsumProcLogRec proc_log) {
		boolean opt_key_all_empty_ = true;
		for (int src_idx_ : DatsumCfg._OptSrcKeyIdxList) {
			if (!PubMethod.IsEmpty(qelem._fields[src_idx_])) {
				opt_key_all_empty_ = false;
				break;
			}
		}
		if (opt_key_all_empty_)
			return;
		DatsumQElem qelem_ = _initQElem(raw_line);
		int key_field_cnt_ = 0;
		_sb.delete(0, _sb.length());
		for (int src_idx_ : DatsumCfg._SumOptKeyIdxList) {
			if (key_field_cnt_ > 0)
				_sb.append(DatsumCfg._InputDelimiterRaw);
			if (qelem_._fields[src_idx_] != null)
				_sb.append(qelem_._fields[src_idx_]);
			++key_field_cnt_;
		}
		qelem_.setKeyStr(_sb.substring(0));
		qelem_._isOptRec = true;
		int thread_hash_ = qelem_.calcHashRemainder(DatsumCfg._WorkerHashNum);
		//L.trace("{}", qelem_.toGsonStr());
		_sumQueues[thread_hash_].add(qelem_);
		if (proc_log.getSpare_num1() == null)
			proc_log.setSpare_num1(0L);
		proc_log.setSpare_num1(proc_log.getSpare_num1() + 1L);
	}

	private String _evalKeyField(DbDatsumCubeCfgRec cfg, DatsumQElem qelem) {
		String result_ = "";
		if (PubMethod.IsEmpty(cfg.getEval())) {
			if (!PubMethod.IsEmpty(qelem._fields[cfg.getSrc_idx()])) {
				result_ = qelem._fields[cfg.getSrc_idx()];
			}
		} else {
			CompiledScript cs_ = _csMap.get(cfg.getEval());
			result_ = cfg.eval(cs_, _bindings, qelem);
		}
		return result_;
	}

	private void _adjAudVal(DatsumQElem qelem, DbDatsumProcLogRec proc_log) {
		if (proc_log.getAud_val() == null)
			proc_log.setAud_val(0L);
		int aud_src_idx_ = DatsumCfg._AudFlagRec.getSrc_idx();
		if (aud_src_idx_ >= 0) {
			if (qelem._fields[aud_src_idx_] != null) {
				long aud_ = Long.parseLong(qelem._fields[aud_src_idx_]);
				_batchAudVal += aud_;
				proc_log.setAud_val(proc_log.getAud_val() + aud_);
			}
		} else {
			++_batchAudVal;
			proc_log.setAud_val(proc_log.getAud_val() + 1L);
		}
	}

	private DatsumQElem _initQElem(String raw_line) {
		DatsumQElem qelem_ = new DatsumQElem();
		if (DatsumCfg._InputIsJson) {
			UdrFmt udr_ = _json.fromJson(raw_line, UdrFmt.class);
			qelem_._fields = udr_._uFields;
			qelem_._udr = udr_;
		} else {
			qelem_._fields = raw_line.split(DatsumCfg._InputDelimiterExp);
		}
		return qelem_;
	}

	private void _scanInputFiles() throws SQLException {
		Map<String, EdbInputFilesRec> edb_query_map_ = _getAllInputFiles();
		File input_dir_ = new File(DatsumCfg._InputDir);
		File[] files_ = input_dir_.listFiles(_filter);
		if (files_ != null && files_.length > 0)
			Arrays.sort(files_);
		Map<String, File> scan_map_ = new TreeMap<String, File>();
		for (File f : files_)
			scan_map_.put(f.getName(), f);
		//L.trace("{} files scaned under {}", scan_map_.size(), input_dir_.getAbsolutePath());

		long now_ = System.currentTimeMillis();
		if (!scan_map_.isEmpty()) {
			_scanTs = now_;
		} else if (now_ - _scanTs > 3600 * 1000) {
			L.info("no files scaned for {} seconds", (now_ - _scanTs) / 1000);
			_scanTs = now_;
		}

		Map<String, EdbInputFilesRec> edb_erase_map_ = new TreeMap<String, EdbInputFilesRec>();
		for (Entry<String, EdbInputFilesRec> ent_ : edb_query_map_.entrySet()) {
			if (scan_map_.get(ent_.getKey()) == null) {
				L.debug("[{}] not exists any more", ent_.getKey());
				edb_erase_map_.put(ent_.getKey(), ent_.getValue());
			}
		}
		for (String k : edb_erase_map_.keySet())
			edb_query_map_.remove(k);

		Map<String, EdbInputFilesRec> edb_delta_map_ = new TreeMap<String, EdbInputFilesRec>();
		EdbInputFilesRec edb_rec_ = null;
		for (File f : files_) {
			edb_rec_ = edb_query_map_.get(f.getName());
			if (edb_rec_ != null) {
				long last_modified_ = f.lastModified();
				if (last_modified_ == edb_rec_._fileTmTs) {
					//L.trace("no need parse [{}] again", f.getName());
					continue;
				}
				L.debug("[{}] was touched o:n [{} : {}], need re-parse", f.getName(), edb_rec_._fileTmStr,
						PubMethod.Long2Str(last_modified_, PubMethod.TimeStrFmt.Fmt23));
			}
			edb_rec_ = _parseOneInputFile(f);
			edb_delta_map_.put(edb_rec_._inputFnm, edb_rec_);
		}
		_mergeInputFiles(edb_erase_map_, edb_delta_map_);
	}

	private void _mergeInputFiles(Map<String, EdbInputFilesRec> erase_map, Map<String, EdbInputFilesRec> delta_map)
			throws SQLException {
		if (erase_map.isEmpty() && delta_map.isEmpty()) {
			//L.trace("no change for INPUT_FILES");
			return;
		}

		PreparedStatement pstmt_ = null;
		_stmt.executeUpdate("BEGIN TRANSACTION");
		if (!erase_map.isEmpty()) {
			pstmt_ = _sqlite.prepareStatement(_D_INPUT_FILES);
			for (String k : erase_map.keySet()) {
				pstmt_.setString(1, k);
				pstmt_.execute();
				L.trace("[{}] earsed from INPUT_FILES", k);
			}
			pstmt_.close();
		}
		if (!delta_map.isEmpty()) {
			pstmt_ = _sqlite.prepareStatement(_I_INPUT_FILES);
			for (Entry<String, EdbInputFilesRec> ent_ : delta_map.entrySet()) {
				EdbInputFilesRec rec_ = ent_.getValue();
				pstmt_.setString(1, rec_._inputFnm);
				pstmt_.setInt(2, rec_._fileType);
				pstmt_.setInt(3, rec_._acntYm);
				pstmt_.setLong(4, rec_._fileSz);
				pstmt_.setInt(5, rec_._rawLines);
				pstmt_.setLong(6, rec_._fileTmTs);
				pstmt_.setString(7, rec_._fileTmStr);
				pstmt_.setLong(8, rec_._scanTmTs);
				pstmt_.setString(9, rec_._scanTmStr);
				pstmt_.execute();
				L.trace("[{}] merged into INPUT_FILES", rec_._inputFnm);
			}
		}
		_stmt.executeUpdate("COMMIT TRANSACTION");
		pstmt_.close();
	}

	private void _addSumTrgCtlRec(EdbSumTrgCtlRec rec) throws SQLException {
		rec._trgTmTs = System.currentTimeMillis();
		rec._trgTmStr = PubMethod.Long2Str(rec._trgTmTs, PubMethod.TimeStrFmt.Fmt23);
		PreparedStatement pstmt_ = _sqlite.prepareStatement(_I_SUM_TRG_CTL);
		pstmt_.setString(1, rec._trgKey);
		pstmt_.setLong(2, rec._trgTmTs);
		pstmt_.setString(3, rec._trgTmStr);
		pstmt_.setString(4, rec._sumFileNm);
		pstmt_.execute();
		L.trace("SUM_TRG_CTL add {}", rec.toGsonStr());
		pstmt_.close();
	}

	private void _purgeExpiredSumTrgCtl() throws SQLException {
		PreparedStatement pstmt_ = _sqlite.prepareStatement(_D_SUM_TRG_CTL);
		long expire_ = System.currentTimeMillis();
		expire_ -= 86400L * 3L * 1000L;
		pstmt_.setLong(1, expire_);
		int rows_affected_ = pstmt_.executeUpdate();
		if (rows_affected_ > 0) {
			L.trace("SUM_TRG_CTL {} expired record(s) purged", rows_affected_);
		}
		pstmt_.close();
	}

	private EdbSumTrgCtlRec _queryNewestSumTrgCtlRec(String trg_key) throws SQLException {
		EdbSumTrgCtlRec rec_ = null;
		PreparedStatement pstmt_ = _sqlite.prepareStatement(_Q_SUM_TRG_CTL);
		pstmt_.setString(1, trg_key);
		ResultSet rs_ = pstmt_.executeQuery();
		while (rs_.next()) {
			rec_ = new EdbSumTrgCtlRec();
			rec_._trgKey = rs_.getString(1);
			rec_._trgTmTs = rs_.getLong(2);
			rec_._trgTmStr = rs_.getString(3);
			rec_._sumFileNm = rs_.getString(4);
		}
		rs_.close();
		pstmt_.close();
		return rec_;
	}

	EdbInputFilesRec _parseOneInputFile(File f) {
		EdbInputFilesRec r = new EdbInputFilesRec();
		r._scanTmTs = System.currentTimeMillis();
		r._scanTmStr = PubMethod.Long2Str(r._scanTmTs, PubMethod.TimeStrFmt.Fmt23);
		r._inputFnm = f.getName();
		try {
			_bindings.clear();
			_bindings.put("RAW_FILE_NM", f.getName());
			LuaTable lua_tbl_ = (LuaTable) _inputFnmParserCS.eval(_bindings);
			String file_type_ = lua_tbl_.get(1).toString();
			String acnt_ym_ = lua_tbl_.get(2).toString();
			r._fileType = Integer.parseInt(file_type_);
			r._acntYm = Integer.parseInt(acnt_ym_);
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_207341001_PARSE_FNM_EXCEPTION, PubMethod.FmtArgs(
					"[%s] parse fnm with [%s] exception", f.getName(), DatsumCfg._InputFnmParser), null);
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			return null;
		}
		r._fileSz = f.length();
		r._fileTmTs = f.lastModified();
		r._fileTmStr = PubMethod.Long2Str(r._fileTmTs, PubMethod.TimeStrFmt.Fmt23);
		LineNumberReader lnr_ = null;
		try {
			lnr_ = new LineNumberReader(new FileReader(f));
			lnr_.skip(Long.MAX_VALUE);
			r._rawLines = lnr_.getLineNumber();
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_207341002_COUNT_LINES_EXCEPTION, PubMethod.FmtArgs(
					"[%s] count lines exception", f.getName()), null);
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_kpi(), e);
			_odbUtils.addRawAlm(null, alm_);
			return null;
		} finally {
			PubMethod.Close(lnr_);
		}
		return r;
	}

	private void _refreshTriggeredMap() throws SQLException {
		_triggeredMap.clear();
		boolean trigger_flag_ = DatsumCfg._TriggerFlag.get();
		if (trigger_flag_) {
			L.info("_TriggerFlag flag detected");
			DatsumCfg._TriggerFlag.set(false);
		}

		Map<String, EdbInputFilesRec> edb_query_map_ = _getAllInputFiles();
		if (edb_query_map_.isEmpty()) {
			if (trigger_flag_) {
				L.info("no files scaned, void _TriggerFlag");
			}
			return;
		}

		List<EdbInputFilesRec> input_list_ = null;
		if (trigger_flag_) {
			for (EdbInputFilesRec rec_ : edb_query_map_.values()) {
				String trg_key_ = rec_.getTrgKey();
				input_list_ = _triggeredMap.get(trg_key_);
				if (input_list_ == null) {
					input_list_ = new ArrayList<EdbInputFilesRec>();
					_triggeredMap.put(trg_key_, input_list_);
				}
				input_list_.add(rec_);
			}
			return;
		}

		Map<String, Long> sum_lines_map_ = new TreeMap<String, Long>();
		for (Entry<String, EdbInputFilesRec> ent_ : edb_query_map_.entrySet()) {
			EdbInputFilesRec rec_ = ent_.getValue();
			String trg_key_ = rec_.getTrgKey();
			if (sum_lines_map_.containsKey(trg_key_)) {
				long sum_ = sum_lines_map_.get(trg_key_) + rec_._rawLines;
				sum_lines_map_.put(trg_key_, sum_);
			} else {
				sum_lines_map_.put(trg_key_, new Long(rec_._rawLines));
			}
		}

		for (Entry<String, Long> ent_ : sum_lines_map_.entrySet()) {
			if (ent_.getValue() >= DatsumCfg._TriggerLines) {
				L.debug("{} has {} sum_lines, ge {}", ent_.getKey(), ent_.getValue(), DatsumCfg._TriggerLines);
				input_list_ = new ArrayList<EdbInputFilesRec>();
				for (EdbInputFilesRec rec_ : edb_query_map_.values()) {
					String trg_key_ = rec_.getTrgKey();
					if (ent_.getKey().equals(trg_key_)) {
						input_list_.add(rec_);
					}
				}
				_triggeredMap.put(ent_.getKey(), input_list_);
				L.debug("{} has {} associated input files", ent_.getKey(), input_list_.size());
			} else {
				L.trace("{} has only {} sum_lines, lt {}, ignore", ent_.getKey(), ent_.getValue(), DatsumCfg._TriggerLines);
			}
		}

		if (DatsumCfg._TriggerInterval <= 0)
			return;

		long now_ = System.currentTimeMillis();
		long trg_ts_ = now_ - DatsumCfg._TriggerInterval * 1000;
		Set<String> sum_keys_ = sum_lines_map_.keySet();
		Set<String> trg_keys_ = _triggeredMap.keySet();
		sum_keys_.removeAll(trg_keys_);
		for (String k : sum_keys_) {
			EdbSumTrgCtlRec ctl_ = _queryNewestSumTrgCtlRec(k);
			if (ctl_ != null) {
				if (ctl_._trgTmTs >= trg_ts_) {
					continue;
				} else {
					L.trace("{}, {} seconds ago, gt {}, will be triggered", k, (now_ - ctl_._trgTmTs) / 1000,
							DatsumCfg._TriggerInterval);
				}
			} else {
				L.trace("{}, has no previous sum_trg_ctl, will be triggered", k);
			}
			input_list_ = new ArrayList<EdbInputFilesRec>();
			for (EdbInputFilesRec rec_ : edb_query_map_.values()) {
				String trg_key_ = rec_.getTrgKey();
				if (k.equals(trg_key_)) {
					input_list_.add(rec_);
				}
			}
			_triggeredMap.put(k, input_list_);
			L.debug("{} newest sum_trg_ctl {}, force sum {} files", k, ctl_ == null ? "null" : ctl_.toGsonStr(), input_list_.size());
		}
	}

	private void _initSqlite() throws Exception {
		Class.forName("org.sqlite.JDBC");
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		String db_file_path_ = String.format("%s/CACHE_%s_%s.db3", DatsumCfg._WorkingDir, cmdline_._module, cmdline_._instNm);
		File dbf_ = new File(db_file_path_);
		if (dbf_.isFile()) {
			L.trace("{} already exists", dbf_.getAbsolutePath());
		} else {
			L.trace("{} not exists yet", dbf_.getAbsolutePath());
		}
		_sqlite = DriverManager.getConnection("jdbc:sqlite:" + dbf_.getAbsolutePath());
		L.info("try initialize {}", dbf_.getAbsolutePath());
		_stmt = _sqlite.createStatement();
		_stmt.executeUpdate(_C_TBL_SUM_TRG_CTL);
		L.trace("SQLite table SUM_TRG_CTL verified");
		_stmt.executeUpdate(_C_TBL_INPUT_FILES);
		L.trace("SQLite table INPUT_FILES verified");
		_stmt.executeUpdate(_C_IX1_INPUT_FILES);
		L.trace("SQLite index IX1_INPUT_FILES verified");
	}

	private Map<String, EdbInputFilesRec> _getAllInputFiles() throws SQLException {
		ResultSet rs_ = null;
		try {
			rs_ = _stmt.executeQuery(_Q_INPUT_FILES_ALL);
			Map<String, EdbInputFilesRec> input_files_map_ = new TreeMap<String, EdbInputFilesRec>();
			while (rs_.next()) {
				EdbInputFilesRec rec_ = new EdbInputFilesRec();
				rec_._inputFnm = rs_.getString(1);
				rec_._fileType = rs_.getInt(2);
				rec_._acntYm = rs_.getInt(3);
				rec_._fileSz = rs_.getLong(4);
				rec_._rawLines = rs_.getInt(5);
				rec_._fileTmTs = rs_.getLong(6);
				rec_._fileTmStr = rs_.getString(7);
				rec_._scanTmTs = rs_.getLong(8);
				rec_._scanTmStr = rs_.getString(9);
				rec_._dbTmStr = rs_.getString(10);
				input_files_map_.put(rec_._inputFnm, rec_);
			}
			//L.trace("{} records queried from INPUT_FILES", input_files_map_.size());
			return input_files_map_;
		} finally {
			_closeResultSet(rs_);
		}
	}

	private void _bakInputFiles(List<EdbInputFilesRec> input_files) {
		for (EdbInputFilesRec rec_ : input_files) {
			String src_path_ = DatsumCfg._InputDir + "/" + rec_._inputFnm;
			String bak_path_ = DatsumCfg._BakDir + "/" + rec_._inputFnm;
			if (PubMethod.MoveAFile(src_path_, bak_path_)) {
				L.info("bak {} to {} ok", src_path_, bak_path_);
			} else {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_207341003_JSON_BAK_FAIL, PubMethod.FmtArgs(
						"bak [%s] to [%s] error", src_path_, bak_path_), _sumFileLogRec.toGsonStr());
				L.warn("{}", alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
			}
		}
	}

	private void _bob(String sum_grp_key) {
		String[] type_ym_ = sum_grp_key.split("_");
		_sumFileLogRec = new DbDatsumFileLogRec();
		_sumFileLogRec.setTs_start(_batchTicker._tsStart);
		_sumFileLogRec.setSum_ymd(Integer.parseInt(PubMethod.Long2Str(_sumFileLogRec.getTs_start(), PubMethod.TimeStrFmt.Fmt8)));
		_sumFileLogRec.setLog_mm((_sumFileLogRec.getSum_ymd() / 100) % 100);
		_sumFileLogRec.setSum_file_pfx(DatsumCfg._OutputFnmPfx);
		_sumFileLogRec.setSum_file_type(Integer.parseInt(type_ym_[0]));
		_sumFileLogRec.setAcnt_ym(Integer.parseInt(type_ym_[1]));
		_sumFileLogRec.setInst_nm(AppCmdline.GetInstance()._instNm);
		_sumFileLogRec.setFmt_nm(DatsumCfg._FmtNm);
		_sumProcLogRecList.clear();
		for (AtomicBoolean eob_flag_ : _eobFlags) {
			eob_flag_.set(false);
		}
		for (BlockingQueue<DatsumQElem> queue_ : _sumQueues) {
			DatsumQElem elem_ = new DatsumQElem(DatsumQElem.CMD_ENUM_BOB);
			queue_.add(elem_);
		}
	}

	private void _eob() throws Exception {
		for (BlockingQueue<DatsumQElem> queue_ : _sumQueues) {
			DatsumQElem elem_ = new DatsumQElem(DatsumQElem.CMD_ENUM_EOB);
			queue_.add(elem_);
		}
		L.trace("EOB emitted");
		for (int i = 0; i < _eobFlags.length; i++) {
			AtomicBoolean eob_flag_ = _eobFlags[i];
			int loops_ = 0;
			synchronized (eob_flag_) {
				while (!eob_flag_.get()) {
					eob_flag_.wait(1000);
					++loops_;
					if (loops_ > 90) {
						throw new RuntimeException(PubMethod.FmtArgs("waiting EOB[%d] timeout, loops_=%d", i, loops_));
					}
				}
			}
		}
		L.trace("EOB join done");
		_sumFileLogRec.setRaw_lines((long) _batchRawLines);
		_sumFileLogRec.setAud_val(_batchAudVal);
	}

	private void _dumpSumFile() throws Exception {
		int seq_ = _odbUtils.nextSeq(_sumFileLogRec.getSum_ymd(), "DATSUM_" + _sumFileLogRec.getSum_file_pfx());
		_sumFileLogRec.setSum_file_seq(seq_);
		_sumFileLogRec.setSum_file_nm(PubMethod.FmtArgs("%s_%d_%02d_%d_%07d.sum", _sumFileLogRec.getSum_file_pfx(),
				_sumFileLogRec.getSum_ymd(), _sumFileLogRec.getSum_file_type(), _sumFileLogRec.getAcnt_ym(), seq_));
		L.debug("dump {} begin", _sumFileLogRec.getSum_file_nm());
		_sumFh = new UdrFileHandle(new File(DatsumCfg._WorkingDir + "/" + _sumFileLogRec.getSum_file_nm()),
				UdrFileHandle.OP_MODE_MOVE, false);
		_sumFh._dstDir = DatsumCfg._OutputDir;
		_sumFh.openSrcFile(DatsumCfg._OutputCharset, false);
		Connection sqlite_ = null;
		Statement stmt_ = null;
		ResultSet rs_ = null;
		ResultSetMetaData meta_ = null;
		for (int idx_thread_ = 0; idx_thread_ < DatsumCfg._WorkerThreadNum; idx_thread_++) {
			String sum_db_path_ = PubMethod.FmtArgs("%s/SUM_%02d.db3", DatsumCfg._WorkingDir, idx_thread_);
			try {
				sqlite_ = DriverManager.getConnection("jdbc:sqlite:" + sum_db_path_);
				for (int idx_hash_ = 0; idx_hash_ < DatsumCfg._WorkerHashNum; idx_hash_++) {
					stmt_ = sqlite_.createStatement();
					rs_ = stmt_.executeQuery(PubMethod.FmtArgs("SELECT * FROM WRK_%02d", idx_hash_));
					L.trace(PubMethod.FmtArgs("dump WRK_%02d in SUM_%02d.db3", idx_hash_, idx_thread_));
					meta_ = rs_.getMetaData();
					while (rs_.next()) {
						_dumpSumLine(meta_, rs_, _sumFh._pw);
					}
				}
			} finally {
				if (rs_ != null)
					rs_.close();
				if (stmt_ != null)
					stmt_.close();
				if (sqlite_ != null)
					sqlite_.close();
			}
		}
		_sumFileLogRec.setSum_lines(_batchSumLines);
		L.debug("dump {} end, {} sum_lines", _sumFileLogRec.getSum_file_nm(), _sumFileLogRec.getSum_lines());
		_sumFh.closeSrcFile();
	}

	private void _dumpSumLine(ResultSetMetaData meta, ResultSet rs, PrintWriter pw) throws Exception {
		String key_str_ = rs.getString(1);
		String[] a_key_ = key_str_.split(DatsumCfg._InputDelimiterExp, -1);
		for (int i = 0; i < _dumpFields.length; i++)
			_dumpFields[i] = null;
		for (int i = 0; i < a_key_.length; i++)
			_dumpFields[DatsumCfg._DumpMappingKeyStr[i]] = a_key_[i];
		for (int i = 2; i <= meta.getColumnCount(); i++) {
			String type_name_ = meta.getColumnTypeName(i);
			if (type_name_.equalsIgnoreCase("INTEGER")) {
				_dumpFields[DatsumCfg._DumpMappingOthers[i - 2]] = Long.toString(rs.getLong(i));
			} else {
				_dumpFields[DatsumCfg._DumpMappingOthers[i - 2]] = rs.getString(i);
			}
		}
		_sb.delete(0, _sb.length());
		for (int i = 0; i < _dumpFields.length; i++) {
			if (i > 0)
				_sb.append(DatsumCfg._OutputDelimiterRaw);
			if (_dumpFields[i] != null)
				_sb.append(_dumpFields[i]);
		}
		_sb.append('\n');
		pw.print(_sb.substring(0));
		++_batchSumLines;
	}

	private void _postProc() {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		Connection conn_ = cli_.getConnection();
		DatsumOdb odb_ = new DatsumOdb();
		_sumFileLogRec.setTs_end(_batchTicker._tsEnd);
		try {
			L.info("{}, transaction {} begin", _sumFileLogRec.getSum_file_nm(), conn_.toString());
			for (DbDatsumProcLogRec proc_log_ : _sumProcLogRecList) {
				proc_log_.setSum_file_nm(_sumFileLogRec.getSum_file_nm());
				odb_.addSttlSumProcLog(conn_, proc_log_);
			}
			odb_.addSttlSumFileLog(conn_, _sumFileLogRec);
			cli_.commit(conn_);
			L.info("{}, transaction {} commit done", _sumFileLogRec.getSum_file_nm(), conn_.toString());
			if (!_sumFh.operation()) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_207341004_SUM_MOV_FAIL, PubMethod.FmtArgs("mv [%s] to [%s/] error",
						_sumFh._srcFilePath, _sumFh._dstDir), _sumFileLogRec.toGsonStr());
				_odbUtils.addRawAlm(null, alm_);
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_207341005_DBLOG_EXCEPTION, PubMethod.FmtArgs(
					"[%s] add db-log exception", _sumFileLogRec.getSum_file_nm()), _sumFileLogRec.toGsonStr());
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			if (conn_ != null) {
				cli_.rollback(conn_);
				L.warn("{}, transaction {} rollback done", _sumFileLogRec.getSum_file_nm(), conn_.toString());
			}
			throw new RuntimeException(e);
		} finally {
			cli_.close(conn_);
		}
	}

	private void _closeResultSet(ResultSet rs) {
		if (rs != null) {
			try {
				rs.close();
			} catch (SQLException e) {
				L.warn("close ResultSet exception, ignore", e);
			}
		}
	}
}
