package com.hp.cmcc.bboss.app.monitr;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import org.hyperic.sigar.SigarException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.app.RestRequestValidator;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

@Path("rest")
public class MonitrRest {
	private static Logger L = LoggerFactory.getLogger(MonitrRest.class);

	@POST
	@Path("refresh/monitr_cfg")
	@Produces(MediaType.TEXT_PLAIN)
	public String refreshMonitrCfg(@Context HttpServletRequest request) {
		L.info("request received, {} bytes", request.getContentLength());
		try {
			String rsps_ = RestRequestValidator.ValidateCurrentTime(request, null);
			if ("0".equals(rsps_)) {
				if (!MonitrCfg.RefreshMonitrCfg()) {
					rsps_ = "call RefreshMonitrCfg error";
				}
			}
			return rsps_;
		} catch (Exception e) {
			L.warn("process refresh request exception", e);
			return e.toString();
		}
	}

	@POST
	@Path("refresh/app_param")
	@Produces(MediaType.TEXT_PLAIN)
	public String refreshAppParam(@Context HttpServletRequest request) {
		L.info("request received, {} bytes", request.getContentLength());
		try {
			String rsps_ = RestRequestValidator.ValidateCurrentTime(request, null);
			if ("0".equals(rsps_)) {
				if (!MonitrCfg.RefreshAppParam()) {
					rsps_ = "call RefreshAppParam error";
				}
			}
			return rsps_;
		} catch (Exception e) {
			L.warn("process refresh request exception", e);
			return e.toString();
		}
	}

	@POST
	@Path("stop")
	@Produces(MediaType.TEXT_PLAIN)
	public String stop(@Context HttpServletRequest request) {
		L.info("request received, {} bytes", request.getContentLength());
		try {
			StringBuilder sb_ = new StringBuilder();
			String rsps_ = RestRequestValidator.ValidateCurrentTime(request, sb_);
			if (!"0".equals(rsps_))
				return rsps_;
			String[] param_ = sb_.substring(0).split(" ");
			if (param_.length < 2) {
				rsps_ = PubMethod.FmtArgs("request [%s] lack parameter", sb_.substring(0));
				L.warn("{}", rsps_);
				return rsps_;
			}
			sb_.delete(0, sb_.length());
			for (int i = 1; i < param_.length; i++) {
				_stopProg(param_[i], sb_);
			}
			if (sb_.length() > 0) {
				rsps_ = sb_.substring(0);
			}
			return rsps_;
		} catch (Exception e) {
			L.warn("process stop request exception", e);
			return e.toString();
		}
	}

	@POST
	@Path("shutdown")
	@Produces(MediaType.TEXT_PLAIN)
	public String shutdown(@Context HttpServletRequest request) {
		L.info("request received, {} bytes", request.getContentLength());
		try {
			StringBuilder sb_ = new StringBuilder();
			String rsps_ = RestRequestValidator.ValidateCurrentTime(request, sb_);
			if (!"0".equals(rsps_))
				return rsps_;
			sb_.delete(0, sb_.length());
			MonitrMain._ProcRunnable.setTerminateFlag();
			List<DbMonitrCfgRec> cfg_list_ = new ArrayList<DbMonitrCfgRec>();
			for (DbMonitrCfgRec rec_ : MonitrCfg._MonitrCfgMap.values()) {
				cfg_list_.add(rec_);
			}
			Collections.sort(cfg_list_);
			for (DbMonitrCfgRec cfg_rec_ : cfg_list_) {
				_stopProg(cfg_rec_.getProg_nm(), sb_);
			}
			AppCmdline cmdline_ = AppCmdline.GetInstance();
			ProcessLock.GenStopRequestFile(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv);
			if (sb_.length() > 0)
				rsps_ = sb_.substring(0);
			return rsps_;
		} catch (Exception e) {
			L.warn("process stop request exception", e);
			return e.toString();
		}
	}

	private void _stopProg(String prog_nm, StringBuilder rsps) {
		DbMonitrCfgRec cfg_rec_ = MonitrCfg._MonitrCfgMap.get(prog_nm);
		if (cfg_rec_ == null) {
			if (rsps.length() > 0)
				rsps.append("; ");
			String err_msg_ = PubMethod.FmtArgs("ukn prog_nm [%s]", prog_nm);
			L.warn("{}", err_msg_);
			rsps.append(err_msg_);
			return;
		}
		Long pid_ = MonitrCfg._ProgPidMap.get(prog_nm);
		if (pid_ == null) {
			L.info("prog_nm [{}] not running, no need stop", prog_nm);
			return;
		} else if (pid_ < 0) {
			L.info("prog_nm [{}] pid is {}, no need stop", prog_nm);
			return;
		}
		if (_kill(cfg_rec_, pid_, 0))
			return;
		if (!_stopCmd(cfg_rec_, pid_, rsps))
			return;
		PubMethod.Sleep(cfg_rec_.getStop_delay() * 1000L);
		if (_kill(cfg_rec_, pid_, 0))
			return;

		boolean end_ = false;
		for (int i = 0; i < cfg_rec_.getKill_times(); i++) {
			PubMethod.Sleep(cfg_rec_.getKill_delay());
			if (_kill(cfg_rec_, pid_, 15)) { // SIGTERM
				end_ = true;
				break;
			}
		}
		if (!end_) {
			PubMethod.Sleep(cfg_rec_.getKill_delay());
			_kill(cfg_rec_, pid_, 9); // SIGKILL
		}
	}

	private boolean _stopCmd(DbMonitrCfgRec cfg_rec, long pid, StringBuilder rsps) {
		if (!cfg_rec.needStopOrKill()) {
			if (rsps.length() > 0)
				rsps.append("; ");
			String err_msg_ = PubMethod.FmtArgs("prog_nm [%s] stop_cmd [%s] shutdown_priority %d, just skip", cfg_rec.getProg_nm(),
					cfg_rec.getStop_cmd(), cfg_rec.getShutdown_priority());
			L.info("{}", err_msg_);
			rsps.append(err_msg_);
			return true;
		}
		if (cfg_rec.getStop_cmd().equals("KILL"))
			return !_kill(cfg_rec, pid, 15);

		List<String> cmd_ = PubMethod.AssembleCmd(cfg_rec.getStop_cmd());
		ProcessBuilder pb_ = new ProcessBuilder(cmd_);
		pb_.inheritIO();
		try {
			pb_.start();
			L.info("[{}] [{}] invoked", cfg_rec.getProg_nm(), cfg_rec.getStop_cmd());
			return true;
		} catch (IOException e) {
			if (rsps.length() > 0)
				rsps.append("; ");
			String err_msg_ = PubMethod.FmtArgs("invoke [%s,%s] exception", cfg_rec.getProg_nm(), cfg_rec.getStop_cmd());
			L.warn("{}", err_msg_, e);
			rsps.append(err_msg_ + ", ");
			rsps.append(e.toString());
			return false;
		}
	}

	private boolean _kill(DbMonitrCfgRec cfg_rec, long pid, int signal) {
		if (!cfg_rec.needStopOrKill()) {
			L.trace("prog_nm [{}], pid {}, stop_cmd [{}], shutdown_priority {} no need kill", cfg_rec.getProg_nm(), pid,
					cfg_rec.getStop_cmd(), cfg_rec.getShutdown_priority());
			return true;
		}
		try {
			MonitrProcRunnable._Sigar.kill(pid, signal);
			L.debug("prog_nm [{}], pid {}, kill signal {} sent ok", cfg_rec.getProg_nm(), pid, signal);
			return false;
		} catch (SigarException e) {
			if (e.getMessage().endsWith("No such process")) {
				L.debug("prog_nm [{}], pid {}, kill signal {} sent but no such process", cfg_rec.getProg_nm(), pid, signal);
				return true;
			}
			L.info("prog_nm [{}], pid {}, kill signal {} exception", cfg_rec.getProg_nm(), pid, signal, e);
			return false;
		}
	}
}
