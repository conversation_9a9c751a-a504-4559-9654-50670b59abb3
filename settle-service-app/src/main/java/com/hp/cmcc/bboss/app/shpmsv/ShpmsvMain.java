package com.hp.cmcc.bboss.app.shpmsv;

import javax.ws.rs.NotFoundException;

import org.jboss.resteasy.plugins.server.tjws.TJWSEmbeddedJaxrsServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.app.RestNotFoundExceptionHandler;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

/**
 * shpmsv：
 * 		（SHared ParaMeter SerVer）公参刷新主服务
 * 		将公参资料加载到SQLite3格式的文件中，存放到BIZ.SHPM_BLOB表，
 * 		供各shpmag模块下载使用；系统中只能部署一个shpmsv实例。
 *
 */
public class ShpmsvMain {
	private static Logger L = LoggerFactory.getLogger(ShpmsvMain.class);
	private static ShpmsvEdbMgr _ShpmsvEdbMgr = null;
	private static ShpmsvTimerRefreshRunnable _HourlyRefreshRunnable = null;
	private static Thread _HourlyRefreshThread = null;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("shpmsv", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_InitRefresh();
			_HourlyRefreshRunnable = new ShpmsvTimerRefreshRunnable();
			_HourlyRefreshThread = new Thread(_HourlyRefreshRunnable, "TTRG"); // TTRG: Timer TRiGger
			_HourlyRefreshThread.setDaemon(true);
			_HourlyRefreshThread.start();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 3, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!ShpmsvCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		ShpmsvCfg.Trace();
		if (!MdbAgt.Init(ShpmsvCfg._SystemParam, 5, 0, 0)) {
			L.error("init mdb connection error, JVM exit");
			System.exit(1);
		}

		ShpmsvRest rest_sv_ = new ShpmsvRest();
		TJWSEmbeddedJaxrsServer tjws = new TJWSEmbeddedJaxrsServer();
		tjws.setPort(ShpmsvCfg._RestPort);
		tjws.setRootResourcePath("/");
		tjws.start();
		tjws.getDeployment().getRegistry().addSingletonResource(rest_sv_);
		tjws.getDeployment().getProviderFactory().getExceptionMappers().put(NotFoundException.class,
				new RestNotFoundExceptionHandler());
		L.info("REST service started at port {}", ShpmsvCfg._RestPort);
	}

	private static void _InitRefresh() {
		_ShpmsvEdbMgr = new ShpmsvEdbMgr();
		if (!_ShpmsvEdbMgr.needInitRefresh())
			return;
		if (!_ShpmsvEdbMgr.refresh()) {
			L.error("init refresh failed, JVM exit");
			System.exit(1);
		} else {
			L.info("init refresh done, ver [{}]", _ShpmsvEdbMgr._shpmVer);
		}
	}

	private static void _MainLoop() {
		L.debug("trap in main loop");
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		long cnt_ = 0;
		while (true) {
			try {
				PubMethod.Sleep(500);
				cnt_++;
				if (ShpmsvTimerRefreshRunnable._RefreshFlag) {
					L.info("refresh flag detected");
					ShpmsvTimerRefreshRunnable._RefreshFlag = false;
					if (!_ShpmsvEdbMgr.refresh()) {
						L.warn("{} refresh failed", _ShpmsvEdbMgr._shpmVer);
					}
				}
				if ((cnt_ % 2 == 0)) {
					if (ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
						L.info("try stop normal");
						_HourlyRefreshRunnable.setTerminateFlag();
						L.debug("try join thread {}", _HourlyRefreshThread.getName());
						_HourlyRefreshThread.join();
						L.debug("thread {} joined", _HourlyRefreshThread.getName());
						break;
					}
				}
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
	}
}
