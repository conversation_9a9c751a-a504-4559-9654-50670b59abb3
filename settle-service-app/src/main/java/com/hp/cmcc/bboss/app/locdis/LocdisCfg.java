package com.hp.cmcc.bboss.app.locdis;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class LocdisCfg {
	private static Logger L = LoggerFactory.getLogger(LocdisCfg.class);
	public static boolean _DebugFlag = false;
	public static int _RestPort;
	public static int _IntervalMove;
	public static int _IntervalArch;
	public static int _IntervalGzip;
	public static int _IntervalUlnk;
	public static int _MinGzipDelay;
	public static int _MinUlnkDelay;
	// public static String _GzipPath;
	public static Map<String, List<DbLocdisCfgRec>> _MoveCfgMap = new TreeMap<String, List<DbLocdisCfgRec>>();
	public static Map<String, List<DbLocdisCfgRec>> _ArchYmCfgMap = new TreeMap<String, List<DbLocdisCfgRec>>();
	public static Map<String, List<DbLocdisCfgRec>> _ArchYmdCfgMap = new TreeMap<String, List<DbLocdisCfgRec>>();
	public static Map<String, List<DbBinmanCfgRec>> _GzipCfgMap = new TreeMap<String, List<DbBinmanCfgRec>>();
	public static Map<String, List<DbBinmanCfgRec>> _UlnkCfgMap = new TreeMap<String, List<DbBinmanCfgRec>>();

	public static boolean Init() {
		if (!OdbSystemParam.GetInstance().refresh()) {
			L.warn("call OdbSttlSystemParam init error");
			return false;
		}
		if (!_RefreshAppParam()) {
			L.warn("call _InitAppParam error");
			return false;
		}
		boolean rc_ = true;
		if (!RefreshLocdisCfg()) {
			L.warn("call RefreshLocdisCfg error");
			rc_ = false;
		}
		if (!RefreshBinmanCfg()) {
			L.warn("call RefreshBinmanCfg error");
			rc_ = false;
		}
		return rc_;
	}

	public static void TraceCfgMaps() {
		int i = 0;
		int j = 0;
		L.debug("{} entries in _MoveCfgMap", _MoveCfgMap.size());
		for (String k : _MoveCfgMap.keySet()) {
			List<DbLocdisCfgRec> l = _MoveCfgMap.get(k);
			L.debug("No. {}, [{}], {} elems in cfg list", ++i, k, l.size());
			j = 0;
			for (DbLocdisCfgRec r : l) {
				L.debug(" no. {}, {}", ++j, r.toGsonStr());
			}
		}

		i = 0;
		L.debug("{} entries in _ArchYmCfgMap", _ArchYmCfgMap.size());
		for (String k : _ArchYmCfgMap.keySet()) {
			List<DbLocdisCfgRec> l = _ArchYmCfgMap.get(k);
			L.debug("No. {}, [{}], {} elems in cfg list", ++i, k, l.size());
			j = 0;
			for (DbLocdisCfgRec r : l) {
				L.debug(" no. {}, {}", ++j, r.toGsonStr());
			}
		}

		i = 0;
		L.debug("{} entries in _ArchYmdCfgMap", _ArchYmdCfgMap.size());
		for (String k : _ArchYmdCfgMap.keySet()) {
			List<DbLocdisCfgRec> l = _ArchYmdCfgMap.get(k);
			L.debug("No. {}, [{}], {} elems in cfg list", ++i, k, l.size());
			j = 0;
			for (DbLocdisCfgRec r : l) {
				L.debug(" no. {}, {}", ++j, r.toGsonStr());
			}
		}

		i = 0;
		L.debug("{} entries in _GzipCfgMap", _GzipCfgMap.size());
		for (String k : _GzipCfgMap.keySet()) {
			List<DbBinmanCfgRec> l = _GzipCfgMap.get(k);
			L.debug("No. {}, [{}], {} elems in cfg list", ++i, k, l.size());
			j = 0;
			for (DbBinmanCfgRec r : l) {
				L.debug(" no. {}, {}", ++j, r.toGsonStr());
			}
		}

		i = 0;
		L.debug("{} entries in _UlnkCfgMap", _UlnkCfgMap.size());
		for (String k : _UlnkCfgMap.keySet()) {
			List<DbBinmanCfgRec> l = _UlnkCfgMap.get(k);
			L.debug("No. {}, [{}], {} elems in cfg list", ++i, k, l.size());
			j = 0;
			for (DbBinmanCfgRec r : l) {
				L.debug(" no. {}, {}", ++j, r.toGsonStr());
			}
		}
	}

	private static boolean _RefreshAppParam() {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(cmdline_._module, cmdline_._instNm);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", cmdline_._instNm);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", cmdline_._instNm);
			return false;
		}

		Long lval_ = app_param_.chkValNum("COMMON", "REST_PORT", 1L, 65535L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_RestPort = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_MOVE", 1L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_IntervalMove = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_ARCH", 1L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_IntervalArch = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_GZIP", 1L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_IntervalGzip = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_ULNK", 1L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_IntervalUlnk = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "MIN_GZIP_DELAY", 60L, 8640000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_MinGzipDelay = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "MIN_ULNK_DELAY", 3600L, 86400000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_MinUlnkDelay = lval_.intValue();
		}

		return rc_;
	}

	public static boolean RefreshLocdisCfg() {
		boolean rc_ = true;
		Map<String, List<DbLocdisCfgRec>> move_cfg_map_ = new TreeMap<String, List<DbLocdisCfgRec>>();
		Map<String, List<DbLocdisCfgRec>> arch_ym_cfg_map_ = new TreeMap<String, List<DbLocdisCfgRec>>();
		Map<String, List<DbLocdisCfgRec>> arch_ymd_cfg_map_ = new TreeMap<String, List<DbLocdisCfgRec>>();
		if (!_RefreshLocdisCfg(DbLocdisCfgRec.ENABLED_1_MOVE, move_cfg_map_)) {
			L.warn("call _RefreshLocdisCfg({}) error", DbLocdisCfgRec.ENABLED_1_MOVE);
			rc_ = false;
		}
		if (!_RefreshLocdisCfg(DbLocdisCfgRec.ENABLED_2_ARCH_YM, arch_ym_cfg_map_)) {
			L.warn("call _RefreshLocdisCfg({}) error", DbLocdisCfgRec.ENABLED_2_ARCH_YM);
			rc_ = false;
		}
		if (!_RefreshLocdisCfg(DbLocdisCfgRec.ENABLED_3_ARCH_YMD, arch_ymd_cfg_map_)) {
			L.warn("call _RefreshLocdisCfg({}) error", DbLocdisCfgRec.ENABLED_3_ARCH_YMD);
			rc_ = false;
		}
		if (rc_) {
			_MoveCfgMap = move_cfg_map_;
			_ArchYmCfgMap = arch_ym_cfg_map_;
			_ArchYmdCfgMap = arch_ymd_cfg_map_;
			L.info("refresh LOCDIS_CFG ok, move:arch_ym:arch_ymd {}:{}:{}", _MoveCfgMap.size(), _ArchYmCfgMap.size(),
					_ArchYmdCfgMap.size());
		}
		return rc_;
	}

	public static boolean RefreshBinmanCfg() {
		boolean rc_ = true;
		Map<String, List<DbBinmanCfgRec>> gzip_cfg_map_ = new TreeMap<String, List<DbBinmanCfgRec>>();
		Map<String, List<DbBinmanCfgRec>> ulnk_cfg_map_ = new TreeMap<String, List<DbBinmanCfgRec>>();
		if (!_RefreshBinmanCfg(DbBinmanCfgRec.ENABLED_1_GZIP, LocdisCfg._MinGzipDelay, gzip_cfg_map_)) {
			L.warn("call _RefreshBinmanCfg({}) error", DbBinmanCfgRec.ENABLED_1_GZIP);
			rc_ = false;
		}
		if (!_RefreshBinmanCfg(DbBinmanCfgRec.ENABLED_2_ULNK, LocdisCfg._MinUlnkDelay, ulnk_cfg_map_)) {
			L.warn("call _RefreshBinmanCfg({}) error", DbBinmanCfgRec.ENABLED_2_ULNK);
			rc_ = false;
		}
		if (rc_) {
			_GzipCfgMap = gzip_cfg_map_;
			_UlnkCfgMap = ulnk_cfg_map_;
			L.info("refresh BINMAN_CFG ok, gzip:ulnk {}:{}", _GzipCfgMap.size(), _UlnkCfgMap.size());
		}
		return rc_;
	}

	private static boolean _RefreshLocdisCfg(int enabled, Map<String, List<DbLocdisCfgRec>> cfg_map) {
		boolean rc_ = true;
		LocdisOdb odb_ = new LocdisOdb();
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cfg_map.clear();
		List<DbLocdisCfgRec> db_result_ = odb_.getLocdisCfg(cmdline_._instNm, enabled);
		if (db_result_ == null) {
			L.warn("query LOCDIS_CFG with [{},{}] returns null", cmdline_._instNm, enabled);
			rc_ = false;
		} else {
			L.debug("query LOCDIS_CFG with [{},{}], {} records fetched", cmdline_._instNm, enabled, db_result_.size());
			for (DbLocdisCfgRec rec_ : db_result_) {
				if (!rec_.validateRec()) {
					L.warn("LOCDIS_CFG {}, validateRec error", rec_.getCfg_id());
					rc_ = false;
					continue;
				}
				List<DbLocdisCfgRec> list_ = cfg_map.get(rec_.getSrc_dir());
				if (list_ == null) {
					list_ = new ArrayList<DbLocdisCfgRec>();
					cfg_map.put(rec_.getSrc_dir(), list_);
				}
				list_.add(rec_);
			}
		}
		return rc_;
	}

	private static boolean _RefreshBinmanCfg(int enabled, int min_delay, Map<String, List<DbBinmanCfgRec>> cfg_map) {
		boolean rc_ = true;
		LocdisOdb odb_ = new LocdisOdb();
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cfg_map.clear();
		List<DbBinmanCfgRec> db_result_ = odb_.getBinmanCfg(cmdline_._instNm, enabled);
		if (db_result_ == null) {
			L.warn("query BINMAN_CFG with [{},{}] returns null", cmdline_._instNm, enabled);
			rc_ = false;
		} else {
			L.debug("query BINMAN_CFG with [{},{}], {} records fetched", cmdline_._instNm, enabled, db_result_.size());
			for (DbBinmanCfgRec rec_ : db_result_) {
				if (!rec_.validateRec(min_delay)) {
					L.warn("BINMAN_CFG {}, validateRec error", rec_.getCfg_id());
					rc_ = false;
					continue;
				}
				List<DbBinmanCfgRec> list_ = cfg_map.get(rec_.getSrc_dir());
				if (list_ == null) {
					list_ = new ArrayList<DbBinmanCfgRec>();
					cfg_map.put(rec_.getSrc_dir(), list_);
				}
				list_.add(rec_);
			}
		}
		return rc_;
	}
}
