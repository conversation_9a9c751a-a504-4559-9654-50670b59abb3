package com.hp.cmcc.bboss.app.dblodr;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DblodrCfg {
	private static Logger L = LoggerFactory.getLogger(DblodrCfg.class);
	public static String _SqlldrPath; // $ORACLE_HOME/bin/sqlldr
	public static int _Interval = 30;
	public static Map<String, DblodrCtlSec> _CtlSecMap;
	public static Map<String, List<DblodrCtlSec>> _ThreadGrpMap;

	public static boolean Init() {
		boolean rc_ = OdbSystemParam.GetInstance().refresh();
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}
		if (!_InitSqlldrPath()) {
			L.warn("call _InitSqlldrPath error");
			rc_ = false;
		}
		if (!_InitAppParam()) {
			L.warn("call _InitAppParam error");
			rc_ = false;
		}
		return rc_;
	}

	public static void Debug() {
		L.debug("_Interval={}, _SqlldrPath={}", _Interval, _SqlldrPath);
		int i = 0;
		for (DblodrCtlSec ctl_ : _CtlSecMap.values()) {
			L.debug("No. {} {}", ++i, ctl_.toGsonStr());
		}
		i = 0;
		for (Entry<String, List<DblodrCtlSec>> entry_ : _ThreadGrpMap.entrySet()) {
			List<String> sec_list_ = new ArrayList<String>();
			for (DblodrCtlSec ctl_ : entry_.getValue()) {
				sec_list_.add(ctl_._secNm.substring(4));
			}
			L.debug("no. {}, thread [{}], {} elements, [{}]", ++i, entry_.getKey(), entry_.getValue().size(),
					PubMethod.Collection2Str(sec_list_, ","));
		}
	}

	private static boolean _InitSqlldrPath() {
		boolean rc_ = true;
		String oracle_home_ = System.getenv("ORACLE_HOME");
		if (PubMethod.IsBlank(oracle_home_)) {
			L.warn("env ORACLE_HOME not set");
			rc_ = false;
		} else {
//			_SqlldrPath = String.format("%s/bin/sqlldr", oracle_home_);
			_SqlldrPath = String.format("%s/sqlldr.exe", oracle_home_);
			File f = new File(oracle_home_);
			if (!f.isDirectory()) {
				L.warn("$ORACLE_HOME [{}] not exists or not a directory", oracle_home_);
				rc_ = false;
			} else {
				f = new File(_SqlldrPath);
				if (!f.canExecute()) {
					L.warn("$ORACLE_HOME/bin/sqlldr [{}] not exists or not executable", _SqlldrPath);
					rc_ = false;
				}
			}
		}
		return rc_;
	}

	private static boolean _InitAppParam() {
		String inst_nm_ = AppCmdline.GetInstance()._instNm;
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(AppCmdline.GetInstance()._module, inst_nm_);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		if (!_InitCommonCfg(app_param_))
			rc_ = false;

		if (!_InitCtlSecMap(app_param_))
			rc_ = false;

		return rc_;
	}

	private static boolean _InitCommonCfg(OdbAppParam app_param) {
		boolean rc_ = true;
		Long lval_ = app_param.chkValNum("COMMON", "INTERVAL", 1L, 3600L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_Interval = lval_.intValue();
		}
		return rc_;
	}

	private static boolean _InitCtlSecMap(OdbAppParam app_param) {
		boolean rc_ = true;
		DblodrCtlSec ctl_sec_ = null;
		Map<String, DblodrCtlSec> ctl_sec_map_ = new TreeMap<String, DblodrCtlSec>();
		for (String sec_nm_ : app_param._paramMap.keySet()) {
			if (!sec_nm_.startsWith("CTL."))
				continue;
			if (ctl_sec_map_.containsKey(sec_nm_)) {
				L.warn("absurd!, section [{}] already exists", sec_nm_);
				continue;
			}
			ctl_sec_ = new DblodrCtlSec();
			ctl_sec_._secNm = sec_nm_;

			File fval_ = app_param.chkValFile(sec_nm_, "SRC_DIR", true, true, true);
			if (fval_ == null) {
				ctl_sec_._secNm = null;
			} else {
				ctl_sec_._srcDir = fval_.getAbsolutePath();
			}

			fval_ = app_param.chkValFile(sec_nm_, "BAK_DIR", true, true, true);
			if (fval_ == null) {
				ctl_sec_._secNm = null;
			} else {
				ctl_sec_._bakDir = fval_.getAbsolutePath();
			}

			fval_ = app_param.chkValFile(sec_nm_, "BAD_DIR", true, true, true);
			if (fval_ == null) {
				ctl_sec_._secNm = null;
			} else {
				ctl_sec_._badDir = fval_.getAbsolutePath();
			}

			Pattern pval_ = app_param.chkValPattern(sec_nm_, "PATTERN", null);
			if (pval_ == null) {
				ctl_sec_._secNm = null;
			} else {
				ctl_sec_._pattern = pval_;
			}

			fval_ = app_param.chkValFile(sec_nm_, "CTL_FILE", true, false, false);
			if (fval_ == null) {
				ctl_sec_._secNm = null;
			} else {
				ctl_sec_._ctlFile = fval_.getAbsolutePath();
			}

			Long lval_ = app_param.chkValNum(sec_nm_, "BAK_BY_YMD", 0L, 1L);
			if (lval_ == null) {
				ctl_sec_._secNm = null;
			} else {
				ctl_sec_._bakByYmd = lval_.intValue();
			}

			String sval_ = app_param.chkValStr(sec_nm_, "SCRIPT", null);
			if (sval_ == null) {
				ctl_sec_._secNm = null;
			} else if (sval_.equals("NULL")) {
				ctl_sec_._script = null;
			} else {
				List<String> args_ = new ArrayList<String>();
				fval_ = app_param.chkValScript(sec_nm_, "SCRIPT", args_);
				if (fval_ == null) {
					ctl_sec_._secNm = null;
				} else {
					ctl_sec_._script = args_.get(0);
					if (args_.size() > 1) {
						ctl_sec_._args = args_;
						ctl_sec_._args.remove(0);
					} else {
						ctl_sec_._args = null;
					}
				}
			}

			sval_ = app_param.chkValStr(sec_nm_, "THREAD_NM", null);
			if (sval_ == null) {
				ctl_sec_._secNm = null;
			} else {
				ctl_sec_._threadNm = sval_;
			}

			if (ctl_sec_._secNm == null) {
				L.warn("section [{}] init error", sec_nm_);
				rc_ = false;
			} else {
				ctl_sec_map_.put(ctl_sec_._secNm, ctl_sec_);
			}
		}

		if (ctl_sec_map_.isEmpty()) {
			L.warn("no valid 'CTL.*' section found");
			rc_ = false;
		}
		if (rc_) {
			_CtlSecMap = ctl_sec_map_;
			L.debug("{} 'CTL.*' sections initialized", _CtlSecMap.size());
			Map<String, List<DblodrCtlSec>> thread_grp_map_ = new TreeMap<String, List<DblodrCtlSec>>();
			for (DblodrCtlSec ctl_ : ctl_sec_map_.values()) {
				List<DblodrCtlSec> ctl_list_ = thread_grp_map_.get(ctl_._threadNm);
				if (ctl_list_ == null) {
					ctl_list_ = new ArrayList<DblodrCtlSec>();
					thread_grp_map_.put(ctl_._threadNm, ctl_list_);
				}
				ctl_list_.add(ctl_);
			}
			_ThreadGrpMap = thread_grp_map_;
		}
		return rc_;
	}
}
