// THIS CLASS IS OBSOLETED !!!

package com.hp.cmcc.bboss.app.shpmsv;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.entity.DbCustomerRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbServBizCodeRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbSttlLinearRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbSttlTierRateRec;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmsvMgr {
	private static Logger L = LoggerFactory.getLogger(ShpmsvMgr.class);
	private static final String _Q_PROVINCE_CD = "SELECT PROV_CD, PROV_NM, PROV_WL, CMCC_OPID FROM PROVINCE_CD ORDER BY PROV_CD";
	private static final String _Q_DOM_LD_AREA_CD_PROV = "SELECT SN, LD_AREA_CD, PROV_CD, LD_AREA_NM, EFF_TM, EXP_TM FROM "
			+ "DOM_LD_AREA_CD_PROV ORDER BY LD_AREA_CD, EFF_TM DESC";
	public String _verTmStr;
	private ShpmsvOdb _odb;
	private AppTicker _ticker;
	private int _rdbCnt; // Relational DB count
	private int _mdbCnt; // Memory DB count

	public ShpmsvMgr() {
		_odb = new ShpmsvOdb();
		_ticker = new AppTicker();
	}

	public boolean needRefresh() {
		boolean need_refresh_ = false;
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		String k = MdbConst.SHPARM_KEY_VER_CMT;
		try {
			List<String> ver_list_ = jedis_.lrange(k, 0, 0);
			if (ver_list_ == null || ver_list_.isEmpty()) {
				L.debug("no {} exists, need refresh", k);
				need_refresh_ = true;
			} else {
				long now_ = System.currentTimeMillis();
				now_ -= (3600 * 1000);
				String ver_expire_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14).substring(2, 12);
				if (ver_list_.get(0).compareTo(ver_expire_) < 0) {
					L.debug("{} current ver {} lt {}, need refresh", new Object[] { k, ver_list_.get(0), ver_expire_ });
					need_refresh_ = true;
				} else {
					L.debug("{} current ver {} ge {}, NO need refresh", new Object[] { k, ver_list_.get(0), ver_expire_ });
				}
			}
			return need_refresh_;
		} catch (Exception e) {
			L.warn(String.format("query %s exception, NO need refresh", k), e);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
			return false;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}

	public boolean refresh() {
		_ticker.tickStart();
		_verTmStr = PubMethod.Long2Str(_ticker._tsStart, PubMethod.TimeStrFmt.Fmt14).substring(2, 12);
		_rdbCnt = 0;
		_mdbCnt = 0;
		try {
			L.info("{} try refresh ver", _verTmStr);
			if (!_verCtlBegin()) {
				L.warn("{} call _verCtlBegin error", _verTmStr);
				return false;
			}
			boolean rc_ = true;
			if (!_refreshProvinceCd()) {
				L.warn("{} call _refreshProvinceCd error", _verTmStr);
				rc_ = false;
			}
			if (!_refreshDomLdAreaCdProv()) {
				L.warn("{} call _refreshDomLdAreaCdProv error", _verTmStr);
				rc_ = false;
			}
			if (!_refreshCustomer()) {
				L.warn("{} call _refreshCustomer error", _verTmStr);
				rc_ = false;
			}
			if (!_refreshServBizCode()) {
				L.warn("{} call _refreshServBizCode error", _verTmStr);
				rc_ = false;
			}
			if (!_refreshSttlLinearRate()) {
				L.warn("{} call _refreshSttlLinearRate error", _verTmStr);
				rc_ = false;
			}
			if (!_refreshSttlTierRate()) {
				L.warn("{} call _refreshSttlTierRate error", _verTmStr);
				rc_ = false;
			}
			if (rc_) {
				rc_ = _verCtlEnd();
			}
			return rc_;
		} catch (Exception e) {
			L.warn("{} refresh exception", _verTmStr, e);
			return false;
		}
	}

	private boolean _verCtlBegin() {
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		boolean rc_ = true;
		try {
			String k = MdbConst.SHPARM_KEY_VER_TMP;
			List<String> l = jedis_.lrange(k, 0, 0);
			if (l != null && !l.isEmpty()) {
				if (_verTmStr.equals(l.get(0))) {
					L.warn("{} ver already exists under key [{}], can not refresh", _verTmStr, k);
					return false;
				}
			}
			Long entries_ = jedis_.lpush(k, _verTmStr);
			L.debug("{} ver lpush ok, {} entries under [{}]", new Object[] { _verTmStr, entries_, k });
		} catch (Exception e) {
			L.warn("{} jedis exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}

	private boolean _verCtlEnd() {
		boolean rc_ = true;
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			tx_.lpush(MdbConst.SHPARM_KEY_VER_CMT, _verTmStr);
			tx_.lrem(MdbConst.SHPARM_KEY_VER_TMP, 0, _verTmStr);
			tx_.exec();
			_ticker.tickEnd(_rdbCnt);
			int rdb_pfm_ = _ticker._pfm;
			_ticker.calcPfm(_mdbCnt);
			int mdb_pfm_ = _ticker._pfm;
			L.info("{} online done, term:rcnt:mcnt:rpfm:mpfm {}:{}:{}:{}:{}", new Object[] { _verTmStr, _ticker._term, _rdbCnt,
					_mdbCnt, rdb_pfm_, mdb_pfm_ });
		} catch (Exception e) {
			L.warn("{} jedis exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}

	private boolean _refreshSttlLinearRate() {
		List<DbSttlLinearRateRec> db_list_ = _odb.getSttlLinearRate();
		if (db_list_ == null) {
			L.warn("{} refresh STTL_LINEAR_RATE from db failed", _verTmStr);
			return false;
		}
		L.trace("{} {} records read from STTL_LINEAR_RATE", _verTmStr, db_list_.size());
		_rdbCnt += db_list_.size();

		boolean rc_ = true;
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			for (DbSttlLinearRateRec rec_ : db_list_) {
				String k = String.format("%s:STLNRT:%s:%s:%s:%s:%s", _verTmStr, rec_.getBiz_type(), rec_.getOffer_code(),
						rec_.getProduct_code(), rec_.getEc_code(), rec_.getAux_key());
				String v = String.format("%s|%s|%d|%d|%d|%d|%s|%s|%s|%s|%s",
						PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14),
						PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14), rec_.getRate_obj(),
						rec_.getRate_unit(), rec_.getRate(), rec_.getRate_id(), rec_.getBiz_type(), rec_.getOffer_code(),
						rec_.getProduct_code(), rec_.getEc_code(), rec_.getAux_key());
				tx_.rpush(k, v);
				++_mdbCnt;
			}
			List<Object> response_ = tx_.exec();
			L.info("{} {} STTL_LINEAR_RATE pushed", _verTmStr, response_.size());
		} catch (Exception e) {
			L.warn("{} refresh STTL_LINEAR_RATE exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}

	private boolean _refreshSttlTierRate() {
		List<DbSttlTierRateRec> db_list_ = _odb.getSttlTierRate();
		if (db_list_ == null) {
			L.warn("{} refresh STTL_TIER_RATE from db failed", _verTmStr);
			return false;
		}
		L.trace("{} {} records read from STTL_TIER_RATE", _verTmStr, db_list_.size());
		_rdbCnt += db_list_.size();

		boolean rc_ = true;
		Map<String, String> biz_type_map_ = new TreeMap<String, String>();
		String prev_key_ = "";
		String curr_key_ = "";
		DbSttlTierRateRec prev_rec_ = null;
		DbSttlTierRateRec curr_rec_ = null;
		for (DbSttlTierRateRec rec_ : db_list_) { // validate integrity
			String biz_type_key_ = PubMethod.FmtArgs("%s:STTRRT:%s", _verTmStr, rec_.getBiz_type());
			biz_type_map_.put(biz_type_key_, rec_.getBiz_type());
			curr_rec_ = rec_;
			curr_key_ = String.format("%s:%s:%s:%s", rec_.getBiz_type(), rec_.getEc_code(), rec_.getAux_key(),
					PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
			if (curr_key_.equals(prev_key_)) {
				if (curr_rec_.getTier_idx() - 1 != prev_rec_.getTier_idx()) {
					L.warn("{} refresh STTL_TIER_RATE, TIER_IDX must be continuous, curr:prev {} : {}", _verTmStr,
							curr_rec_.toGsonStr(), prev_rec_.toGsonStr());
					rc_ = false;
				} else if (curr_rec_.getTier_min() - 1 != prev_rec_.getTier_max()) {
					L.warn("{} refresh STTL_TIER_RATE, curr TIER_MIN must be continuous with prev TIER_MAX, curr:prev {} : {}",
							_verTmStr, curr_rec_.toGsonStr(), prev_rec_.toGsonStr());
					rc_ = false;
				}
			} else {
				if (curr_rec_.getTier_idx() != 0) {
					L.warn("{} refresh STTL_TIER_RATE, TIER_IDX must start from 0, {}", _verTmStr, curr_rec_.toGsonStr());
					rc_ = false;
				} else if (curr_rec_.getTier_min() != 1) {
					L.warn("{} refresh STTL_TIER_RATE, TIER_MIN must start from 1 for TIER_IDX 0, {}", _verTmStr,
							curr_rec_.toGsonStr());
					rc_ = false;
				}
			}
			prev_key_ = curr_key_;
			prev_rec_ = curr_rec_;
		}
		if (!rc_) {
			L.warn("{} validate integrity of STTL_TIER_RATE error", _verTmStr);
			return false;
		} else {
			L.trace("{} validate integrity of STTL_TIER_RATE ok", _verTmStr);
		}

		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			for (DbSttlTierRateRec rec_ : db_list_) {
				String k = String.format("%s:STTRRT:%s:%s:%s", _verTmStr, rec_.getBiz_type(), rec_.getEc_code(), rec_.getAux_key());
				String v = String.format("%s|%s|%d|%d|%d|%d|%d|%d|%d|%d|%d|%s|%s|%s",
						PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14),
						PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14), rec_.getTier_idx(),
						rec_.getTier_obj(), rec_.getTier_unit(), rec_.getTier_min(), rec_.getTier_max(), rec_.getRate_obj(),
						rec_.getRate_unit(), rec_.getRate(), rec_.getRate_id(), rec_.getBiz_type(), rec_.getEc_code(),
						rec_.getAux_key());
				tx_.rpush(k, v);
				++_mdbCnt;
			}
			for (Entry<String, String> entry_ : biz_type_map_.entrySet()) {
				tx_.set(entry_.getKey(), entry_.getValue());
				++_mdbCnt;
			}
			List<Object> response_ = tx_.exec();
			L.info("{} {} STTL_TIER_RATE pushed", _verTmStr, response_.size());
		} catch (Exception e) {
			L.warn("{} refresh STTL_TIER_RATE exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}

	private boolean _refreshServBizCode() throws SQLException {
		List<DbServBizCodeRec> db_list_ = _odb.getServBizCode(null);
		if (db_list_ == null) {
			L.warn("refresh SERV_BIZ_CODE from db failed");
			return false;
		}
		L.trace("{} {} records read from SERV_BIZ_CODE", _verTmStr, db_list_.size());
		_rdbCnt += db_list_.size();

		boolean rc_ = true;
		StringBuilder sb_ = new StringBuilder();
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			for (DbServBizCodeRec rec_ : db_list_) {
				String k1_ = null;
				String k2_ = null;
				if (!PubMethod.IsBlank(rec_.getOrder_id())) {
					k1_ = String.format("%s:SVBZCD:ORDER_ID:%s", _verTmStr, rec_.getOrder_id());
				}
				if (!PubMethod.IsBlank(rec_.getServ_code())) {
					k2_ = String.format("%s:SVBZCD:SERV_CODE:%s", _verTmStr, rec_.getServ_code());
				}
				if (k1_ == null && k2_ == null) {
					L.warn("both ORDER_ID and SERV_CODE is blank, {}", rec_.toGsonStr());
					continue;
				}
				sb_.delete(0, sb_.length());
				sb_.append(PubMethod.Timestamp2Str(rec_.getEffective_date(), PubMethod.TimeStrFmt.Fmt14)); // 0
				sb_.append("|");
				sb_.append(PubMethod.Timestamp2Str(rec_.getExpiry_date(), PubMethod.TimeStrFmt.Fmt14)); // 1
				sb_.append("|");
				sb_.append(rec_.getEc_code() == null ? "" : rec_.getEc_code()); // 2
				sb_.append("|");
				sb_.append(rec_.getServ_code() == null ? "" : rec_.getServ_code()); // 3
				sb_.append("|");
				sb_.append(rec_.getBiz_code() == null ? "" : rec_.getBiz_code()); // 4
				sb_.append("|");
				sb_.append(rec_.getProd_order_id() == null ? "" : rec_.getProd_order_id()); // 5
				sb_.append("|");
				sb_.append(rec_.getOrder_id() == null ? "" : rec_.getOrder_id()); // 6
				sb_.append("|");
				sb_.append(rec_.getBiz_code_apply() == null ? "" : rec_.getBiz_code_apply()); // 7
				sb_.append("|");
				sb_.append(rec_.getProv_code() == null ? "" : rec_.getProv_code()); // 8
				sb_.append("|");
				sb_.append(rec_.getProduct_code() == null ? "" : rec_.getProduct_code()); // 9
				sb_.append("|");
				sb_.append(rec_.getService_code() == null ? "" : rec_.getService_code()); // 10
				sb_.append("|");
				sb_.append(rec_.getEc_group_id() == null ? "" : rec_.getEc_group_id()); // 11
				sb_.append("|");
				sb_.append(rec_.getService_type() == null ? "" : rec_.getService_type()); // 12
				sb_.append("|");
				sb_.append(rec_.getSend_prov() == null ? "" : rec_.getSend_prov()); // 13
				sb_.append("|");
				sb_.append(rec_.getProd_order_mode() == null ? "" : rec_.getProd_order_mode()); // 14
				sb_.append("|");
				sb_.append(rec_.getSub_group_flag() == null ? "" : rec_.getSub_group_flag()); // 15
				sb_.append("|");
				sb_.append(rec_.getCarry_type() == null ? "" : rec_.getCarry_type()); // 16
				sb_.append("|");
				sb_.append(rec_.getSign_entity() == null ? "" : rec_.getSign_entity()); // 17
				sb_.append("|");
				sb_.append(rec_.getParent_order_id() == null ? "" : rec_.getParent_order_id()); // 18
				sb_.append("|");
				sb_.append(rec_.getLocon_flag() == null ? "" : rec_.getLocon_flag()); // 19
				sb_.append("|");
				sb_.append(rec_.getOrder_level() == null ? "" : rec_.getOrder_level()); // 20
				sb_.append("|");

				String v = sb_.substring(0);
				if (!PubMethod.IsBlank(k1_)) {
					tx_.rpush(k1_, v);
					++_mdbCnt;
				}
				if (!PubMethod.IsBlank(k2_)) {
					tx_.rpush(k2_, v);
					++_mdbCnt;
				}
			}
			List<Object> response_ = tx_.exec();
			L.info("{} {} SERV_BIZ_CODE pushed", _verTmStr, response_.size());
		} catch (Exception e) {
			L.warn("{} refresh SERV_BIZ_CODE exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}

	private boolean _refreshProvinceCd() {
		boolean rc_ = true;
		OdbCli odb_cli_ = OdbAgt.GetBizInstance();
		List<Object[]> dbrs_ = odb_cli_.queryForTsList(_Q_PROVINCE_CD);
		if (dbrs_ == null) {
			L.warn("{} refresh PROVINCE_CD from db error", _verTmStr);
			return false;
		}
		L.trace("{} {} records read from PROVINCE_CD", _verTmStr, dbrs_.size());
		_rdbCnt += dbrs_.size();

		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			for (Object[] o : dbrs_) {
				String prov_cd_ = (String) o[0];
				String prov_nm_ = (String) o[1];
				String prov_wl_ = (String) o[2];
				String cmcc_opid_ = (String) o[3];
				String k1_ = String.format("%s:PROVCD:%s", _verTmStr, prov_cd_);
				String k2_ = String.format("%s:PROVOP:%s", _verTmStr, cmcc_opid_);
				String val_ = String.format("19700101000000|99990101000000|%s|%s|%s|%s", prov_cd_,
						prov_wl_ == null ? "" : prov_wl_, cmcc_opid_, prov_nm_);
				tx_.set(k1_, val_);
				++_mdbCnt;
				tx_.set(k2_, val_);
				++_mdbCnt;
			}
			List<Object> response_ = tx_.exec();
			L.info("{} {} PROVINCE_CD pushed", _verTmStr, response_.size());
		} catch (Exception e) {
			L.warn("{} refresh PROVINCE_CD exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}

	private boolean _refreshDomLdAreaCdProv() {
		boolean rc_ = true;
		OdbCli odb_cli_ = OdbAgt.GetBizInstance();
		List<Object[]> dbrs_ = odb_cli_.queryForTsList(_Q_DOM_LD_AREA_CD_PROV);
		if (dbrs_ == null) {
			L.warn("{} refresh DOM_LD_AREA_CD_PROV from db error", _verTmStr);
			return false;
		}
		L.trace("{} {} records read from DOM_LD_AREA_CD_PROV", _verTmStr, dbrs_.size());
		_rdbCnt += dbrs_.size();

		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			for (Object[] o : dbrs_) {
				Long sn_ = ((BigDecimal) o[0]).longValue();
				String ld_area_cd_ = (String) o[1];
				Integer prov_cd_ = ((BigDecimal) o[2]).intValue();
				String ld_area_nm_ = (String) o[3];
				Date eff_tm_ = (Date) o[4];
				Date exp_tm_ = (Date) o[5];
				String k1_ = String.format("%s:AREACD:%s", _verTmStr, ld_area_cd_);
				String k2_ = String.format("%s:AREANZ:%s", _verTmStr, ld_area_cd_.replaceFirst("^0+", ""));
				String val_ = String.format("%s|%s|%d|%s|%d|%s", PubMethod.Time2Str(eff_tm_, PubMethod.TimeStrFmt.Fmt14),
						PubMethod.Time2Str(exp_tm_, PubMethod.TimeStrFmt.Fmt14), sn_, ld_area_cd_, prov_cd_, ld_area_nm_);
				tx_.set(k1_, val_);
				++_mdbCnt;
				tx_.set(k2_, val_);
				++_mdbCnt;
			}
			List<Object> response_ = tx_.exec();
			L.info("{} {} DOM_LD_AREA_CD_PROV pushed", _verTmStr, response_.size());
		} catch (Exception e) {
			L.warn("{} refresh DOM_LD_AREA_CD_PROV exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}

	private boolean _refreshCustomer() throws SQLException {
		List<DbCustomerRec> db_list_ = _odb.getCustomer();
		if (db_list_ == null) {
			L.warn("{} refresh CUSTOMER from db failed", _verTmStr);
			return false;
		}
		L.trace("{} {} records read from CUSTOMER", _verTmStr, db_list_.size());
		_rdbCnt += db_list_.size();
		boolean rc_ = true;
		StringBuilder sb_ = new StringBuilder();
		MdbCli cli_ = MdbAgt.GetShparmInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			for (DbCustomerRec rec_ : db_list_) {
				String k1_ = String.format("%s:CUSTCD:%s", _verTmStr, rec_.getCustomer_code());
				String k2_ = String.format("%s:CUSTID:%s", _verTmStr, rec_.getId());
				sb_.delete(0, sb_.length());
				sb_.append(PubMethod.Timestamp2Str(rec_.getEffective_date(), PubMethod.TimeStrFmt.Fmt14));
				sb_.append("|");
				sb_.append(PubMethod.Timestamp2Str(rec_.getExpiry_date(), PubMethod.TimeStrFmt.Fmt14));
				sb_.append("|");
				sb_.append(rec_.getId());
				sb_.append("|");
				sb_.append(rec_.getParent_id() == null ? "" : rec_.getParent_id());
				sb_.append("|");
				sb_.append(rec_.getCustomer_code());
				sb_.append("|");
				sb_.append(rec_.getProvince_code() == null ? "" : rec_.getProvince_code());
				sb_.append("|");
				sb_.append(rec_.getCustomer_type() == null ? "" : rec_.getCustomer_type());
				sb_.append("|");
				sb_.append(rec_.getBillable_flag() == null ? "" : rec_.getBillable_flag());
				sb_.append("|");
				sb_.append(rec_.getPaid_type() == null ? "" : rec_.getPaid_type());
				sb_.append("|");
				sb_.append(rec_.getFirst_name());
				tx_.set(k1_, sb_.substring(0));
				++_mdbCnt;
				tx_.set(k2_, sb_.substring(0));
				++_mdbCnt;
			}
			List<Object> response_ = tx_.exec();
			L.info("{} {} CUSTOMER pushed", _verTmStr, response_.size());
		} catch (Exception e) {
			L.warn("{} refresh CUSTOMER exception", _verTmStr, e);
			rc_ = false;
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return rc_;
	}
}
