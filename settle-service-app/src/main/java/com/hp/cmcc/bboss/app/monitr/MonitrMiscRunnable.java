package com.hp.cmcc.bboss.app.monitr;

import org.hyperic.sigar.FileSystem;
import org.hyperic.sigar.FileSystemUsage;
import org.hyperic.sigar.Mem;
import org.hyperic.sigar.Sigar;
import org.hyperic.sigar.SigarException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class MonitrMiscRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(MonitrMiscRunnable.class);
	public static Sigar _Sigar;
	private long _tsLoad;
	private long _tsMem;
	private long _tsFs;
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public MonitrMiscRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		long now_;
		while (!_terminateFlag) {
			try {
				PubMethod.Sleep(1000);
				if (_terminateFlag) {
					L.debug("_terminateFlag detected");
					continue;
				}
				now_ = System.currentTimeMillis();
				if (now_ > _tsLoad) {
					_tsLoad = now_ + MonitrCfg._IntervalLoad * 1000;
					_chkLoad();
					now_ = System.currentTimeMillis();
					_tsLoad = now_ + MonitrCfg._IntervalLoad * 1000;
				}
				if (now_ > _tsMem) {
					_tsMem = now_ + MonitrCfg._IntervalMem * 1000;
					_chkMem();
					now_ = System.currentTimeMillis();
					_tsMem = now_ + MonitrCfg._IntervalMem * 1000;
				}
				if (now_ > _tsFs) {
					_tsFs = now_ + MonitrCfg._IntervalFs * 1000;
					_chkFs();
					now_ = System.currentTimeMillis();
					_tsFs = now_ + MonitrCfg._IntervalFs * 1000;
				}
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
		L.info("thread end");
	}

	private void _chkLoad() {
		try {
			double[] load_ = _Sigar.getLoadAverage();
			if (load_ == null) {
				L.warn("getLoadAverage returns null");
				return;
			}
			if (load_.length < 3) {
				L.warn("getLoadAverage returns {} elements, lt 3", load_.length);
				return;
			}
			L.debug("load avg 01:05:15 {}:{}:{}", load_[0], load_[1], load_[2]);
			if (load_[2] > MonitrCfg._Load15Threshold) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200321001_LOAD_15_HEAVY, PubMethod.FmtArgs("load avg 15 %f > %f",
						load_[2], MonitrCfg._Load15Threshold), null);
				_odbUtils.addRawAlm(null, alm_);
				return;
			}
			if (load_[1] > MonitrCfg._Load05Threshold) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200321002_LOAD_05_HEAVY, PubMethod.FmtArgs("load avg 05 %f > %f",
						load_[1], MonitrCfg._Load05Threshold), null);
				_odbUtils.addRawAlm(null, alm_);
				return;
			}
			if (load_[0] > MonitrCfg._Load01Threshold) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200221003_LOAD_01_HEAVY, PubMethod.FmtArgs("load avg 01 %f > %f",
						load_[0], MonitrCfg._Load01Threshold), null);
				_odbUtils.addRawAlm(null, alm_);
			}
		} catch (SigarException e) {
			L.warn("getLoadAverage exception", e);
		}
	}

	private void _chkMem() {
		try {
			Mem mem_ = _Sigar.getMem();
			if (mem_ == null) {
				L.warn("getMem returns null");
				return;
			}
			L.debug("mem total:used:free:afree {}:{}:{}:{}", mem_.getTotal(), mem_.getUsed(), mem_.getFree(), mem_.getActualFree());
			long free_kb_ = mem_.getFree() / 1024;
			long tot_kb_ = mem_.getTotal() / 1024;
			double usage_ = (100.0 * (mem_.getTotal() - mem_.getFree())) / mem_.getTotal();
			if (free_kb_ < MonitrCfg._MemFreeKbThreshold) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200321004_MEM_FREE_LOW, PubMethod.FmtArgs(
						"mem free %d < %d, (total %d, usage %f%%)", free_kb_, MonitrCfg._MemFreeKbThreshold, tot_kb_, usage_), null);
				_odbUtils.addRawAlm(null, alm_);
			}
		} catch (SigarException e) {
			L.warn("getMem exception", e);
		}
	}

	private void _chkFs() {
		try {
			FileSystem[] fs_list_ = _Sigar.getFileSystemList();
			if (fs_list_ == null) {
				L.warn("getFileSystemList returns null");
				return;
			}
			L.trace("list {} fs info", fs_list_.length);
			for (MonitrFsThreshold threshold_ : MonitrCfg._FsThresholdMap.values()) {
				boolean found_ = false;
				for (FileSystem fs_ : fs_list_) {
					if (threshold_._deviceNm.equals(fs_.getDevName())) {
						found_ = true;
						_chkOneFs(fs_, threshold_);
						break;
					}
				}
				if (!found_)
					L.warn("perhaps mis-configured DEVICE_NM [{}], pls chk", threshold_._deviceNm);
			}
		} catch (SigarException e) {
			L.warn("getFileSystemList exception", e);
		}
	}

	private void _chkOneFs(FileSystem fs, MonitrFsThreshold threshold) {
		try {
			FileSystemUsage fs_usage_ = _Sigar.getMountedFileSystemUsage(fs.getDirName());
			if (fs_usage_ == null) {
				L.warn("getFileSystemUsage({},{}) returns null", fs.getDevName(), fs.getDirName());
				return;
			}
			double used_pct_ = (100.0 * (fs_usage_.getTotal() - fs_usage_.getAvail())) / fs_usage_.getTotal();
			L.debug("fs {},{}, tot:use:avail:pct:upct {}:{}:{}:{}:{}",
					new Object[] { fs.getDevName(), fs.getDirName(), fs_usage_.getTotal(), fs_usage_.getUsed(),
							fs_usage_.getAvail(), fs_usage_.getUsePercent(), used_pct_ });
			if (used_pct_ > threshold._thresholdUsePct) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200321005_FS_FREE_LOW, PubMethod.FmtArgs(
						"fs %s (%s) usage %.3f%% > %.3f%%, tot=%d used=%d avail=%d free=%d (KB)", fs.getDevName(), fs.getDirName(),
						used_pct_, threshold._thresholdUsePct, fs_usage_.getTotal(), fs_usage_.getUsed(), fs_usage_.getAvail(),
						fs_usage_.getFree()), null);
				_odbUtils.addRawAlm(null, alm_);
			}
		} catch (SigarException e) {
			L.warn("get fs usage of {} exception", fs.getDevName(), e);
		}
	}
}
