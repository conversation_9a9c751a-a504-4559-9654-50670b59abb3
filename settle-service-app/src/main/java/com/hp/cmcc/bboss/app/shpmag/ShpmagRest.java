package com.hp.cmcc.bboss.app.shpmag;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.app.RestRequestValidator;

@Path("rest")
public class ShpmagRest {
	private static Logger L = LoggerFactory.getLogger(ShpmagRest.class);

	@POST
	@Path("trigger")
	@Produces(MediaType.TEXT_PLAIN)
	public String trigger(@Context HttpServletRequest request) {
		L.info("request received, {} bytes", request.getContentLength());
		try {
			String rsps_ = RestRequestValidator.ValidateCurrentTime(request, null);
			if ("0".equals(rsps_)) {
				ShpmagMgr._TriggerFlag = true;
			}
			return rsps_;
		} catch (Exception e) {
			L.warn("process trigger request exception", e);
			return e.toString();
		}
	}
}
