package com.hp.cmcc.bboss.app.rmtdis;

import java.sql.Timestamp;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class RmtdisOdb {
	private static Logger L = LoggerFactory.getLogger(RmtdisOdb.class);
	private static final String _Q_RMTDIS_TRANS_CFG = "SELECT A.TRANS_CFG_NM TRANS_CFG_NM, A.INST_NM INST_NM, A.THREAD_NM "
			+ "THREAD_NM, A.LOGIN_CFG_NM LOGIN_CFG_NM, A.TRANS_MODE TRANS_MODE, A.RMT_DIR RMT_DIR, A.RMT_TMP_DIR RMT_TMP_DIR, "
			+ "A.RMT_BAK_DIR RMT_BAK_DIR, A.LOC_DIR LOC_DIR, A.LOC_TMP_DIR LOC_TMP_DIR, A.LOC_BAK_DIR LOC_BAK_DIR, "
			+ "A.PATTERN_MAT PATTERN_MAT, A.PATTERN_IGN PATTERN_IGN, A.MIN_INTERVAL MIN_INTERVAL, A.TMP_RENAME_RULE "
			+ "TMP_RENAME_RULE FROM RMTDIS_TRANS_CFG A, RMTDIS_LOGIN_CFG B WHERE A.INST_NM = ? AND A.ENABLED = 1 "
			+ "AND A.LOGIN_CFG_NM = B.LOGIN_CFG_NM AND B.ENABLED = 1";
	private static final String _Q_RMTDIS_LOGIN_CFG = "SELECT DISTINCT A.LOGIN_CFG_NM LOGIN_CFG_NM, A.RMT_HOST RMT_HOST, "
			+ "A.RMT_PORT RMT_PORT, A.RMT_USER RMT_USER, A.RMT_PASSWD_CRYPT RMT_PASSWD_CRYPT FROM RMTDIS_LOGIN_CFG A, "
			+ "RMTDIS_TRANS_CFG B WHERE A.LOGIN_CFG_NM = B.LOGIN_CFG_NM AND B.INST_NM = ?";
	private static final String _I_RMTDIS_LOG = "INSERT INTO RMTDIS_LOG (MMDD, INST_NM, TRANS_CFG_NM, LOGIN_CFG_NM, TRANS_MODE, "
			+ "FILE_NM, FILE_SZ, FILE_CKSUM, RMT_HOST, RMT_PORT, RMT_USER, RMT_DIR, RMT_MODIFY_TM, LOC_HOST, LOC_PORT, LOC_DIR, "
			+ "LOC_MODIFY_TM, TS_START, TS_END, TM_END, TRANS_STATUS, ERR_MSG) VALUES "
			+ "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	public List<DbRmtdisTransCfgRec> getTransCfg(String inst_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbRmtdisTransCfgRec> result_ = cli_.queryForOList(_Q_RMTDIS_TRANS_CFG, DbRmtdisTransCfgRec.class, inst_nm);
		return result_;
	}

	public List<DbRmtdisLoginCfgRec> getLoginCfg(String inst_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbRmtdisLoginCfgRec> result_ = cli_.queryForOList(_Q_RMTDIS_LOGIN_CFG, DbRmtdisLoginCfgRec.class, inst_nm);
		return result_;
	}

	public void addRmtdisLog(DbRmtdisLogRec rec) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		long now_ = System.currentTimeMillis();
		rec.setTs_end(now_);
		rec.setTm_end(new Timestamp(now_));
		rec.setMmdd(Integer.parseInt(PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt8).substring(4, 8)));
		cli_.update(_I_RMTDIS_LOG, rec.asInsertObjArray());
		L.info("add {}", rec.toGsonStr());
	}
}
