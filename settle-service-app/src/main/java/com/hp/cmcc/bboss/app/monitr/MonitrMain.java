package com.hp.cmcc.bboss.app.monitr;

import javax.ws.rs.NotFoundException;

import org.hyperic.sigar.Sigar;
import org.jboss.resteasy.plugins.server.tjws.TJWSEmbeddedJaxrsServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.app.RestNotFoundExceptionHandler;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class MonitrMain {
	private static Logger L = LoggerFactory.getLogger(MonitrMain.class);
	static MonitrProcRunnable _ProcRunnable;
	private static MonitrMiscRunnable _MiscRunnable;
	private static MonitrDirRunnable _DirRunnable;
	private static Thread _ProcThread;
	private static Thread _MiscThread;
	private static Thread _DirThread;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("monitr", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 4, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!MonitrCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		try {
			MonitrProcRunnable._Sigar = new Sigar();
			MonitrMiscRunnable._Sigar = new Sigar();
		} catch (Exception e) {
			L.error("sigar exception, pls chk ${SHLIB_PATH}, .so/.sl/.dll or classpath, JVM exit", e);
			System.exit(1);
		}

		MonitrRest rest_sv_ = new MonitrRest();
		TJWSEmbeddedJaxrsServer tjws = new TJWSEmbeddedJaxrsServer();
		tjws.setPort(MonitrCfg._RestPort);
		tjws.setRootResourcePath("/");
		tjws.start();
		tjws.getDeployment().getRegistry().addSingletonResource(rest_sv_);
		tjws.getDeployment().getProviderFactory().getExceptionMappers()
				.put(NotFoundException.class, new RestNotFoundExceptionHandler());
		L.info("REST service started at port {}", MonitrCfg._RestPort);

		_ProcRunnable = new MonitrProcRunnable();
		_MiscRunnable = new MonitrMiscRunnable();
		_DirRunnable = new MonitrDirRunnable();
		_ProcThread = new Thread(_ProcRunnable, "PROC");
		_MiscThread = new Thread(_MiscRunnable, "MISC");
		_DirThread = new Thread(_DirRunnable, "SDIR");
		_ProcThread.start();
		_MiscThread.start();
		_DirThread.start();
	}

	private static void _MainLoop() throws InterruptedException {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}
			L.info("try stop normal");
			_ProcRunnable.setTerminateFlag();
			_MiscRunnable.setTerminateFlag();
			_DirRunnable.setTerminateFlag();
			L.debug("try join thread {}", _ProcThread.getName());
			_ProcThread.join();
			L.debug("thread {} joined", _ProcThread.getName());
			L.debug("try join thread {}", _MiscThread.getName());
			_MiscThread.join();
			L.debug("thread {} joined", _MiscThread.getName());
			L.debug("try join thread {}", _DirThread.getName());
			_DirThread.join();
			L.debug("thread {} joined", _DirThread.getName());
			break;
		}
	}
}
