package com.hp.cmcc.bboss.app.erorcy;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbErorcyLogRec extends GsonObj {
	private Integer log_mm;
	private String rcy_file_nm;
	private Integer rcy_status;
	private Integer rcy_cnt;
	private Long rcy_file_sz;
	private String biz_type;
	private Timestamp task_tm;
	private Integer file_type;
	private String rcy_table;
	private Integer acnt_ym;
	private String rcy_condition;
	private Timestamp rcy_tm;
	private Long ts_start;
	private Long ts_end;
	private String rcy_desc;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[15];
		o[0] = log_mm;
		o[1] = rcy_file_nm;
		o[2] = rcy_status;
		o[3] = rcy_cnt;
		o[4] = rcy_file_sz;
		o[5] = biz_type;
		o[6] = task_tm;
		o[7] = file_type;
		o[8] = rcy_table;
		o[9] = acnt_ym;
		o[10] = rcy_condition;
		o[11] = rcy_tm;
		o[12] = ts_start;
		o[13] = ts_end;
		o[14] = rcy_desc;
		return o;
	}

	public Integer getLog_mm() {
		return log_mm;
	}

	public void setLog_mm(Integer log_mm) {
		this.log_mm = log_mm;
	}

	public String getRcy_file_nm() {
		return rcy_file_nm;
	}

	public void setRcy_file_nm(String rcy_file_nm) {
		this.rcy_file_nm = rcy_file_nm;
	}

	public Integer getRcy_status() {
		return rcy_status;
	}

	public void setRcy_status(Integer rcy_status) {
		this.rcy_status = rcy_status;
	}

	public Integer getRcy_cnt() {
		return rcy_cnt;
	}

	public void setRcy_cnt(Integer rcy_cnt) {
		this.rcy_cnt = rcy_cnt;
	}

	public Long getRcy_file_sz() {
		return rcy_file_sz;
	}

	public void setRcy_file_sz(Long rcy_file_sz) {
		this.rcy_file_sz = rcy_file_sz;
	}

	public String getBiz_type() {
		return biz_type;
	}

	public void setBiz_type(String biz_type) {
		this.biz_type = biz_type;
	}

	public Timestamp getTask_tm() {
		return task_tm;
	}

	public void setTask_tm(Timestamp task_tm) {
		this.task_tm = task_tm;
	}

	public Integer getFile_type() {
		return file_type;
	}

	public void setFile_type(Integer file_type) {
		this.file_type = file_type;
	}

	public String getRcy_table() {
		return rcy_table;
	}

	public void setRcy_table(String rcy_table) {
		this.rcy_table = rcy_table;
	}

	public Integer getAcnt_ym() {
		return acnt_ym;
	}

	public void setAcnt_ym(Integer acnt_ym) {
		this.acnt_ym = acnt_ym;
	}

	public String getRcy_condition() {
		return rcy_condition;
	}

	public void setRcy_condition(String rcy_condition) {
		this.rcy_condition = rcy_condition;
	}

	public Timestamp getRcy_tm() {
		return rcy_tm;
	}

	public void setRcy_tm(Timestamp rcy_tm) {
		this.rcy_tm = rcy_tm;
	}

	public Long getTs_start() {
		return ts_start;
	}

	public void setTs_start(Long ts_start) {
		this.ts_start = ts_start;
	}

	public Long getTs_end() {
		return ts_end;
	}

	public void setTs_end(Long ts_end) {
		this.ts_end = ts_end;
	}

	public String getRcy_desc() {
		return rcy_desc;
	}

	public void setRcy_desc(String rcy_desc) {
		this.rcy_desc = rcy_desc;
	}
}
