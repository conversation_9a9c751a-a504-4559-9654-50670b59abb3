package com.hp.cmcc.bboss.app.udr2mq;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.GzipRunnable;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

/**
 * udr2mq：(Usage Detailed Record to Message Queue) 话单加载，文件到kafka队列；系统中至少部署一个实例。
 *
 */
public class Udr2mqMain {
	private static Logger L = LoggerFactory.getLogger(Udr2mqMain.class);
	static BlockingQueue<String> _GzipTaskQueue = new LinkedBlockingQueue<String>();
	static ExecutorService _ToJsonPool = null;
	private static GzipRunnable _GzipRunnable = null;
	private static Thread _GzipThread = null;
	private static List<Udr2mqBizRunnable> _RunnableList = new ArrayList<Udr2mqBizRunnable>();
	private static List<Thread> _ThreadList = new ArrayList<Thread>();
	private static Udr2mqDustmanRunnable _DustmanRunnable = null; //
	private static Thread _DustmanThread = null;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("udr2mq", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);//初始化lock中的stop文件
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);//
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() throws Exception {
		//通过参数 k 来通过redis获取Oracle数据库配置连接Oracle数据库
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, _RunnableList.size() + 1, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		//加载Oracle数据库中配置信息
		if (!Udr2mqCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		//日志DEBUG级别加载信息日志打印
		Udr2mqCfg.Debug();
		if (!MdbAgt.Init(OdbSystemParam.GetInstance(), 2, Udr2mqCfg._BizCfgMap.size() + 1, 0)) {
			L.error("init mdb connections error, JVM exit");
			System.exit(1);
		}
		//创建固定JSON格式转换线程池(数据库中APP_PARAM key=TO_JSON_POOL_SIZE配置)
		_ToJsonPool = Executors.newFixedThreadPool(Udr2mqCfg._ToJsonPoolSize);
		
		for (Map.Entry<String, List<Udr2mqBizCfg>> entry_ : Udr2mqCfg._ThreadGrpMap.entrySet()) {
			Udr2mqBizRunnable udr2mqBizRunnable = new Udr2mqBizRunnable(entry_.getValue());
			udr2mqBizRunnable.init();
			Thread udr2mqBizThread = new Thread(udr2mqBizRunnable, entry_.getKey());
			udr2mqBizThread.start();
			_RunnableList.add(udr2mqBizRunnable);
			_ThreadList.add(udr2mqBizThread);
		}
		GzipRunnable gzipRunnable = new GzipRunnable(_GzipTaskQueue, OdbSystemParam.GetInstance()._gzipPath, true);
		Thread gzipThread = new Thread(gzipRunnable, "GZIP");
		gzipThread.start();
		_GzipRunnable = gzipRunnable;
		_GzipThread = gzipThread;

		_DustmanRunnable = new Udr2mqDustmanRunnable();
		_DustmanThread = new Thread(_DustmanRunnable, "DSTM");
		_DustmanThread.start();
	}

	/**
	 * 主进程循环判断是否中断
	 * @throws InterruptedException
	 */
	private static void _MainLoop() throws InterruptedException {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}
			L.info("try stop normal");

			_DustmanRunnable.setTerminateFlag();
			for (Udr2mqBizRunnable runnable_ : _RunnableList) {
				runnable_.setTerminateFlag();
			}

			L.debug("try join thread {}", _DustmanThread.getName());
			_DustmanThread.join(); //等待该进程死亡
			L.debug("thread {} joined", _DustmanThread.getName());

			L.debug("{} worker threads to be joined", _ThreadList.size());
			for (Thread thr_ : _ThreadList) {
				L.debug("try join thread {}", thr_.getName());
				thr_.join(); //等待该进程死亡
				L.debug("thread {} joined", thr_.getName());
			}

			_ToJsonPool.shutdown(); //启动一个有序的关机，在以前提交的任务被执行，但没有新的任务将被接受。
			L.debug("_ToGsonPool shutdown done");

			if (_GzipRunnable != null) {
				_GzipRunnable.setTerminateFlag();
			}
			L.debug("try join thread {}", _GzipThread.getName());
			_GzipThread.join(); //等待该进程死亡
			L.debug("thread {} joined", _GzipThread.getName());
			break;
		}
	}
}
