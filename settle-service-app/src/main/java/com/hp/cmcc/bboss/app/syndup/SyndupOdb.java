package com.hp.cmcc.bboss.app.syndup;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.entity.DbDupchkUdrRec;

public class SyndupOdb {
	private static Logger L = LoggerFactory.getLogger(SyndupOdb.class);
	private static final String _I_DUPCHK_SYN = "INSERT INTO DUPCHK_SYN (MMDD, HASH_KEY, EVENT_TIME, AUX_KEY, FILE_ID, LINE_NUM) "
			+ "VALUES(?, ?, ?, ?, ?, ?)";
	private static final String _M_DUPCHK_SYN_2_DUPCHK_UDR = "MERGE INTO DUPCHK_UDR A USING DUPCHK_SYN B ON "
			+ "(A.MMDD = B.MMDD AND A.HASH_KEY = B.HASH_KEY AND A.EVENT_TIME = B.EVENT_TIME AND A.AUX_KEY = B.AUX_KEY) "
			+ "WHEN NOT MATCHED THEN INSERT VALUES (B.MMDD, B.HASH_KEY, B.EVENT_TIME, B.AUX_KEY, B.FILE_ID, B.LINE_NUM)";
	private static final String _T_DUPCHK_SYN = "TRUNCATE TABLE DUPCHK_SYN";

	public void addSyndup(Connection conn, List<DbDupchkUdrRec> rec_list) throws SQLException {
		if (rec_list == null || rec_list.isEmpty())
			return;
		OdbCli cli_ = OdbAgt.GetUdrInstance();
		Object[][] params_ = new Object[rec_list.size()][];
		int i = 0;
		for (DbDupchkUdrRec rec_ : rec_list) {
			params_[i] = rec_.asInsertObjArray();
			i++;
		}
		cli_.insetBatch(conn, _I_DUPCHK_SYN, params_);
		L.debug("{} records inserted into DUPCHK_SYN", rec_list.size());
	}

	public int merge2SttlDupchk(Connection conn) throws SQLException {
		OdbCli cli_ = OdbAgt.GetUdrInstance();
		int rows_affected_ = cli_.update(conn, _M_DUPCHK_SYN_2_DUPCHK_UDR);
		L.debug("{} rows affected by merge", rows_affected_);
		return rows_affected_;
	}

	public void truncateSyndup(Connection conn) throws SQLException {
		OdbCli cli_ = OdbAgt.GetUdrInstance();
		cli_.update(conn, _T_DUPCHK_SYN);
		L.trace("SYNDUP truncated");
	}
}
