package com.hp.cmcc.bboss.app;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.PubMethod;

public class RestRequestValidator {
	private static Logger L = LoggerFactory.getLogger(RestRequestValidator.class);

	public static String ValidateCurrentTime(HttpServletRequest request, StringBuilder raw_rqst) throws IOException {
		String rsps_ = "0";
		byte[] buf_ = new byte[2048];
		int rd_ = request.getInputStream().read(buf_);
		if (rd_ <= 0 || rd_ >= 2047) {
			rsps_ = PubMethod.FmtArgs("read %d bytes from input stream, bad length, len should between 1 and 2047", rd_);
			<PERSON>.warn("{}", rsps_);
			return rsps_;
		}
		String rqst_ = new String(buf_, 0, rd_).trim();
		if (raw_rqst != null) {
			raw_rqst.delete(0, raw_rqst.length());
			raw_rqst.append(rqst_);
		}
		L.debug("raw refresh rqst [{}]", rqst_);
		if (!PubMethod._ExpTMFMT14.matcher(rqst_).find()) {
			rsps_ = PubMethod.FmtArgs("rqst [%s] not match pattern [%s]", rqst_, PubMethod._ExpTMFMT14.pattern());
			L.warn("{}", rsps_);
			return rsps_;
		}
		long now_millis_ = System.currentTimeMillis();
		long rqst_ts_ = PubMethod.Str2Long(rqst_, PubMethod.TimeStrFmt.Fmt14);
		long diff_ = now_millis_ - rqst_ts_;
		long abs_diff_ = Math.abs(diff_);
		if (abs_diff_ > 300 * 1000) {
			rsps_ = PubMethod.FmtArgs("rqst [%s] time diff is %d millis, too large to accept", rqst_, diff_);
			L.warn("{}", rsps_);
			return rsps_;
		}
		L.info("rqst [{}] accepted, time diff is {} millis", rqst_, diff_);
		return rsps_;
	}
}
