package com.hp.cmcc.bboss.app.shpmag;

import javax.ws.rs.NotFoundException;

import org.jboss.resteasy.plugins.server.tjws.TJWSEmbeddedJaxrsServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.app.RestNotFoundExceptionHandler;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

/**
 * （SHared ParaMeter AGent）公参发布Agent，将公参落地到SQLite3格式的文件，供各storm worker使用；
 * 	每台运行storm worker的主机部署一个shpmag实例。
 * <AUTHOR>
 *
 */
public class ShpmagMain {
	private static Logger L = LoggerFactory.getLogger(ShpmagMain.class);
	private static ShpmagMgr _ShpmagMgr;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("shpmag", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 2, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!ShpmagCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		ShpmagCfg.Trace();
		_ShpmagMgr = new ShpmagMgr();

		ShpmagRest rest_sv_ = new ShpmagRest();
		TJWSEmbeddedJaxrsServer tjws = new TJWSEmbeddedJaxrsServer();
		tjws.setPort(ShpmagCfg._RestPort);
		tjws.setRootResourcePath("/");
		tjws.start();
		tjws.getDeployment().getRegistry().addSingletonResource(rest_sv_);
		tjws.getDeployment().getProviderFactory().getExceptionMappers()
				.put(NotFoundException.class, new RestNotFoundExceptionHandler());
		L.info("REST service started at port {}", ShpmagCfg._RestPort);
	}

	private static void _MainLoop() {
		L.debug("trap in main loop");
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		long cnt_ = 0;
		while (true) {
			try {
				PubMethod.Sleep(500);
				cnt_++;
				if (ShpmagMgr._TriggerFlag) {
					L.info("trigger flag detected");
					ShpmagMgr._TriggerFlag = false;
					if (_ShpmagMgr.needUpdate())
						_ShpmagMgr.update();
					continue;
				} else if ((cnt_ % 2 == 0)) {
					if (ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
						L.info("try stop normal");
						break;
					}
				} else if (cnt_ % 19 == 0) {
					if (_ShpmagMgr.needUpdate())
						_ShpmagMgr.update();
				}
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
	}
}
