package com.hp.cmcc.bboss.app.monitr;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.regex.Pattern;

import org.hyperic.sigar.ProcCpu;
import org.hyperic.sigar.ProcCredName;
import org.hyperic.sigar.ProcExe;
import org.hyperic.sigar.ProcMem;
import org.hyperic.sigar.ProcState;
import org.hyperic.sigar.ProcTime;
import org.hyperic.sigar.Sigar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class MonitrProcRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(MonitrProcRunnable.class);
	public static Sigar _Sigar;
	private final static Pattern _ExpJpsOutput = Pattern.compile("^[0-9]+ +[^ ]+");
	private String _me; // current os user
	private Map<Long, MonitrPsInfo> _psInfoMap;
	private Map<String, Set<Long>> _userPidMap;
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public MonitrProcRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_psInfoMap = new TreeMap<Long, MonitrPsInfo>();
		_userPidMap = new TreeMap<String, Set<Long>>();
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				if (_refreshPsInfo()) {
					_monitor();
				}
				_interval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
		L.info("thread end");
	}

	private void _monitor() {
		Map<String, DbMonitrCfgRec> monitr_cfg_map_ = MonitrCfg._MonitrCfgMap;
		Map<String, Long> prog_pid_map_ = MonitrCfg._ProgPidMap;
		for (DbMonitrCfgRec cfg_ : monitr_cfg_map_.values()) {
			MonitrPsInfo ps_info_ = _match(cfg_);
			Long old_pid_ = prog_pid_map_.get(cfg_.getProg_nm());
			if (ps_info_ != null) {
				if (old_pid_ == null) {
					prog_pid_map_.put(cfg_.getProg_nm(), ps_info_._pid);
					L.debug("[{}] is running, {}", cfg_.getProg_nm(), ps_info_.toGsonStr());
				} else if (ps_info_._pid != old_pid_) {
					prog_pid_map_.put(cfg_.getProg_nm(), ps_info_._pid);
					L.debug("[{}] pid change o:n {}:{}, {}", cfg_.getProg_nm(), old_pid_, ps_info_._pid, ps_info_.toGsonStr());
					DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200321008_PROC_PID_CHG,
							PubMethod.FmtArgs("%s [%s] pid change o:n %d:%d", cfg_.getProg_nm(), cfg_.getPs_pattern(), old_pid_,
									ps_info_._pid),
							ps_info_.toGsonStr());
					_odbUtils.addRawAlm(null, alm_);
				}
				continue;
			}

			if (old_pid_ != null && old_pid_ < 0) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200331007_PROC_NOT_RUNNING,
						PubMethod.FmtArgs("%s [%s] not running", cfg_.getProg_nm(), cfg_.getPs_pattern()), null);
				_odbUtils.addRawAlm(null, alm_);
			} else {
				prog_pid_map_.put(cfg_.getProg_nm(), -1L);
			}

			if (cfg_.getOs_user().equals("-1") || cfg_.getOs_user().equals(_me)) {
				_startApp(cfg_);
			}
		}
	}

	private void _startApp(DbMonitrCfgRec cfg) {
		if (cfg.getStart_cmd().equals("NULL")) {
			L.trace("prog_nm [{}], no start_cmd, ignore", cfg.getProg_nm());
			return;
		}
		List<String> cmd_ = PubMethod.AssembleCmd(cfg.getStart_cmd());
		ProcessBuilder pb_ = new ProcessBuilder(cmd_);
		pb_.inheritIO();
		try {
			pb_.start();
			L.info("[{}] [{}] invoked", cfg.getProg_nm(), cfg.getStart_cmd());
		} catch (IOException e) {
			L.warn("invoke [{},{}] exception", cfg.getProg_nm(), cfg.getStart_cmd(), e);
		}
	}

	private MonitrPsInfo _match(DbMonitrCfgRec cfg) {
		String os_user_ = cfg.getOs_user();
		if (os_user_.equals("-1"))
			os_user_ = _me;
		Set<Long> pid_set_ = _userPidMap.get(os_user_);
		if (pid_set_ == null) {
			L.info("ps info of user [{}] not found, prog_nm [{}] not running", os_user_, cfg.getProg_nm());
			return null;
		}
		for (Long pid_ : pid_set_) {
			MonitrPsInfo ps_info_ = _psInfoMap.get(pid_);
			if (ps_info_ == null) {
				L.warn("absurd, pid {} has no correspond info, skip", pid_);
				continue;
			}
			if (cfg.getIs_jps() == 0) {
				if (cfg._psPattern.matcher(ps_info_._cmd).find()) {
					// L.trace("prog_nm [{}] is running, {}", cfg.getProg_nm(), ps_info_.toGsonStr());
					return ps_info_;
				}
			} else if (ps_info_._jps != null) {
				if (cfg._psPattern.matcher(ps_info_._jps).find()) {
					// L.trace("prog_nm [{}] jps is running, {}", cfg.getProg_nm(), ps_info_.toGsonStr());
					return ps_info_;
				}
			}
		}
		L.info("prog_nm [{}] not running", cfg.getProg_nm());
		return null;
	}

	private boolean _refreshPsInfo() {
		AppTicker ticker_ = new AppTicker();
		long[] pids_ = null;
		Map<Long, MonitrPsInfo> ps_info_map_ = new TreeMap<Long, MonitrPsInfo>();
		Map<String, Set<Long>> user_pid_map_ = new TreeMap<String, Set<Long>>();
		try {
			pids_ = _Sigar.getProcList();
			MonitrPsInfo ps_info_ = _getPsInfoByPid(Long.parseLong(PubMethod._JvmPid), null);
			_me = ps_info_._user;
			for (long pid_ : pids_) {
				ps_info_ = _getPsInfoByPid(pid_, null);
				if (ps_info_ != null) {
					ps_info_map_.put(pid_, ps_info_);
					Set<Long> pid_set_ = user_pid_map_.get(ps_info_._user);
					if (pid_set_ == null) {
						pid_set_ = new TreeSet<Long>();
						user_pid_map_.put(ps_info_._user, pid_set_);
					}
					pid_set_.add(ps_info_._pid);
				}
			}
		} catch (Exception e) {
			L.warn("get process list exception", e);
			_psInfoMap.clear();
			_userPidMap.clear();
			return false;
		}

		_psInfoMap = ps_info_map_;
		_userPidMap = user_pid_map_;
		_refreshJpsInfo();
		if (_psInfoMap.size() > MonitrCfg._MaxProcThreshold) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200321006_TOO_MANY_PROC,
					PubMethod.FmtArgs("process num %d > %d", _psInfoMap.size(), MonitrCfg._MaxProcThreshold), null);
			_odbUtils.addRawAlm(null, alm_);
		}
		ticker_.tickEnd(_psInfoMap.size());
		L.debug("{} ps info gathered in {} seconds, pfm {}/s, _me [{}]",
				new Object[] { ticker_._recNum, ticker_._term, ticker_._pfm, _me });
		return true;
	}

	private void _refreshJpsInfo() {
		AppTicker ticker_ = new AppTicker();
		List<String> cmd_ = PubMethod.AssembleCmd(MonitrCfg._Jps + " -lvm");
		ProcessBuilder pb_ = new ProcessBuilder(cmd_);
		pb_.redirectErrorStream(true);
		BufferedReader br_ = null;
		int jps_cnt_ = 0;
		try {
			Process proc_ = pb_.start();
			br_ = new BufferedReader(new InputStreamReader(proc_.getInputStream()));
			String[] jps_ = null;
			String raw_line_ = null;
			while ((raw_line_ = br_.readLine()) != null) {
				if (!_ExpJpsOutput.matcher(raw_line_).find()) {
					L.trace("[{}] not match [{}], skip", raw_line_, _ExpJpsOutput.pattern());
					continue;
				}
				jps_ = raw_line_.split(" +", 2);
				if (jps_.length != 2) {
					L.debug("[{}] insufficient fields, skip", raw_line_);
					continue;
				}
				long pid_ = Long.parseLong(jps_[0]);
				MonitrPsInfo ps_info_ = _psInfoMap.get(pid_);
				if (ps_info_ == null) {
					// L.trace("pid [{}] not in _psInfoMap, skip [{}]", pid_, jps_[1]);
					continue;
				}
				ps_info_._jps = jps_[1];
				// L.trace("jps {} added to _psInfoMap", pid_);
				++jps_cnt_;
			}
			if (jps_cnt_ > 0) {
				ticker_.tickEnd(jps_cnt_);
				L.debug("{} jps info gathered in {} seconds, pfm {}/s", jps_cnt_, ticker_._term, ticker_._pfm);
			}
		} catch (Exception e) {
			L.warn("call jps exception, sleep 3 seconds", e);
			PubMethod.Sleep(3000);
		} finally {
			PubMethod.Close(br_);
		}
	}

	private MonitrPsInfo _getPsInfoByPid(long pid, String user) {
		String user_ = null;
		try {
			ProcCredName pcred_ = _Sigar.getProcCredName(pid);
			user_ = pcred_ == null ? "NULL" : pcred_.getUser();
		} catch (Exception e) {
			// L.warn("getProcCredName({}) exception, pls be noticed", pid, e);
			user_ = "???";
		}

		if (!PubMethod.IsEmpty(user)) {
			if (!user.equals(user_))
				return null;
		}

		MonitrPsInfo ps_info_ = new MonitrPsInfo();
		ps_info_._collectTm = new Timestamp(System.currentTimeMillis());
		ps_info_._pid = pid;
		ps_info_._user = user_;

		long ppid_ = 0;
		try {
			ProcState pstate_ = _Sigar.getProcState(pid);
			ppid_ = pstate_ == null ? 0 : pstate_.getPpid();
		} catch (Exception e) {
			// L.warn("getProcState({}) exception, pls be noticed", pid, e);
		}
		ps_info_._ppid = ppid_;

		String cwd_ = "/working/dir/" + pid;
		String name_ = Long.toString(pid);
		try {
			ProcExe pexe_ = _Sigar.getProcExe(pid);
			if (pexe_ != null) {
				cwd_ = pexe_.getCwd();
				name_ = pexe_.getName();
			}
		} catch (Exception e) {
			// L.warn("getProcExe({}) exception, pls be noticed", pid, e);
		}
		ps_info_._cwd = cwd_;
		ps_info_._name = name_;

		long start_ts_ = 0;
		try {
			ProcTime ptime_ = _Sigar.getProcTime(pid);
			if (ptime_ != null)
				start_ts_ = ptime_.getStartTime();
		} catch (Exception e) {
			// L.warn("getProcTime({}) exception, pls be noticed", pid, e);
		}
		ps_info_._startTm = new Timestamp(start_ts_);

		try {
			ProcMem pmem_ = _Sigar.getProcMem(pid);
			if (pmem_ != null) {
				ps_info_._memResident = pmem_.getResident();
				ps_info_._memShare = pmem_.getShare();
				ps_info_._memSize = pmem_.getSize();
			}
		} catch (Exception e) {
			// L.warn("getProcMem({}) exception, pls ignore", pid, e);
		}

		try {
			ProcCpu pcpu_ = _Sigar.getProcCpu(pid);
			if (pcpu_ != null) {
				ps_info_._cpuSys = pcpu_.getSys();
				ps_info_._cpuUser = pcpu_.getUser();
				ps_info_._cpuTotal = pcpu_.getTotal();
			}
		} catch (Exception e) {
			// L.warn("getProcCpu({}) exception, pls ignore", pid, e);
		}

		String cmd_ = Long.toString(pid);
		try {
			String[] args_ = _Sigar.getProcArgs(pid);
			if (args_ != null && args_.length > 0) {
				StringBuilder sb_ = new StringBuilder();
				for (int i = 0; i < args_.length - 1; ++i) {
					sb_.append(args_[i]);
					sb_.append(" ");
				}
				sb_.append(args_[args_.length - 1]);
				cmd_ = sb_.substring(0);
			}
		} catch (Exception e) {
			// L.warn("getProcArgs({}) exception, pls chk", pid, e);
		}
		ps_info_._cmd = cmd_.trim();
		return ps_info_;
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		for (int i = 0; i < MonitrCfg._IntervalProc; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}
}
