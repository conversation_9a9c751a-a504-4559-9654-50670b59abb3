package com.hp.cmcc.bboss.app.bizrcy;

import com.google.gson.Gson;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.udr.UdrFileHandle;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.pub.udr.UdrKafkaProducerFactory;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.io.File;
import java.sql.Timestamp;
import java.util.Map;
import java.util.Map.Entry;

public class BizrcyMgr {
	private static Logger L = LoggerFactory.getLogger(BizrcyMgr.class);
	private static final String _PipeSpliter = "\\|";
	private DbBizrcyLogRec _log;
	private UdrFileHandle _fh;
	private Producer<String, String> _producer;
	private Gson _gson;
	private long _delayTs;
	private String _delayTm;
	private OdbUtils _odbUtils;
	private AppTicker _ticker;

	BizrcyMgr() {
		_odbUtils = new OdbUtils();
		_producer = UdrKafkaProducerFactory.GetKafkaProducuer(OdbSystemParam.GetInstance()._kafkaCluster);
		_gson = new Gson();
		_ticker = new AppTicker();
	}

	public void run() throws Exception {
		_ticker.tickStart();
		_initLog();
		_scanKeys();
	}

	private void _scanKeys() {
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		ScanParams scan_params_ = new ScanParams();
		scan_params_.match("U:*");
		String cursor_ = ScanParams.SCAN_POINTER_START;
		int scan_cnt_ = 0;
		int raw_key_cnt_ = 0;
		Map<String, String> hmap_ = null;
		try {
			while (true) {
				ScanResult<String> scan_result_ = jedis_.scan(cursor_, scan_params_);
				++scan_cnt_;
				for (String k : scan_result_.getResult()) {
					++raw_key_cnt_;
					if (BizrcyCfg._KeyPattern.matcher(k).find()) {
						hmap_ = jedis_.hgetAll(k);
						String tag_ = _procKey(k, hmap_);
						_fh._pw.append(k);
						_fh._pw.append(UdrFmt.ULD_DELIMETER_ERR);
						_fh._pw.append("STATUS");
						_fh._pw.append(UdrFmt.ULD_DELIMETER_ERR);
						_fh._pw.append(tag_);
						_fh._pw.append(UdrFmt.ULD_DELIMETER_EOL);
					}
				}
				cursor_ = scan_result_.getCursor();
				if (cursor_.equals(ScanParams.SCAN_POINTER_START)) {
					L.info("scan end, scan_cnt:raw_key_cnt {}:{}", scan_cnt_, raw_key_cnt_);
					break;
				}
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_214341001_SCAN_BIZLOG_EXCEPTION,
					PubMethod.FmtArgs("scan bizlog exception, dmp_file_nm [%s]", _log.getDmp_file_nm()), null);
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
			_fh.closeSrcFile();
			_fh.operation();
			L.info("{}", _log.toGsonStr());
			if (!BizrcyCfg._SimulationMode) {
				BizrcyOdb odb_ = new BizrcyOdb();
				odb_.addBizrcyLog(_log);
			}
			_ticker.tickEnd(_log.getUdr_tot_cnt().intValue());
			L.info("tot:dur:pfm {}:{}:{}", _ticker._recNum, _ticker._term, _ticker._pfm);
		}
	}

	private String _procKey(String udr_key, Map<String, String> hmap) {
		_log.setUdr_tot_cnt(_log.getUdr_tot_cnt() + 1L);
		try {
			_dmpKey(udr_key, hmap);
			if (_isIgn(udr_key, hmap)) {
				_log.setUdr_ign_cnt(_log.getUdr_ign_cnt() + 1L);
				return "ign";
			}
			UdrFmt udr_ = _getRaw(udr_key, hmap);
			if (udr_ == null) {
				_log.setUdr_ini_cnt(_log.getUdr_ini_cnt() + 1L);
				return "ini";
			}
			if (_isRat(udr_key, hmap, udr_)) {
				_log.setUdr_rat_cnt(_log.getUdr_rat_cnt() + 1L);
				return "rat";
			}
			if (_isDup(udr_key, hmap, udr_)) {
				_log.setUdr_dup_cnt(_log.getUdr_dup_cnt() + 1L);
				return "dup";
			}
			if (_isErr(udr_key, hmap, udr_)) {
				_log.setUdr_err_cnt(_log.getUdr_err_cnt() + 1L);
				return "err";
			}
			if (_isRrt(udr_key, hmap, udr_)) {
				_log.setUdr_rrt_cnt(_log.getUdr_rrt_cnt() + 1L);
				return "rrt";
			}
			_rwk(udr_key, hmap, udr_);
			_log.setUdr_rwk_cnt(_log.getUdr_rwk_cnt() + 1L);
			return "rwk";
		} catch (Exception e) {
			L.warn("handle {} exception", udr_key, e);
			_log.setUdr_bad_cnt(_log.getUdr_bad_cnt() + 1L);
			return "bad";
		}
	}

	private void _rwk(String udr_key, Map<String, String> hmap, UdrFmt udr) {
		UdrFmtMsg msg_ = _resend2mq(udr);
		L.info("{} {} rwk resend, [{}]", udr_key, msg_._rndmId, udr._eFields[UdrFmt.E_13_A14_PROC_FLAG]);
	}

	private boolean _isRrt(String udr_key, Map<String, String> hmap, UdrFmt udr) {
		String[] array_ = null;
		String err_code_ = null;
		String val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_CHKDUP);
		if (PubMethod.IsEmpty(val_)) {
			return false;
		}
		udr.setProcFlag(UdrFmt.PROC_FLAG_CHKDUP);
		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_RATING);
		if (!PubMethod.IsEmpty(val_)) {
			array_ = val_.split(_PipeSpliter, -1);
			err_code_ = array_[2];
			UdrFmtMsg msg_ = _resend2mq(udr); // no need set PROC_FLAG_RATING
			if (!PubMethod.IsEmpty(err_code_) && !UdrFmt.V000_NML_UDR.equals(err_code_)) {
				L.info("{} {} rrt resend, with orig rating err [{}]", udr_key, msg_._rndmId, err_code_);
			} else {
				L.info("{} {} rrt resend, rating [{}]", udr_key, msg_._rndmId, udr._eFields[UdrFmt.E_13_A14_PROC_FLAG]);
			}
			return true;
		}
		return false;
	}

	private boolean _isErr(String udr_key, Map<String, String> hmap, UdrFmt udr) {
		String[] array_ = null;
		String err_code_ = null;
		String val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_VALIDATION);
		if (!PubMethod.IsEmpty(val_)) {
			array_ = val_.split(_PipeSpliter, -1);
			err_code_ = array_[2];
			if (!PubMethod.IsEmpty(err_code_) && !UdrFmt.V000_NML_UDR.equals(err_code_)) {
				udr.setProcFlag(UdrFmt.PROC_FLAG_VALIDATION);
				UdrFmtMsg msg_ = _resend2mq(udr);
				L.info("{} {} err resend, validation [{}]", udr_key, msg_._rndmId, err_code_);
				return true;
			}
		}

		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_CHKDUP);
		if (!PubMethod.IsEmpty(val_)) {
			array_ = val_.split(_PipeSpliter, -1);
			err_code_ = array_[2];
			if (!PubMethod.IsEmpty(err_code_) && !UdrFmt.V000_NML_UDR.equals(err_code_)) {
				UdrFmtMsg msg_ = _resend2mq(udr); // no need set PROC_FLAG_CHKDUP
				L.info("{} {} err resend, chkdup [{}]", udr_key, msg_._rndmId, err_code_);
				return true;
			}
		}

		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_GUIDING);
		if (!PubMethod.IsEmpty(val_)) {
			array_ = val_.split(_PipeSpliter, -1);
			err_code_ = array_[2];
			if (!PubMethod.IsEmpty(err_code_) && !UdrFmt.V000_NML_UDR.equals(err_code_)) {
				udr.setProcFlag(UdrFmt.PROC_FLAG_CHKDUP);
				udr.setProcFlag(UdrFmt.PROC_FLAG_GUIDING);
				UdrFmtMsg msg_ = _resend2mq(udr);
				L.info("{} {} err resend, guiding [{}]", udr_key, msg_._rndmId, err_code_);
				return true;
			}
		}

		return false;
	}

	private boolean _isDup(String udr_key, Map<String, String> hmap, UdrFmt udr) {
		String val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_CHKDUP);
		if (PubMethod.IsEmpty(val_)) {
			return false;
		}
		String[] array_ = val_.split(_PipeSpliter, -1);
		String err_code_ = array_[2];
		if (!UdrFmt.V009_DUP_UDR.equals(err_code_)) {
			return false;
		}
		udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V009_DUP_UDR;
		udr._eFields[UdrFmt.E_13_A14_PROC_FLAG] = UdrFmt.PROC_FLAG_VALIDATION + UdrFmt.PROC_FLAG_CHKDUP;
		udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_39_ACCUMULATION_KEY_40);
		udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = array_[3];
		String[] ufields_ = udr._eFields[UdrFmt.E_24_R01_RAW_UDR].split(UdrFmt.CSV_NONE_ESC_PATTERN, -1);
		udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = ufields_[UdrFmt.U_39_ACCUMULATION_KEY_40];
		UdrFmtMsg msg_ = _resend2mq(udr);
		L.info("{} {} dup resend, [{}]", udr_key, msg_._rndmId, udr._eFields[UdrFmt.E_22_E08_ERR_REASON]);
		return true;
	}

	private boolean _isRat(String udr_key, Map<String, String> hmap, UdrFmt udr) {
		String val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_RATING);
		if (PubMethod.IsEmpty(val_)) {
			return false;
		}
		String[] array_ = val_.split(_PipeSpliter, -1);
		String err_code_ = array_[2];
		if (!PubMethod.IsEmpty(err_code_) && !UdrFmt.V000_NML_UDR.equals(err_code_)) {
			return false;
		}
		if (array_.length < 5) {
			return false;
		}
		String charge_info_ = array_[3];
		String acumlt_info_ = array_[4];
		if (PubMethod.IsEmpty(charge_info_)) {
			return false;
		}
		udr._eFields[UdrFmt.E_27_X02_BIZRCY_CHARGE] = charge_info_;
		udr._eFields[UdrFmt.E_28_X03_BIZRCY_ACUMLT] = acumlt_info_;
		udr._eFields[UdrFmt.E_13_A14_PROC_FLAG] = UdrFmt.PROC_FLAG_VALIDATION + UdrFmt.PROC_FLAG_CHKDUP + UdrFmt.PROC_FLAG_GUIDING
				+ UdrFmt.PROC_FLAG_RATING;
		UdrFmtMsg msg_ = _resend2mq(udr);
		L.info("{} {} rat resend, [{}|{}]", udr_key, msg_._rndmId, charge_info_, acumlt_info_);
		return true;
	}

	private UdrFmt _getRaw(String udr_key, Map<String, String> hmap) {
		String init_ = hmap.get(MdbConst.BIZLOG_HK_UDR_INIT);
		if (PubMethod.IsEmpty(init_)) {
			L.info("{} {} not exists, ini", udr_key, MdbConst.BIZLOG_HK_UDR_INIT);
			return null;
		}
		String raw_ = hmap.get(MdbConst.BIZLOG_HK_UDR_RAW);
		if (PubMethod.IsEmpty(raw_)) {
			L.info("{} {} [{}] has no {}, ini", udr_key, MdbConst.BIZLOG_HK_UDR_INIT, init_, MdbConst.BIZLOG_HK_UDR_RAW);
			return null;
		}
		UdrFmt udr_ = _gson.fromJson(raw_, UdrFmt.class);
		if (!PubMethod.IsEmpty(udr_._eFields[UdrFmt.E_13_A14_PROC_FLAG])) {
			L.debug("{} A14_PROC_FLAG [{}] will be reset", udr_key, udr_._eFields[UdrFmt.E_13_A14_PROC_FLAG]);
			udr_._eFields[UdrFmt.E_13_A14_PROC_FLAG] = null;
		}
		return udr_;
	}

	private boolean _isIgn(String udr_key, Map<String, String> hmap) {
		int hit_cnt_ = 0;
		String[] array_ = null;
		long ts_ = 0;

		String val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_RATING);
		if (!PubMethod.IsEmpty(val_)) {
			++hit_cnt_;
			array_ = val_.split(_PipeSpliter, -1);
			ts_ = PubMethod.Str2Long(array_[0], PubMethod.TimeStrFmt.Fmt23);
			if (ts_ > _delayTs) {
				L.debug("{} {} {} gt {}, ign", udr_key, MdbConst.BIZLOG_HK_UDR_RATING, array_[0], _delayTm);
				return true;
			}
		}

		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_GUIDING);
		if (!PubMethod.IsEmpty(val_)) {
			++hit_cnt_;
			array_ = val_.split(_PipeSpliter, -1);
			ts_ = PubMethod.Str2Long(array_[0], PubMethod.TimeStrFmt.Fmt23);
			if (ts_ > _delayTs) {
				L.debug("{} {} {} gt {}, ign", udr_key, MdbConst.BIZLOG_HK_UDR_GUIDING, array_[0], _delayTm);
				return true;
			}
		}

		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_CHKDUP);
		if (!PubMethod.IsEmpty(val_)) {
			++hit_cnt_;
			array_ = val_.split(_PipeSpliter, -1);
			ts_ = PubMethod.Str2Long(array_[0], PubMethod.TimeStrFmt.Fmt23);
			if (ts_ > _delayTs) {
				L.debug("{} {} {} gt {}, ign", udr_key, MdbConst.BIZLOG_HK_UDR_CHKDUP, array_[0], _delayTm);
				return true;
			}
		}

		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_VALIDATION);
		if (!PubMethod.IsEmpty(val_)) {
			++hit_cnt_;
			array_ = val_.split(_PipeSpliter, -1);
			ts_ = PubMethod.Str2Long(array_[0], PubMethod.TimeStrFmt.Fmt23);
			if (ts_ > _delayTs) {
				L.debug("{} {} {} gt {}, ign", udr_key, MdbConst.BIZLOG_HK_UDR_VALIDATION, array_[0], _delayTm);
				return true;
			}
		}

		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_SPOUT);
		if (!PubMethod.IsEmpty(val_)) {
			++hit_cnt_;
			array_ = val_.split(_PipeSpliter, -1);
			ts_ = PubMethod.Str2Long(array_[0], PubMethod.TimeStrFmt.Fmt23);
			if (ts_ > _delayTs) {
				L.debug("{} {} {} gt {}, ign", udr_key, MdbConst.BIZLOG_HK_UDR_SPOUT, array_[0], _delayTm);
				return true;
			}
		}

		val_ = hmap.get(MdbConst.BIZLOG_HK_UDR_INIT);
		if (!PubMethod.IsEmpty(val_)) {
			++hit_cnt_;
			array_ = val_.split(_PipeSpliter, -1);
			ts_ = PubMethod.Str2Long(array_[0], PubMethod.TimeStrFmt.Fmt23);
			if (ts_ > _delayTs) {
				L.debug("{} {} {} gt {}, ign", udr_key, MdbConst.BIZLOG_HK_UDR_INIT, array_[0], _delayTm);
				return true;
			}
		}

		if (hit_cnt_ == 0) {
			L.warn("{} no valid fields found, ign", udr_key);
			return true;
		}

		return false;
	}

	private void _dmpKey(String udr_key, Map<String, String> hmap) {
		for (Entry<String, String> ent_ : hmap.entrySet()) {
			_fh._pw.append(udr_key);
			_fh._pw.append(UdrFmt.ULD_DELIMETER_ERR);
			_fh._pw.append(ent_.getKey());
			_fh._pw.append(UdrFmt.ULD_DELIMETER_ERR);
			_fh._pw.append(ent_.getValue());
			_fh._pw.append(UdrFmt.ULD_DELIMETER_EOL);
		}
	}

	UdrFmtMsg _resend2mq(UdrFmt udr) {
		UdrFmtMsg msg_ = new UdrFmtMsg();
		msg_._udrFmtList.add(udr);
		msg_.renewRndmId();
//		KeyedMessage<String, String> topic_msg_ = new KeyedMessage<String, String>(BizrcyCfg._KafkaTopicRaw, msg_._rndmId, msg_.toGsonStr());
		ProducerRecord<String, String> producerRecord = new ProducerRecord<>(BizrcyCfg._KafkaTopicRaw, msg_._rndmId, msg_.toGsonStr());
		if (!BizrcyCfg._SimulationMode) {
//			_producer.send(topic_msg_);
			_producer.send(producerRecord);
		}
		return msg_;
	}

	private void _initLog() throws Exception {
		_log = new DbBizrcyLogRec();
		_log.setRcy_tm(new Timestamp(_ticker._tsStart));
		String tm_str_ = PubMethod.Timestamp2Str(_log.getRcy_tm(), PubMethod.TimeStrFmt.Fmt14);
		_log.setLog_mm(Integer.parseInt(tm_str_.substring(4, 6)));
		_log.setDmp_file_nm(tm_str_ + ".dmp");
		_log.setUdr_tot_cnt(0L);
		_log.setUdr_ign_cnt(0L);
		_log.setUdr_ini_cnt(0L);
		_log.setUdr_rat_cnt(0L);
		_log.setUdr_dup_cnt(0L);
		_log.setUdr_err_cnt(0L);
		_log.setUdr_rrt_cnt(0L);
		_log.setUdr_rwk_cnt(0L);
		_log.setUdr_bad_cnt(0L);
		_log.setKey_pattern(BizrcyCfg._KeyPattern.pattern());

		_delayTs = _ticker._tsStart;
		_delayTs -= BizrcyCfg._DelaySeconds * 1000L;
		_delayTm = PubMethod.Long2Str(_delayTs, PubMethod.TimeStrFmt.Fmt23);

		File f = new File(BizrcyCfg._WorkingDir + "/" + _log.getDmp_file_nm());
		_fh = new UdrFileHandle(f, UdrFileHandle.OP_MODE_MOVE, true);
		_fh._dstDir = BizrcyCfg._DmpBakDir;
		_fh.openSrcFile("utf-8", false);
	}
}
