package com.hp.cmcc.bboss.app.locdis;

import java.io.File;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class LocdisUlnkRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(LocdisUlnkRunnable.class);
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public LocdisUlnkRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
				_interval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 3 seconds", e);
				PubMethod.Sleep(3000);
			}
		}
		L.info("thread end");
	}

	private void _exec() {
		Map<String, List<DbBinmanCfgRec>> ulnk_cfg_map_ = LocdisCfg._UlnkCfgMap;
		for (String k : ulnk_cfg_map_.keySet()) {
			File src_dir_ = new File(k);
			if (!src_dir_.isDirectory()) {
				L.warn("[{}] dir not exists anymore", k);
				continue;
			}
			List<DbBinmanCfgRec> cfg_list_ = ulnk_cfg_map_.get(k);
			_execSrcDir(src_dir_, cfg_list_);
		}
	}

	private void _execSrcDir(File src_dir, List<DbBinmanCfgRec> cfg_list) {
		List<File> file_list_ = new LinkedList<File>();
		for (DbBinmanCfgRec cfg_ : cfg_list) {
			_scanSrcDir(src_dir, cfg_, file_list_);
			_execOneCfg(cfg_, file_list_);
		}
	}

	private void _scanSrcDir(File src_dir, DbBinmanCfgRec cfg, List<File> result) {
		result.clear();
		File[] files_ = src_dir.listFiles();
		if (files_ == null || files_.length == 0)
			return;
		Arrays.sort(files_);
		if (cfg._patternSub == null) {
			for (File f : files_) {
				if (!f.isFile())
					continue;
				result.add(f);
			}
		} else {
			List<File> sub_list_ = new LinkedList<File>();
			for (File f : files_) {
				if (!f.isDirectory())
					continue;
				if (!cfg._patternSub.matcher(f.getName()).find())
					continue;
				sub_list_.add(f);
			}
			for (File sub_ : sub_list_) {
				files_ = sub_.listFiles();
				if (files_ == null)
					continue;
				if (files_.length == 0) { // empty directory
					long sub_millis_ = sub_.lastModified();
					long now_ = System.currentTimeMillis();
					long diff_ = now_ - sub_millis_;
					if (diff_ > cfg.getDelay_dur() * 1000L) {
						String sub_tm_ = PubMethod.Long2Str(sub_millis_, PubMethod.TimeStrFmt.Fmt14);
						if (sub_.delete()) {
							L.info("{}, [{}/] unlinked, {} seconds ago, tm {}", cfg.getCfg_id(), sub_.getAbsolutePath(),
									diff_ / 1000L, sub_tm_);
						} else {
							DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341031_ULNK_DIR_FAIL, PubMethod.FmtArgs(
									"[%s/] unlink error, %d seconds ago, tm %s", sub_.getAbsolutePath(), diff_ / 1000L, sub_tm_),
									cfg.toGsonStr());
							L.warn("{}, {}", cfg.getCfg_id(), alm_.getAlm_msg());
							_odbUtils.addRawAlm(null, alm_);
						}
					}
				} else {
					Arrays.sort(files_);
					for (File f : files_) {
						if (!f.isFile())
							continue;
						result.add(f);
					}
				}
			}
		}
	}

	private void _execOneCfg(DbBinmanCfgRec cfg, List<File> file_list) {
		//List<File> reserved_list_ = new LinkedList<File>();
		while (!file_list.isEmpty()) {
			File f = file_list.remove(0);
			if (cfg._patternIgn != null) {
				if (cfg._patternIgn.matcher(f.getName()).find()) {
					//reserved_list_.add(f);
					//L.trace("{}, {} match PATTERN_IGN [{}]", cfg.getCfg_id(), f.getName(), cfg.getPattern_ign());
					continue;
				}
			}
			if (!cfg._patternMat.matcher(f.getName()).find()) {
				//reserved_list_.add(f);
				//L.trace("{}, {} not match PATTERN_MAT [{}]", cfg.getCfg_id(), f.getName(), cfg.getPattern_mat());
				continue;
			}
			long now_ = System.currentTimeMillis();
			long modify_millis_ = f.lastModified();
			long diff_ = now_ - modify_millis_;
			if (diff_ < cfg.getDelay_dur() * 1000L) {
				//reserved_list_.add(f);
				//L.trace("{}, {} diff {} lt delay {} * 1000", cfg.getCfg_id(), f.getName(), diff_, cfg.getDelay_dur());
				continue;
			}
			String modify_tm_ = PubMethod.Long2Str(modify_millis_, PubMethod.TimeStrFmt.Fmt14);
			long sz_ = f.length();
			if (f.delete()) {
				L.info("{}, [{}] unlinked, {} seconds ago, tm:sz {}:{}", new Object[] { cfg.getCfg_id(), f.getAbsolutePath(),
						diff_ / 1000L, modify_tm_, sz_ });
			} else {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341032_ULNK_FAIL, PubMethod.FmtArgs(
						"[%s] unlink error, %d seconds ago, tm:sz %s:%d", f.getAbsolutePath(), diff_ / 1000L, modify_tm_, sz_),
						cfg.toGsonStr());
				L.warn("{}, {}", cfg.getCfg_id(), alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
			}
		}
		//for (File f : reserved_list_)
		//	file_list.add(f);
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		for (int i = 0; i < LocdisCfg._IntervalUlnk; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}
}
