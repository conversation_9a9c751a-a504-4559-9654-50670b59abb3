package com.hp.cmcc.bboss.app.erorcy;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbBizTypeDefRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.udr.UdrFileHandle;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ErorcyMgr {
	private static final String _Q_ERR_STTL_UDR = "SELECT A01_RAW_FILE_NM, A02_RAW_LINE_NUM, A03_ERR_CODE, A04_ORG_FILE_ID, "
			+ "A05_FILE_ID, A06_LINE_NUM, A07_RCV_MM, A08_RCV_YMDH, A09_RCV_TM, A10_FILE_YMD, A11_FILE_PROV, A12_ERCY_TIMES, "
			+ "A14_PROC_FLAG, A15_ACUMLT, R01_RAW_UDR FROM %s WHERE SUBSTR(E06_ACCT_DAY, 1, 6) = '%06d' AND ";
	private static final String _Delimiter = "#|#";
	private static Logger L = LoggerFactory.getLogger(ErorcyMgr.class);
	private StringBuilder _sb;
	private String _bizTypes;
	private List<DbErorcyTaskRec> _taskList;
	private ErorcyBizCfg _bizCfg;
	private DbErorcyLogRec _log;
	private ErorcyOdb _odb;
	private OdbUtils _odbUtils;
	private UdrFileHandle _hndl;
	private Connection _bizConn;
	private Connection _udrConn;

	ErorcyMgr() {
		_sb = new StringBuilder();
		int i = 0;
		for (Entry<String, ErorcyBizCfg> entry_ : ErorcyCfg._BizCfgMap.entrySet()) {
			if (++i > 1)
				_sb.append(',');
			_sb.append("'");
			_sb.append(entry_.getValue()._bizType);
			_sb.append("'");
		}
		_bizTypes = _sb.substring(0);
		L.debug("_bizTypes=[{}]", _bizTypes);
		_odb = new ErorcyOdb();
		_odbUtils = new OdbUtils();
	}

	public void run() throws Exception {
		_taskList = _odb.getErorcyTask(AppCmdline.GetInstance()._instNm, _bizTypes);
		if (_taskList.isEmpty()) {
			L.trace("no task scaned for _bizTypes [{}]", _bizTypes);
			return;
		}
		OdbCli cli_biz_ = OdbAgt.GetBizInstance();
		OdbCli cli_udr_ = OdbAgt.GetUdrInstance();
		for (DbErorcyTaskRec task_ : _taskList) {
			try {
				_initLog(task_);
				_bizConn = cli_biz_.getConnection();
				L.info("{}, transaction {} begin", _log.getRcy_file_nm(), _bizConn.toString());
				_matchBizCfg();
				_dumpFile(task_);
				_postProc(task_);
			} catch (Exception e) {
				task_.setRcy_status(DbErorcyTaskRec.STATUS_3_FAILED);
				_log.setRcy_status(task_.getRcy_status());
				_log.setRcy_desc(e.getMessage());
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_208341001_ERORCY_EXCEPTION, PubMethod.FmtArgs("recycle exception"),
						task_.toGsonStr());
				alm_.setAlm_kpi(e.toString());
				L.warn("{}", alm_.getAlm_msg(), e);
				_odbUtils.addRawAlm(null, alm_);
				if (_udrConn != null) {
					cli_udr_.rollback(_udrConn);
					L.warn("{}, transaction {} rollback done", _log.getRcy_file_nm(), _udrConn.toString());
				}
				if (_bizConn != null) {
					cli_biz_.rollback(_bizConn);
					L.warn("{}, transaction {} rollback done", _log.getRcy_file_nm(), _bizConn.toString());
				}
				_odb.bakErorcyTask(null, task_);
				_odb.addErorcyLog(null, _log);
			} finally {
				cli_udr_.close(_udrConn);
				_udrConn = null;
				cli_biz_.close(_bizConn);
				_bizConn = null;
			}
		}
	}

	private void _initLog(DbErorcyTaskRec task) {
		DbBizTypeDefRec biz_type_def_rec_ = ErorcyCfg._BizTypeMap.get(task.getBiz_type());
		task.setFile_type(biz_type_def_rec_.getType_id());
		_log = new DbErorcyLogRec();
		_log.setTs_start(System.currentTimeMillis());
		_log.setRcy_tm(new Timestamp(_log.getTs_start()));
		L.debug("{}", task.toGsonStr());
		String rcy_tm_str_ = PubMethod.Long2Str(_log.getTs_start(), PubMethod.TimeStrFmt.Fmt14);
		_log.setLog_mm(Integer.parseInt(rcy_tm_str_.substring(4, 6)));
		_log.setRcy_status(DbErorcyTaskRec.STATUS_0_INIT);
		_log.setRcy_cnt(0);
		_log.setRcy_file_sz(0L);
		_log.setBiz_type(task.getBiz_type());
		_log.setTask_tm(task.getTask_tm());
		_log.setFile_type(task.getFile_type());
		_log.setRcy_table(task.getRcy_table());
		_log.setAcnt_ym(task.getAcnt_ym());
		_log.setRcy_condition(task.getRcy_condition());
		int rcy_ymd_ = Integer.parseInt(rcy_tm_str_.substring(0, 8));
		int seq_ = _odbUtils.nextSeq(rcy_ymd_, String.format("RCY_%02d", _log.getFile_type()));
		String rcy_file_nm_ = String.format("RCY_%08d_%03d_%05d_%06d", rcy_ymd_, _log.getFile_type(), seq_, task.getAcnt_ym());
		_log.setRcy_file_nm(rcy_file_nm_);
		L.debug("{}", _log.toGsonStr());
		task.setRcy_desc(_log.getRcy_file_nm());
	}

	private void _matchBizCfg() {
		_bizCfg = null;
		for (ErorcyBizCfg biz_cfg_ : ErorcyCfg._BizCfgMap.values()) {
			if (biz_cfg_._bizType.equals(_log.getBiz_type())) {
				_bizCfg = biz_cfg_;
				L.trace("biz_cfg matched: {}", _bizCfg.toGsonStr());
				_hndl = new UdrFileHandle(new File(ErorcyCfg._WrkDir + "/" + _log.getRcy_file_nm()), UdrFileHandle.OP_MODE_MOVE,
						true);
				_hndl._dstDir = _bizCfg._dstDir;
			}
		}
	}

	private void _dumpFile(DbErorcyTaskRec task) throws Exception {
		OdbCli cli_udr_ = OdbAgt.GetUdrInstance();
		_udrConn = cli_udr_.getConnection();
		L.info("{}, transaction {} begin", _log.getRcy_file_nm(), _udrConn.toString());
		Statement stmt_ = null;
		ResultSet rs_ = null;
		try {
			stmt_ = _udrConn.createStatement();
			stmt_.setFetchSize(1000);
			String sql_ = _genQuery();
			rs_ = stmt_.executeQuery(sql_);
			L.debug("SQL [{}] executed", sql_);
			while (rs_.next()) {
				_genLine(rs_);
				_dmpLine();
			}
		} finally {
			cli_udr_.close(rs_, stmt_, null);
			_hndl.closeSrcFile();
		}

		if (_log.getRcy_cnt() == 0) {
			L.info("no records dumped");
			task.setRcy_status(DbErorcyTaskRec.STATUS_2_OK_NO_FILE);
		} else {
			task.setRcy_status(DbErorcyTaskRec.STATUS_1_OK_WITH_FILE);
			_log.setRcy_file_sz(_hndl._srcFile.length());
			L.debug("{} records, {} bytes dumped into {}", _log.getRcy_cnt(), _log.getRcy_file_sz(), _log.getRcy_file_nm());
		}
		_log.setRcy_status(task.getRcy_status());
	}

	private void _postProc(DbErorcyTaskRec task) throws Exception {
		OdbCli cli_biz_ = OdbAgt.GetBizInstance();
		OdbCli cli_udr_ = OdbAgt.GetUdrInstance();
		try {
			int deleted_rows_ = _odb.delErrSttlUdr(_udrConn, _log.getRcy_table(), _log.getAcnt_ym(), _log.getRcy_condition());
			if (deleted_rows_ != _log.getRcy_cnt()) {
				String alm_ = PubMethod.FmtArgs("%s, deleted %d ne dumped %d", _log.getRcy_file_nm(), deleted_rows_,
						_log.getRcy_cnt());
				L.warn("{}", alm_);
				throw new RuntimeException(alm_);
			}
			_odb.bakErorcyTask(_bizConn, task);
			_log.setTs_end(System.currentTimeMillis());
			_odb.addErorcyLog(_bizConn, _log);
			cli_udr_.commit(_udrConn);
			L.info("{}, transaction {} commit done", _log.getRcy_file_nm(), _udrConn.toString());
			cli_biz_.commit(_bizConn);
			L.info("{}, transaction {} commit done", _log.getRcy_file_nm(), _bizConn.toString());
		} catch (Exception e) {
			_hndl._dstDir = ErorcyCfg._FailDir;
			throw e;
		} finally {
			_hndl.operation();
		}

		/*
		 * if (exception_flag_) { try { conn_ = agt_.getConnection(); L.info("{}, transaction {} begin", _log.getRcy_file_nm(),
		 * conn_.toString()); _odb.bakErorcyTask(conn_, task); _odb.addErorcyLog(conn_, _log); agt_.commit(conn_); L.info(
		 * "{}, transaction {} commit done", _log.getRcy_file_nm(), conn_.toString()); } catch (Exception e) { L.warn(
		 * "{}, exception", _log.getRcy_file_nm(), e); if (conn_ != null) { agt_.rollback(conn_); L.warn(
		 * "{}, transaction {} rollback done", _log.getRcy_file_nm(), conn_.toString()); } } finally { agt_.close(conn_); } }
		 */
	}

	private String _genQuery() {
		_sb.delete(0, _sb.length());
		_sb.append(String.format(_Q_ERR_STTL_UDR, _log.getRcy_table(), _log.getAcnt_ym()));
		_sb.append(_log.getRcy_condition());
		return _sb.substring(0);
	}

	private void _genLine(ResultSet rs) throws SQLException {
		_sb.delete(0, _sb.length());
		_sb.append(rs.getString(1)); // A01_RAW_FILE_NM
		_sb.append(_Delimiter);
		_sb.append(rs.getInt(2)); // A02_RAW_LINE_NUM
		_sb.append(_Delimiter);
		_sb.append(rs.getString(3)); // A03_ERR_CODE
		_sb.append(_Delimiter);
		_sb.append(rs.getLong(4)); // A04_ORG_FILE_ID
		_sb.append(_Delimiter);
		_sb.append(rs.getLong(5)); // A05_FILE_ID
		_sb.append(_Delimiter);
		_sb.append(rs.getInt(6)); // A06_LINE_NUM
		_sb.append(_Delimiter);
		_sb.append(rs.getInt(7)); // A07_RCV_MM
		_sb.append(_Delimiter);
		_sb.append(rs.getLong(8)); // A08_RCV_YMDH
		_sb.append(_Delimiter);
		Timestamp ts_ = rs.getTimestamp(9); // A09_RCV_TM
		_sb.append(PubMethod.Timestamp2Str(ts_, PubMethod.TimeStrFmt.Fmt14));
		_sb.append(_Delimiter);
		_sb.append(rs.getInt(10)); // A10_FILE_YMD
		_sb.append(_Delimiter);
		_sb.append(rs.getInt(11)); // A11_FILE_PROV
		_sb.append(_Delimiter);
		_sb.append(rs.getInt(12)); // A12_ERCY_TIMES
		_sb.append(_Delimiter);
		_sb.append(PubMethod.Long2Str(_log.getTs_start(), PubMethod.TimeStrFmt.Fmt14)); // A13_ERCY_TIME
		_sb.append(_Delimiter);
		_sb.append(rs.getString(13)); // A14_PROC_FLAG
		_sb.append(_Delimiter);
		_sb.append(rs.getLong(14)); // A15_ACUMLT
		_sb.append(_Delimiter);
		_sb.append(rs.getString(15)); // R01_RAW_UDR
	}

	private void _dmpLine() throws Exception {
		if (_hndl._pw == null) {
			_hndl.openSrcFile(_bizCfg._charset, true);
		}
		_hndl._pw.println(_sb.substring(0));
		_log.setRcy_cnt(_log.getRcy_cnt() + 1);
	}
}
