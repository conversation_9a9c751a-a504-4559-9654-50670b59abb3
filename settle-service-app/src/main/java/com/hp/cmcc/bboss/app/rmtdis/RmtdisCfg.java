package com.hp.cmcc.bboss.app.rmtdis;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class RmtdisCfg {
	private static Logger L = LoggerFactory.getLogger(RmtdisCfg.class);
	public static Map<String, DbRmtdisLoginCfgRec> _LoginCfgMap;
	public static Map<String, List<DbRmtdisTransCfgRec>> _TransCfgMap;

	public static boolean Init() {
		if (!_RefreshTransCfg()) {
			L.warn("call _RefreshTransCfg error");
			return false;
		}
		return true;
	}

	public static void TraceCfgMaps() {
		int i = 0;
		L.debug("{} entries in _LoginCfgMap", _LoginCfgMap.size());
		for (Entry<String, DbRmtdisLoginCfgRec> ent_ : _LoginCfgMap.entrySet()) {
			L.debug("No. {}, key {}, val {}", ++i, ent_.getKey(), ent_.getValue().toGsonStr());
		}
		i = 0;
		L.debug("{} entries in _TransCfgMap", _TransCfgMap.size());
		for (Entry<String, List<DbRmtdisTransCfgRec>> ent_ : _TransCfgMap.entrySet()) {
			L.debug("No. {}, key {}, {} elem in list", ++i, ent_.getKey(), ent_.getValue().size());
			for (int j = 0; j < ent_.getValue().size(); j++) {
				L.debug(" no. {}, {}", j + 1, ent_.getValue().get(j).toGsonStr());
			}
		}
	}

	private static boolean _RefreshTransCfg() {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		RmtdisOdb odb_ = new RmtdisOdb();
		List<DbRmtdisLoginCfgRec> db_result_login_ = odb_.getLoginCfg(cmdline_._instNm);
		if (db_result_login_ == null) {
			L.warn("query RMTDIS_LOGIN_CFG returns null");
			return false;
		}
		if (db_result_login_.isEmpty()) {
			L.warn("query RMTDIS_LOGIN_CFG no result, pls chk cfg");
			return false;
		}
		L.debug("query RMTDIS_LOGIN_CFG {} records fetched", db_result_login_.size());

		List<DbRmtdisTransCfgRec> db_result_trans_ = odb_.getTransCfg(cmdline_._instNm);
		if (db_result_trans_ == null) {
			L.warn("query RMTDIS_TRANS_CFG returns null");
			return false;
		}
		if (db_result_trans_.isEmpty()) {
			L.warn("query RMTDIS_TRANS_CFG no result, pls chk cfg");
			return false;
		}
		L.debug("query RMTDIS_TRANS_CFG {} records fetched", db_result_trans_.size());

		Map<String, DbRmtdisLoginCfgRec> login_cfg_map_ = new TreeMap<String, DbRmtdisLoginCfgRec>();
		Map<String, List<DbRmtdisTransCfgRec>> trans_cfg_map_ = new TreeMap<String, List<DbRmtdisTransCfgRec>>();
		boolean rc_ = true;
		for (DbRmtdisLoginCfgRec rec_ : db_result_login_) {
			if (!rec_.validateRec()) {
				L.warn("RMTDIS_LOGIN_CFG {}, validateRec error", rec_.getLogin_cfg_nm());
				rc_ = false;
				continue;
			}
			login_cfg_map_.put(rec_.getLogin_cfg_nm(), rec_);
		}
		for (DbRmtdisTransCfgRec rec_ : db_result_trans_) {
			if (!rec_.validateRec()) {
				L.warn("RMTDIS_TRANS_CFG {}, validateRec error", rec_.getTrans_cfg_nm());
				rc_ = false;
				continue;
			}
			String key_ = rec_.getThread_nm();
			List<DbRmtdisTransCfgRec> val_ = trans_cfg_map_.get(key_);
			if (val_ == null) {
				val_ = new ArrayList<DbRmtdisTransCfgRec>(2);
				trans_cfg_map_.put(key_, val_);
			}
			val_.add(rec_);
		}
		if (rc_) {
			_LoginCfgMap = login_cfg_map_;
			_TransCfgMap = trans_cfg_map_;
		}
		return rc_;
	}
}
