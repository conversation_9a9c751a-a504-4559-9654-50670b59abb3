package com.hp.cmcc.bboss.app.dblodr;

import java.util.List;
import java.util.regex.Pattern;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DblodrCtlSec extends GsonObj {
	public String _secNm;
	public String _srcDir;
	public String _bakDir;
	public String _badDir;
	public Pattern _pattern;
	public String _ctlFile;
	public int _bakByYmd; // 0:NoArchive; 1:ArchiveByDate and gzip
	public String _script;
	public List<String> _args; // args of _script
	public String _threadNm;
}
