package com.hp.cmcc.bboss.app.dblodr;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbDblodrLogRec extends GsonObj {
	public static final int _FLAG_0_SUCCESS = 0;
	public static final int _FLAG_1_BAD = 1;
	public static final int _FLAG_2_FAIL = 2;
	public static final int _FLAG_3_LOG_PARSE_ERR = 3;

	private Integer log_mm;
	private String file_nm;
	private Integer proc_flag;
	private Integer tot_cnt;
	private Integer db_cnt;
	private Integer bad_cnt;
	private Integer dscd_cnt;
	private String tbl_nm;
	private Long file_sz;
	private Long ts_start;
	private Long ts_end;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[11];
		o[0] = log_mm;
		o[1] = file_nm;
		o[2] = proc_flag;
		o[3] = tot_cnt;
		o[4] = db_cnt;
		o[5] = bad_cnt;
		o[6] = dscd_cnt;
		o[7] = tbl_nm;
		o[8] = file_sz;
		o[9] = ts_start;
		o[10] = ts_end;
		return o;
	}

	public Integer getLog_mm() {
		return log_mm;
	}

	public void setLog_mm(Integer log_mm) {
		this.log_mm = log_mm;
	}

	public String getFile_nm() {
		return file_nm;
	}

	public void setFile_nm(String file_nm) {
		this.file_nm = file_nm;
	}

	public Integer getProc_flag() {
		return proc_flag;
	}

	public void setProc_flag(Integer proc_flag) {
		this.proc_flag = proc_flag;
	}

	public Integer getTot_cnt() {
		return tot_cnt;
	}

	public void setTot_cnt(Integer tot_cnt) {
		this.tot_cnt = tot_cnt;
	}

	public Integer getDb_cnt() {
		return db_cnt;
	}

	public void setDb_cnt(Integer db_cnt) {
		this.db_cnt = db_cnt;
	}

	public Integer getBad_cnt() {
		return bad_cnt;
	}

	public void setBad_cnt(Integer bad_cnt) {
		this.bad_cnt = bad_cnt;
	}

	public Integer getDscd_cnt() {
		return dscd_cnt;
	}

	public void setDscd_cnt(Integer dscd_cnt) {
		this.dscd_cnt = dscd_cnt;
	}

	public String getTbl_nm() {
		return tbl_nm;
	}

	public void setTbl_nm(String tbl_nm) {
		this.tbl_nm = tbl_nm;
	}

	public Long getFile_sz() {
		return file_sz;
	}

	public void setFile_sz(Long file_sz) {
		this.file_sz = file_sz;
	}

	public Long getTs_start() {
		return ts_start;
	}

	public void setTs_start(Long ts_start) {
		this.ts_start = ts_start;
	}

	public Long getTs_end() {
		return ts_end;
	}

	public void setTs_end(Long ts_end) {
		this.ts_end = ts_end;
	}
}
