package com.hp.cmcc.bboss.app.erruld;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.sql.Connection;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.Map.Entry;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import com.hp.cmcc.bboss.pub.edb.EdbDupchkPool;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.udr.UdrFileHandle;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PatternFileFilter;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ErruldBizRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(ErruldBizRunnable.class);
	private static Pattern _ErrFnmPattern = Pattern.compile("^E_[0-9]{3}_[0-9]{6}_.+\\.bcp$");
	private BlockingQueue<UdrFmt> _blockingQueue;
	private String _prevHHMM;
	private String _currHHMM;
	private long _prevTs;
	private long _currTs;
	private Map<String, UdrFileHandle> _hndlMap; // E_ttt_yyyymm_<raw>.rand.bcp
	private Map<String, DbErruldLogRec> _logMap;
	private List<String> _tmpKeys; // prevent java.util.ConcurrentModificationException
	private OdbUtils _odbUtils;
	private EdbDupchkPool _edbDupchkPool;
	private boolean _terminateFlag = false;

	public ErruldBizRunnable(BlockingQueue<UdrFmt> blocking_queue) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_blockingQueue = blocking_queue;
		_prevTs = System.currentTimeMillis();
		_currTs = _prevTs;
		_prevHHMM = PubMethod.Long2Str(_prevTs, PubMethod.TimeStrFmt.Fmt14).substring(8, 12);
		_currHHMM = _prevHHMM;
		_hndlMap = new TreeMap<String, UdrFileHandle>();
		_logMap = new TreeMap<String, DbErruldLogRec>();
		_tmpKeys = new ArrayList<String>();
		_odbUtils = new OdbUtils();
		_edbDupchkPool = new EdbDupchkPool(ErruldCfg._UldCacheDir, "DUPCHKE", 2, ErruldCfg._UldDupchkHashYmd,
				ErruldCfg._UldDupchkHashTbl);
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	public void recover() {
		File cache_dir_ = new File(ErruldCfg._UldCacheDir);
		PatternFileFilter filter_ = new PatternFileFilter(_ErrFnmPattern, PatternFileFilter.TypeFilter.TypeFile);
		File[] cache_files_ = cache_dir_.listFiles(filter_);
		if (cache_files_ == null || cache_files_.length == 0) {
			L.info("no cached file scaned in {}, no need recovery", ErruldCfg._UldCacheDir);
			return;
		}
		Arrays.sort(cache_files_);
		L.info("{} cached file(s) scaned in {}", cache_files_.length, ErruldCfg._UldCacheDir);
		for (File f : cache_files_) {
			_recover(f);
		}
		_traceMaps();
	}

	@Override
	public void run() {
		L.info("thread started");
		UdrFmt udr_ = null;
		while (!_terminateFlag) {
			try {
				_chkCutoffDur();
				udr_ = _blockingQueue.poll(500, TimeUnit.MILLISECONDS);
				if (udr_ == null)
					continue;
				_procUdr(udr_);
			} catch (Exception e) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_212341001_UKN_EXCEPTION, "erruld exception", null);
				alm_.setAlm_kpi(e.toString());
				if (udr_ != null)
					alm_.setAlm_json(udr_.toGsonStr());
				L.warn("{}, sleep 500 millis", alm_.getAlm_msg(), e);
				_odbUtils.addRawAlm(null, alm_);
				PubMethod.Sleep(500);
			}
		}
		for (Entry<String, UdrFileHandle> entry_ : _hndlMap.entrySet()) {
			entry_.getValue().closeSrcFile();
		}
		_edbDupchkPool.forceCommit();
		L.info("thread end");
	}

	private void _recover(File f) {
		if (f.length() == 0) {
			L.warn("{} is an empty file, skip recovery", f.getAbsolutePath());
			return;
		}
		ErruldOdb odb_ = new ErruldOdb();
		DbErruldLogRec uld_log_ = odb_.getErrUldLog(f.getName());
		if (uld_log_ == null) {
			L.warn("{} not found in ERRULD_LOG, can not recovery", f.getName());
			return;
		}
		if (uld_log_.getUld_status() != 0) {
			L.warn("{} ULD_STATUS {} ne 0, skip recovery", f.getName(), uld_log_.getUld_status());
			return;
		}
		uld_log_.setUld_cnt(0);
		uld_log_.setErcy_cnt(0);
		uld_log_.setSpare_num1(0L);
		uld_log_.setSpare_num2(0L);
		String key_ = String.format("L:%d:%d", uld_log_.getRcv_ymdh(), uld_log_.getFile_id());
		_recoverGuts(key_, uld_log_, f);
	}

	private void _recoverGuts(String key, DbErruldLogRec uld_log, File uld_file) {
		UdrFileHandle hndl_ = new UdrFileHandle(uld_file, UdrFileHandle.OP_MODE_MOVE, false);
		hndl_._dstDir = ErruldCfg._UldOutputDir;
		BufferedReader br_ = null;
		try {
			hndl_.openSrcFile(ErruldCfg._UldCharset, true);
			br_ = new BufferedReader(new FileReader(uld_file));
			L.trace("{} opened for recovery, key_={}", uld_file.getAbsolutePath(), key);
			String line_;
			while ((line_ = br_.readLine()) != null) {
				String[] fields_ = line_.split("#\\|#", -1);
				uld_log.setUld_cnt(uld_log.getUld_cnt() + 1);
				if (!fields_[UdrFmt.E_03_A04_ORG_FILE_ID].equals(fields_[UdrFmt.E_04_A05_FILE_ID])) {
					uld_log.setErcy_cnt(uld_log.getErcy_cnt() + 1);
				}
				if (!PubMethod.IsEmpty(fields_[UdrFmt.E_32_X07_FEEDBACK_TAG])) {
					uld_log.setSpare_num1(uld_log.getSpare_num1() + 1);
				}
			}
		} catch (Exception e) {
			L.warn("exception, recovery {} abort", uld_file.getAbsolutePath(), e);
			return;
		} finally {
			PubMethod.Close(br_);
		}
		_logMap.put(key, uld_log);
		_hndlMap.put(key, hndl_);
	}

	private void _chkCutoffDur() {
		_currTs = System.currentTimeMillis();
		if (_currTs - _prevTs < 500)
			return;
		_prevTs = _currTs;
		_currHHMM = PubMethod.Long2Str(_currTs, PubMethod.TimeStrFmt.Fmt14).substring(8, 12);
		if (_currHHMM.equals(_prevHHMM))
			return;
		L.trace("{}:{} minute change detected", _prevHHMM, _currHHMM);
		_prevHHMM = _currHHMM;

		_edbDupchkPool.forceCommit();
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		_tmpKeys.clear();
		for (String key_ : _logMap.keySet())
			_tmpKeys.add(key_);
		for (String key_ : _tmpKeys) {
			long now_ = System.currentTimeMillis();
			DbErruldLogRec uld_log_ = _logMap.get(key_);
			long diff_ = now_ - uld_log_.getTs_end();
			if (diff_ < 60 * 1000) {
				L.trace("{} uld just {} millis ago, need more wait", key_, diff_);
				continue;
			}
			try {
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
				Map<String, String> biz_map_ = jedis_.hgetAll(key_);
				if (biz_map_ == null) {
					L.warn("bizlog [{}] not exists, pls check", key_);
					continue;
				}
				String str_tot_cnt_ = biz_map_.get(MdbConst.BIZLOG_HK_LOG_CNT_TOT);
				String str_fmt_err_cnt_ = biz_map_.get(MdbConst.BIZLOG_HK_LOG_CNT_FMT);
				String str_rat_err_cnt_ = biz_map_.get(MdbConst.BIZLOG_HK_LOG_CNT_RAT);
				String str_dup_cnt_ = biz_map_.get(MdbConst.BIZLOG_HK_LOG_CNT_DUP);
				String str_nml_cnt_ = biz_map_.get(MdbConst.BIZLOG_HK_LOG_CNT_NML);
				long tot_cnt_ = PubMethod.A2L(str_tot_cnt_);
				long fmt_err_cnt_ = PubMethod.A2L(str_fmt_err_cnt_);
				long rat_err_cnt_ = PubMethod.A2L(str_rat_err_cnt_);
				long dup_cnt_ = PubMethod.A2L(str_dup_cnt_);
				long nml_cnt_ = PubMethod.A2L(str_nml_cnt_);
				long uld_cnt_ = fmt_err_cnt_ + rat_err_cnt_ + dup_cnt_ + nml_cnt_;
				if (tot_cnt_ > 0 && tot_cnt_ == uld_cnt_) {
					L.info("{} uld done, tot_cnt {}, fmt:rat:dup:nml {}:{}:{}:{}",
							new Object[] { key_, tot_cnt_, fmt_err_cnt_, rat_err_cnt_, dup_cnt_, nml_cnt_ });
					_cutoff(key_);
					continue;
				} else if (uld_cnt_ > tot_cnt_) {
					L.warn("{} uld_cnt {} gt tot_cnt {}, pls check, fmt:rat:dup:nml {}:{}:{}:{}",
							new Object[] { key_, uld_cnt_, tot_cnt_, fmt_err_cnt_, rat_err_cnt_, dup_cnt_, nml_cnt_ });
				}
				if (diff_ > ErruldCfg._UldCutoffDur * 1000) {
					L.info("{} {} waiting {} seconds, expire and force cutoff, tot:uld {}:{}, fmt:rat:dup:nml {}:{}:{}:{}",
							new Object[] { key_, uld_log_.getUld_file_nm(), diff_ / 1000, tot_cnt_, uld_cnt_, fmt_err_cnt_,
									rat_err_cnt_, dup_cnt_, nml_cnt_ });
					_cutoff(key_);
				} else if (uld_cnt_ < tot_cnt_) {
					L.trace("{} uld_cnt {} lt tot_cnt {}, just wait, fmt:rat:dup:nml {}:{}:{}:{}",
							new Object[] { key_, uld_cnt_, tot_cnt_, fmt_err_cnt_, rat_err_cnt_, dup_cnt_, nml_cnt_ });
				}
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("jedis hgetAll [%s] exception", key_);
				L.warn(alm_, e);
				cli_.returnBrokenResource(null, pool_, jedis_);
				jedis_ = null;
			} finally {
				cli_.returnResource(pool_, jedis_);
			}
		}
	}

	private void _procUdr(UdrFmt udr) throws Exception {
		String yyyymmdd_ = udr._eFields[UdrFmt.E_04_A05_FILE_ID].substring(0, 8);
		String dup_key_ = udr._eFields[UdrFmt.E_04_A05_FILE_ID].substring(8) + ":" + udr._eFields[UdrFmt.E_05_A06_LINE_NUM];
		if (udr.isFeedback()) {
			dup_key_ = dup_key_ + ":" + udr._eFields[UdrFmt.E_32_X07_FEEDBACK_TAG];
		}
		if (!_edbDupchkPool.chkDup(yyyymmdd_, dup_key_)) {
			L.warn("{} dup and skip, {}", udr.getBizlogUid(), udr.toGsonStr());
			return;
		}

		if (udr._eFields[UdrFmt.E_16_E02_ERR_VAL] != null) {
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._eFields[UdrFmt.E_16_E02_ERR_VAL].replace("\r", "");
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._eFields[UdrFmt.E_16_E02_ERR_VAL].replace("\n", "");
		}
		UdrFileHandle hndl_ = _getHndl(udr);
		udr.dumpErr(hndl_._pw, ErruldCfg._UldCharset);
		hndl_.flush();
		_adjOraLog(udr);
		_adjBizLog(udr);
	}

	private UdrFileHandle _getHndl(UdrFmt udr) throws Exception {
		String key_ = udr.getBizlogLid();
		UdrFileHandle hndl_ = _hndlMap.get(key_);
		if (hndl_ == null) {
			hndl_ = _addHndl(key_, udr);
		}
		return hndl_;
	}

	private UdrFileHandle _addHndl(String key, UdrFmt udr) throws Exception {
		UdrFileHandle hndl_ = null;
		String uld_fnm_ = null;
		String file_type_ = udr._eFields[UdrFmt.E_04_A05_FILE_ID].substring(8, 11);
		String rand_ = PubMethod.RandomStr(4);
		if (PubMethod.IsEmpty(udr._eFields[UdrFmt.E_29_X04_ERCY_FILE_NM])) {
			uld_fnm_ = PubMethod.FmtArgs("E_%s_%s_%s.%s.bcp", file_type_, udr._eFields[UdrFmt.E_30_X05_ACNT_YM],
					udr._eFields[UdrFmt.E_00_A01_RAW_FILE_NM], rand_);
		} else {
			uld_fnm_ = PubMethod.FmtArgs("E_%s_%s_%s.%s.bcp", file_type_, udr._eFields[UdrFmt.E_30_X05_ACNT_YM],
					udr._eFields[UdrFmt.E_29_X04_ERCY_FILE_NM], rand_);
		}
		DbErruldLogRec uld_log_ = new DbErruldLogRec();
		uld_log_.setUld_file_nm(uld_fnm_);
		uld_log_.setRcv_mm(Integer.parseInt(udr._eFields[UdrFmt.E_06_A07_RCV_MM]));
		uld_log_.setRcv_ymdh(Long.parseLong(udr._eFields[UdrFmt.E_07_A08_RCV_YMDH]));
		uld_log_.setAcnt_ym((int) PubMethod.A2L(udr._eFields[UdrFmt.E_30_X05_ACNT_YM]));
		uld_log_.setUld_status(0);
		uld_log_.setTs_start(System.currentTimeMillis());
		uld_log_.setUld_start_tm(new Timestamp(uld_log_.getTs_start()));
		uld_log_.setUld_end_tm(new Timestamp(0L));
		uld_log_.setFile_id(Long.parseLong(udr._eFields[UdrFmt.E_04_A05_FILE_ID]));
		uld_log_.setTs_end(uld_log_.getTs_start());
		uld_log_.setUld_cnt(0);
		uld_log_.setErcy_cnt(0);
		uld_log_.setSpare_num1(0L);
		uld_log_.setSpare_num2(0L);
		hndl_ = new UdrFileHandle(new File(ErruldCfg._UldCacheDir + "/" + uld_log_.getUld_file_nm()), UdrFileHandle.OP_MODE_MOVE,
				false);
		hndl_._dstDir = ErruldCfg._UldOutputDir;
		hndl_.openSrcFile(ErruldCfg._UldCharset, false);
		ErruldOdb odb_ = new ErruldOdb();
		odb_.addErrUldLog(null, uld_log_);
		_hndlMap.put(key, hndl_);
		_logMap.put(key, uld_log_);
		return hndl_;
	}

	private void _adjOraLog(UdrFmt udr) {
		String key_ = udr.getBizlogLid();
		DbErruldLogRec uld_log_ = _logMap.get(key_);
		if (udr.isFeedback()) {
			uld_log_.setSpare_num1(uld_log_.getSpare_num1() + 1);
		} else {
			uld_log_.setUld_cnt(uld_log_.getUld_cnt() + 1);
		}
		if (!PubMethod.IsEmpty(udr._eFields[UdrFmt.E_29_X04_ERCY_FILE_NM])) {
			uld_log_.setErcy_cnt(uld_log_.getErcy_cnt() + 1);
		}
		uld_log_.setTs_end(System.currentTimeMillis());
	}

	private void _adjBizLog(UdrFmt udr) {
		String bizlog_cid_ = udr.getBizlogCid();
		String bizlog_lid_ = udr.getBizlogLid();
		String bizlog_uid_ = udr.getBizlogUid();
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		Transaction tx_ = null;
		try {
			pool_ = cli_.getPool();
			jedis_ = pool_.getResource();
			tx_ = jedis_.multi();
			tx_.hincrBy(bizlog_cid_, udr._eFields[UdrFmt.E_07_A08_RCV_YMDH], -1);
			if (udr.isFeedback()) {
				if (UdrFmt.V009_DUP_UDR.equals(udr._eFields[UdrFmt.E_02_A03_ERR_CODE])) {
					tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_FBK_DUP, 1);
				} else if (UdrFmt.V070_RATING_NO_MATCH.equals(udr._eFields[UdrFmt.E_02_A03_ERR_CODE])) {
					tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_FBK_RAT, 1);
				} else {
					tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_FBK_FMT, 1);
				}
			} else {
				if (UdrFmt.V009_DUP_UDR.equals(udr._eFields[UdrFmt.E_02_A03_ERR_CODE])) {
					tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_CNT_DUP, 1);
				} else if (UdrFmt.V070_RATING_NO_MATCH.equals(udr._eFields[UdrFmt.E_02_A03_ERR_CODE])) {
					tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_CNT_RAT, 1);
				} else {
					tx_.hincrBy(bizlog_lid_, MdbConst.BIZLOG_HK_LOG_CNT_FMT, 1);
				}
			}
			tx_.del(bizlog_uid_);
			List<Object> rsps_ = tx_.exec();
			L.trace("{}, {}, {}", bizlog_uid_, udr._eFields[UdrFmt.E_02_A03_ERR_CODE], PubMethod.Collection2Str(rsps_, ":"));
		} catch (Exception e) {
			String alm_ = PubMethod.FmtArgs("[%s] jedis exception, %s", bizlog_uid_, udr.toGsonStr());
			L.warn(alm_, e);
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}

	private void _cutoff(String key) {
		Connection conn_ = null;
		ErruldOdb odb_ = new ErruldOdb();
		DbErruldLogRec uld_log_ = _logMap.get(key);
		if (uld_log_ == null) {
			L.warn("key {} has no correspond erruld_log, can not cutoff", key);
			return;
		}
		UdrFileHandle hndl_ = _hndlMap.get(key);
		OdbCli cli_ = OdbAgt.GetBizInstance();
		try {
			conn_ = cli_.getConnection();
			uld_log_.setUld_status(1);
			uld_log_.setUld_end_tm(new Timestamp(System.currentTimeMillis()));
			odb_.updErrUldLog(conn_, uld_log_);
			if (!hndl_.operation()) {
				L.warn("{} operation error, pls chk", uld_log_.getUld_file_nm());
			}
			cli_.commit(conn_);
			L.info("transaction {} commit done", conn_.toString());
			hndl_.closeSrcFile();
			_hndlMap.remove(key);
			_logMap.remove(key);
		} catch (Exception e) {
			L.warn("{} exception", uld_log_.getUld_file_nm(), e);
			if (conn_ != null) {
				cli_.rollback(conn_);
				L.warn("transaction {} rollback done", conn_.toString());
			}
		} finally {
			cli_.close(conn_);
		}
	}

	private void _traceMaps() {
		StringBuilder sb_ = new StringBuilder();
		sb_.append(PubMethod.FmtArgs("total %d keys", _hndlMap.size()));
		int i = 0;
		for (String key_ : _hndlMap.keySet()) {
			++i;
			UdrFileHandle hndl_ = _hndlMap.get(key_);
			sb_.append(PubMethod.FmtArgs("%nNo. %-3d %s %s%n", i, key_, hndl_.toGsonStr()));
			DbErruldLogRec uld_log_ = _logMap.get(key_);
			sb_.append(PubMethod.FmtArgs("No. %-3d %s %s%n", i, key_, uld_log_.toGsonStr()));
		}
		L.trace(sb_.substring(0));
	}
}
