package com.hp.cmcc.bboss.app.rmtdis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.LoginPasswd;

public class DbRmtdisLoginCfgRec extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(DbRmtdisLoginCfgRec.class);
	@Expose
	private String login_cfg_nm;
	@Expose
	private String rmt_host;
	@Expose
	private Integer rmt_port;
	@Expose
	private String rmt_user;
	private String rmt_passwd_plain;
	@Expose
	private String rmt_passwd_crypt;

	@Override
	public String toGsonStr() {
		Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().setDateFormat(GsonObj._DATE_TIME_FMT).create();
		return gson.toJson(this);
	}

	public boolean validateRec() {
		boolean rc_ = true;
		if (rmt_port < 1 || rmt_port > 65535) {
			L.warn("{} RMT_PORT {} out of range, should between [1,65535]", login_cfg_nm, rmt_port);
			rc_ = false;
		}
		String passwd_plain_ = LoginPasswd.PasswdDecryptAES(rmt_passwd_crypt);
		if (passwd_plain_ == null) {
			L.warn("{} decrypt RMT_PASSWD_CRYPT [{}] error", login_cfg_nm, rmt_passwd_crypt);
			rc_ = false;
		} else {
			rmt_passwd_plain = passwd_plain_;
		}
		return rc_;
	}

	public String getLogin_cfg_nm() {
		return login_cfg_nm;
	}

	public void setLogin_cfg_nm(String login_cfg_nm) {
		this.login_cfg_nm = login_cfg_nm;
	}

	public String getRmt_host() {
		return rmt_host;
	}

	public void setRmt_host(String rmt_host) {
		this.rmt_host = rmt_host;
	}

	public Integer getRmt_port() {
		return rmt_port;
	}

	public void setRmt_port(Integer rmt_port) {
		this.rmt_port = rmt_port;
	}

	public String getRmt_user() {
		return rmt_user;
	}

	public void setRmt_user(String rmt_user) {
		this.rmt_user = rmt_user;
	}

	public String getRmt_passwd_plain() {
		return rmt_passwd_plain;
	}

	public void setRmt_passwd_plain(String rmt_passwd_plain) {
		this.rmt_passwd_plain = rmt_passwd_plain;
	}

	public String getRmt_passwd_crypt() {
		return rmt_passwd_crypt;
	}

	public void setRmt_passwd_crypt(String rmt_passwd_crypt) {
		this.rmt_passwd_crypt = rmt_passwd_crypt;
	}
}
