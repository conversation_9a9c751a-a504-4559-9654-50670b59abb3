package com.hp.cmcc.bboss.app.locdis;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("rest")
public class LocdisRest {
	private static Logger L = LoggerFactory.getLogger(LocdisRest.class);

	@GET
	@Path("debug")
	@Produces(MediaType.TEXT_PLAIN)
	public String debug(@Context HttpServletRequest request) {
		try {
			String rsps_ = "0";
			L.trace("request received");
			LocdisCfg._DebugFlag = true;
			return rsps_;
		} catch (Exception e) {
			L.warn("process debug request exception", e);
			return e.toString();
		}
	}
}
