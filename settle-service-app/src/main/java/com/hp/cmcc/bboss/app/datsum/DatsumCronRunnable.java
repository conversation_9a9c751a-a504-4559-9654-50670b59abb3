package com.hp.cmcc.bboss.app.datsum;

import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DatsumCronRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(DatsumCronRunnable.class);
	private boolean _terminateFlag = false;
	private AtomicBoolean _triggerBoolean;
	private String _triggerCron;
	private Map<String, Object> _cronMap;
	private String _prevHHMM;
	private String _currHHMM;
	private long _prevTs;
	private long _currTs;

	public DatsumCronRunnable(AtomicBoolean trigger_boolean, String trigger_cron) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_triggerBoolean = trigger_boolean;
		_triggerCron = trigger_cron;
		_cronMap = new TreeMap<String, Object>();
		_prevTs = System.currentTimeMillis();
		_currTs = _prevTs;
		_prevHHMM = PubMethod.Long2Str(_prevTs, PubMethod.TimeStrFmt.Fmt14).substring(8, 12);
		_currHHMM = _prevHHMM;
		String[] hhmm_ = _triggerCron.split(":");
		String[] a_hh_ = null;
		if (hhmm_[0].equals("*")) {
			a_hh_ = new String[24];
			for (int i = 0; i < 24; i++) {
				a_hh_[i] = String.format("%02d", i);
			}
		} else {
			a_hh_ = hhmm_[0].split(",");
		}
		String[] a_mm_ = hhmm_[1].split(",");
		for (String hh_ : a_hh_) {
			for (String mm_ : a_mm_) {
				_cronMap.put(String.format("%s%s", hh_, mm_), new Object());
			}
		}
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		L.trace("{} keys in _cronMap", _cronMap.size());
		int i = 0;
		for (String k : _cronMap.keySet()) {
			L.trace("No. {}, {}", ++i, k);
		}
		while (!_terminateFlag) {
			try {
				PubMethod.Sleep(500);
				_currTs = System.currentTimeMillis();
				_prevTs = _currTs;
				_currHHMM = PubMethod.Long2Str(_currTs, PubMethod.TimeStrFmt.Fmt14).substring(8, 12);
				if (_currHHMM.equals(_prevHHMM))
					continue;
				L.trace("{}:{} minute change detected", _prevHHMM, _currHHMM);
				_prevHHMM = _currHHMM;
				if (_cronMap.containsKey(_currHHMM)) {
					L.debug("{} hit _cronMap, _triggerBoolean will be set", _currHHMM);
					_triggerBoolean.set(true);
				}
			} catch (Exception e) {
				L.warn("encounter exception, sleep 3 seconds");
				PubMethod.Sleep(3000);
			}
		}
		L.info("thread terminated");
	}
}
