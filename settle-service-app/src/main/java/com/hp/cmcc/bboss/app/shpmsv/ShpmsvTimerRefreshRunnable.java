package com.hp.cmcc.bboss.app.shpmsv;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmsvTimerRefreshRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(ShpmsvTimerRefreshRunnable.class);
	public static boolean _RefreshFlag = false;
	private String _prevHHMM;
	private String _currHHMM;
	private boolean _terminateFlag = false;

	public ShpmsvTimerRefreshRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		long now_ = System.currentTimeMillis();
		String tm14_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14);
		_currHHMM = tm14_.substring(8, 12);
		_prevHHMM = _currHHMM;
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5 * 1000);
			}
		}
		L.info("thread terminated");
	}

	private void _exec() {
		PubMethod.Sleep(500);
		long now_ = System.currentTimeMillis();
		String tm14_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14);
		_currHHMM = tm14_.substring(8, 12);
		if (_prevHHMM.equals(_currHHMM)) {
			String ss_ = tm14_.substring(12, 14);
			if (Integer.parseInt(ss_) < 57) {
				PubMethod.Sleep(1000);
				return;
			}
			return;
		}
		_prevHHMM = _currHHMM;
		if (_currHHMM.substring(3).equals("0")) {
			L.info("it is decimal refresh time");
			_RefreshFlag = true;
		}
	}
}
