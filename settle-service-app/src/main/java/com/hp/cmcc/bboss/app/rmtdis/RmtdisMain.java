package com.hp.cmcc.bboss.app.rmtdis;

import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class RmtdisMain {
	private static Logger L = LoggerFactory.getLogger(RmtdisMain.class);
	private static List<RmtdisTransRunnable> _TransRunnableList;
	private static List<Thread> _TransThreadList;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("rmtdis", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}
	
	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 10, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!RmtdisCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		RmtdisCfg.TraceCfgMaps();
		_TransRunnableList = new ArrayList<RmtdisTransRunnable>();
		_TransThreadList = new ArrayList<Thread>();
		for (Entry<String, List<DbRmtdisTransCfgRec>> ent_ :RmtdisCfg._TransCfgMap.entrySet()) {
			RmtdisTransRunnable runnable_ = new RmtdisTransRunnable(ent_.getValue());
			_TransRunnableList.add(runnable_);
			Thread thread_ = new Thread(runnable_, ent_.getKey());
			_TransThreadList.add(thread_);
		}
		for (Thread t : _TransThreadList) {
			t.start();
		}
	}
	
	private static void _MainLoop() throws InterruptedException {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}
			L.info("try stop normal");
			for (RmtdisTransRunnable r : _TransRunnableList) {
				r.setTerminateFlag();
			}
			for (Thread t : _TransThreadList) {
				L.debug("try join thread {}", t.getName());
				t.join();
				L.debug("thread {} joined", t.getName());
			}
			break;
		}
	}
}
