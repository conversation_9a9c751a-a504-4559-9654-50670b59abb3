package com.hp.cmcc.bboss.app.syndup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class SyndupCfg {
	private static Logger L = LoggerFactory.getLogger(SyndupCfg.class);
	public static int _Interval; // in seconds
	public static int _BatchSize;

	public static boolean Init() {
		boolean rc_ = OdbSystemParam.GetInstance().refresh();
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}
		if (!_InitAppParam()) {
			L.warn("call _InitAppParam error");
			rc_ = false;
		}
		return rc_;
	}

	private static boolean _InitAppParam() {
		String inst_nm_ = AppCmdline.GetInstance()._instNm;
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(AppCmdline.GetInstance()._module, inst_nm_);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		Long lval_ = app_param_.chkValNum("COMMON", "INTERVAL", 1L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_Interval = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "BATCH_SIZE", 1L, 1000000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_BatchSize = lval_.intValue();
		}

		return rc_;
	}
}
