package com.hp.cmcc.bboss.app.locdis;

import java.io.File;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class LocdisMoveRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(LocdisMoveRunnable.class);
	private LocdisOdb _odb;
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public LocdisMoveRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_odb = new LocdisOdb();
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
				_interval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 3 seconds", e);
				PubMethod.Sleep(3000);
			}
		}
		L.info("thread end");
	}

	private void _exec() {
		Map<String, List<DbLocdisCfgRec>> move_cfg_map_ = LocdisCfg._MoveCfgMap;
		for (String k : move_cfg_map_.keySet()) {
			File src_dir_ = new File(k);
			if (!src_dir_.isDirectory()) {
				L.warn("[{}] dir not exists anymore", k);
				continue;
			}
			List<DbLocdisCfgRec> cfg_list_ = move_cfg_map_.get(k);
			_execSrcDir(src_dir_, cfg_list_);
		}
	}

	private void _execSrcDir(File src_dir, List<DbLocdisCfgRec> cfg_list) {
		File[] files_ = src_dir.listFiles();
		if (files_ == null || files_.length == 0)
			return;
		Arrays.sort(files_);
		List<File> file_list_ = new LinkedList<File>();
		for (File f : files_) {
			if (!f.isFile())
				continue;
			file_list_.add(f);
		}
		for (DbLocdisCfgRec cfg_ : cfg_list) {
			_execOneCfg(cfg_, file_list_);
		}
	}

	private void _execOneCfg(DbLocdisCfgRec cfg, List<File> file_list) {
		List<File> reserved_list_ = new LinkedList<File>();
		while (!file_list.isEmpty()) {
			File f = file_list.remove(0);
			if (cfg._patternIgn != null) {
				if (cfg._patternIgn.matcher(f.getName()).find()) {
					reserved_list_.add(f);
					// L.trace("{}, {} match PATTERN_IGN [{}]", cfg.getCfg_id(), f.getName(), cfg.getPattern_ign());
					continue;
				}
			}
			if (!cfg._patternMat.matcher(f.getName()).find()) {
				reserved_list_.add(f);
				// L.trace("{}, {} not match PATTERN_MAT [{}]", cfg.getCfg_id(), f.getName(), cfg.getPattern_mat());
				continue;
			}
			long now_ = System.currentTimeMillis();
			long modify_millis_ = f.lastModified();
			long diff_ = now_ - modify_millis_;
			if (diff_ < cfg.getDelay_dur() * 1000L) {
				reserved_list_.add(f);
				// L.trace("{}, {} diff {} lt delay {} * 1000", cfg.getCfg_id(), f.getName(), diff_, cfg.getDelay_dur());
				continue;
			}
			DbLocdisLogRec log_ = _fillLogRec(f, cfg);
			String src_path_ = cfg.getSrc_dir() + "/" + f.getName();
			if (cfg.getBak_dir() != null) {
				String bak_path_ = cfg.getBak_dir() + "/" + f.getName();
				if (PubMethod.CopyAFile(src_path_, bak_path_)) {
					L.info("bak [{}] to [{}] ok", src_path_, bak_path_);
				} else {
					DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341001_MOVE_BAK_FAIL,
							PubMethod.FmtArgs("bak [%s] to [%s] error", src_path_, bak_path_), cfg.toGsonStr());
					L.warn("{}, {}", cfg.getCfg_id(), alm_.getAlm_msg());
					_odbUtils.addRawAlm(null, alm_);
				}
			}
			if (cfg.getAct_script() != null) {
				if (!_execActScript(f, cfg.getAct_script())) {
					log_.setDis_status(DbLocdisLogRec.DIS_STATUS_2_ACT_SCRIPT_ERROR);
					DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341002_MOVE_ACT_FAIL,
							PubMethod.FmtArgs("[%s] exec act_script [%s] error", f.getAbsolutePath(), cfg.getAct_script()),
							cfg.toGsonStr());
					L.warn("{}, {}", cfg.getCfg_id(), alm_.getAlm_msg());
					_odbUtils.addRawAlm(null, alm_);
				}
				if (!f.isFile()) {
					DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341003_MOVE_ACT_LOST, PubMethod
							.FmtArgs("[%s] disappear after exec act_script [%s]", f.getAbsolutePath(), cfg.getAct_script()),
							cfg.toGsonStr());
					L.warn("{}, {}", cfg.getCfg_id(), alm_.getAlm_msg());
					log_.setDis_status(DbLocdisLogRec.DIS_STATUS_2_ACT_SCRIPT_ERROR);
					if (cfg.getLog_flag() == DbLocdisCfgRec.LOG_FLAG_1_ENABLED) {
						_odb.addLocdisLog(null, log_);
					}
					_odbUtils.addRawAlm(null, alm_);
					continue;
				}
			}
			String dst_path_ = cfg.getDst_dir() + "/" + f.getName();
			if (PubMethod.MoveAFile(src_path_, dst_path_)) {
				L.info("move ok, {}", log_.toGsonStr());
			} else {
				log_.setDis_status(DbLocdisLogRec.DIS_STATUS_1_MOVE_ERROR);
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341004_MOVE_MOV_FAIL,
						PubMethod.FmtArgs("mv [%s] to [%s] error", src_path_, dst_path_), cfg.toGsonStr());
				alm_.setAlm_kpi(log_.toGsonStr());
				L.warn("{}, {}, {}", cfg.getCfg_id(), alm_.getAlm_msg(), log_.toGsonStr());
				_odbUtils.addRawAlm(null, alm_);
			}
			if (cfg.getLog_flag() == DbLocdisCfgRec.LOG_FLAG_1_ENABLED) {
				_odb.addLocdisLog(null, log_);
			}
		}
		for (File f : reserved_list_)
			file_list.add(f);
	}

	private DbLocdisLogRec _fillLogRec(File f, DbLocdisCfgRec cfg) {
		DbLocdisLogRec log_ = new DbLocdisLogRec();
		long now_ = System.currentTimeMillis();
		log_.setMmdd(Integer.parseInt(PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14).substring(4, 8)));
		log_.setInst_nm(AppCmdline.GetInstance()._instNm);
		log_.setFile_nm(f.getName());
		log_.setFile_sz(f.length());
		log_.setFile_cksum(PubMethod.FileCksum(f.getAbsolutePath()));
		log_.setFile_tm(new Timestamp(f.lastModified()));
		log_.setDis_tm(new Timestamp(now_));
		log_.setSrc_dir(cfg.getSrc_dir());
		log_.setDst_dir(cfg.getDst_dir());
		log_.setDis_status(DbLocdisLogRec.DIS_STATUS_0_SUCCESS);
		log_.setCfg_id(cfg.getCfg_id());
		return log_;
	}

	private boolean _execActScript(File src_file, String act_script) {
		StringBuilder sb_ = new StringBuilder();
		String[] act_args_ = act_script.split("\\s+");
		for (int i = 0; i < act_args_.length; i++) {
			sb_.append(act_args_[i]);
			if (i == 0) {
				sb_.append(" ");
				sb_.append(src_file.getAbsolutePath());
			}
			if (i < act_args_.length - 1)
				sb_.append(" ");
		}
		String act_cmd_ = sb_.substring(0);
		List<String> cmd_ = PubMethod.AssembleCmd(act_cmd_);
		ProcessBuilder pb_ = new ProcessBuilder(cmd_);
		pb_.inheritIO();
		try {
			Process proc_ = pb_.start();
			int exit_code_ = proc_.waitFor();
			if (exit_code_ == 0) {
				L.debug("invoke [{}] ok", act_cmd_);
			} else {
				L.warn("invoke [{}] returns {}", act_cmd_, exit_code_);
				return false;
			}
		} catch (Exception e) {
			L.warn(String.format("invoke [%s] exception", act_cmd_), e);
			return false;
		}
		return true;
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		for (int i = 0; i < LocdisCfg._IntervalMove; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}
}
