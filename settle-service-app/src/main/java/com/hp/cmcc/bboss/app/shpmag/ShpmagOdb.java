package com.hp.cmcc.bboss.app.shpmag;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.List;

//import oracle.jdbc.driver.OracleBlobInputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmBlobRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmCtlRec;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmagOdb {
	private static Logger L = LoggerFactory.getLogger(ShpmagOdb.class);
	private static final String _Q_SHPM_BLOB = "SELECT VER_MM, INST_NM, SHPM_VER, SHPM_SIZE, SHPM_CKSUM, SHPM_BLOB "
			+ "FROM SHPM_BLOB WHERE INST_NM = ? AND SHPM_VER = ?";
	private static final String _Q_SHPM_CTL = "SELECT VER_MM, INST_NM, SHPM_VER, AGT_INST, CTL_FLAG, TM_RQST, "
			+ "TM_RSPS, TM_DONE, TS_RQST, TS_RSPS, TS_DONE, ERR_MSG FROM SHPM_CTL "
			+ "WHERE INST_NM = ? AND AGT_INST = ? AND CTL_FLAG = 0 ORDER BY SHPM_VER DESC";
	private static final String _U_SHPM_CTL = "UPDATE SHPM_CTL SET CTL_FLAG = ?, TM_DONE = ?, TS_DONE = ?, ERR_MSG = ? "
			+ "WHERE INST_NM = ? AND AGT_INST = ? AND SHPM_VER = ?";

	public List<DbShpmCtlRec> getShpmCtlFlag0(String agt_inst) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbShpmCtlRec> result_ = cli_.queryForOList(_Q_SHPM_CTL, DbShpmCtlRec.class, OdbSystemParam.GetInstance()._shpmsvInstNm,
				agt_inst);
		return result_;
	}

	public void updShpmCtlDone(String agt_inst, String shpm_ver, int ctl_flag, String err_msg) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		long now_ts_ = System.currentTimeMillis();
		Timestamp now_tm_ = new Timestamp(now_ts_);
		int rows_affected_ = cli_.update(_U_SHPM_CTL, ctl_flag, now_tm_, now_ts_, err_msg,
				OdbSystemParam.GetInstance()._shpmsvInstNm, agt_inst, shpm_ver);
		L.debug("upd [{}, {}], {} rows affected", agt_inst, shpm_ver, rows_affected_);
	}

	public File downloadShpmBlob(DbShpmCtlRec ctl_rec, File working_dir, String gzip_path) {
		if (!working_dir.isDirectory()) {
			L.warn("working_dir {} not exists", working_dir.getAbsolutePath());
			return null;
		}
		DbShpmBlobRec rec_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		Connection conn_ = null;
		FileOutputStream fos_ = null;
		InputStream obis_ = null;
		OdbCli cli_ = OdbAgt.GetBizInstance();
		try {
			conn_ = cli_.getCanonicalConnection();
			pstmt_ = conn_.prepareStatement(_Q_SHPM_BLOB);
			pstmt_.setString(1, ctl_rec.getInst_nm());
			pstmt_.setString(2, ctl_rec.getShpm_ver());
			rs_ = pstmt_.executeQuery();
			byte[] buf_ = new byte[4096];
			while (rs_.next()) {
				int idx_ = 0;
				rec_ = new DbShpmBlobRec();
				rec_.setVer_mm(rs_.getInt(++idx_));
				rec_.setInst_nm(rs_.getString(++idx_));
				rec_.setShpm_ver(rs_.getString(++idx_));
				rec_.setShpm_size(rs_.getInt(++idx_));
				rec_.setShpm_cksum(rs_.getString(++idx_));
				String path_ = working_dir.getAbsolutePath() + "/" + rec_.getShpm_ver() + ".db3";
				String path_gz_ = working_dir.getAbsolutePath() + "/" + rec_.getShpm_ver() + ".db3.gz";
				fos_ = new FileOutputStream(path_gz_);
				L.trace("{} opened for dumping", path_gz_);
//				obis_ = (OracleBlobInputStream) rs_.getBinaryStream(++idx_);
				obis_ =  rs_.getBinaryStream(++idx_);
				int total_ = 0;
				int chunk_ = 0;
				while ((chunk_ = obis_.read(buf_)) != -1) {
					fos_.write(buf_, 0, chunk_);
					total_ += chunk_;
				}
				obis_.close();
				obis_ = null;
				fos_.close();
				fos_ = null;
				L.trace("{} total {} bytes dumped", path_gz_, total_);
				boolean rc_ = PubMethod.Gzip(OdbSystemParam.GetInstance()._gzipPath, new File(path_gz_), false);
				if (!rc_) {
					ctl_rec.setErr_msg(PubMethod.FmtArgs("unzip %s error", path_gz_));
					L.warn("{}", ctl_rec.getErr_msg());
					return null;
				}
				File f_db3_ = new File(path_);
				if (f_db3_.length() != rec_.getShpm_size()) {
					ctl_rec.setErr_msg(PubMethod.FmtArgs("%s size %d bytes, ne with %s, pls chk", path_, f_db3_.length(),
							rec_.getShpm_size()));
					L.warn("{}", ctl_rec.getErr_msg());
					return null;
				}
				long cksum_long_ = PubMethod.FileCksum(path_);
				String cksum_ = Long.toString(cksum_long_);
				if (!cksum_.equals(rec_.getShpm_cksum())) {
					ctl_rec.setErr_msg(PubMethod.FmtArgs("%s cksum %s, ne with %s, pls chk", path_, cksum_, rec_.getShpm_cksum()));
					L.warn("{}", ctl_rec.getErr_msg());
					return null;
				}
				return f_db3_;
			}
		} catch (Exception e) {
			ctl_rec.setErr_msg(e.getMessage());
			L.warn("encounter exception", e);
			return null;
		} finally {
			PubMethod.Close(obis_);
			PubMethod.Close(fos_);
			cli_.close(rs_, pstmt_, conn_);
		}

		return null;
	}
}
