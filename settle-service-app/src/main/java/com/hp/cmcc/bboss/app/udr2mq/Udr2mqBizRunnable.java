package com.hp.cmcc.bboss.app.udr2mq;

import com.hp.cmcc.bboss.app.shpmsv.ShpmsvOdb;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmVerRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.pub.udr.UdrKafkaProducerFactory;
import com.hp.cmcc.bboss.pub.util.*;
import kafka.producer.KeyedMessage;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.luaj.vm2.LuaTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import redis.clients.jedis.*;

import javax.script.*;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.Future;
import java.util.regex.Pattern;

public class Udr2mqBizRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(Udr2mqBizRunnable.class);
	private static Pattern _ExpAcctDay = Pattern.compile("^[0-9]{8}$");
	private static Pattern _ExpAcntYm = Pattern.compile("^[0-9]{6}$");
	private List<Udr2mqBizCfg> _bizCfgList;
	private Producer<String, String> _producer;
	private Udr2mqOdb _odb;
	private DbUdr2mqLogRec _logRec;
	private List<UdrFmt> _udrList;
	private AppTicker _ticker;
	private OdbUtils _odbUtils;
	private long _latestScanTs;
	private boolean _rawFileScaned = false;
	private Bindings _bindings;
	private ScriptEngine _luaj;
	private Map<String, CompiledScript> _csMap;
	private boolean _terminateFlag = false;

	public Udr2mqBizRunnable(List<Udr2mqBizCfg> biz_cfg_list) throws ScriptException {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_bizCfgList = biz_cfg_list;
		_odb = new Udr2mqOdb();
		_udrList = new LinkedList<UdrFmt>();
		_ticker = new AppTicker();
		_odbUtils = new OdbUtils();
		_latestScanTs = System.currentTimeMillis();
		_bindings = new SimpleBindings();
		_luaj = new ScriptEngineManager().getEngineByName("luaj");
		_csMap = new HashMap<String, CompiledScript>();
		for (Udr2mqBizCfg cfg_ : _bizCfgList) {
			if (!_csMap.containsKey(cfg_._fnmParser)) {
				CompiledScript cs_ = ((Compilable) _luaj).compile(cfg_._fnmParser);
				_csMap.put(cfg_._fnmParser, cs_);
			}
		}
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	public void init() {
		_producer = UdrKafkaProducerFactory.GetKafkaProducuer(OdbSystemParam.GetInstance()._kafkaCluster);
	}

	@Override
	public void run() {
		if (_producer == null) {
			L.warn("_producer is null, can not run");
			return;
		}
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
				_cacheCntInterval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
		L.info("thread terminated");
	}

	private void _exec() {
		for (Udr2mqBizCfg biz_cfg_ : _bizCfgList) {
			try {
				_execBizCfg(biz_cfg_);
			} catch (Exception e) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_210341001_UKN_EXCEPTION, "unknown exception", null);
				alm_.setAlm_kpi(e.toString());
				L.warn("{}, sleep 5 seconds", alm_.getAlm_msg(), e);
				_odbUtils.addRawAlm(null, alm_);
				PubMethod.Sleep(5000);
			}
		}
	}

	private void _execBizCfg(Udr2mqBizCfg biz_cfg) {
		long cache_cnt_ = _cacheCnt(biz_cfg);
		if (cache_cnt_ > biz_cfg._cacheThreshold) {
			L.debug("biz_type {} {} udrs still cached, gt threshold {}", biz_cfg._bizType, cache_cnt_, biz_cfg._cacheThreshold);
			return;
		}
		File raw_dir_ = new File(biz_cfg._rawDir);
		PatternFileFilter filter_ = new PatternFileFilter(biz_cfg._rawPattern, PatternFileFilter.TypeFilter.TypeFile);
		File[] raw_files_ = raw_dir_.listFiles(filter_);
		if (raw_files_.length > 0) {
			Arrays.sort(raw_files_);
			L.debug("{} raw file(s) scaned in [{}]", raw_files_.length, biz_cfg._rawDir);
			_rawFileScaned = true;
			_latestScanTs = System.currentTimeMillis();
		} else {
			// L.trace("no file scanned");
			_rawFileScaned = false;
			long now_ = System.currentTimeMillis();
			long diff_ = now_ - _latestScanTs;
			if (diff_ > 1800 * 1000L) {
				L.trace("idling {} seconds", diff_ / 1000L);
				_latestScanTs = now_;
			}
			return;
		}
		for (File f : raw_files_) {
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
			_ticker.tickStart();
			_procOneFile(biz_cfg, f);
			if (!_logRec.getProc_status().startsWith("F")) {
				_ticker.tickEnd(_logRec.getTot_cnt());
				L.debug("{} term:cnt:pfm {}:{}:{}", new Object[] { f.getName(), _ticker._term, _ticker._recNum, _ticker._pfm });
			} else {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_210341004_PROC_FAIL, PubMethod.FmtArgs("[%s] %s reject, [%s]",
						_logRec.getRaw_file_nm(), _logRec.getProc_status(), _logRec.getErr_msg()), _logRec.toGsonStr());
				if (_logRec.getException() != null)
					alm_.setAlm_kpi(_logRec.getException().toString());
				L.warn("{}", alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
			}
			if ((cache_cnt_ = _cacheCnt(biz_cfg)) > biz_cfg._cacheThreshold) {
				L.debug("biz_type {} {} udrs in cache, gt threshold {}", biz_cfg._bizType, cache_cnt_, biz_cfg._cacheThreshold);
				break;
			}
		}
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		for (int i = 0; i < Udr2mqCfg._Interval; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}

	private void _cacheCntInterval() {
		if (!_rawFileScaned) {
			_interval();
			return;
		}
		if (_terminateFlag)
			return;
		Map<String, Udr2mqBizCfg> threshold_cfg_map_ = new HashMap<String, Udr2mqBizCfg>(); // key: biz_type
		Map<String, Long> cache_map_ = new HashMap<String, Long>();
		for (Udr2mqBizCfg cfg_ : _bizCfgList) {
			if (cfg_._isErcy == 0) {
				long cache_cnt_ = _cacheCnt(cfg_);
				if (cache_cnt_ == Long.MAX_VALUE || cache_cnt_ <= 0)
					continue;
				threshold_cfg_map_.put(cfg_._bizType, cfg_);
				cache_map_.put(cfg_._bizType, cache_cnt_);
			}
		}
		for (int i = 0; i < Udr2mqCfg._Interval; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
			for (Udr2mqBizCfg cfg_ : threshold_cfg_map_.values()) {
				long cache_cnt_cur_ = _cacheCnt(cfg_);
				long cache_cnt_prv_ = cache_map_.get(cfg_._bizType);
				if (cache_cnt_prv_ > cache_cnt_cur_ && cache_cnt_cur_ <= cfg_._cacheThreshold) {
					L.trace("biz_type {} cache cnt prv:cur {}:{} le threshold {}, no more wait", cfg_._bizType, cache_cnt_prv_,
							cache_cnt_cur_, cfg_._cacheThreshold);
					return;
				}
				cache_map_.put(cfg_._bizType, cache_cnt_cur_);
			}
		}
	}

	private void _procOneFile(Udr2mqBizCfg biz_cfg, File f) {
		String bak_path_ = null;
		_initProcLogRec(biz_cfg, f);
		boolean exception_flag_ = false;
		try {
			_chkShparmVer();
			_rcvChk(biz_cfg, f);
			_fillUdrList(biz_cfg, f);
			_pipelineBizlog();
			_adjBizlog(biz_cfg);
			if (!_logRec.getProc_status().startsWith("F")) {
				_logRec.setProc_status("10000");
				_splitAndSend();
			}
		} catch (Exception e) {
			exception_flag_ = true;
			_logRec.setProc_status(Udr2mqCfg.F1999_INTERNAL_ERR);
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_210341002_PROC_EXCEPTION,
					PubMethod.FmtArgs("process [%s] exception, %s reject", f.getName(), _logRec.getProc_status()),
					_logRec.toGsonStr());
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
		} finally {
			_odb.addUdr2mqLog(_logRec);
			if (_logRec.getProc_status().startsWith("F")) {
				bak_path_ = String.format("%s/%s_%s", biz_cfg._errDir, _logRec.getProc_status(), f.getName());
			} else {
				bak_path_ = String.format("%s/%s", biz_cfg._bakDir, f.getName());
			}
			if (PubMethod.MoveAFile(f.getAbsolutePath(), bak_path_)) {
				L.info("_procOneFile mv {} to {} done", f.getAbsolutePath(), bak_path_);
				if (!_logRec.getProc_status().startsWith("F")) {
					Udr2mqMain._GzipTaskQueue.add(bak_path_);
				}
			} else {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_210341003_BAK_FAIL,
						PubMethod.FmtArgs("mv [%s] to [%s] failed, pls chk", f.getAbsolutePath(), bak_path_), _logRec.toGsonStr());
				L.warn("{}", alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
			}
			if (exception_flag_) {
				while (!_terminateFlag) {
					for (int i = 60; i > 0; i--) {
						L.info("_interval count down {}", i);
						_interval();
					}
				}
			}
		}
	}

	private long _cacheCnt(Udr2mqBizCfg biz_cfg) {
		String bizlog_key_cache_cnt_ = String.format(MdbConst.BIZLOG_KEY_CACHE_CNT, biz_cfg._typeId);//?
		long cnt_ = 0;
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		try {
			pool_ = cli_.getPool();
			jedis_ = pool_.getResource();
			Map<String, String> cnt_map_ = jedis_.hgetAll(bizlog_key_cache_cnt_);
			if (cnt_map_ == null) {
				L.debug("hgetAll({}) returns null", bizlog_key_cache_cnt_);
				cnt_ = 0;
			} else if (cnt_map_.isEmpty()) {
				cnt_ = 0;
			} else {
				for (Entry<String, String> entry_ : cnt_map_.entrySet()) {
					long l = Long.parseLong(entry_.getValue());
					if (l < 0) {
						L.debug("key {}, hkey {}, hval {} should ge 0, skip", bizlog_key_cache_cnt_, entry_.getKey(),
								entry_.getValue());
					} else {
						cnt_ += l;
					}
				}
			}
		} catch (Exception e) {
			L.warn("hgetAll({}) exception", bizlog_key_cache_cnt_, e);
			cnt_ = Long.MAX_VALUE;
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return cnt_;
	}

	private void _initProcLogRec(Udr2mqBizCfg biz_cfg, File f) {
		long now_ = System.currentTimeMillis();
		String rcv_tm_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt14);
		_logRec = new DbUdr2mqLogRec();
		_logRec.setRaw_file_nm(f.getName());
		_logRec.setRcv_mm(Integer.parseInt(rcv_tm_.substring(4, 6)));
		_logRec.setRcv_ymdh(Long.parseLong(rcv_tm_.substring(0, 10)));
		_logRec.setRcv_tm(new Timestamp(now_));
		_logRec.setPp_file_id(0L);
		_logRec.setBiz_type("-");
		_logRec.setAcnt_ym(0);
		_logRec.setFile_ymd(0);
		_logRec.setFile_prov(0);
		_logRec.setIs_ercy(biz_cfg._isErcy);
		_logRec.setProc_status("00000");
		_logRec.setFile_type(biz_cfg._typeId);
		_logRec.setTot_cnt(0);
		_logRec.setFmt_err_cnt(0);
		_logRec.setRat_err_cnt(0);
		_logRec.setDup_cnt(0);
		_logRec.setNml_cnt(0);
		_logRec.setTs_start(_ticker._tsStart);
		_logRec.setFile_id(0L);
		String seq_nm_ = String.format("UDR2MQ_%s", biz_cfg._bizType);
		int file_seq_ = _odbUtils.nextSeq(Integer.parseInt(rcv_tm_.substring(0, 8)), seq_nm_);
		if (file_seq_ > 999999 || file_seq_ < 1) {
			String alm_ = PubMethod.FmtArgs("[%s] nextSeq [%d] out of range [1,999999]", seq_nm_, file_seq_);
			L.warn(alm_);
			throw new RuntimeException(alm_);
		}
		_logRec.setFile_id(Integer.parseInt(rcv_tm_.substring(0, 8)) * 1000000000L + biz_cfg._typeId * 1000000L + file_seq_);
	}

	private void _chkShparmVer() {
		if (_logRec.getProc_status().startsWith("F"))
			return;

		ShpmsvOdb odb_ = new ShpmsvOdb();
		DbShpmVerRec shpm_ver_ = odb_.getShpmVerNewest(OdbSystemParam.GetInstance()._shpmsvInstNm, DbShpmVerRec.VER_FLAG_1_SUCCESS);
		if (shpm_ver_ == null) {
			_logRec.setProc_status(Udr2mqCfg.F1999_INTERNAL_ERR);
			_logRec.setErr_msg("no shpm available");
			L.warn("[{}] {}, {} reject", _logRec.getRaw_file_nm(), _logRec.getErr_msg(), _logRec.getProc_status());
			return;
		}
		_logRec.setShparm_ver(shpm_ver_.getShpm_ver());
		L.trace("[{}] bind shparm ver {}", _logRec.getRaw_file_nm(), _logRec.getShparm_ver());
	}

	private void _rcvChk(Udr2mqBizCfg biz_cfg, File f) {
		if (_logRec.getProc_status().startsWith("F"))
			return;

		if (f.length() == 0) {
			_logRec.setProc_status(Udr2mqCfg.F1000_EMPTY_FILE);
			_logRec.setErr_msg(PubMethod.FmtArgs("empty file, %s reject", _logRec.getProc_status()));
			L.warn("[{}] {}", f.getName(), _logRec.getErr_msg());
			return;
		}

		try {
			_bindings.clear();
			_bindings.put("RAW_FILE_NM", f.getName());
			CompiledScript cs_ = _csMap.get(biz_cfg._fnmParser);
			LuaTable lua_tbl_ = (LuaTable) cs_.eval(_bindings);
			String ymd_ = lua_tbl_.get(1).toString();
			String prov_ = lua_tbl_.get(2).toString();
			long ymd_ts_ = PubMethod.Str2Long(ymd_, PubMethod.TimeStrFmt.Fmt8);
			String ymd_cmp_ = PubMethod.Long2Str(ymd_ts_, PubMethod.TimeStrFmt.Fmt8);
			if (!ymd_.equals(ymd_cmp_)) {
				String alm_ = PubMethod.FmtArgs("invalid yyyymmdd [%s]", ymd_);
				throw new RuntimeException(alm_);
			}
			_logRec.setFile_ymd(Integer.parseInt(ymd_));
			_logRec.setFile_prov(Integer.parseInt(prov_));
			L.trace("[{}] file_ymd={}, file_prov={}", _logRec.getRaw_file_nm(), _logRec.getFile_ymd(), _logRec.getFile_prov());
		} catch (Exception e) {
			_logRec.setProc_status(Udr2mqCfg.F1001_INVALID_FNM_TIME);
			_logRec.setErr_msg(PubMethod.FmtArgs("parse ymd prov exception, %s reject", _logRec.getProc_status()));
			_logRec.setException(e);
			L.warn("[{}] {}", f.getName(), _logRec.getErr_msg(), e);
			return;
		}

		DbUdr2mqLogRec dup_rec_ = _odb.getDupUdr2mqLog(f.getName());
		if (dup_rec_ != null) {
			_logRec.setProc_status(Udr2mqCfg.F1009_DUP_FILE);
			_logRec.setErr_msg(PubMethod.FmtArgs("dup with [%d, %s], %s reject", dup_rec_.getFile_id(),
					PubMethod.Timestamp2Str(dup_rec_.getRcv_tm(), PubMethod.TimeStrFmt.Fmt14), _logRec.getProc_status()));
			L.warn("[{}] {}", f.getName(), _logRec.getErr_msg());
			return;
		}
	}

	private void _fillUdrList(Udr2mqBizCfg biz_cfg, File f) {
		_udrList.clear();
		if (_logRec.getProc_status().startsWith("F"))
			return;

		int cnt_ = 0;
		String raw_line_ = null;
		BufferedReader br_ = null;
		try {
			br_ = new BufferedReader(new InputStreamReader(new FileInputStream(f), biz_cfg._fileCharset));
			L.trace("[{}] opened for parsing", f.getName());
			while ((raw_line_ = br_.readLine()) != null) {
				++cnt_;
				if (cnt_ == 1) {
					if (!_parse1stUdr(biz_cfg, raw_line_))
						break;
				}
				UdrFmt udr_ = new UdrFmt();
				_fillUdr(biz_cfg, udr_, raw_line_, cnt_);
				_udrList.add(udr_);
			}
			_logRec.setTot_cnt(_udrList.size());
			L.trace("[{}] read done, tot_cnt {}", f.getName(), _udrList.size());
		} catch (Exception e) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("process exception, cnt_=%d, %s reject", cnt_, _logRec.getProc_status()));
			_logRec.setException(e);
			L.warn("[{}] {}", f.getName(), _logRec.getErr_msg(), e);
		} finally {
			PubMethod.Close(br_);
		}
		L.debug("[{}] _udrList.size()={}", _logRec.getRaw_file_nm(), _udrList.size());
	}

	private boolean _parse1stUdr(Udr2mqBizCfg biz_cfg, String raw_line) {
		String first_rec_ = raw_line;
		if (biz_cfg._isErcy == 1) {
			String[] efields_ = raw_line.split("#\\|#", -1);
			if (efields_.length < 15) {
				_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
				_logRec.setErr_msg(PubMethod.FmtArgs("1st line has only %d fields, less than 15, %s reject", efields_.length,
						_logRec.getProc_status()));
				L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
				return false;
			}
			first_rec_ = efields_[15];
		}
		String[] fields_ = first_rec_.split(UdrFmt.CSV_NONE_ESC_PATTERN, -1);
		if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
			return _parse1stUIDD(fields_);
		} else if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
			return _parse1stSIDD(fields_);
		} else {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("ukn SUB_SYSTEM_TYPE %d, %s reject", OdbSystemParam.GetInstance()._subSystemType,
					_logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
	}

	private boolean _parse1stUIDD(String[] fields) {
		if (fields.length < UdrFmt.U_FIELD_MIN_56) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st line has only %d fields, less than %d, %s reject", fields.length,
					UdrFmt.U_FIELD_MIN_56, _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		if (PubMethod.IsBlank(fields[UdrFmt.U_36_BIZ_TYPE_37])) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st rec BIZ_TYPE_25 is blank, %s reject", _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		if (PubMethod.IsBlank(fields[UdrFmt.U_34_PP_FILE_ID_35])) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st rec PP_FILE_ID_23 is blank, %s reject", _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		if (PubMethod.IsBlank(fields[UdrFmt.U_35_ACCT_DAY_36])) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st rec ACCT_DAY_24 is blank, %s reject", _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		if (!_ExpAcctDay.matcher(fields[UdrFmt.U_35_ACCT_DAY_36]).find()) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st rec ACCT_DAY_24 not match [%s], %s reject", _ExpAcctDay.pattern(),
					_logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		_logRec.setBiz_type(fields[UdrFmt.U_36_BIZ_TYPE_37].trim());
		int acnt_ymd_ = Integer.parseInt(fields[UdrFmt.U_35_ACCT_DAY_36]);
		_logRec.setAcnt_ym(acnt_ymd_ / 100);
		long pp_file_id_ = Long.parseLong(fields[UdrFmt.U_34_PP_FILE_ID_35]);
		_logRec.setPp_file_id(pp_file_id_);
		return true;
	}

	private boolean _parse1stSIDD(String[] fields) {
		if (fields.length < UdrFmt.S_FIELD_MIN_40) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st line has only %d fields, less than %d, %s reject", fields.length,
					UdrFmt.S_FIELD_MIN_40, _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		if (PubMethod.IsBlank(fields[UdrFmt.S_02_BIZ_TYPE_03])) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st rec BIZ_TYPE_03 is blank, %s reject", _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		if (PubMethod.IsBlank(fields[UdrFmt.S_31_FILE_ID_32])) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st rec FILE_ID_32 is blank, %s reject", _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		_logRec.setBiz_type(fields[UdrFmt.S_02_BIZ_TYPE_03].trim());
		if (!_ExpAcntYm.matcher(fields[UdrFmt.S_25_SETTLE_MONTH_26]).find()) {
			_logRec.setProc_status(Udr2mqCfg.F1008_1ST_UDR_BAD_FMT);
			_logRec.setErr_msg(PubMethod.FmtArgs("1st rec SETTLE_MONTH_26 %s not match %s, %s reject",
					fields[UdrFmt.S_25_SETTLE_MONTH_26], _ExpAcntYm.pattern(), _logRec.getProc_status()));
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg());
			return false;
		}
		_logRec.setAcnt_ym(Integer.parseInt(fields[UdrFmt.S_25_SETTLE_MONTH_26]));
		long pp_file_id_ = Long.parseLong(fields[UdrFmt.S_31_FILE_ID_32]);
		_logRec.setPp_file_id(pp_file_id_);
		return true;
	}

	private void _fillUdr(Udr2mqBizCfg biz_cfg, UdrFmt udr, String raw_line, int line_num) {
		udr._eFields[UdrFmt.E_04_A05_FILE_ID] = _logRec.getFile_id().toString();
		udr._eFields[UdrFmt.E_05_A06_LINE_NUM] = Integer.toString(line_num);
		udr._eFields[UdrFmt.E_06_A07_RCV_MM] = _logRec.getRcv_mm().toString();
		udr._eFields[UdrFmt.E_07_A08_RCV_YMDH] = _logRec.getRcv_ymdh().toString();
		udr._eFields[UdrFmt.E_08_A09_RCV_TM] = PubMethod.Timestamp2Str(_logRec.getRcv_tm(), PubMethod.TimeStrFmt.Fmt14);
		udr._eFields[UdrFmt.E_26_X01_SHPARM_VER] = _logRec.getShparm_ver();
		udr._eFields[UdrFmt.E_30_X05_ACNT_YM] = Integer.toString(_logRec.getAcnt_ym());
		if (biz_cfg._isErcy == 0) {
			udr._eFields[UdrFmt.E_24_R01_RAW_UDR] = raw_line;
			udr._eFields[UdrFmt.E_00_A01_RAW_FILE_NM] = _logRec.getRaw_file_nm();
			udr._eFields[UdrFmt.E_01_A02_RAW_LINE_NUM] = Integer.toString(line_num);
			udr._eFields[UdrFmt.E_03_A04_ORG_FILE_ID] = _logRec.getFile_id().toString();
			udr._eFields[UdrFmt.E_09_A10_FILE_YMD] = _logRec.getFile_ymd().toString();
			udr._eFields[UdrFmt.E_10_A11_FILE_PROV] = _logRec.getFile_prov().toString();
			udr._eFields[UdrFmt.E_11_A12_ERCY_TIMES] = "0";
		} else {
			String[] rcy_fields_ = raw_line.split("#\\|#", -1);
			udr._eFields[UdrFmt.E_24_R01_RAW_UDR] = rcy_fields_[15];
			udr._eFields[UdrFmt.E_00_A01_RAW_FILE_NM] = rcy_fields_[0];
			udr._eFields[UdrFmt.E_01_A02_RAW_LINE_NUM] = rcy_fields_[1];
			udr._eFields[UdrFmt.E_03_A04_ORG_FILE_ID] = rcy_fields_[3];
			udr._eFields[UdrFmt.E_09_A10_FILE_YMD] = rcy_fields_[9];
			udr._eFields[UdrFmt.E_10_A11_FILE_PROV] = rcy_fields_[10];
			udr._eFields[UdrFmt.E_11_A12_ERCY_TIMES] = Integer.toString(Integer.parseInt(rcy_fields_[11]) + 1);
			udr._eFields[UdrFmt.E_12_A13_ERCY_TIME] = rcy_fields_[12];
			udr._eFields[UdrFmt.E_13_A14_PROC_FLAG] = rcy_fields_[13];
			udr._eFields[UdrFmt.E_14_A15_ACUMLT] = rcy_fields_[14];
			udr._eFields[UdrFmt.E_29_X04_ERCY_FILE_NM] = _logRec.getRaw_file_nm();
		}
	}

	private void _pipelineBizlog() {
		if (_logRec.getProc_status().startsWith("F"))
			return;
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = null;
		Pipeline pipeline_ = null;
		try {
			List<Future<GsonStrObj>> future_list_ = new ArrayList<>();
			for (UdrFmt udr_ : _udrList) {
				String k = udr_.getBizlogUid();
				GsonStrObjCallable callable_ = new GsonStrObjCallable(k, udr_);
				Future<GsonStrObj> future_ = Udr2mqMain._ToJsonPool.submit(callable_);
				future_list_.add(future_);
			}
			L.trace("[{}] {} udr callables submit done", _logRec.getRaw_file_nm(), future_list_.size());
			jedis_ = pool_.getResource();
			pipeline_ = jedis_.pipelined();
			for (Future<GsonStrObj> future_ : future_list_) {
				GsonStrObj gson_str_obj_ = future_.get();
				pipeline_.hset(gson_str_obj_._id, MdbConst.BIZLOG_HK_UDR_INIT,
						PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23));
				pipeline_.hset(gson_str_obj_._id, MdbConst.BIZLOG_HK_UDR_RAW, gson_str_obj_._gsonStr);
			}
			List<Object> echo_list_ = pipeline_.syncAndReturnAll();
			L.trace("[{}] pipeline done, echo_list_.size() {}", _logRec.getRaw_file_nm(), echo_list_.size());
		} catch (Exception e) {
			_logRec.setProc_status(Udr2mqCfg.F1999_INTERNAL_ERR);
			_logRec.setErr_msg(PubMethod.FmtArgs("pipeline exception, %s reject", _logRec.getProc_status()));
			_logRec.setException(e);
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg(), e);
		} finally {
			PubMethod.Close(pipeline_);
			cli_.returnResource(pool_, jedis_);
		}
	}

	private void _adjBizlog(Udr2mqBizCfg biz_cfg) {
		if (_logRec.getProc_status().startsWith("F"))
			return;

		String bizlog_key_cache_cnt = String.format(MdbConst.BIZLOG_KEY_CACHE_CNT, biz_cfg._typeId);
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = null;
		Transaction tx_ = null;
		String k = null;
		try {
			jedis_ = pool_.getResource();
			tx_ = jedis_.multi();
			k = _udrList.get(0).getBizlogLid();
			tx_.hset(k, MdbConst.BIZLOG_HK_LOG_RAW_FILE_NM, _logRec.getRaw_file_nm());
			tx_.hset(k, MdbConst.BIZLOG_HK_LOG_RCV_TM, PubMethod.Timestamp2Str(_logRec.getRcv_tm(), PubMethod.TimeStrFmt.Fmt14));
			tx_.hset(k, MdbConst.BIZLOG_HK_LOG_SHPARM_VER, _logRec.getShparm_ver());
			tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_CNT_TOT, _logRec.getTot_cnt());
			tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_CNT_FMT, _logRec.getFmt_err_cnt());
			tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_CNT_RAT, _logRec.getRat_err_cnt());
			tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_CNT_DUP, _logRec.getDup_cnt());
			tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_CNT_NML, _logRec.getNml_cnt());
			tx_.hincrBy(bizlog_key_cache_cnt, _logRec.getRcv_ymdh().toString(), _logRec.getTot_cnt());
			List<Object> response_ = tx_.exec();
			L.trace("[{}] {} bizlogs put", _logRec.getRaw_file_nm(), response_.size());
		} catch (Exception e) {
			_logRec.setProc_status(Udr2mqCfg.F1999_INTERNAL_ERR);
			_logRec.setErr_msg(PubMethod.FmtArgs("jedis exception, %s reject", _logRec.getProc_status()));
			_logRec.setException(e);
			L.warn("[{}] {}", _logRec.getRaw_file_nm(), _logRec.getErr_msg(), e);
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}

	private void _splitAndSend() throws Exception {
		int udr_cnt_ = 0;
		int callable_cnt_ = 0;
		int split_cnt_ = 0;
		UdrFmtMsg msg_ = null;
		KeyedMessage<String, String> topic_msg_ = null;
		List<Future<GsonStrObj>> future_list_ = new ArrayList<>();
		while (!_udrList.isEmpty()) {
			if (msg_ == null) {
				msg_ = new UdrFmtMsg();
				msg_.renewRndmId();
			}
			UdrFmt udr_ = _udrList.remove(0);
			msg_._udrFmtList.add(udr_);
			udr_cnt_++;
			if (msg_._udrFmtList.size() >= Udr2mqCfg._KafkaMsgSize || _udrList.isEmpty()) {
				GsonStrObjCallable callable_ = new GsonStrObjCallable(msg_._rndmId, msg_);
				Future<GsonStrObj> future_ = Udr2mqMain._ToJsonPool.submit(callable_);
				future_list_.add(future_);
				++callable_cnt_;
				_logRec.setSpare_num1((long) udr_cnt_);
				msg_ = null;
			}
		}
		L.trace("[{}] {} msg {} callables submit done", _logRec.getRaw_file_nm(), udr_cnt_, callable_cnt_);
		for (Future<GsonStrObj> future_ : future_list_) {
			GsonStrObj gson_str_obj_ = future_.get();
			//测试
			L.debug("To Kafka Message : {}|{}|{}", Udr2mqCfg._KafkaTopic, gson_str_obj_._id, gson_str_obj_._gsonStr);
			
//			topic_msg_ = new KeyedMessage<String, String>(Udr2mqCfg._KafkaTopic, gson_str_obj_._id, gson_str_obj_._gsonStr);

			ProducerRecord<String, String> producerRecord =
					new ProducerRecord<>(Udr2mqCfg._KafkaTopic, gson_str_obj_._id, gson_str_obj_._gsonStr);
			_producer.send(producerRecord);
			split_cnt_++;
			if (split_cnt_ % 100 == 0)
				L.trace("topic {}, split {}, {}", Udr2mqCfg._KafkaTopic, split_cnt_, gson_str_obj_._id);
		}
	}
}
