package com.hp.cmcc.bboss.app.erorcy;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbErorcyTaskRec extends GsonObj {
	public static final int STATUS_0_INIT = 0;
	public static final int STATUS_1_OK_WITH_FILE = 1;
	public static final int STATUS_2_OK_NO_FILE = 2;
	public static final int STATUS_3_FAILED = 3;

	private String inst_nm;
	private String biz_type;
	private Timestamp task_tm;
	private Integer file_type; // Apr/18/2016 removed from schema
	private String rcy_table;
	private Integer acnt_ym;
	private String rcy_condition;
	private Integer rcy_status;
	private Timestamp bak_tm;
	private String rcy_desc;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[8];
		o[0] = inst_nm;
		o[1] = biz_type;
		o[2] = task_tm;
		o[3] = rcy_table;
		o[4] = acnt_ym;
		o[5] = rcy_condition;
		o[6] = rcy_status;
		o[7] = rcy_desc;
		return o;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getBiz_type() {
		return biz_type;
	}

	public void setBiz_type(String biz_type) {
		this.biz_type = biz_type;
	}

	public Timestamp getTask_tm() {
		return task_tm;
	}

	public void setTask_tm(Timestamp task_tm) {
		this.task_tm = task_tm;
	}

	public Integer getFile_type() {
		return file_type;
	}

	public void setFile_type(Integer file_type) {
		this.file_type = file_type;
	}

	public String getRcy_table() {
		return rcy_table;
	}

	public void setRcy_table(String rcy_table) {
		this.rcy_table = rcy_table;
	}

	public Integer getAcnt_ym() {
		return acnt_ym;
	}

	public void setAcnt_ym(Integer acnt_ym) {
		this.acnt_ym = acnt_ym;
	}

	public String getRcy_condition() {
		return rcy_condition;
	}

	public void setRcy_condition(String rcy_condition) {
		this.rcy_condition = rcy_condition;
	}

	public Integer getRcy_status() {
		return rcy_status;
	}

	public void setRcy_status(Integer rcy_status) {
		this.rcy_status = rcy_status;
	}

	public Timestamp getBak_tm() {
		return bak_tm;
	}

	public void setBak_tm(Timestamp bak_tm) {
		this.bak_tm = bak_tm;
	}

	public String getRcy_desc() {
		return rcy_desc;
	}

	public void setRcy_desc(String rcy_desc) {
		this.rcy_desc = rcy_desc;
	}
}
