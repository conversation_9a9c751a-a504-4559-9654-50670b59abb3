package com.hp.cmcc.bboss.app.rmtdis;

import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.jcraft.jsch.SftpProgressMonitor;

public class RmtdisSftpGuage extends GsonObj implements SftpProgressMonitor {
	private static Logger L = LoggerFactory.getLogger(RmtdisSftpGuage.class);
	private static final int _GUAGE_BLOCK_SZ = 10000000;
	public DbRmtdisLogRec _logRec;
	public AtomicBoolean _terminateFlag;
	@Expose
	private int _opMode;
	@Expose
	private String _srcFnm;
	@Expose
	private String _dstFnm;
	@Expose
	private long _size;
	private long _trans;
	private AppTicker _ticker;

	public RmtdisSftpGuage() {
		_ticker = new AppTicker();
	}

	@Override
	public String toGsonStr() {
		Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().setDateFormat(GsonObj._DATE_TIME_FMT).create();
		return gson.toJson(this);
	}

	@Override
	public boolean count(long count) { // write log every _GUAGE_BLOCK_SZ or 10 percent
		long prev_trans_ = _trans;
		_trans += count;
		if (_trans == _size)
			return true;
		if (_terminateFlag != null && _terminateFlag.get()) {
			L.info("_terminateFlag detected");
			throw new RuntimeException("_terminateFlag detected, transfer force broken");
		}
		if (_trans > _size) {
			L.info("{} {} _trans {} gt _size {}", _logRec.getTrans_cfg_nm(), _logRec.getFile_nm(), _trans, _size);
			return true;
		}
		double prev_pct_ = (double) prev_trans_ / (double) _size;
		double curr_pct_ = (double) _trans / (double) _size;
		int prev_10pct_ = (int) (prev_pct_ * 10.0);
		int curr_10pct_ = (int) (curr_pct_ * 10.0);
		if (curr_10pct_ > prev_10pct_) {
			L.trace("{} {} {} bytes transfered, {}%", _logRec.getTrans_cfg_nm(), _logRec.getFile_nm(), _trans,
					(int) (curr_pct_ * 100));
			return true;
		}
		long prev_10m_ = prev_trans_ / _GUAGE_BLOCK_SZ;
		long curr_10m_ = _trans / _GUAGE_BLOCK_SZ;
		if (curr_10m_ > prev_10m_) {
			L.trace("{} {} {} bytes transfered, {}%", _logRec.getTrans_cfg_nm(), _logRec.getFile_nm(), _trans,
					PubMethod.FmtArgs("%.2f", curr_pct_ * 100.0));
			return true;
		}
		return true;
	}

	@Override
	public void end() {
		String op_mode_ = null;
		if (_opMode == SftpProgressMonitor.PUT) {
			op_mode_ = DbRmtdisTransCfgRec.TRANS_MODE_PUT;
		} else if (_opMode == SftpProgressMonitor.GET) {
			op_mode_ = DbRmtdisTransCfgRec.TRANS_MODE_GET;
		} else {
			op_mode_ = "ukn:" + _opMode;
		}
		_ticker.tickEnd((int) _trans);
		double pct_ = 0.0;
		if (_trans == _size) {
			pct_ = 1.0;
		} else if (_size != 0.0) {
			pct_ = _trans / _size;
		}
		pct_ *= 100.0;
		L.info("{} {} {}, {} --> {}, size:trans {}:{}, pct:pfm {}%:{}",
				new Object[] { _logRec.getTrans_cfg_nm(), _logRec.getFile_nm(), op_mode_, _srcFnm, _dstFnm, _size, _trans, pct_,
						_ticker._pfm });
	}

	@Override
	public void init(int op_mode, String src_fnm, String dst_fnm, long size) {
		_ticker.tickStart();
		_opMode = op_mode;
		_srcFnm = src_fnm;
		_dstFnm = dst_fnm;
		_size = size;
		_trans = 0;
		L.trace("{} {} {}", _logRec.getTrans_cfg_nm(), _logRec.getFile_nm(), this.toGsonStr());
	}
}
