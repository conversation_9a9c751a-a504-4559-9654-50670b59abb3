package com.hp.cmcc.bboss.app.erorcy;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class ErorcyOdb {
	private static Logger L = LoggerFactory.getLogger(ErorcyOdb.class);
	private static final String _Q_ERORCY_TASK = "SELECT INST_NM, BIZ_TYPE, TASK_TM, RCY_TABLE, ACNT_YM, RCY_CONDITION, "
			+ "RCY_STATUS, BAK_TM, RCY_DESC FROM ERORCY_TASK WHERE INST_NM = ? AND BIZ_TYPE IN (%s) ORDER BY TASK_TM";
	private static final String _I_ERORCY_TASK_HIS = "INSERT INTO ERORCY_TASK_HIS (INST_NM, BIZ_TYPE, TASK_TM, "
			+ "RCY_TABLE, ACNT_YM, RCY_CONDITION, RCY_STATUS, BAK_TM, RCY_DESC) VALUES (?, ?, ?, ?, ?, ?, ?, SYSDATE, ?)";
	private static final String _D_ERORCY_TASK = "DELETE FROM ERORCY_TASK WHERE INST_NM = ? AND BIZ_TYPE = ? AND TASK_TM = ?";
	private static final String _I_ERORCY_LOG = "INSERT INTO ERORCY_LOG (LOG_MM, RCY_FILE_NM, RCY_STATUS, RCY_CNT, "
			+ "RCY_FILE_SZ, BIZ_TYPE, TASK_TM, FILE_TYPE, RCY_TABLE, ACNT_YM, RCY_CONDITION, RCY_TM, TS_START, TS_END, "
			+ "RCY_DESC) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String _D_ERR_STTL_UDR = "DELETE FROM %s WHERE SUBSTR(E06_ACCT_DAY, 1, 6) = '%06d' AND %s";

	public List<DbErorcyTaskRec> getErorcyTask(String inst_nm, String biz_types) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		String sql_ = String.format(_Q_ERORCY_TASK, biz_types);
		List<DbErorcyTaskRec> db_result_ = cli_.queryForOList(sql_, DbErorcyTaskRec.class, inst_nm);
		if (db_result_ != null && !db_result_.isEmpty())
			L.trace("{} erorcy_task(s) scaned", db_result_.size());
		return db_result_;
	}

	public int delErrSttlUdr(Connection conn, String rcy_table, int acnt_ym, String rcy_condition) throws SQLException {
		String sql_ = String.format(_D_ERR_STTL_UDR, rcy_table, acnt_ym, rcy_condition);
		OdbCli cli_ = OdbAgt.GetUdrInstance();
		int rows_affected_ = 0;
		if (conn == null) {
			rows_affected_ = cli_.update(sql_);
		} else {
			rows_affected_ = cli_.update(conn, sql_);
		}
		L.debug("{} rows deleted, SQL: {}", rows_affected_, sql_);
		return rows_affected_;
	}

	public void bakErorcyTask(Connection conn, DbErorcyTaskRec task) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		int rows_inserted_ = cli_.update(conn, _I_ERORCY_TASK_HIS, task.asInsertObjArray());
		int rows_deleted_ = cli_.update(conn, _D_ERORCY_TASK, task.getInst_nm(), task.getBiz_type(), task.getTask_tm());
		L.debug("{} bak done, i:d {}:{}", task.toGsonStr(), rows_inserted_, rows_deleted_);
	}

	public void addErorcyLog(Connection conn, DbErorcyLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		if (conn == null) {
			cli_.update(_I_ERORCY_LOG, rec.asInsertObjArray());
			L.info("add {}", rec.toGsonStr());
		} else {
			cli_.update(conn, _I_ERORCY_LOG, rec.asInsertObjArray());
			L.info("transaction {}, add {}", conn.toString(), rec.toGsonStr());
		}
	}
}
