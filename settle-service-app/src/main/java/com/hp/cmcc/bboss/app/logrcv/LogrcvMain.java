package com.hp.cmcc.bboss.app.logrcv;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class LogrcvMain {
	private static Logger L = LoggerFactory.getLogger(LogrcvMain.class);

	public static void main(String[] args) throws Exception {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("logrcv", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _MainLoop() {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		L.info("started");
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}
			L.info("try stop normal");
			break;
		}
	}
}
