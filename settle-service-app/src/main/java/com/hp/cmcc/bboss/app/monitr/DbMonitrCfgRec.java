package com.hp.cmcc.bboss.app.monitr;

import java.io.File;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DbMonitrCfgRec extends GsonObj implements Comparable<DbMonitrCfgRec> {
	private static Logger L = LoggerFactory.getLogger(DbMonitrCfgRec.class);
	public Pattern _psPattern;
	private String inst_nm;
	private String prog_nm;
	private String os_user;
	private Integer is_jps;
	private String ps_pattern;
	private String start_cmd;
	private String stop_cmd;
	private Integer stop_delay;
	private Integer kill_times;
	private Integer kill_delay;
	private Integer shutdown_priority;
	private Integer enabled;

	public int compareTo(DbMonitrCfgRec obj) {
		if (this.shutdown_priority == null) {
			L.warn("this.shutdown_priority is null, can not compare {}", this.toGsonStr());
			return -1;
		}
		if (obj.getShutdown_priority() == null) {
			L.warn("obj.shutdown_priority is null, can not compare {}", obj.toGsonStr());
			return -1;
		}
		if (this.shutdown_priority.intValue() < obj.getShutdown_priority().intValue()) {
			return -1;
		} else if (this.shutdown_priority.intValue() > obj.getShutdown_priority().intValue()) {
			return 1;
		} else {
			return 0;
		}
	}

	public boolean validatePsPattern() {
		try {
			_psPattern = Pattern.compile(ps_pattern);
		} catch (Exception e) {
			L.warn("[{}] invalid ps_pattern [{}]", prog_nm, ps_pattern);
			_psPattern = null;
			return false;
		}
		return true;
	}

	public boolean validateStartCmd() {
		String start_ = PubMethod.SubStrByEnv(start_cmd);
		if (start_ == null) {
			L.warn("[{}] start_cmd SubStrByEnv [{}] failed", prog_nm, start_cmd);
			return false;
		}
		if (start_.equals("NULL"))
			return true;
		String[] cmd_array_ = start_.split(" ");
		File cmd_ = new File(cmd_array_[0]);
		if (!cmd_.isFile()) {
			L.warn("[{}] start_cmd [{}], cmd [{}] not exists", new Object[] { prog_nm, start_cmd, cmd_ });
			return false;
		}
		if (!cmd_.canExecute()) {
			L.warn("[{}] start_cmd [{}], cmd [{}] not executable", new Object[] { prog_nm, start_cmd, cmd_ });
			return false;
		}
		start_cmd = start_;
		return true;
	}

	public boolean validateStopCmd() {
		if (stop_cmd.equals("NULL") || stop_cmd.equals("KILL"))
			return true;
		String stop_ = PubMethod.SubStrByEnv(stop_cmd);
		if (stop_ == null) {
			L.warn("[{}] stop_cmd SubStrByEnv [{}] failed", prog_nm, stop_cmd);
			return false;
		}
		String[] cmd_array_ = stop_.split(" ");
		File cmd_ = new File(cmd_array_[0]);
		if (!cmd_.isFile()) {
			L.warn("[{}] stop_cmd [{}], cmd [{}] not exists", new Object[] { prog_nm, stop_cmd, cmd_ });
			return false;
		}
		if (!cmd_.canExecute()) {
			L.warn("[{}] stop_cmd [{}], cmd [{}] not executable", new Object[] { prog_nm, stop_cmd, cmd_ });
			return false;
		}
		stop_cmd = stop_;
		return true;
	}

	public boolean needStopOrKill() {
		if (stop_cmd == null)
			return false;
		if (stop_cmd.equals("NULL"))
			return false;
		if (shutdown_priority == null)
			return false;
		if (shutdown_priority >= 900)
			return false;
		return true;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getProg_nm() {
		return prog_nm;
	}

	public void setProg_nm(String prog_nm) {
		this.prog_nm = prog_nm;
	}

	public String getOs_user() {
		return os_user;
	}

	public void setOs_user(String os_user) {
		this.os_user = os_user;
	}

	public Integer getIs_jps() {
		return is_jps;
	}

	public void setIs_jps(Integer is_jps) {
		this.is_jps = is_jps;
	}

	public String getPs_pattern() {
		return ps_pattern;
	}

	public void setPs_pattern(String ps_pattern) {
		this.ps_pattern = ps_pattern;
	}

	public String getStart_cmd() {
		return start_cmd;
	}

	public void setStart_cmd(String start_cmd) {
		this.start_cmd = start_cmd;
	}

	public String getStop_cmd() {
		return stop_cmd;
	}

	public void setStop_cmd(String stop_cmd) {
		this.stop_cmd = stop_cmd;
	}

	public Integer getStop_delay() {
		return stop_delay;
	}

	public void setStop_delay(Integer stop_delay) {
		this.stop_delay = stop_delay;
	}

	public Integer getKill_times() {
		return kill_times;
	}

	public void setKill_times(Integer kill_times) {
		this.kill_times = kill_times;
	}

	public Integer getKill_delay() {
		return kill_delay;
	}

	public void setKill_delay(Integer kill_delay) {
		this.kill_delay = kill_delay;
	}

	public Integer getShutdown_priority() {
		return shutdown_priority;
	}

	public void setShutdown_priority(Integer shutdown_priority) {
		this.shutdown_priority = shutdown_priority;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}
}
