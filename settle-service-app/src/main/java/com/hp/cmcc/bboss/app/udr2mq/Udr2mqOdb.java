package com.hp.cmcc.bboss.app.udr2mq;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class Udr2mqOdb {
	private static Logger L = LoggerFactory.getLogger(Udr2mqOdb.class);
	private static final String _Q_UDR2MQ_LOG = "SELECT RAW_FILE_NM, RCV_MM, RCV_YMDH, RCV_TM, ACNT_YM, FILE_ID, "
			+ "FILE_TYPE, PP_FILE_ID, BIZ_TYPE, FILE_YMD, FILE_PROV, IS_ERCY, PROC_STATUS, SHPARM_VER, TOT_CNT, FMT_ERR_CNT, "
			+ "RAT_ERR_CNT, DUP_CNT, NML_CNT FROM UDR2MQ_LOG WHERE RAW_FILE_NM = ? AND PROC_STATUS NOT LIKE 'F%'";
	private static final String _I_UDR2MQ_LOG = "INSERT INTO UDR2MQ_LOG (RAW_FILE_NM, RCV_MM, RCV_YMDH, RCV_TM, "
			+ "ACNT_YM, FILE_ID, FILE_TYPE, PP_FILE_ID, BIZ_TYPE, FILE_YMD, FILE_PROV, IS_ERCY, PROC_STATUS, SHPARM_VER, "
			+ "TOT_CNT, FMT_ERR_CNT, RAT_ERR_CNT, DUP_CNT, NML_CNT, TS_START, TS_END, SPARE_NUM1) VALUES "
			+ "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String _U_UDR2MQ_LOG = "UPDATE UDR2MQ_LOG SET PROC_STATUS = SUBSTR(PROC_STATUS, 1, 1) || '1' "
			+ "|| SUBSTR(PROC_STATUS, 3), FMT_ERR_CNT = ?, RAT_ERR_CNT = ?, DUP_CNT = ?, NML_CNT = ?, BNM_TM = SYSDATE "
			+ "WHERE FILE_ID = ?";

	public DbUdr2mqLogRec getDupUdr2mqLog(String raw_file_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbUdr2mqLogRec> db_result_ = cli_.queryForOList(_Q_UDR2MQ_LOG, DbUdr2mqLogRec.class, raw_file_nm);
		if (db_result_ == null) {
			L.warn("query UDR2MQ_LOG for [{}] returns null, regard as non-exists", raw_file_nm);
			return null;
		}
		if (db_result_.isEmpty())
			return null;
		L.info("query UDR2MQ_LOG for [{}] returns {} normal records", raw_file_nm, db_result_.size());
		return db_result_.get(0);
	}

	public void addUdr2mqLog(DbUdr2mqLogRec rec) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		rec.setTs_end(System.currentTimeMillis());
		cli_.update(_I_UDR2MQ_LOG, rec.asInsertObjArray());
		L.info("add-addUdr2mqLog {}", rec.toGsonStr());
	}

	public void updUdr2mqLog(long file_id, int fmt_err_cnt, int rat_err_cnt, int dup_cnt, int nml_cnt) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		int rows_affected_ = cli_.update(_U_UDR2MQ_LOG, new Object[] { new Integer(fmt_err_cnt), new Integer(rat_err_cnt),
				new Integer(dup_cnt), new Integer(nml_cnt), new Long(file_id) });
		L.debug("upd {}, {} rows affected", file_id, rows_affected_);
	}
}
