package com.hp.cmcc.bboss.app.monitr;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class MonitrPsInfo extends GsonObj {
	public Timestamp _collectTm;
	public long _pid;
	public long _ppid;
	public String _user;
	public String _cwd;
	public Timestamp _startTm;
	public long _memResident;
	public long _memShare;
	public long _memSize;
	public long _cpuSys;
	public long _cpuUser;
	public long _cpuTotal;
	public String _name;
	public String _jps;
	public String _cmd;
}
