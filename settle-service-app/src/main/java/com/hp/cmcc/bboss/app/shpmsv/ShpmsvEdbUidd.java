package com.hp.cmcc.bboss.app.shpmsv;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.odb.entity.DbDomLdAreaCdProvRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbProvinceCdRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbServBizCodeRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbSttlLinearRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbSttlTierRateRec;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmsvEdbUidd extends ShpmsvEdb {
	private static Logger L = LoggerFactory.getLogger(ShpmsvEdbUidd.class);

	private static final String _C_TBL_PROV = "CREATE TABLE IF NOT EXISTS PROV (PROV_CD TEXT NOT NULL, CMCC_OPID TEXT NOT NULL, "
			+ "PROV_WL TEXT, PROV_NM TEXT NOT NULL, UNIQUE (PROV_CD) ON CONFLICT REPLACE)";
	private static final String _C_UX1_PROV = "CREATE UNIQUE INDEX IF NOT EXISTS UX1_PROV ON PROV (CMCC_OPID)";
	private static final String _I_PROV = "INSERT INTO PROV (PROV_CD, CMCC_OPID, PROV_WL, PROV_NM) VALUES (?, ?, ?, ?)";

	private static final String _C_TBL_AREACD = "CREATE TABLE IF NOT EXISTS AREACD (SN INTEGER NOT NULL, LD_AREA_CD TEXT NOT NULL, "
			+ "ZONE_CD INTEGER NOT NULL, PROV_CD INTEGER NOT NULL, LD_AREA_NM TEXT NOT NULL, EFF_TM TEXT NOT NULL, "
			+ "EXP_TM TEXT NOT NULL, UNIQUE (SN) ON CONFLICT REPLACE)";
	private static final String _C_IX1_AREACD = "CREATE INDEX IF NOT EXISTS IX1_AREACD ON AREACD (LD_AREA_CD)";
	private static final String _C_IX2_AREACD = "CREATE INDEX IF NOT EXISTS IX2_AREACD ON AREACD (ZONE_CD)";
	private static final String _I_AREACD = "INSERT INTO AREACD (SN, LD_AREA_CD, ZONE_CD, PROV_CD, LD_AREA_NM, EFF_TM, EXP_TM) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_SVBZCD = "CREATE TABLE IF NOT EXISTS SVBZCD (ID INTEGER NOT NULL, EC_CODE TEXT NOT NULL, "
			+ "SERV_CODE TEXT, BIZ_CODE TEXT, PROD_ORDER_ID TEXT NOT NULL, ORDER_ID TEXT, BIZ_CODE_APPLY INTEGER NOT NULL, "
			+ "PROV_CODE TEXT, PRODUCT_CODE TEXT, SERVICE_CODE TEXT, EFF_TM TEXT NOT NULL, EXP_TM TEXT NOT NULL, "
			+ "EC_GROUP_ID TEXT NOT NULL, SERVICE_TYPE TEXT NOT NULL, SEND_PROV TEXT, PROD_ORDER_MODE TEXT NOT NULL, "
			+ "SUB_GROUP_FLAG INTEGER NOT NULL, CARRY_TYPE TEXT, SIGN_ENTITY TEXT, PARENT_ORDER_ID TEXT, LOCON_FLAG INTEGER, "
			+ "ORDER_LEVEL INTEGER, UNIQUE (ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_SVBZCD = "CREATE INDEX IF NOT EXISTS IX1_SVBZCD ON SVBZCD (ORDER_ID)";
	private static final String _C_IX2_SVBZCD = "CREATE INDEX IF NOT EXISTS IX2_SVBZCD ON SVBZCD (SERV_CODE)";
	private static final String _I_SVBZCD = "INSERT INTO SVBZCD (ID, EC_CODE, SERV_CODE, BIZ_CODE, PROD_ORDER_ID, ORDER_ID, "
			+ "BIZ_CODE_APPLY, PROV_CODE, PRODUCT_CODE, SERVICE_CODE, EFF_TM, EXP_TM, EC_GROUP_ID, SERVICE_TYPE, SEND_PROV, "
			+ "PROD_ORDER_MODE, SUB_GROUP_FLAG, CARRY_TYPE, SIGN_ENTITY, PARENT_ORDER_ID, LOCON_FLAG, ORDER_LEVEL) VALUES "
			+ "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_LINEAR = "CREATE TABLE IF NOT EXISTS LINEAR (RATE_ID INTEGER NOT NULL, "
			+ "BIZ_TYPE TEXT NOT NULL, OFFER_CODE TEXT NOT NULL, PRODUCT_CODE TEXT NOT NULL, EC_CODE TEXT NOT NULL, "
			+ "AUX_KEY TEXT NOT NULL, RATE_OBJ INTEGER NOT NULL, RATE_UNIT INTEGER NOT NULL, RATE INTEGER NOT NULL, "
			+ "EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, MEMO TEXT, UNIQUE (RATE_ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_LINEAR = "CREATE INDEX IF NOT EXISTS IX1_LINEAR ON LINEAR "
			+ "(BIZ_TYPE, OFFER_CODE, PRODUCT_CODE, EC_CODE)";
	private static final String _I_LINEAR = "INSERT INTO LINEAR (RATE_ID, BIZ_TYPE, OFFER_CODE, PRODUCT_CODE, EC_CODE, "
			+ "AUX_KEY, RATE_OBJ, RATE_UNIT, RATE, EFF_DATE, EXP_DATE, MEMO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_TIER = "CREATE TABLE IF NOT EXISTS TIER (RATE_ID INTEGER NOT NULL, "
			+ "BIZ_TYPE TEXT NOT NULL, EC_CODE TEXT NOT NULL, AUX_KEY TEXT NOT NULL, RATE_OBJ INTEGER NOT NULL, "
			+ "RATE_UNIT INTEGER NOT NULL, RATE INTEGER NOT NULL, TIER_OBJ INTEGER NOT NULL, TIER_UNIT INTEGER NOT NULL, "
			+ "TIER_IDX INTEGER NOT NULL, TIER_MIN INTEGER NOT NULL, TIER_MAX INTEGER NOT NULL, EFF_DATE TEXT NOT NULL, "
			+ "EXP_DATE TEXT NOT NULL, IS_MONTHLY INTEGER NOT NULL, MEMO TEXT, UNIQUE (RATE_ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_TIER = "CREATE INDEX IF NOT EXISTS IX1_TIER ON TIER (BIZ_TYPE, EC_CODE)";
	private static final String _I_TIER = "INSERT INTO TIER (RATE_ID, BIZ_TYPE, EC_CODE, AUX_KEY, RATE_OBJ, RATE_UNIT, "
			+ "RATE, TIER_OBJ, TIER_UNIT, TIER_IDX, TIER_MIN, TIER_MAX, EFF_DATE, EXP_DATE, IS_MONTHLY, MEMO) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	public ShpmsvEdbUidd(ShpmsvOdb odb) {
		super(odb);
	}

	@Override
	public File encapsulateEdb(String shpm_ver) throws Exception {
		File fdb3_ = new File(ShpmsvCfg._WorkingDir + "/" + shpm_ver + ".db3");
		Edb edb_ = null;
		try {
			edb_ = new Edb(fdb3_.getAbsolutePath(), false);
			_initEdbSchema(edb_);
			_fillEdbProv(edb_);
			_fillEdbAreacd(edb_);
			_fillEdbSvbzcd(edb_);
			_fillEdbLinear(edb_);
			_fillEdbTier(edb_);
		} finally {
			if (edb_ != null)
				edb_.destroy();
		}
		return fdb3_;
	}

	private void _initEdbSchema(Edb edb) throws SQLException {
		_rdbCnt = 0;
		_edbCnt = 0;
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(_C_TBL_PROV);
			stmt_.executeUpdate(_C_UX1_PROV);
			stmt_.executeUpdate(_C_TBL_AREACD);
			stmt_.executeUpdate(_C_IX1_AREACD);
			stmt_.executeUpdate(_C_IX2_AREACD);
			stmt_.executeUpdate(_C_TBL_SVBZCD);
			stmt_.executeUpdate(_C_IX1_SVBZCD);
			stmt_.executeUpdate(_C_IX2_SVBZCD);
			stmt_.executeUpdate(_C_TBL_LINEAR);
			stmt_.executeUpdate(_C_IX1_LINEAR);
			stmt_.executeUpdate(_C_TBL_TIER);
			stmt_.executeUpdate(_C_IX1_TIER);
		} finally {
			Edb.Close(null, stmt_, null);
		}
	}

	private void _fillEdbProv(Edb edb) throws SQLException {
		List<DbProvinceCdRec> db_list_ = _odb.getProvinceCd();
		L.debug("{} records fetched from PROVINCE_CD", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_PROV);
			for (DbProvinceCdRec rec_ : db_list_) {
				pstmt_.setString(1, rec_.getProv_cd());
				pstmt_.setString(2, rec_.getCmcc_opid());
				pstmt_.setString(3, rec_.getProv_wl());
				pstmt_.setString(4, rec_.getProv_nm());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("PROV", null);
		L.debug("{} records inserted into PROV", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbAreacd(Edb edb) throws SQLException {
		List<DbDomLdAreaCdProvRec> db_list_ = _odb.getDomLdAreaCdProv();
		L.debug("{} records fetched from DOM_LD_AREA_CD_PROV", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_AREACD);
			for (DbDomLdAreaCdProvRec rec_ : db_list_) {
				pstmt_.setLong(1, rec_.getSn());
				pstmt_.setString(2, rec_.getLd_area_cd());
				pstmt_.setInt(3, Integer.parseInt(rec_.getLd_area_cd()));
				pstmt_.setInt(4, rec_.getProv_cd());
				pstmt_.setString(5, rec_.getLd_area_nm());
				pstmt_.setString(6, PubMethod.Timestamp2Str(rec_.getEff_tm(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(7, PubMethod.Timestamp2Str(rec_.getExp_tm(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("AREACD", null);
		L.debug("{} records inserted into AREACD", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbSvbzcd(Edb edb) throws SQLException {
		List<DbServBizCodeRec> db_list_ = _odb.getServBizCode(null);
		L.debug("{} records fetched from SERV_BIZ_CODE", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_SVBZCD);
			int idx_ = 0;
			for (DbServBizCodeRec rec_ : db_list_) {
				idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getId());
				pstmt_.setString(++idx_, rec_.getEc_code());
				pstmt_.setString(++idx_, rec_.getServ_code());
				pstmt_.setString(++idx_, rec_.getBiz_code());
				pstmt_.setString(++idx_, rec_.getProd_order_id());
				pstmt_.setString(++idx_, rec_.getOrder_id());
				pstmt_.setLong(++idx_, rec_.getBiz_code_apply());
				pstmt_.setString(++idx_, rec_.getProv_code());
				pstmt_.setString(++idx_, rec_.getProduct_code());
				pstmt_.setString(++idx_, rec_.getService_code());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEffective_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExpiry_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, rec_.getEc_group_id());
				pstmt_.setString(++idx_, rec_.getService_type());
				pstmt_.setString(++idx_, rec_.getSend_prov());
				pstmt_.setString(++idx_, rec_.getProd_order_mode());
				if (rec_.getSub_group_flag() == null) {
					pstmt_.setNull(++idx_, java.sql.Types.INTEGER);
				} else {
					pstmt_.setInt(++idx_, rec_.getSub_group_flag());
				}
				pstmt_.setString(++idx_, rec_.getCarry_type());
				pstmt_.setString(++idx_, rec_.getSign_entity());
				pstmt_.setString(++idx_, rec_.getParent_order_id());
				if (rec_.getLocon_flag() == null) {
					pstmt_.setNull(++idx_, java.sql.Types.INTEGER);
				} else {
					pstmt_.setInt(++idx_, rec_.getLocon_flag());
				}
				if (rec_.getOrder_level() == null) {
					pstmt_.setNull(++idx_, java.sql.Types.INTEGER);
				} else {
					pstmt_.setInt(++idx_, rec_.getOrder_level());
				}
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("SVBZCD", null);
		L.debug("{} records inserted into SVBZCD", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbLinear(Edb edb) throws SQLException {
		List<DbSttlLinearRateRec> db_list_ = _odb.getSttlLinearRate();
		L.debug("{} records fetched from STTL_LINEAR_RATE", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_LINEAR);
			int idx_ = 0;
			for (DbSttlLinearRateRec rec_ : db_list_) {
				idx_ = 0;
				pstmt_.setInt(++idx_, rec_.getRate_id());
				pstmt_.setString(++idx_, rec_.getBiz_type());
				pstmt_.setString(++idx_, rec_.getOffer_code());
				pstmt_.setString(++idx_, rec_.getProduct_code());
				pstmt_.setString(++idx_, rec_.getEc_code());
				pstmt_.setString(++idx_, rec_.getAux_key());
				pstmt_.setInt(++idx_, rec_.getRate_obj());
				pstmt_.setInt(++idx_, rec_.getRate_unit());
				pstmt_.setInt(++idx_, rec_.getRate());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, rec_.getMemo());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("LINEAR", null);
		L.debug("{} records inserted into LINEAR", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbTier(Edb edb) throws SQLException {
		List<DbSttlTierRateRec> db_list_ = _odb.getSttlTierRate();
		L.debug("{} records fetched from STTL_TIER_RATE", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_TIER);
			int idx_ = 0;
			for (DbSttlTierRateRec rec_ : db_list_) {
				idx_ = 0;
				pstmt_.setInt(++idx_, rec_.getRate_id());
				pstmt_.setString(++idx_, rec_.getBiz_type());
				pstmt_.setString(++idx_, rec_.getEc_code());
				pstmt_.setString(++idx_, rec_.getAux_key());
				pstmt_.setInt(++idx_, rec_.getRate_obj());
				pstmt_.setInt(++idx_, rec_.getRate_unit());
				pstmt_.setInt(++idx_, rec_.getRate());
				pstmt_.setInt(++idx_, rec_.getTier_obj());
				pstmt_.setInt(++idx_, rec_.getTier_unit());
				pstmt_.setInt(++idx_, rec_.getTier_idx());
				pstmt_.setLong(++idx_, rec_.getTier_min());
				pstmt_.setLong(++idx_, rec_.getTier_max());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setInt(++idx_, rec_.getIs_monthly());
				pstmt_.setString(++idx_, rec_.getMemo());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("TIER", null);
		L.debug("{} records inserted into TIER", edb_cnt_);
		_edbCnt += edb_cnt_;
	}
}
