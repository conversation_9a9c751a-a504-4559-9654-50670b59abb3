package com.hp.cmcc.bboss.app.locdis;

import java.io.File;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class LocdisArchRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(LocdisArchRunnable.class);
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public LocdisArchRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
				_interval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 3 seconds", e);
				PubMethod.Sleep(3000);
			}
		}
		L.info("thread end");
	}

	private void _exec() {
		Map<String, List<DbLocdisCfgRec>> arch_map_ = LocdisCfg._ArchYmCfgMap;
		_execArchMap(arch_map_);
		arch_map_ = LocdisCfg._ArchYmdCfgMap;
		_execArchMap(arch_map_);
	}

	private void _execArchMap(Map<String, List<DbLocdisCfgRec>> arch_map) {
		for (String k : arch_map.keySet()) {
			File src_dir_ = new File(k);
			if (!src_dir_.isDirectory()) {
				L.warn("[{}] dir not exists anymore", k);
				continue;
			}
			List<DbLocdisCfgRec> cfg_list_ = arch_map.get(k);
			_execSrcDir(src_dir_, cfg_list_);
		}
	}

	private void _execSrcDir(File src_dir, List<DbLocdisCfgRec> cfg_list) {
		File[] files_ = src_dir.listFiles();
		if (files_ == null || files_.length == 0)
			return;
		Arrays.sort(files_);
		List<File> file_list_ = new LinkedList<File>();
		for (File f : files_) {
			if (!f.isFile())
				continue;
			file_list_.add(f);
		}
		for (DbLocdisCfgRec cfg_ : cfg_list) {
			_execOneCfg(cfg_, file_list_);
		}
	}

	private void _execOneCfg(DbLocdisCfgRec cfg, List<File> file_list) {
		List<File> reserved_list_ = new LinkedList<File>();
		while (!file_list.isEmpty()) {
			File f = file_list.remove(0);
			if (cfg._patternIgn != null) {
				if (cfg._patternIgn.matcher(f.getName()).find()) {
					reserved_list_.add(f);
					//L.trace("{}, {} match PATTERN_IGN [{}]", cfg.getCfg_id(), f.getName(), cfg.getPattern_ign());
					continue;
				}
			}
			if (!cfg._patternMat.matcher(f.getName()).find()) {
				reserved_list_.add(f);
				//L.trace("{}, {} not match PATTERN_MAT [{}]", cfg.getCfg_id(), f.getName(), cfg.getPattern_mat());
				continue;
			}
			long now_ = System.currentTimeMillis();
			long modify_millis_ = f.lastModified();
			long diff_ = now_ - modify_millis_;
			if (diff_ < cfg.getDelay_dur() * 1000L) {
				reserved_list_.add(f);
				//L.trace("{}, {} diff {} lt delay {} * 1000", cfg.getCfg_id(), f.getName(), diff_, cfg.getDelay_dur());
				continue;
			}
			_archOneFile(f, cfg);
		}
		for (File f : reserved_list_)
			file_list.add(f);
	}

	private void _archOneFile(File f, DbLocdisCfgRec cfg) {
		long modify_millis_ = f.lastModified();
		String sub_dir_;
		if (cfg.getEnabled() == DbLocdisCfgRec.ENABLED_3_ARCH_YMD) {
			sub_dir_ = PubMethod.Long2Str(modify_millis_, PubMethod.TimeStrFmt.Fmt14).substring(0, 8);
		} else if (cfg.getEnabled() == DbLocdisCfgRec.ENABLED_2_ARCH_YM) {
			sub_dir_ = PubMethod.Long2Str(modify_millis_, PubMethod.TimeStrFmt.Fmt14).substring(0, 6);
		} else {
			L.warn("absurd!!! unsupported arch mode {}, {}, skip {}", cfg.getEnabled(), cfg.toGsonStr(), f.getAbsolutePath());
			return;
		}
		File arch_dir_ = new File(cfg.getDst_dir() + "/" + sub_dir_);
		if (!arch_dir_.isDirectory()) {
			if (!arch_dir_.mkdir()) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341011_ARCH_MKDIR_FAIL, PubMethod.FmtArgs(
						"mkdir [%s] error, skip [%s]", arch_dir_.getAbsolutePath(), f.getAbsolutePath()), cfg.toGsonStr());
				L.warn("{}", alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
				return;
			}
			L.debug("{}, mkdir [{}] done", cfg.getCfg_id(), arch_dir_.getAbsolutePath());
		}
		String arch_path_ = arch_dir_.getAbsolutePath() + "/" + f.getName();
		if (PubMethod.MoveAFile(f.getAbsolutePath(), arch_path_)) {
			L.info("{}, arch [{}] to [{}/] ok", cfg.getCfg_id(), f.getAbsolutePath(), arch_dir_.getAbsolutePath());
		} else {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_201341012_ARCH_FAIL, PubMethod.FmtArgs("arch [%s] to [%s/] error",
					f.getAbsolutePath(), arch_dir_.getAbsolutePath()), cfg.toGsonStr());
			L.warn("{}, {}", cfg.getCfg_id(), alm_.getAlm_msg());
			_odbUtils.addRawAlm(null, alm_);
		}
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		for (int i = 0; i < LocdisCfg._IntervalArch; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}
}
