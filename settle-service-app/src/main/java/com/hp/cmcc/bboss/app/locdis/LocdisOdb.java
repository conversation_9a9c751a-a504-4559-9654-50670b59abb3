package com.hp.cmcc.bboss.app.locdis;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class LocdisOdb {
	private static Logger L = LoggerFactory.getLogger(LocdisOdb.class);
	private static final String _Q_LOCDIS_CFG = "SELECT CFG_ID, INST_NM, SRC_DIR, PRIORITY, DST_DIR, BAK_DIR, PATTERN_MAT, "
			+ "PATTERN_IGN, DELAY_DUR, ACT_SCRIPT, ENABLED, LOG_FLAG FROM LOCDIS_CFG WHERE INST_NM = ? AND ENABLED = ? "
			+ "ORDER BY SRC_DIR, PRIORITY";
	private static final String _Q_BINMAN_CFG = "SELECT CFG_ID, INST_NM, SRC_DIR, PRIORITY, PATTERN_MAT, PATTERN_IGN, "
			+ "PATTERN_SUB, DELAY_DUR, ENABLED FROM BINMAN_CFG WHERE INST_NM = ? AND ENABLED = ? ORDER BY SRC_DIR, PRIORITY";
	private static final String _I_LOCDIS_LOG = "INSERT INTO LOCDIS_LOG (MMDD, INST_NM, FILE_NM, FILE_SZ, FILE_CKSUM, FILE_TM, "
			+ "DIS_TM, SRC_DIR, DST_DIR, DIS_STATUS, CFG_ID) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	public List<DbLocdisCfgRec> getLocdisCfg(String inst_nm, int enabled) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbLocdisCfgRec> result_ = cli_.queryForOList(_Q_LOCDIS_CFG, DbLocdisCfgRec.class, inst_nm, enabled);
		return result_;
	}

	public List<DbBinmanCfgRec> getBinmanCfg(String inst_nm, int enabled) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbBinmanCfgRec> result_ = cli_.queryForOList(_Q_BINMAN_CFG, DbBinmanCfgRec.class, inst_nm, enabled);
		return result_;
	}

	public boolean addLocdisLog(Connection conn, DbLocdisLogRec rec) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		try {
			if (conn == null) {
				cli_.updateWithThrow(_I_LOCDIS_LOG, rec.asInsertArray());
				L.info("add {}", rec.toGsonStr());
			} else {
				cli_.update(conn, _I_LOCDIS_LOG, rec.asInsertArray());
				L.info("transaction {}, add {}", conn.toString(), rec.asInsertArray());
			}
			return true;
		} catch (SQLException e) {
			if (conn == null) {
				L.warn("add {} exception", rec.toGsonStr(), e);
			} else {
				L.warn(PubMethod.FmtArgs("transaction %s, add %s exception", conn.toString(), rec.toGsonStr()), e);
			}
			return false;
		}
	}
}
