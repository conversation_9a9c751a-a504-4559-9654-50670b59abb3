package com.hp.cmcc.bboss.app.udr2mq;

import java.sql.Timestamp;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbUdr2mqLogRec extends GsonObj {
	@Expose
	private String raw_file_nm;
	@Expose
	private Integer rcv_mm;
	@Expose
	private Long rcv_ymdh;
	@Expose
	private Timestamp rcv_tm;
	@Expose
	private Integer acnt_ym;
	@Expose
	private Long file_id;
	@Expose
	private Integer file_type;
	@Expose
	private Long pp_file_id;
	@Expose
	private String biz_type;
	@Expose
	private Integer file_ymd;
	@Expose
	private Integer file_prov;
	@Expose
	private Integer is_ercy;
	@Expose
	private String proc_status;
	@Expose
	private String shparm_ver;
	@Expose
	private Integer tot_cnt;
	@Expose
	private Integer fmt_err_cnt;
	@Expose
	private Integer rat_err_cnt;
	@Expose
	private Integer dup_cnt;
	@Expose
	private Integer nml_cnt;
	@Expose
	private Long ts_start;
	@Expose
	private Long ts_end;
	@Expose
	private Long spare_num1;
	@Expose
	private Long spare_num2;
	@Expose
	private String spare_str1;
	@Expose
	private String spare_str2;
	private String err_msg;
	private Exception exception;

	@Override
	public String toGsonStr() {
		Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().setDateFormat(GsonObj._DATE_TIME_FMT).create();
		return gson.toJson(this);
	}

	public Object[] asInsertObjArray() {
		Object[] o = new Object[22];
		o[0] = raw_file_nm;
		o[1] = rcv_mm;
		o[2] = rcv_ymdh;
		o[3] = rcv_tm;
		o[4] = acnt_ym;
		o[5] = file_id;
		o[6] = file_type;
		o[7] = pp_file_id;
		o[8] = biz_type;
		o[9] = file_ymd;
		o[10] = file_prov;
		o[11] = is_ercy;
		o[12] = proc_status;
		o[13] = shparm_ver;
		o[14] = tot_cnt;
		o[15] = fmt_err_cnt;
		o[16] = rat_err_cnt;
		o[17] = dup_cnt;
		o[18] = nml_cnt;
		o[19] = ts_start;
		o[20] = ts_end;
		o[21] = spare_num1;
		return o;
	}

	public String getRaw_file_nm() {
		return raw_file_nm;
	}

	public void setRaw_file_nm(String raw_file_nm) {
		this.raw_file_nm = raw_file_nm;
	}

	public Integer getRcv_mm() {
		return rcv_mm;
	}

	public void setRcv_mm(Integer rcv_mm) {
		this.rcv_mm = rcv_mm;
	}

	public Long getRcv_ymdh() {
		return rcv_ymdh;
	}

	public void setRcv_ymdh(Long rcv_ymdh) {
		this.rcv_ymdh = rcv_ymdh;
	}

	public Timestamp getRcv_tm() {
		return rcv_tm;
	}

	public void setRcv_tm(Timestamp rcv_tm) {
		this.rcv_tm = rcv_tm;
	}

	public Integer getAcnt_ym() {
		return acnt_ym;
	}

	public void setAcnt_ym(Integer acnt_ym) {
		this.acnt_ym = acnt_ym;
	}

	public Long getFile_id() {
		return file_id;
	}

	public void setFile_id(Long file_id) {
		this.file_id = file_id;
	}

	public Integer getFile_type() {
		return file_type;
	}

	public void setFile_type(Integer file_type) {
		this.file_type = file_type;
	}

	public Long getPp_file_id() {
		return pp_file_id;
	}

	public void setPp_file_id(Long pp_file_id) {
		this.pp_file_id = pp_file_id;
	}

	public String getBiz_type() {
		return biz_type;
	}

	public void setBiz_type(String biz_type) {
		this.biz_type = biz_type;
	}

	public Integer getFile_ymd() {
		return file_ymd;
	}

	public void setFile_ymd(Integer file_ymd) {
		this.file_ymd = file_ymd;
	}

	public Integer getFile_prov() {
		return file_prov;
	}

	public void setFile_prov(Integer file_prov) {
		this.file_prov = file_prov;
	}

	public Integer getIs_ercy() {
		return is_ercy;
	}

	public void setIs_ercy(Integer is_ercy) {
		this.is_ercy = is_ercy;
	}

	public String getProc_status() {
		return proc_status;
	}

	public void setProc_status(String proc_status) {
		this.proc_status = proc_status;
	}

	public String getShparm_ver() {
		return shparm_ver;
	}

	public void setShparm_ver(String shparm_ver) {
		this.shparm_ver = shparm_ver;
	}

	public Integer getTot_cnt() {
		return tot_cnt;
	}

	public void setTot_cnt(Integer tot_cnt) {
		this.tot_cnt = tot_cnt;
	}

	public Integer getFmt_err_cnt() {
		return fmt_err_cnt;
	}

	public void setFmt_err_cnt(Integer fmt_err_cnt) {
		this.fmt_err_cnt = fmt_err_cnt;
	}

	public Integer getRat_err_cnt() {
		return rat_err_cnt;
	}

	public void setRat_err_cnt(Integer rat_err_cnt) {
		this.rat_err_cnt = rat_err_cnt;
	}

	public Integer getDup_cnt() {
		return dup_cnt;
	}

	public void setDup_cnt(Integer dup_cnt) {
		this.dup_cnt = dup_cnt;
	}

	public Integer getNml_cnt() {
		return nml_cnt;
	}

	public void setNml_cnt(Integer nml_cnt) {
		this.nml_cnt = nml_cnt;
	}

	public Long getTs_start() {
		return ts_start;
	}

	public void setTs_start(Long ts_start) {
		this.ts_start = ts_start;
	}

	public Long getTs_end() {
		return ts_end;
	}

	public void setTs_end(Long ts_end) {
		this.ts_end = ts_end;
	}

	public Long getSpare_num1() {
		return spare_num1;
	}

	public void setSpare_num1(Long spare_num1) {
		this.spare_num1 = spare_num1;
	}

	public Long getSpare_num2() {
		return spare_num2;
	}

	public void setSpare_num2(Long spare_num2) {
		this.spare_num2 = spare_num2;
	}

	public String getSpare_str1() {
		return spare_str1;
	}

	public void setSpare_str1(String spare_str1) {
		this.spare_str1 = spare_str1;
	}

	public String getSpare_str2() {
		return spare_str2;
	}

	public void setSpare_str2(String spare_str2) {
		this.spare_str2 = spare_str2;
	}

	public String getErr_msg() {
		return err_msg;
	}

	public void setErr_msg(String err_msg) {
		this.err_msg = err_msg;
	}

	public Exception getException() {
		return exception;
	}

	public void setException(Exception exception) {
		this.exception = exception;
	}
}
