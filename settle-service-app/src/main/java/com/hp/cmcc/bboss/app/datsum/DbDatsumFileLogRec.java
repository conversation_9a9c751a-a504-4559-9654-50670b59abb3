package com.hp.cmcc.bboss.app.datsum;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbDatsumFileLogRec extends GsonObj {
	private Integer log_mm;
	private String sum_file_nm;
	private String sum_file_pfx;
	private Integer sum_file_type;
	private Integer acnt_ym;
	private Integer sum_ymd;
	private Integer sum_file_seq;
	private String inst_nm;
	private String fmt_nm;
	private Long sum_lines;
	private Long raw_lines;
	private Long aud_val;
	private Long ts_start;
	private Long ts_end;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[14];
		o[0] = log_mm;
		o[1] = sum_file_nm;
		o[2] = sum_file_pfx;
		o[3] = sum_file_type;
		o[4] = acnt_ym;
		o[5] = sum_ymd;
		o[6] = sum_file_seq;
		o[7] = inst_nm;
		o[8] = fmt_nm;
		o[9] = sum_lines;
		o[10] = raw_lines;
		o[11] = aud_val;
		o[12] = ts_start;
		o[13] = ts_end;
		return o;
	}

	public Integer getLog_mm() {
		return log_mm;
	}

	public void setLog_mm(Integer log_mm) {
		this.log_mm = log_mm;
	}

	public String getSum_file_nm() {
		return sum_file_nm;
	}

	public void setSum_file_nm(String sum_file_nm) {
		this.sum_file_nm = sum_file_nm;
	}

	public String getSum_file_pfx() {
		return sum_file_pfx;
	}

	public void setSum_file_pfx(String sum_file_pfx) {
		this.sum_file_pfx = sum_file_pfx;
	}

	public Integer getSum_file_type() {
		return sum_file_type;
	}

	public void setSum_file_type(Integer sum_file_type) {
		this.sum_file_type = sum_file_type;
	}

	public Integer getAcnt_ym() {
		return acnt_ym;
	}

	public void setAcnt_ym(Integer acnt_ym) {
		this.acnt_ym = acnt_ym;
	}

	public Integer getSum_ymd() {
		return sum_ymd;
	}

	public void setSum_ymd(Integer sum_ymd) {
		this.sum_ymd = sum_ymd;
	}

	public Integer getSum_file_seq() {
		return sum_file_seq;
	}

	public void setSum_file_seq(Integer sum_file_seq) {
		this.sum_file_seq = sum_file_seq;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getFmt_nm() {
		return fmt_nm;
	}

	public void setFmt_nm(String fmt_nm) {
		this.fmt_nm = fmt_nm;
	}

	public Long getSum_lines() {
		return sum_lines;
	}

	public void setSum_lines(Long sum_lines) {
		this.sum_lines = sum_lines;
	}

	public Long getRaw_lines() {
		return raw_lines;
	}

	public void setRaw_lines(Long raw_lines) {
		this.raw_lines = raw_lines;
	}

	public Long getAud_val() {
		return aud_val;
	}

	public void setAud_val(Long aud_val) {
		this.aud_val = aud_val;
	}

	public Long getTs_start() {
		return ts_start;
	}

	public void setTs_start(Long ts_start) {
		this.ts_start = ts_start;
	}

	public Long getTs_end() {
		return ts_end;
	}

	public void setTs_end(Long ts_end) {
		this.ts_end = ts_end;
	}
}
