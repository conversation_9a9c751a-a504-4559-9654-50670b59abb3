package com.hp.cmcc.bboss.app.udruld;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbUdruldFileLogRec extends GsonObj {
	private String uld_file_nm;
	private Integer rcv_mm;
	private Long rcv_ymdh;
	private Integer acnt_ym;
	private Integer uld_status;
	private Timestamp uld_start_tm;
	private Timestamp uld_end_tm;
	private Integer uld_cnt;
	private Integer ercy_cnt;
	private Long sum_batch;
	private Long ts_start;
	private Long ts_end;
	private Long spare_num1;
	private Long spare_num2;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[14];
		o[0] = uld_file_nm;
		o[1] = rcv_mm;
		o[2] = rcv_ymdh;
		o[3] = acnt_ym;
		o[4] = uld_status;
		o[5] = uld_start_tm;
		o[6] = uld_end_tm;
		o[7] = uld_cnt;
		o[8] = ercy_cnt;
		o[9] = sum_batch;
		o[10] = ts_start;
		o[11] = ts_end;
		o[12] = spare_num1;
		o[13] = spare_num2;
		return o;
	}

	public String getUld_file_nm() {
		return uld_file_nm;
	}

	public void setUld_file_nm(String uld_file_nm) {
		this.uld_file_nm = uld_file_nm;
	}

	public Integer getRcv_mm() {
		return rcv_mm;
	}

	public void setRcv_mm(Integer rcv_mm) {
		this.rcv_mm = rcv_mm;
	}

	public Long getRcv_ymdh() {
		return rcv_ymdh;
	}

	public void setRcv_ymdh(Long rcv_ymdh) {
		this.rcv_ymdh = rcv_ymdh;
	}

	public Integer getAcnt_ym() {
		return acnt_ym;
	}

	public void setAcnt_ym(Integer acnt_ym) {
		this.acnt_ym = acnt_ym;
	}

	public Integer getUld_status() {
		return uld_status;
	}

	public void setUld_status(Integer uld_status) {
		this.uld_status = uld_status;
	}

	public Timestamp getUld_start_tm() {
		return uld_start_tm;
	}

	public void setUld_start_tm(Timestamp uld_start_tm) {
		this.uld_start_tm = uld_start_tm;
	}

	public Timestamp getUld_end_tm() {
		return uld_end_tm;
	}

	public void setUld_end_tm(Timestamp uld_end_tm) {
		this.uld_end_tm = uld_end_tm;
	}

	public Integer getUld_cnt() {
		return uld_cnt;
	}

	public void setUld_cnt(Integer uld_cnt) {
		this.uld_cnt = uld_cnt;
	}

	public Integer getErcy_cnt() {
		return ercy_cnt;
	}

	public void setErcy_cnt(Integer ercy_cnt) {
		this.ercy_cnt = ercy_cnt;
	}

	public Long getSum_batch() {
		return sum_batch;
	}

	public void setSum_batch(Long sum_batch) {
		this.sum_batch = sum_batch;
	}

	public Long getTs_start() {
		return ts_start;
	}

	public void setTs_start(Long ts_start) {
		this.ts_start = ts_start;
	}

	public Long getTs_end() {
		return ts_end;
	}

	public void setTs_end(Long ts_end) {
		this.ts_end = ts_end;
	}

	public Long getSpare_num1() {
		return spare_num1;
	}

	public void setSpare_num1(Long spare_num1) {
		this.spare_num1 = spare_num1;
	}

	public Long getSpare_num2() {
		return spare_num2;
	}

	public void setSpare_num2(Long spare_num2) {
		this.spare_num2 = spare_num2;
	}
}
