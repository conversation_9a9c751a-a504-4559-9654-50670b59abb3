package com.hp.cmcc.bboss.app.bizrcy;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.StrTruncator;

public class DbBizrcyLogRec extends GsonObj {
	private Integer log_mm;
	private Timestamp rcy_tm;
	private String dmp_file_nm;
	private Long udr_tot_cnt;
	private Long udr_ign_cnt;
	private Long udr_ini_cnt;
	private Long udr_rat_cnt;
	private Long udr_dup_cnt;
	private Long udr_err_cnt;
	private Long udr_rrt_cnt;
	private Long udr_rwk_cnt;
	private Long udr_bad_cnt;
	private String key_pattern;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[13];
		o[0] = log_mm;
		o[1] = rcy_tm;
		o[2] = dmp_file_nm;
		o[3] = udr_tot_cnt;
		o[4] = udr_ign_cnt;
		o[5] = udr_ini_cnt;
		o[6] = udr_rat_cnt;
		o[7] = udr_dup_cnt;
		o[8] = udr_err_cnt;
		o[9] = udr_rrt_cnt;
		o[10] = udr_rwk_cnt;
		o[11] = udr_bad_cnt;
		StrTruncator truncator_ = StrTruncator.GetStrTruncator("utf-8");
		o[12] = truncator_.truncate(key_pattern, 255);
		return o;
	}

	public Integer getLog_mm() {
		return log_mm;
	}

	public void setLog_mm(Integer log_mm) {
		this.log_mm = log_mm;
	}

	public Timestamp getRcy_tm() {
		return rcy_tm;
	}

	public void setRcy_tm(Timestamp rcy_tm) {
		this.rcy_tm = rcy_tm;
	}

	public String getDmp_file_nm() {
		return dmp_file_nm;
	}

	public void setDmp_file_nm(String dmp_file_nm) {
		this.dmp_file_nm = dmp_file_nm;
	}

	public Long getUdr_tot_cnt() {
		return udr_tot_cnt;
	}

	public void setUdr_tot_cnt(Long udr_tot_cnt) {
		this.udr_tot_cnt = udr_tot_cnt;
	}

	public Long getUdr_ign_cnt() {
		return udr_ign_cnt;
	}

	public void setUdr_ign_cnt(Long udr_ign_cnt) {
		this.udr_ign_cnt = udr_ign_cnt;
	}

	public Long getUdr_ini_cnt() {
		return udr_ini_cnt;
	}

	public void setUdr_ini_cnt(Long udr_ini_cnt) {
		this.udr_ini_cnt = udr_ini_cnt;
	}

	public Long getUdr_rat_cnt() {
		return udr_rat_cnt;
	}

	public void setUdr_rat_cnt(Long udr_rat_cnt) {
		this.udr_rat_cnt = udr_rat_cnt;
	}

	public Long getUdr_dup_cnt() {
		return udr_dup_cnt;
	}

	public void setUdr_dup_cnt(Long udr_dup_cnt) {
		this.udr_dup_cnt = udr_dup_cnt;
	}

	public Long getUdr_err_cnt() {
		return udr_err_cnt;
	}

	public void setUdr_err_cnt(Long udr_err_cnt) {
		this.udr_err_cnt = udr_err_cnt;
	}

	public Long getUdr_rrt_cnt() {
		return udr_rrt_cnt;
	}

	public void setUdr_rrt_cnt(Long udr_rrt_cnt) {
		this.udr_rrt_cnt = udr_rrt_cnt;
	}

	public Long getUdr_rwk_cnt() {
		return udr_rwk_cnt;
	}

	public void setUdr_rwk_cnt(Long udr_rwk_cnt) {
		this.udr_rwk_cnt = udr_rwk_cnt;
	}

	public Long getUdr_bad_cnt() {
		return udr_bad_cnt;
	}

	public void setUdr_bad_cnt(Long udr_bad_cnt) {
		this.udr_bad_cnt = udr_bad_cnt;
	}

	public String getKey_pattern() {
		return key_pattern;
	}

	public void setKey_pattern(String key_pattern) {
		this.key_pattern = key_pattern;
	}
}
