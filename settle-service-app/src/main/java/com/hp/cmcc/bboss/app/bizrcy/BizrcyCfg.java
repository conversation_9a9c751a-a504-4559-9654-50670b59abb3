package com.hp.cmcc.bboss.app.bizrcy;

import java.io.File;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class BizrcyCfg {
	private static Logger L = LoggerFactory.getLogger(BizrcyCfg.class);
	public static String _WorkingDir;
	public static String _DmpBakDir;
	public static long _DelaySeconds;
	public static Pattern _KeyPattern;
	public static String _KafkaTopicRaw;
	public static boolean _SimulationMode = false;

	public static boolean Init() {
		boolean rc_ = OdbSystemParam.GetInstance().refresh();
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}
		if (!_InitAppParam()) {
			L.warn("call _InitAppParam error");
			rc_ = false;
		}
		if (!_InitTopologyParam()) {
			L.warn("call _InitTopologyParam error");
			rc_ = false;
		}
		return rc_;
	}

	public static void Debug() {
		L.debug("_WorkingDir=[{}]", _WorkingDir);
		L.debug("_DmpBakDir=[{}]", _DmpBakDir);
		L.debug("_KeyPattern=[{}]", _KeyPattern.pattern());
		L.debug("_DelaySeconds={}", _DelaySeconds);
		L.debug("_KafkaTopicRaw={}", _KafkaTopicRaw);
		L.debug("_SimulationMode={}", _SimulationMode);
	}

	private static boolean _InitAppParam() {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(cmdline_._module, cmdline_._instNm);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", cmdline_._instNm);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", cmdline_._instNm);
			return false;
		}

		File fval_ = app_param_.chkValFile("COMMON", "WORKING_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_WorkingDir = fval_.getAbsolutePath();
		}

		fval_ = app_param_.chkValFile("COMMON", "DMP_BAK_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_DmpBakDir = fval_.getAbsolutePath();
		}

		Long lval_ = app_param_.chkValNum("COMMON", "DELAY_SECONDS", 600L, 999999999L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_DelaySeconds = lval_;
		}

		Pattern pval_ = app_param_.chkValPattern("COMMON", "KEY_PATTERN", null);
		if (pval_ == null) {
			rc_ = false;
		} else {
			_KeyPattern = pval_;
		}

		lval_ = app_param_.chkValNum("COMMON", "SIMULATION_MODE", 0L, 1L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_SimulationMode = lval_ == 1L ? true : false;
		}

		return rc_;
	}

	private static boolean _InitTopologyParam() {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		OdbAppParam app_param_ = new OdbAppParam();
		String inst_nm_ = "topology";
		boolean rc_ = app_param_.refresh(cmdline_._dbloginKey, inst_nm_);
		if (!rc_) {
			L.warn("fetch app_param [{},{}] error", cmdline_._dbloginKey, inst_nm_);
			return false;
		}

		String sval_ = app_param_.chkValStr("COMMON", "KAFKA_TOPIC_RAW", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_KafkaTopicRaw = sval_;
		}

		return rc_;
	}
}
