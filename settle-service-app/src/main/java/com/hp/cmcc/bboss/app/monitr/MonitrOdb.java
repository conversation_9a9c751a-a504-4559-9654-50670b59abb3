package com.hp.cmcc.bboss.app.monitr;

import java.util.List;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class MonitrOdb {
	private static final String _Q_MONITR_CFG = "SELECT INST_NM, PROG_NM, OS_USER, IS_JPS, PS_PATTERN, START_CMD, STOP_CMD, "
			+ "STOP_DELAY, KILL_TIMES, KILL_DELAY, SHUTDOWN_PRIORITY, ENABLED FROM MONITR_CFG WHERE INST_NM = ? AND ENABLED = 1";

	public List<DbMonitrCfgRec> getMonitrCfg(String inst_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbMonitrCfgRec> result_ = cli_.queryForOList(_Q_MONITR_CFG, DbMonitrCfgRec.class, inst_nm);
		return result_;
	}
}
