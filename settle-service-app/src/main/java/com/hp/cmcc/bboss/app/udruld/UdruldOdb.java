package com.hp.cmcc.bboss.app.udruld;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class UdruldOdb {
	private static Logger L = LoggerFactory.getLogger(UdruldOdb.class);
	private static final String _Q_UDRULD_FMT_CFG = "SELECT B.TYPE_ID, A.BIZ_TYPE, A.AUX_KEY, A.ULD_IDX, A.ULD_MIMONI, "
			+ "A.IS_NUM, A.MAX_LEN, A.DFT_VAL, A.SRC_TYPE, A.SRC_IDX, A.EVAL FROM UDRULD_FMT_CFG A, BIZ_TYPE_DEF B "
			+ "WHERE A.BIZ_TYPE = B.BIZ_TYPE AND B.ENABLED = 1 ORDER BY A.BIZ_TYPE, A.AUX_KEY, A.ULD_IDX";
	private static final String _Q_UDRULD_FILE_LOG = "SELECT ULD_FILE_NM, RCV_MM, RCV_YMDH, ACNT_YM, ULD_STATUS, "
			+ "ULD_START_TM, ULD_END_TM, ULD_CNT, ERCY_CNT, SUM_BATCH, TS_START, TS_END, SPARE_NUM1, SPARE_NUM2 "
			+ "FROM UDRULD_FILE_LOG WHERE RCV_MM = ? AND RCV_YMDH = ? AND ULD_FILE_NM = ?";
	private static final String _I_UDRULD_FILE_LOG = "INSERT INTO UDRULD_FILE_LOG (ULD_FILE_NM, RCV_MM, RCV_YMDH, ACNT_YM, "
			+ "ULD_STATUS, ULD_START_TM, ULD_END_TM, ULD_CNT, ERCY_CNT, SUM_BATCH, TS_START, TS_END, SPARE_NUM1, SPARE_NUM2) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String _D_UDRULD_FILE_LOG = "DELETE FROM UDRULD_FILE_LOG WHERE RCV_MM = ? "
			+ "AND RCV_YMDH = ? AND ULD_FILE_NM = ?";
	private static final String _U_UDRULD_FILE_LOG = "UPDATE UDRULD_FILE_LOG SET ULD_STATUS = ?, ULD_END_TM = ?, "
			+ "ULD_CNT = ?, ERCY_CNT = ?, TS_END = ?, SPARE_NUM1 = ?, SPARE_NUM2 = ? "
			+ "WHERE RCV_MM = ? AND RCV_YMDH = ? AND ULD_FILE_NM = ?";
	private static final String _I_UDRULD_PROC_LOG = "INSERT INTO UDRULD_PROC_LOG (ULD_FILE_NM, RCV_MM, RCV_YMDH, ACNT_YM, "
			+ "FILE_ID, ORG_FILE_ID, PP_FILE_ID, ULD_CNT, ERCY_CNT, SPARE_NUM1, SPARE_NUM2) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	public Map<String, List<DbUdruldFmtCfgRec>> getUldFmtCfg() {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbUdruldFmtCfgRec> result_ = cli_.queryForOList(_Q_UDRULD_FMT_CFG, DbUdruldFmtCfgRec.class);
		if (result_ == null) {
			L.warn("query UDRULD_FMT_CFG returns null");
			return null;
		}
		L.debug("{} records fetched from UDRULD_FMT_CFG", result_.size());
		Map<String, List<DbUdruldFmtCfgRec>> map_ = new HashMap<String, List<DbUdruldFmtCfgRec>>();
		for (DbUdruldFmtCfgRec rec_ : result_) {
			String key_ = String.format("%03d:%s", rec_.getType_id(), rec_.getAux_key());
			List<DbUdruldFmtCfgRec> val_ = map_.get(key_);
			if (val_ == null) {
				val_ = new ArrayList<DbUdruldFmtCfgRec>();
				map_.put(String.format("%03d:%s", rec_.getType_id(), rec_.getAux_key()), val_);
			}
			val_.add(rec_);
		}
		return map_;
	}

	public DbUdruldFileLogRec getUldFileLog(int rcv_mm, long rcv_ymdh, String uld_file_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		DbUdruldFileLogRec result_ = cli_.queryForObject(_Q_UDRULD_FILE_LOG, DbUdruldFileLogRec.class, rcv_mm, rcv_ymdh,
				uld_file_nm);
		return result_;
	}

	public void addUldFileLog(Connection conn, DbUdruldFileLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		if (conn == null) {
			cli_.update(_I_UDRULD_FILE_LOG, rec.asInsertObjArray());
			L.info("add {}", rec.toGsonStr());
		} else {
			cli_.update(conn, _I_UDRULD_FILE_LOG, rec.asInsertObjArray());
			L.info("transaction {}, add {}", conn.toString(), rec.toGsonStr());
		}
	}

	public int delUldFileLog(DbUdruldFileLogRec rec) { // useless method, can be removed
		OdbCli cli_ = OdbAgt.GetBizInstance();
		int rows_affected_ = cli_.update(_D_UDRULD_FILE_LOG, rec.getRcv_mm(), rec.getRcv_ymdh(), rec.getUld_file_nm());
		L.info("del {}, rows affected {}", rec.toGsonStr(), rows_affected_);
		return rows_affected_;
	}

	public void updUldFileLog(Connection conn, DbUdruldFileLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		Object[] o = new Object[10];
		o[0] = rec.getUld_status();
		o[1] = rec.getUld_end_tm();
		o[2] = rec.getUld_cnt();
		o[3] = rec.getErcy_cnt();
		o[4] = rec.getTs_end();
		o[5] = rec.getSpare_num1();
		o[6] = rec.getSpare_num2();
		o[7] = rec.getRcv_mm();
		o[8] = rec.getRcv_ymdh();
		o[9] = rec.getUld_file_nm();
		if (conn == null) {
			cli_.update(_U_UDRULD_FILE_LOG, o);
			L.info("upd {}", rec.toGsonStr());
		} else {
			cli_.update(conn, _U_UDRULD_FILE_LOG, o);
			L.info("transaction{}, upd {}", conn.toString(), rec.toGsonStr());
		}
	}

	public void addUldProcLog(Connection conn, DbUdruldProcLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		if (conn == null) {
			cli_.update(_I_UDRULD_PROC_LOG, rec.asInsertObjArray());
			L.info("add {}", rec.toGsonStr());
		} else {
			cli_.update(conn, _I_UDRULD_PROC_LOG, rec.asInsertObjArray());
			L.info("transaction {}, add {}", conn.toString(), rec.toGsonStr());
		}
	}
}
