package com.hp.cmcc.bboss.app.shpmag;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class ShpmagCfg {
	private static Logger L = LoggerFactory.getLogger(ShpmagCfg.class);
	public static OdbSystemParam _SystemParam;
	public static int _RestPort;
	public static String _WorkingDir;
	public static String _ShpmBaseDir;

	public static boolean Init() {
		String inst_nm_ = AppCmdline.GetInstance()._instNm;
		_SystemParam = OdbSystemParam.GetInstance();
		boolean rc_ = _SystemParam.refresh();
		if (!rc_) {
			L.warn("init system param error");
			return false;
		}

		OdbAppParam app_param_ = new OdbAppParam();
		rc_ = app_param_.refresh("shpmag", inst_nm_);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		File fval_ = app_param_.chkValFile("COMMON", "WORKING_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_WorkingDir = fval_.getAbsolutePath();
		}

		Long lval_ = app_param_.chkValNum("COMMON", "REST_PORT", 0L, 65535L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_RestPort = lval_.intValue();
		}

		inst_nm_ = "topology";
		if (!app_param_.refresh(AppCmdline.GetInstance()._dbloginKey, inst_nm_)) {
			L.warn("fetch app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		if (!app_param_.subValByEnv()) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		fval_ = app_param_.chkValFile("COMMON", "SHPM_BASE_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_ShpmBaseDir = fval_.getAbsolutePath();
		}

		return rc_;
	}

	public static void Trace() {
		if (_SystemParam != null) {
			L.debug("_SttlSystemParam: {}", _SystemParam.toString());
		} else {
			L.debug("_SttlSystemParam is null");
		}
		L.debug("_RestPort={}", _RestPort);
		L.debug("_WorkingDir=[{}]", _WorkingDir);
		L.debug("_ShpmBaseDir=[{}]", _ShpmBaseDir);
	}
}
