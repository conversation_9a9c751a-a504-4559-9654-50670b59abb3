package com.hp.cmcc.bboss.app.bizrcy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;

public class BizrcyMain {
	private static Logger L = LoggerFactory.getLogger(BizrcyMain.class);

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("bizrcy", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 2, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!BizrcyCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		BizrcyCfg.Debug();
		if (!MdbAgt.Init(OdbSystemParam.GetInstance(), 0, 2, 0)) {
			L.error("init mdb connection error, JVM exit");
			System.exit(1);
		}
	}

	private static void _MainLoop() throws Exception {
		BizrcyMgr mgr_ = new BizrcyMgr();
		mgr_.run();
	}
}
