package com.hp.cmcc.bboss.app.locdis;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbLocdisLogRec extends GsonObj {
	public static final int DIS_STATUS_0_SUCCESS = 0;
	public static final int DIS_STATUS_1_MOVE_ERROR = 1;
	public static final int DIS_STATUS_2_ACT_SCRIPT_ERROR = 2;

	private Integer mmdd;
	private String inst_nm;
	private String file_nm;
	private Long file_sz;
	private Long file_cksum;
	private Timestamp file_tm;
	private Timestamp dis_tm;
	private String src_dir;
	private String dst_dir;
	private Integer dis_status;
	private Integer cfg_id;

	public Object[] asInsertArray() {
		Object[] o = new Object[11];
		o[0] = mmdd;
		o[1] = inst_nm;
		o[2] = file_nm;
		o[3] = file_sz;
		o[4] = file_cksum;
		o[5] = file_tm;
		o[6] = dis_tm;
		o[7] = src_dir;
		o[8] = dst_dir;
		o[9] = dis_status;
		o[10] = cfg_id;
		return o;
	}

	public Integer getMmdd() {
		return mmdd;
	}

	public void setMmdd(Integer mmdd) {
		this.mmdd = mmdd;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getFile_nm() {
		return file_nm;
	}

	public void setFile_nm(String file_nm) {
		this.file_nm = file_nm;
	}

	public Long getFile_sz() {
		return file_sz;
	}

	public void setFile_sz(Long file_sz) {
		this.file_sz = file_sz;
	}

	public Long getFile_cksum() {
		return file_cksum;
	}

	public void setFile_cksum(Long file_cksum) {
		this.file_cksum = file_cksum;
	}

	public Timestamp getFile_tm() {
		return file_tm;
	}

	public void setFile_tm(Timestamp file_tm) {
		this.file_tm = file_tm;
	}

	public Timestamp getDis_tm() {
		return dis_tm;
	}

	public void setDis_tm(Timestamp dis_tm) {
		this.dis_tm = dis_tm;
	}

	public String getSrc_dir() {
		return src_dir;
	}

	public void setSrc_dir(String src_dir) {
		this.src_dir = src_dir;
	}

	public String getDst_dir() {
		return dst_dir;
	}

	public void setDst_dir(String dst_dir) {
		this.dst_dir = dst_dir;
	}

	public Integer getDis_status() {
		return dis_status;
	}

	public void setDis_status(Integer dis_status) {
		this.dis_status = dis_status;
	}

	public Integer getCfg_id() {
		return cfg_id;
	}

	public void setCfg_id(Integer cfg_id) {
		this.cfg_id = cfg_id;
	}
}
