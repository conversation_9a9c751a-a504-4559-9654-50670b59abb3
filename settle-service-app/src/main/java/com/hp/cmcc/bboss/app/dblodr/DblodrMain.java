package com.hp.cmcc.bboss.app.dblodr;

import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.GzipRunnable;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DblodrMain {
	private static Logger L = LoggerFactory.getLogger(DblodrMain.class);
	static BlockingQueue<String> _GzipTaskQueue = new LinkedBlockingQueue<String>();
	private static GzipRunnable _GzipRunnable = null;
	private static Thread _GzipThread = null;
	private static List<DblodrBizRunnable> _RunnableList = new ArrayList<DblodrBizRunnable>();
	private static List<Thread> _ThreadList = new ArrayList<Thread>();

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("dblodr", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 10, 0, 1)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!DblodrCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		DblodrCfg.Debug();

		for (Entry<String, List<DblodrCtlSec>> e : DblodrCfg._ThreadGrpMap.entrySet()) {
			DblodrBizRunnable runnable_ = new DblodrBizRunnable(e.getValue());
			_RunnableList.add(runnable_);
			_ThreadList.add(new Thread(runnable_, e.getKey()));
		}
		L.info("{} biz threads created", _ThreadList.size());
		for (Thread t : _ThreadList) {
			t.start();
		}

		GzipRunnable runnable_ = new GzipRunnable(_GzipTaskQueue, OdbSystemParam.GetInstance()._gzipPath, true);
		Thread thr_ = new Thread(runnable_, "GZIP");
		thr_.start();
		_GzipRunnable = runnable_;
		_GzipThread = thr_;
	}

	private static void _MainLoop() throws InterruptedException {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}

			L.info("try stop normal");
			for (DblodrBizRunnable r : _RunnableList) {
				r.setTerminateFlag();
			}
			L.debug("{} biz threads to be joined", _ThreadList.size());
			for (Thread t : _ThreadList) {
				L.debug("try join thrad {}", t.getName());
				t.join();
				L.debug("thread {} joined", t.getName());
			}

			if (_GzipRunnable != null) {
				_GzipRunnable.setTerminateFlag();
			}
			L.debug("try join thread {}", _GzipThread.getName());
			_GzipThread.join();
			L.debug("thread {} joined", _GzipThread.getName());
			break;
		}
	}
}
