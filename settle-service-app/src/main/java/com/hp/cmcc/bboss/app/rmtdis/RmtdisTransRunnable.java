package com.hp.cmcc.bboss.app.rmtdis;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.Vector;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PatternFileFilter;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.SftpATTRS;

public class RmtdisTransRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(RmtdisTransRunnable.class);
	private AtomicBoolean _terminateFlag = new AtomicBoolean(false);
	private Map<String, DbRmtdisTransCfgRec> _transCfgMap;
	private Map<String, Long> _transTimeMap;
	private Map<String, RmtdisSftpCli> _cliMap;
	private RmtdisOdb _odb;
	private OdbUtils _odbUtils;
	private RmtdisSftpGuage _guage;

	public RmtdisTransRunnable(List<DbRmtdisTransCfgRec> trans_cfg_list) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_transCfgMap = new TreeMap<String, DbRmtdisTransCfgRec>();
		_transTimeMap = new TreeMap<String, Long>();
		_cliMap = new TreeMap<String, RmtdisSftpCli>();
		for (DbRmtdisTransCfgRec rec_ : trans_cfg_list) {
			_transCfgMap.put(rec_.getTrans_cfg_nm(), rec_);
			_transTimeMap.put(rec_.getTrans_cfg_nm(), 0L);
			_cliMap.put(rec_.getTrans_cfg_nm(), new RmtdisSftpCli());
		}
		_odb = new RmtdisOdb();
		_odbUtils = new OdbUtils();
		_guage = new RmtdisSftpGuage();
		_guage._terminateFlag = _terminateFlag;
	}

	public void setTerminateFlag() {
		_terminateFlag.set(true);
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag.get()) {
			try {
				_interval();
				_exec();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 3 seconds", e);
				PubMethod.Sleep(3000);
			}
		}
		L.info("thread end");
	}

	private void _exec() {
		for (DbRmtdisTransCfgRec trans_cfg_ : _transCfgMap.values()) {
			long now_ = System.currentTimeMillis();
			long next_act_ts_ = _transTimeMap.get(trans_cfg_.getTrans_cfg_nm()) + trans_cfg_.getMin_interval() * 1000L;
			if (now_ > next_act_ts_) {
				_execCfg(trans_cfg_);
			}
		}
	}

	private void _execCfg(DbRmtdisTransCfgRec trans_cfg) {
		try {
			if (DbRmtdisTransCfgRec.TRANS_MODE_PUT.equals(trans_cfg.getTrans_mode())) {
				_execPut(trans_cfg);
			} else if (DbRmtdisTransCfgRec.TRANS_MODE_GET.equals(trans_cfg.getTrans_mode())) {
				_execGet(trans_cfg);
			} else {
				L.warn("{} ukn trans_mode [{}]", trans_cfg.getTrans_cfg_nm(), trans_cfg.getTrans_mode());
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341001_TRANS_EXCEPTION,
					PubMethod.FmtArgs("%s encounter exception", trans_cfg.getTrans_cfg_nm()), trans_cfg.toGsonStr());
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
		} finally {
			_transTimeMap.put(trans_cfg.getTrans_cfg_nm(), System.currentTimeMillis());
		}
	}

	private void _execGet(DbRmtdisTransCfgRec trans_cfg) throws Exception {
		DbRmtdisLoginCfgRec login_cfg_ = RmtdisCfg._LoginCfgMap.get(trans_cfg.getLogin_cfg_nm());
		RmtdisSftpCli cli_ = _cliMap.get(trans_cfg.getTrans_cfg_nm());
		if (cli_._sftp == null || !cli_._sftp.isConnected())
			cli_.login(login_cfg_);
		if (trans_cfg.getRmt_bak_dir() != null) {
			cli_._sftp.cd(trans_cfg.getRmt_bak_dir());
			// L.trace("{} verify RMT_BAK_DIR [{}] ok", trans_cfg.getTrans_cfg_nm(), trans_cfg.getRmt_bak_dir());
		}
		cli_._sftp.cd(trans_cfg.getRmt_dir());
		// L.trace("{} cd RMT_DIR [{}] ok", trans_cfg.getTrans_cfg_nm(), trans_cfg.getRmt_dir());
		@SuppressWarnings("unchecked")
		Vector<LsEntry> scaned_ = cli_._sftp.ls(".");
		for (LsEntry ls_entry_ : scaned_) {
			if (_terminateFlag.get())
				break;
			if (!ls_entry_.getAttrs().isReg())
				continue;
			if (trans_cfg._patternIgn != null && trans_cfg._patternIgn.matcher(ls_entry_.getFilename()).find())
				continue;
			if (!trans_cfg._patternMat.matcher(ls_entry_.getFilename()).find())
				continue;
			if (!cli_._sftp.isConnected()) {
				L.warn("{} connection lost while preparing GET {}", trans_cfg.getTrans_cfg_nm(), ls_entry_.getFilename());
				break;
			}
			_getOneFile(trans_cfg, cli_, ls_entry_);
		}
	}

	private void _getOneFile(DbRmtdisTransCfgRec trans_cfg, RmtdisSftpCli cli, LsEntry ls_entry) {
		DbRmtdisLogRec log_ = _initLogRecGet(trans_cfg, cli, ls_entry);
		_guage._logRec = log_;
		String loc_dst_ = trans_cfg.getLoc_dir() + "/" + log_.getFile_nm();
		String loc_tmp_ = trans_cfg.getTmpFnm(log_.getFile_nm());
		String loc_get_ = null;
		if (trans_cfg.getLoc_tmp_dir() != null) {
			loc_get_ = trans_cfg.getLoc_tmp_dir() + "/" + loc_tmp_;
		} else {
			loc_get_ = trans_cfg.getLoc_dir() + "/" + loc_tmp_;
		}
		try {
			cli._sftp.get(log_.getFile_nm(), loc_get_, _guage, ChannelSftp.OVERWRITE);
			File loc_fget_ = new File(loc_get_);
			if (!loc_fget_.isFile()) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341022_GET_LOC_NONEXISTS,
						PubMethod.FmtArgs("[%s] GET done but [%s] not exists", log_.getFile_nm(), loc_get_), trans_cfg.toGsonStr());
				L.warn("{} {}", trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg());
				log_.setTrans_status(DbRmtdisLogRec._TRANS_STATUS_1_FAIL);
				log_.setErr_msg(alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
				return;
			}
			log_.setLoc_modify_tm(new Timestamp(loc_fget_.lastModified()));
			if (ls_entry.getAttrs().getSize() != loc_fget_.length()) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341023_GET_SIZE_NE,
						PubMethod.FmtArgs("[%s] GET verify size rmt ne loc %d:%d", log_.getFile_nm(), ls_entry.getAttrs().getSize(),
								loc_fget_.length()),
						trans_cfg.toGsonStr());
				L.warn("{} {}", trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg());
				log_.setTrans_status(DbRmtdisLogRec._TRANS_STATUS_1_FAIL);
				log_.setErr_msg(alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
				return;
			}
			L.debug("{} {} GET verify size rmt eq loc {}:{}, mtime {} {}",
					new Object[] { trans_cfg.getTrans_cfg_nm(), log_.getFile_nm(), ls_entry.getAttrs().getSize(),
							loc_fget_.length(), loc_fget_.lastModified(),
							PubMethod.Timestamp2Str(log_.getLoc_modify_tm(), PubMethod.TimeStrFmt.Fmt23) });
			log_.setFile_cksum(PubMethod.FileCksum(loc_fget_.getAbsolutePath()));
			if (trans_cfg.getRmt_bak_dir() == null) {
				cli._sftp.rm(log_.getFile_nm());
				L.info("{} {} rmt del done", trans_cfg.getTrans_cfg_nm(), log_.getFile_nm());
			} else {
				String rmt_dst_ = trans_cfg.getRmt_bak_dir() + "/" + log_.getFile_nm();
				cli._sftp.rename(log_.getFile_nm(), rmt_dst_);
				L.info("{} {} rmt bak done, {}", trans_cfg.getRmt_bak_dir(), log_.getFile_nm(), rmt_dst_);
			}
			if (PubMethod.MoveAFile(loc_get_, loc_dst_)) {
				L.info("{} {} loc mv done, {}, {}", trans_cfg.getTrans_cfg_nm(), log_.getFile_nm(), loc_get_, loc_dst_);
			} else {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341024_GET_LOC_MOV_FAIL,
						PubMethod.FmtArgs("[%s] GET loc mv [%s] --> [%s] error, pls chk", log_.getFile_nm(), loc_get_, loc_dst_),
						trans_cfg.toGsonStr());
				L.warn("{} {}", trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg());
				log_.setErr_msg(alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341021_GET_EXCEPTION,
					PubMethod.FmtArgs("GET [%s] exception", log_.getFile_nm()), trans_cfg.toGsonStr());
			alm_.setAlm_kpi(e.getCause().toString());
			L.warn("{} {}", new Object[] { trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg(), e });
			log_.setTrans_status(DbRmtdisLogRec._TRANS_STATUS_1_FAIL);
			log_.setErr_msg(e.getMessage());
			_odbUtils.addRawAlm(null, alm_);
		} finally {
			_odb.addRmtdisLog(log_);
		}
	}

	private DbRmtdisLogRec _initLogRecGet(DbRmtdisTransCfgRec trans_cfg, RmtdisSftpCli cli, LsEntry ls_entry) {
		DbRmtdisLogRec log_ = _initLogRec(trans_cfg, cli);
		log_.setFile_nm(ls_entry.getFilename());
		log_.setFile_sz(ls_entry.getAttrs().getSize());
		log_.setRmt_modify_tm(new Timestamp(ls_entry.getAttrs().getMTime() * 1000L));
		return log_;
	}

	private void _execPut(DbRmtdisTransCfgRec trans_cfg) throws Exception {
		DbRmtdisLoginCfgRec login_cfg_ = RmtdisCfg._LoginCfgMap.get(trans_cfg.getLogin_cfg_nm());
		RmtdisSftpCli cli_ = _cliMap.get(trans_cfg.getTrans_cfg_nm());
		List<File> scaned_ = _scanLocDir(trans_cfg);
		if (scaned_.isEmpty()) {
			// L.trace("{} no qualified file scaned under {}", trans_cfg.getTrans_cfg_nm(), trans_cfg.getLoc_dir());
			return;
		}
		L.debug("{} {} files scaned under {}", trans_cfg.getTrans_cfg_nm(), scaned_.size(), trans_cfg.getLoc_dir());
		if (cli_._sftp == null || !cli_._sftp.isConnected())
			cli_.login(login_cfg_);
		cli_._sftp.cd(trans_cfg.getRmt_dir());
		L.trace("{} verify RMT_DIR [{}] ok", trans_cfg.getTrans_cfg_nm(), trans_cfg.getRmt_dir());
		if (trans_cfg.getRmt_tmp_dir() != null) {
			cli_._sftp.cd(trans_cfg.getRmt_tmp_dir());
			L.trace("{} cd RMT_TMP_DIR [{}] done", trans_cfg.getTrans_cfg_nm(), trans_cfg.getRmt_tmp_dir());
		}
		for (File f : scaned_) {
			if (_terminateFlag.get())
				break;
			if (!cli_._sftp.isConnected()) {
				L.warn("{} connection lost while preparing PUT {}", trans_cfg.getTrans_cfg_nm(), f.getName());
				break;
			}
			_putOneFile(trans_cfg, cli_, f);
		}
	}

	private void _putOneFile(DbRmtdisTransCfgRec trans_cfg, RmtdisSftpCli cli, File loc_file) {
		DbRmtdisLogRec log_ = _initLogRecPut(trans_cfg, cli, loc_file);
		_guage._logRec = log_;
		String rmt_tmp_ = trans_cfg.getTmpFnm(log_.getFile_nm());
		String rmt_dst_ = trans_cfg.getRmt_dir() + "/" + log_.getFile_nm();
		try {
			cli._sftp.put(loc_file.getAbsolutePath(), rmt_tmp_, _guage, ChannelSftp.OVERWRITE);
			SftpATTRS attrs_ = cli._sftp.stat(rmt_tmp_);
			log_.setRmt_modify_tm(new Timestamp(attrs_.getMTime() * 1000L));
			if (loc_file.length() != attrs_.getSize()) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341012_PUT_SIZE_NE, PubMethod
						.FmtArgs("[%s] PUT verify size ne loc:rmt %d:%d", log_.getFile_nm(), loc_file.length(), attrs_.getSize()),
						trans_cfg.toGsonStr());
				L.warn("{} {}", trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg());
				log_.setTrans_status(DbRmtdisLogRec._TRANS_STATUS_1_FAIL);
				log_.setErr_msg(alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
				return;
			}
			L.debug("{} {} PUT verify size {} eq {}, mtime {} {}", new Object[] { trans_cfg.getTrans_cfg_nm(), log_.getFile_nm(),
					loc_file.length(), attrs_.getSize(), attrs_.getMTime(), attrs_.getMtimeString() });
			cli._sftp.rename(rmt_tmp_, rmt_dst_);
			L.debug("{} {} PUT rmt rename to {} ok", trans_cfg.getTrans_cfg_nm(), rmt_tmp_, rmt_dst_);
			if (trans_cfg.getLoc_bak_dir() == null) {
				if (loc_file.delete()) {
					L.info("{} {} loc del done", trans_cfg.getTrans_cfg_nm(), loc_file.getAbsolutePath());
				} else {
					DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341013_PUT_LOC_DEL_FAIL,
							PubMethod.FmtArgs("[%s] loc del error, pls chk", loc_file.getAbsolutePath()), trans_cfg.toGsonStr());
					L.warn("{} {}", trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg());
					log_.setErr_msg(alm_.getAlm_msg());
					_odbUtils.addRawAlm(null, alm_);
				}
			} else {
				String loc_bak_ = trans_cfg.getLoc_bak_dir() + "/" + log_.getFile_nm();
				if (PubMethod.MoveAFile(loc_file.getAbsolutePath(), loc_bak_)) {
					L.info("{} {} loc bak done, {}, {}", trans_cfg.getTrans_cfg_nm(), log_.getFile_nm(), loc_file.getAbsolutePath(),
							loc_bak_);
				} else {
					DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341014_PUT_LOC_BAK_FAIL,
							PubMethod.FmtArgs("[%s] loc bak [%s] --> [%s] error, pls chk", log_.getFile_nm(),
									loc_file.getAbsolutePath(), loc_bak_),
							trans_cfg.toGsonStr());
					L.warn("{} {}", trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg());
					log_.setErr_msg(alm_.getAlm_msg());
					_odbUtils.addRawAlm(null, alm_);
				}
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_202341011_PUT_EXCEPTION,
					PubMethod.FmtArgs("PUT [%s] exception", loc_file.getAbsolutePath()), trans_cfg.toGsonStr());
			alm_.setAlm_kpi(e.getCause().toString());
			L.warn("{} {}", new Object[] { trans_cfg.getTrans_cfg_nm(), alm_.getAlm_msg(), e });
			log_.setTrans_status(DbRmtdisLogRec._TRANS_STATUS_1_FAIL);
			log_.setErr_msg(e.getMessage());
			_odbUtils.addRawAlm(null, alm_);
		} finally {
			_odb.addRmtdisLog(log_);
		}
	}

	private DbRmtdisLogRec _initLogRecPut(DbRmtdisTransCfgRec trans_cfg, RmtdisSftpCli cli, File loc_file) {
		DbRmtdisLogRec log_ = _initLogRec(trans_cfg, cli);
		log_.setFile_nm(loc_file.getName());
		log_.setFile_sz(loc_file.length());
		log_.setFile_cksum(PubMethod.FileCksum(loc_file.getAbsolutePath()));
		log_.setLoc_modify_tm(new Timestamp(loc_file.lastModified()));
		return log_;
	}

	private DbRmtdisLogRec _initLogRec(DbRmtdisTransCfgRec trans_cfg, RmtdisSftpCli cli) {
		DbRmtdisLogRec log_ = new DbRmtdisLogRec();
		DbRmtdisLoginCfgRec login_cfg_ = RmtdisCfg._LoginCfgMap.get(trans_cfg.getLogin_cfg_nm());
		log_.setTs_start(System.currentTimeMillis());
		log_.setInst_nm(AppCmdline.GetInstance()._instNm);
		log_.setTrans_cfg_nm(trans_cfg.getTrans_cfg_nm());
		log_.setLogin_cfg_nm(trans_cfg.getLogin_cfg_nm());
		log_.setTrans_mode(trans_cfg.getTrans_mode());
		log_.setRmt_host(login_cfg_.getRmt_host());
		log_.setRmt_port(login_cfg_.getRmt_port());
		log_.setRmt_user(login_cfg_.getRmt_user());
		log_.setRmt_dir(trans_cfg.getRmt_dir());
		log_.setLoc_dir(trans_cfg.getLoc_dir());
		// log_.setLoc_host(cli._session.getLocalIpAddress());
		// log_.setLoc_port(cli._session.getLocalPort());
		log_.setLoc_host("0.0.0.0");
		log_.setLoc_port(0);
		log_.setTrans_status(DbRmtdisLogRec._TRANS_STATUS_0_SUCCESS);
		return log_;
	}

	private List<File> _scanLocDir(DbRmtdisTransCfgRec trans_cfg) {
		File loc_dir_ = new File(trans_cfg.getLoc_dir());
		PatternFileFilter filter_ = new PatternFileFilter(trans_cfg._patternMat, PatternFileFilter.TypeFilter.TypeFile);
		File[] scan_result_ = loc_dir_.listFiles(filter_);
		List<File> final_result_ = new ArrayList<File>();
		for (File f : scan_result_) {
			if (trans_cfg._patternIgn != null) {
				if (trans_cfg._patternIgn.matcher(f.getName()).find()) {
					continue;
				}
			}
			final_result_.add(f);
		}
		return final_result_;
	}

	private void _interval() {
		while (true) {
			if (_terminateFlag.get())
				return;
			PubMethod.Sleep(1000);
			long now_ = System.currentTimeMillis();
			for (Entry<String, Long> ent_ : _transTimeMap.entrySet()) {
				long next_act_ts_ = ent_.getValue() + _transCfgMap.get(ent_.getKey()).getMin_interval() * 1000L;
				if (now_ > next_act_ts_) {
					L.trace("{} next act ts {} lt now {}, {} lt {}",
							new Object[] { ent_.getKey(), next_act_ts_, now_,
									PubMethod.Long2Str(next_act_ts_, PubMethod.TimeStrFmt.Fmt23),
									PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23) });
					return;
				}
			}
		}
	}
}
