package com.hp.cmcc.bboss.app.rmtdis;

import java.io.File;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DbRmtdisTransCfgRec extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(DbRmtdisTransCfgRec.class);
	public static final String TRANS_MODE_GET = "GET";
	public static final String TRANS_MODE_PUT = "PUT";

	public Pattern _patternMat;
	public Pattern _patternIgn;
	public Pattern _tmpLhsP;
	public String _tmpRhsS;

	private String trans_cfg_nm;
	private String inst_nm;
	private String thread_nm;
	private String login_cfg_nm;
	private String trans_mode;
	private String rmt_dir;
	private String rmt_tmp_dir;
	private String rmt_bak_dir;
	private String loc_dir;
	private String loc_tmp_dir;
	private String loc_bak_dir;
	private String pattern_mat;
	private String pattern_ign;
	private Integer min_interval;
	private String tmp_rename_rule;

	public String getTmpFnm(String file_nm) {
		if (_tmpLhsP == null)
			return file_nm;
		Matcher m = _tmpLhsP.matcher(file_nm);
		if (m.find()) {
			String replaced_ = m.replaceFirst(_tmpRhsS);
			L.trace("{} TMP_RENAME_RULE [{}], file_nm [{}] --> [{}]", trans_cfg_nm, tmp_rename_rule, file_nm, replaced_);
			return replaced_;
		} else {
			L.warn("{} TMP_RENAME_RULE [{}], unsatisfied file_nm [{}], just return it", trans_cfg_nm, tmp_rename_rule, file_nm);
			return file_nm;
		}
	}

	public boolean validateRec() {
		boolean rc_ = true;
		if (!TRANS_MODE_GET.equals(trans_mode) && !TRANS_MODE_PUT.equals(trans_mode)) {
			L.warn("{} TRANS_MODE {} invalid, should be {} or {}", new Object[] { trans_cfg_nm, trans_mode, TRANS_MODE_GET,
					TRANS_MODE_PUT });
			rc_ = false;
		}
		if (rmt_dir.contains("$")) {
			L.warn("{} RMT_DIR [{}] can not contains '$'", trans_cfg_nm, rmt_dir);
			rc_ = false;
		}
		if (rmt_tmp_dir != null && rmt_tmp_dir.contains("$")) {
			L.warn("{} RMT_TMP_DIR [{}] can not contains '$'", trans_cfg_nm, rmt_tmp_dir);
			rc_ = false;
		}
		if (rmt_bak_dir != null && rmt_bak_dir.contains("$")) {
			L.warn("{} RMT_BAK_DIR [{}] can not contains '$'", trans_cfg_nm, rmt_bak_dir);
			rc_ = false;
		}
		File f = _validateDir(loc_dir, "LOC_DIR");
		if (f == null) {
			rc_ = false;
		} else {
			loc_dir = f.getAbsolutePath();
		}
		if (loc_tmp_dir != null) {
			f = _validateDir(loc_tmp_dir, "LOC_TMP_DIR");
			if (f == null) {
				rc_ = false;
			} else {
				loc_tmp_dir = f.getAbsolutePath();
			}
		}
		if (loc_bak_dir != null) {
			f = _validateDir(loc_bak_dir, "LOC_BAK_DIR");
			if (f == null) {
				rc_ = false;
			} else {
				loc_bak_dir = f.getAbsolutePath();
			}
		}
		Pattern p = _validatePattern(pattern_mat, "PATTERN_MAT");
		if (p == null) {
			rc_ = false;
		} else {
			_patternMat = p;
		}
		if (pattern_ign != null) {
			p = _validatePattern(pattern_ign, "PATTERN_IGN");
			if (p == null) {
				rc_ = false;
			} else {
				_patternIgn = p;
			}
		}
		if (!_validateMinInterval()) {
			rc_ = false;
		}
		if (!_validateTmpRenameRule()) {
			rc_ = false;
		}
		if (rc_) {
			if (TRANS_MODE_GET.equals(trans_mode)) {
				if (loc_tmp_dir == null && tmp_rename_rule == null) {
					L.warn("{} LOC_TMP_DIR or TMP_RENAME_RULE must be set for trans_mode {},", trans_cfg_nm, TRANS_MODE_GET);
					rc_ = false;
				}
			} else {
				if (rmt_tmp_dir == null && tmp_rename_rule == null) {
					L.warn("{} RMT_TMP_DIR or TMP_RENAME_RULE must be set for trans_mode {},", trans_cfg_nm, TRANS_MODE_PUT);
					rc_ = false;
				}
			}
		}
		//		if (rc_ && tmp_rename_rule != null) {
		//			if (TRANS_MODE_GET.equals(trans_mode)) {
		//				if (loc_tmp_dir != null) {
		//					L.info("{} LOC_TMP_DIR [{}] is set, TMP_RENAME_RULE [{}] will be ignored", trans_cfg_nm, loc_tmp_dir,
		//							tmp_rename_rule);
		//				}
		//			} else {
		//				if (rmt_tmp_dir != null) {
		//					L.info("{} RMT_TMP_DIR [{}] is set, TMP_RENAME_RULE [{}] will be ignored", trans_cfg_nm, rmt_tmp_dir,
		//							tmp_rename_rule);
		//				}
		//			}
		//		}
		return rc_;
	}

	private File _validateDir(String dir, String tag) {
		String s = PubMethod.SubStrByEnv(dir);
		if (s == null) {
			L.warn("{} {} [{}] SubStrByEnv failed", trans_cfg_nm, tag, dir);
			return null;
		}
		File f = new File(s);
		if (!f.isDirectory()) {
			L.warn("{} {} [{}] not exists or not a directory", trans_cfg_nm, tag, s);
			return null;
		}
		return f;
	}

	private Pattern _validatePattern(String pattern, String tag) {
		try {
			Pattern p = Pattern.compile(pattern);
			return p;
		} catch (Exception e) {
			L.warn("{} invalid {} [{}]", new Object[] { trans_cfg_nm, tag, pattern, e });
			return null;
		}
	}

	private boolean _validateMinInterval() {
		if (min_interval < 1 || min_interval > 86400) {
			L.warn("{} invalid MIN_INTERVAL {}, should between [1,86400]", trans_cfg_nm, min_interval);
			return false;
		}
		return true;
	}

	private boolean _validateTmpRenameRule() {
		if (tmp_rename_rule == null)
			return true;
		String[] a = tmp_rename_rule.split("/");
		if (a.length != 2) {
			L.warn("{} invalid TMP_RENAME_RULE {}, should be split by '/' into 2 parts, but encounter {}", trans_cfg_nm,
					tmp_rename_rule, a.length);
			return false;
		}
		Pattern p = null;
		try {
			p = Pattern.compile(a[0]);
		} catch (Exception e) {
			L.warn("{} TMP_RENAME_RULE {}, lhs [{}] invalid pattern", new Object[] { trans_cfg_nm, tmp_rename_rule, a[0] });
			return false;
		}
		_tmpLhsP = p;
		_tmpRhsS = a[1];
		return true;
	}

	public String getTrans_cfg_nm() {
		return trans_cfg_nm;
	}

	public void setTrans_cfg_nm(String trans_cfg_nm) {
		this.trans_cfg_nm = trans_cfg_nm;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getThread_nm() {
		return thread_nm;
	}

	public void setThread_nm(String thread_nm) {
		this.thread_nm = thread_nm;
	}

	public String getLogin_cfg_nm() {
		return login_cfg_nm;
	}

	public void setLogin_cfg_nm(String login_cfg_nm) {
		this.login_cfg_nm = login_cfg_nm;
	}

	public String getTrans_mode() {
		return trans_mode;
	}

	public void setTrans_mode(String trans_mode) {
		this.trans_mode = trans_mode;
	}

	public String getRmt_dir() {
		return rmt_dir;
	}

	public void setRmt_dir(String rmt_dir) {
		this.rmt_dir = rmt_dir;
	}

	public String getRmt_tmp_dir() {
		return rmt_tmp_dir;
	}

	public void setRmt_tmp_dir(String rmt_tmp_dir) {
		this.rmt_tmp_dir = rmt_tmp_dir;
	}

	public String getRmt_bak_dir() {
		return rmt_bak_dir;
	}

	public void setRmt_bak_dir(String rmt_bak_dir) {
		this.rmt_bak_dir = rmt_bak_dir;
	}

	public String getLoc_dir() {
		return loc_dir;
	}

	public void setLoc_dir(String loc_dir) {
		this.loc_dir = loc_dir;
	}

	public String getLoc_tmp_dir() {
		return loc_tmp_dir;
	}

	public void setLoc_tmp_dir(String loc_tmp_dir) {
		this.loc_tmp_dir = loc_tmp_dir;
	}

	public String getLoc_bak_dir() {
		return loc_bak_dir;
	}

	public void setLoc_bak_dir(String loc_bak_dir) {
		this.loc_bak_dir = loc_bak_dir;
	}

	public String getPattern_mat() {
		return pattern_mat;
	}

	public void setPattern_mat(String pattern_mat) {
		this.pattern_mat = pattern_mat;
	}

	public String getPattern_ign() {
		return pattern_ign;
	}

	public void setPattern_ign(String pattern_ign) {
		this.pattern_ign = pattern_ign;
	}

	public Integer getMin_interval() {
		return min_interval;
	}

	public void setMin_interval(Integer min_interval) {
		this.min_interval = min_interval;
	}

	public String getTmp_rename_rule() {
		return tmp_rename_rule;
	}

	public void setTmp_rename_rule(String tmp_rename_rule) {
		this.tmp_rename_rule = tmp_rename_rule;
	}
}
