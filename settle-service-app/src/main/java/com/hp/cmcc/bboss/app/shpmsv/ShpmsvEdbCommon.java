package com.hp.cmcc.bboss.app.shpmsv;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbBizTypeDefRec;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmsvEdbCommon extends ShpmsvEdb {
	private static Logger L = LoggerFactory.getLogger(ShpmsvEdbCommon.class);

	private static final String _C_TBL_BIZT = "CREATE TABLE IF NOT EXISTS BIZT (BIZ_TYPE TEXT NOT NULL, "
			+ "TYPE_ID INTEGER NOT NULL, ENABLED INTEGER NOT NULL, EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, "
			+ "UNIQUE (BIZ_TYPE) ON CONFLICT REPLACE)";
	private static final String _C_UX1_BIZT = "CREATE UNIQUE INDEX IF NOT EXISTS UX1_BIZT ON BIZT (TYPE_ID)";
	private static final String _I_BIZT = "INSERT INTO BIZT (BIZ_TYPE, TYPE_ID, ENABLED, EFF_DATE, EXP_DATE) "
			+ "VALUES (?, ?, ?, ?, ?)";

	public ShpmsvEdbCommon(ShpmsvOdb odb) {
		super(odb);
	}

	@Override
	public File encapsulateEdb(String shpm_ver) throws Exception {
		L.warn("trap in de facto pure virtual function, pls chk");
		return null;
	}

	public void initEdbCommon(Edb edb) throws SQLException {
		_initSchemaCommon(edb);
		_fillEdbBizt(edb);
	}

	private void _initSchemaCommon(Edb edb) throws SQLException {
		_rdbCnt = 0;
		_edbCnt = 0;
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(_C_TBL_BIZT);
			stmt_.executeUpdate(_C_UX1_BIZT);
		} finally {
			Edb.Close(null, stmt_, null);
		}
	}

	private void _fillEdbBizt(Edb edb) throws SQLException {
		OdbUtils odb_ = new OdbUtils();
		Map<String, DbBizTypeDefRec> biz_type_map_ = new HashMap<String, DbBizTypeDefRec>();
		odb_.getBizTypeDef(biz_type_map_, null);
		L.debug("{} records fetched from BIZ_TYPE_DEF", biz_type_map_.size());
		_rdbCnt += biz_type_map_.size();

		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_BIZT);
			for (DbBizTypeDefRec rec_ : biz_type_map_.values()) {
				int idx_ = 0;
				pstmt_.setString(++idx_, rec_.getBiz_type());
				pstmt_.setInt(++idx_, rec_.getType_id());
				pstmt_.setInt(++idx_, rec_.getEnabled());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("BIZT", null);
		L.debug("{} records inserted into BIZT", edb_cnt_);
		_edbCnt += edb_cnt_;
	}
}
