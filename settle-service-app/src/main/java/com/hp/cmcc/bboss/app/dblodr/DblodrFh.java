package com.hp.cmcc.bboss.app.dblodr;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.udr.UdrFileHandle;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DblodrFh {
	private static Logger L = LoggerFactory.getLogger(DblodrFh.class);
	private final DblodrCtlSec _ctlSec;
	public File _bcpFile;
	public UdrFileHandle _bcpHandle;
	public UdrFileHandle _badHandle;
	public UdrFileHandle _logHandle;
	private OdbUtils _odbUtils;

	public DblodrFh(DblodrCtlSec ctl_sec, File bcp_file) {
		_ctlSec = ctl_sec;
		_bcpFile = bcp_file;

		_bcpHandle = new UdrFileHandle(_bcpFile, UdrFileHandle.OP_MODE_MOVE, false);
		_bcpHandle._dstDir = _ctlSec._bakDir;

		String bad_path_ = AppCmdline.GetInstance()._workingDir + "/" + _bcpFile.getName() + ".bad";
		_badHandle = new UdrFileHandle(new File(bad_path_), UdrFileHandle.OP_MODE_MOVE, false);
		_badHandle._dstDir = _ctlSec._badDir;

		String log_path_ = AppCmdline.GetInstance()._workingDir + "/" + _bcpFile.getName() + ".log";
		_logHandle = new UdrFileHandle(new File(log_path_), UdrFileHandle.OP_MODE_MOVE, true);
		_logHandle._dstDir = _ctlSec._badDir;
	}

	public void successHandle() {
		_bcpHandle._dstDir = _ctlSec._bakDir;
		_bakHandle();
		_logHandle._opMode = UdrFileHandle.OP_MODE_DELETE;
		if (!_logHandle.operation()) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341001_FH_ULNK_LOG_FAIL, PubMethod.FmtArgs("unlink [%s] error",
					_logHandle._srcFilePath), _ctlSec.toGsonStr());
			L.warn("{}", alm_.getAlm_msg());
			_odbUtils.addRawAlm(null, alm_);
		} else {
			//L.debug("[{}] unlink done", _logHandle._srcFilePath);
		}
	}

	public void badHandle() {
		_bcpHandle._dstDir = _ctlSec._badDir;
		_bakHandle();
		if (_badHandle.operation()) {
			//L.info("mv {} to {}/ done", _badHandle._srcFilePath, _badHandle._dstDir);
		} else {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341002_FH_MOV_BAD_FAIL, PubMethod.FmtArgs(
					"mv [%s] to [%s/] failed, pls chk", _badHandle._srcFilePath, _badHandle._dstDir), _ctlSec.toGsonStr());
			L.warn("{}", alm_.getAlm_msg());
			_odbUtils.addRawAlm(null, alm_);
		}

		if (_logHandle.operation()) {
			//L.info("mv {} to {}/ done", _logHandle._srcFilePath, _logHandle._dstDir);
		} else {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341003_FH_MOV_LOG_FAIL, PubMethod.FmtArgs(
					"mv [%s] to [%s/] failed, pls chk", _logHandle._srcFilePath, _logHandle._dstDir), _ctlSec.toGsonStr());
			L.warn("{}", alm_.getAlm_msg());
			_odbUtils.addRawAlm(null, alm_);
		}
	}

	private void _bakHandle() {
		if (_bcpHandle.operation()) {
			//L.info("bak {} to {}/ ok", _bcpFile.getAbsolutePath(), _bcpHandle._dstDir);
			if (_bcpHandle._dstDir.equals(_ctlSec._bakDir) && _ctlSec._bakByYmd == 1) {
				DblodrMain._GzipTaskQueue.add(_bcpHandle._dstDir + "/" + _bcpFile.getName());
			}
		} else {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341004_FH_BAK_BCP_FAIL, PubMethod.FmtArgs(
					"bak [%s] to [%s/] failed, pls chk", _bcpFile.getAbsolutePath(), _bcpHandle._dstDir), _ctlSec.toGsonStr());
			L.warn("{}", alm_.getAlm_msg());
			_odbUtils.addRawAlm(null, alm_);
		}
	}
}
