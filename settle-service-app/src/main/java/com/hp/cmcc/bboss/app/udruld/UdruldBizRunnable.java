package com.hp.cmcc.bboss.app.udruld;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.script.Bindings;
import javax.script.CompiledScript;
import javax.script.SimpleBindings;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.google.gson.Gson;
import com.hp.cmcc.bboss.pub.edb.EdbDupchkPool;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.udr.UdrFileHandle;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PatternFileFilter;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.pub.util.StrTruncator;

public class UdruldBizRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(UdruldBizRunnable.class);
	private static Pattern _JsonFnmPattern = Pattern.compile("^ULD_[0-9]{3}_[0-9]{10}_[0-9]{5}_[0-9]{6}_.+\\.json$");
	private static Pattern _JsonTmpPattern = Pattern.compile("^ULD_[0-9]{3}_[0-9]{10}_[0-9]{5}_[0-9]{6}_.+\\.json\\.tmp$");
	private static Pattern _BcpSfxPattern = Pattern.compile("\\.bcp$");
	private BlockingQueue<UdrFmt> _bizBlockingQueue;
	private BlockingQueue<UdrFmt> _adjBlockingQueue;
	private String _prevHHMM;
	private String _currHHMM;
	private long _prevTs;
	private long _currTs;
	private Map<String, DbUdruldFileLogRec> _fileMap;
	private Map<String, Map<String, DbUdruldProcLogRec>> _procMap;
	private Map<String, UdrFileHandle> _hndlMap;
	private Map<String, UdrFileHandle> _jsonMap;
	private Set<String> _flushSet;
	private List<String> _tmpKeys; // prevent java.util.ConcurrentModificationException
	private OdbUtils _odbUtils;
	private Bindings _bindings;
	private EdbDupchkPool _edbDupchkPool;
	private boolean _terminateFlag = false;

	public UdruldBizRunnable(BlockingQueue<UdrFmt> biz_blocking_queue, BlockingQueue<UdrFmt> adj_blocking_queue) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_bizBlockingQueue = biz_blocking_queue;
		_adjBlockingQueue = adj_blocking_queue;
		_prevTs = System.currentTimeMillis();
		_currTs = _prevTs;
		_prevHHMM = PubMethod.Long2Str(_prevTs, PubMethod.TimeStrFmt.Fmt14).substring(8, 12);
		_currHHMM = _prevHHMM;

		// key:'TYPE_ID:AUX_KEY:E_07_A08_RCV_YMDH:ACNT_YM'
		_fileMap = new TreeMap<String, DbUdruldFileLogRec>();
		_procMap = new TreeMap<String, Map<String, DbUdruldProcLogRec>>();
		_hndlMap = new TreeMap<String, UdrFileHandle>();
		_jsonMap = new TreeMap<String, UdrFileHandle>();
		_flushSet = new HashSet<String>();
		_tmpKeys = new ArrayList<String>();
		_odbUtils = new OdbUtils();
		_bindings = new SimpleBindings();
		_edbDupchkPool = new EdbDupchkPool(UdruldCfg._UldCacheDir, "DUPCHKU", 2, UdruldCfg._UldDupchkHashYmd,
				UdruldCfg._UldDupchkHashTbl);
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	public void recover() {
		L.debug("*** *** recover *** ***");
		File cache_dir_ = new File(UdruldCfg._UldCacheDir);
		PatternFileFilter filter_ = new PatternFileFilter(_JsonTmpPattern, PatternFileFilter.TypeFilter.TypeFile);
		File[] cache_files_ = cache_dir_.listFiles(filter_);
		for (File f : cache_files_) {
			String tmp_ = f.getAbsolutePath();
			File dest_ = new File(tmp_.substring(0, tmp_.length() - 4));
			boolean r = f.renameTo(dest_);
			L.info("restore {} to {}, {}", f.getAbsolutePath(), dest_.getAbsolutePath(), r);
		}

		filter_ = new PatternFileFilter(_JsonFnmPattern, PatternFileFilter.TypeFilter.TypeFile);
		cache_files_ = cache_dir_.listFiles(filter_);
		if (cache_files_ == null || cache_files_.length == 0) {
			L.info("no cached json file scaned in {}, no need recovery", UdruldCfg._UldCacheDir);
			return;
		}
		Arrays.sort(cache_files_);
		L.info("{} cached file(s) scaned in {}", cache_files_.length, UdruldCfg._UldCacheDir);
		for (File fjson_ : cache_files_) {
			_recover(fjson_);
		}
		_traceMaps();
	}

	@Override
	public void run() {
		L.info("thread started");
		UdrFmt udr_ = null;
		while (!_terminateFlag) {
			try {
				_chkCutoffDur();
				udr_ = _bizBlockingQueue.poll(500, TimeUnit.MILLISECONDS);
				if (udr_ == null) {
					_flush();
					continue;
				}
				_procUdr(udr_);
			} catch (Exception e) {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_211341001_UKN_EXCEPTION, "udruld exception", null);
				alm_.setAlm_kpi(e.toString());
				if (udr_ != null)
					alm_.setAlm_json(udr_.toGsonStr());
				L.warn("{}, sleep 500 millis", alm_.getAlm_msg(), e);
				_odbUtils.addRawAlm(null, alm_);
				PubMethod.Sleep(500);
			}
		}
		for (UdrFileHandle hndl_ : _hndlMap.values())
			hndl_.closeSrcFile();
		for (UdrFileHandle hndl_ : _jsonMap.values())
			hndl_.closeSrcFile();
		_edbDupchkPool.forceCommit();
		L.info("thread end");
	}

	private void _recover(File fjson) {
		if (!fjson.isFile()) {
			L.warn("{} not exists, skip recovery", fjson.getAbsolutePath());
			return;
		}
		if (fjson.length() == 0) {
			L.warn("{} is an empty file, skip recovery", fjson.getAbsolutePath());
			return;
		}
		UdruldOdb odb_ = new UdruldOdb();
		int rcv_mm_ = Integer.parseInt(fjson.getName().substring(12, 14));
		long rcv_ymdh_ = Long.parseLong(fjson.getName().substring(8, 18));
		String bcp_file_nm_ = fjson.getName().substring(0, fjson.getName().length() - 5) + ".bcp";
		DbUdruldFileLogRec file_log_ = odb_.getUldFileLog(rcv_mm_, rcv_ymdh_, bcp_file_nm_);
		if (file_log_ == null) {
			L.warn("{} not found in UDRULD_FILE_LOG, can not recovery {}", bcp_file_nm_, fjson.getName());
			return;
		}
		if (file_log_.getUld_status() != 0) {
			L.warn("{} ULD_STATUS {} ne 0, skip recovery {}", bcp_file_nm_, file_log_.getUld_status(), fjson.getName());
			return;
		}
		_recoverGuts(file_log_, fjson);
	}

	private void _recoverGuts(DbUdruldFileLogRec file_log, File fjson) {
		Gson gson_ = new Gson();
		BufferedReader br_ = null;
		File fjson_tmp_ = new File(fjson.getAbsolutePath() + ".tmp");
		boolean r = fjson.renameTo(fjson_tmp_);
		L.info("rename {} to {}, {}", fjson.getAbsolutePath(), fjson_tmp_.getAbsolutePath(), r);
		try {
			br_ = new BufferedReader(new FileReader(fjson_tmp_));
			L.trace("{} opened for recovery", fjson_tmp_.getAbsolutePath());
			String line_;
			while ((line_ = br_.readLine()) != null) {
				UdrFmt udr_ = gson_.fromJson(line_, UdrFmt.class);
				String aux_key_ = _getAuxKey(udr_);
				UdrFileHandle hndl_ = _getHndl(udr_, aux_key_, file_log);
				String key_ = udr_.getUldKey(aux_key_);
				UdrFileHandle json_ = _jsonMap.get(key_);
				json_._pw.println(line_);
				if (!_flushSet.contains(key_)) {
					_flushSet.add(key_);
				}
				int type_id_ = Integer.parseInt(udr_.getFileType());
				String fmt_key_ = String.format("%03d:%s", type_id_, aux_key_);
				List<DbUdruldFmtCfgRec> fmt_list_ = UdruldCfg._UldFmtCfgMap.get(fmt_key_);
				if (fmt_list_ == null) { // default dump all fields
					udr_.dumpIDD(hndl_._pw, UdruldCfg._UldCharset);
				} else {
					_dumpUdr(hndl_._pw, udr_, fmt_list_, aux_key_);
				}
				_adjOraLog(udr_, aux_key_);
			}
		} catch (Exception e) {
			L.warn("exception, recovery {} abort", fjson_tmp_.getAbsolutePath(), e);
			return;
		} finally {
			PubMethod.Close(br_);
		}
		fjson_tmp_.delete();
	}

	private void _chkCutoffDur() {
		_currTs = System.currentTimeMillis();
		if (_currTs - _prevTs < 500)
			return;
		_prevTs = _currTs;
		_currHHMM = PubMethod.Long2Str(_currTs, PubMethod.TimeStrFmt.Fmt14).substring(8, 12);
		if (_currHHMM.equals(_prevHHMM))
			return;
		L.trace("{}:{} minute change detected", _prevHHMM, _currHHMM);
		_prevHHMM = _currHHMM;

		_edbDupchkPool.forceCommit();
		long expire_ts_ = _currTs - UdruldCfg._UldCutoffDur * 1000L;
		_tmpKeys.clear();
		for (String key_ : _hndlMap.keySet())
			_tmpKeys.add(key_);
		for (String key_ : _tmpKeys) {
			DbUdruldFileLogRec file_log_ = _fileMap.get(key_);
			if (file_log_.getUld_start_tm().getTime() < expire_ts_) {
				long elapse_ = _currTs - file_log_.getUld_start_tm().getTime();
				elapse_ /= 1000;
				L.info("{} uld_start_tm {}, elpase {} seconds, gt ULD_CUTOFF_DUR {}, force cutoff", file_log_.getUld_file_nm(),
						PubMethod.Timestamp2Str(file_log_.getUld_start_tm(), PubMethod.TimeStrFmt.Fmt23), elapse_,
						UdruldCfg._UldCutoffDur);
				_cutoff(key_);
			}
		}
	}

	private void _procUdr(UdrFmt udr) throws Exception {
		if (!_chkDup(udr))
			return;
		String aux_key_ = _getAuxKey(udr);
		UdrFileHandle hndl_ = _getHndl(udr, aux_key_, null);
		String key_ = udr.getUldKey(aux_key_);
		UdrFileHandle json_ = _jsonMap.get(key_);
		if (PubMethod.IsEmpty(udr._gsonStr)) {
			json_._pw.println(udr.toGsonStr());
		} else {
			json_._pw.println(udr._gsonStr);
		}
		if (!_flushSet.contains(key_)) {
			_flushSet.add(key_);
		}

		int type_id_ = Integer.parseInt(udr.getFileType());
		String fmt_key_ = String.format("%03d:%s", type_id_, aux_key_);
		List<DbUdruldFmtCfgRec> fmt_list_ = UdruldCfg._UldFmtCfgMap.get(fmt_key_);
		if (fmt_list_ == null) { // default dump all fields
			udr.dumpIDD(hndl_._pw, UdruldCfg._UldCharset);
		} else {
			_dumpUdr(hndl_._pw, udr, fmt_list_, aux_key_);
		}

		_adjOraLog(udr, aux_key_);
		_adjBizLog(udr);
		_cntCutoff(udr, aux_key_);
	}

	private boolean _chkDup(UdrFmt udr) {
		String yyyymmdd_ = udr._eFields[UdrFmt.E_04_A05_FILE_ID].substring(0, 8);
		String dup_key_ = udr._eFields[UdrFmt.E_04_A05_FILE_ID].substring(8) + ":" + udr._eFields[UdrFmt.E_05_A06_LINE_NUM];
		if (udr.isFeedback()) {
			dup_key_ = dup_key_ + ":" + udr._eFields[UdrFmt.E_32_X07_FEEDBACK_TAG];
		}
		if (!_edbDupchkPool.chkDup(yyyymmdd_, dup_key_)) {
			L.warn("{} dup and skip, {}", udr.getBizlogUid(), udr.toGsonStr());
			return false;
		}
		return true;
	}

	private String _getAuxKey(UdrFmt udr) {
		String aux_key_ = null;
		int type_id_ = Integer.parseInt(udr.getFileType());
		CompiledScript script_ = UdruldCfg._AuxKeyScriptMap.get(type_id_);
		if (script_ != null) {
			try {
				_bindings.clear();
				_bindings.put("E", udr._eFields);
				aux_key_ = (String) script_.eval(_bindings);
				L.trace("{} eval aux_key [{}]", udr.getBizlogUid(), aux_key_);
			} catch (Exception e) {
				L.warn("{} eval aux_key exception, {}", udr.getBizlogUid(), udr.toGsonStr(), e);
			}
		}
		if (PubMethod.IsBlank(aux_key_))
			aux_key_ = UdrFmt.AUX_KEY_NULL_FILLER;
		return aux_key_;
	}

	private void _dumpUdr(PrintWriter pw, UdrFmt udr, List<DbUdruldFmtCfgRec> fmt_list, String aux_key) {
		StringBuilder sb_ = new StringBuilder();
		StrTruncator truncator_ = StrTruncator.GetStrTruncator(UdruldCfg._UldCharset);
		_dumpOneLine(udr, udr._uFields, fmt_list, aux_key, sb_, truncator_);
		if (udr._uList != null && !udr._uList.isEmpty()) {
			for (String[] u_fields_ : udr._uList) {
				_dumpOneLine(udr, u_fields_, fmt_list, aux_key, sb_, truncator_);
			}
		}
		pw.print(sb_.substring(0));
	}

	private void _dumpOneLine(UdrFmt udr, String[] u_fields, List<DbUdruldFmtCfgRec> fmt_list, String aux_key, StringBuilder sb,
			StrTruncator truncator) {
		String[] attach_ = null;
		for (int i = 0; i < fmt_list.size(); i++) {
			DbUdruldFmtCfgRec fmt_cfg_ = fmt_list.get(i);
			if (!PubMethod.IsBlank(fmt_cfg_.getEval())) {
				try {
					String result_ = null;
					String script_key_ = fmt_cfg_.getScriptKey();
					CompiledScript script_ = UdruldCfg._UldFmtScriptMap.get(script_key_);
					if (script_ == null) {
						L.warn("absurd!!! script_key_ [{}] not exists", script_key_);
					} else {
						_bindings.clear();
						_bindings.put("E", udr._eFields);
						_bindings.put("U", u_fields);
						_bindings.put("A", udr._eFields[UdrFmt.E_25_R02_RAW_ATTACHMENT]);
						result_ = (String) script_.eval(_bindings);
					}
					result_ = result_ == null ? "" : result_.trim();
					if (fmt_cfg_.getIs_num() == 1) {
						sb.append(result_);
					} else {
						sb.append(truncator.truncate(result_, fmt_cfg_.getMax_len()));
					}
				} catch (Exception e) {
					String key_ = udr.getUldKey(aux_key);
					DbUdruldFileLogRec file_log_ = _fileMap.get(key_);
					L.warn("{} {} {} eval [{},{}] [{}] exception",
							new Object[] { file_log_.getUld_file_nm(), file_log_.getUld_cnt(), udr.getBizlogUid(),
									fmt_cfg_.getSrc_type(), fmt_cfg_.getUld_idx(), fmt_cfg_.getEval(), e });
				}
			} else {
				if (DbUdruldFmtCfgRec.SRC_TYPE_U.equals(fmt_cfg_.getSrc_type())) {
					_appendField(sb, u_fields, fmt_cfg_, truncator);
				} else if (DbUdruldFmtCfgRec.SRC_TYPE_E.equals(fmt_cfg_.getSrc_type())) {
					_appendField(sb, udr._eFields, fmt_cfg_, truncator);
				} else if (DbUdruldFmtCfgRec.SRC_TYPE_A.equals(fmt_cfg_.getSrc_type())) {
					if (attach_ == null) {
						if (udr._eFields[UdrFmt.E_25_R02_RAW_ATTACHMENT] == null) {
							attach_ = "".split(UdrFmt.CSV_WITH_ESC_PATTERN);
						} else {
							attach_ = udr._eFields[UdrFmt.E_25_R02_RAW_ATTACHMENT].split(UdrFmt.CSV_WITH_ESC_PATTERN);
							for (int j = 0; j < attach_.length; j++) {
								attach_[j] = attach_[j].trim();
							}
						}
					}
					_appendField(sb, attach_, fmt_cfg_, truncator);
				} else {
					// do nothing
				}
			}
			if (i < fmt_list.size() - 1)
				sb.append(UdrFmt.ULD_DELIMETER_NML);
		}
		sb.append(UdrFmt.ULD_DELIMETER_EOL);
	}

	private void _appendField(StringBuilder sb, String[] src_array, DbUdruldFmtCfgRec fmt_cfg, StrTruncator truncator) {
		if (fmt_cfg.getSrc_idx() <= (src_array.length - 1)) {
			if (PubMethod.IsEmpty(src_array[fmt_cfg.getSrc_idx()])) {
				if (fmt_cfg.getDft_val() != null)
					sb.append(fmt_cfg.getDft_val());
			} else if (fmt_cfg.getIs_num() == 1) {
				sb.append(src_array[fmt_cfg.getSrc_idx()]);
			} else {
				sb.append(truncator.truncate(src_array[fmt_cfg.getSrc_idx()], fmt_cfg.getMax_len()));
			}
		}
	}

	private UdrFileHandle _getHndl(UdrFmt udr, String aux_key, DbUdruldFileLogRec file_log) throws Exception {
		String key_ = udr.getUldKey(aux_key);
		UdrFileHandle hndl_ = _hndlMap.get(key_);
		if (hndl_ == null) {
			DbUdruldFileLogRec file_log_ = null;
			if (file_log == null) {
				file_log_ = _newFileLog(udr, aux_key);
			} else {
				file_log_ = file_log;
			}
			hndl_ = new UdrFileHandle(new File(UdruldCfg._UldCacheDir + "/" + file_log_.getUld_file_nm()),
					UdrFileHandle.OP_MODE_MOVE, false);
			hndl_._dstDir = UdruldCfg._UldOutputDir;
			hndl_.openSrcFile(UdruldCfg._UldCharset, false);
			Matcher m = _BcpSfxPattern.matcher(hndl_._srcFilePath);
			String json_path_ = null;
			if (m.find()) {
				json_path_ = m.replaceFirst(".json");
			} else {
				L.warn("absurd!!! [{}] not matches [{}]", hndl_._srcFilePath, _BcpSfxPattern.pattern());
			}
			UdrFileHandle json_ = new UdrFileHandle(new File(json_path_), UdrFileHandle.OP_MODE_MOVE, false);
			json_._dstDir = UdruldCfg._UldJsonDir;
			json_.openSrcFile(UdruldCfg._UldCharset, false);
			UdruldOdb udruld_odb_ = new UdruldOdb();
			if (file_log == null) {
				udruld_odb_.addUldFileLog(null, file_log_);
			}
			_hndlMap.put(key_, hndl_);
			_jsonMap.put(key_, json_);
			_fileMap.put(key_, file_log_);
		}
		return hndl_;
	}

	/**
	 * 
	 * @param udr
	 * @param aux_key
	 * @return
	 */
	private DbUdruldFileLogRec _newFileLog(UdrFmt udr, String aux_key) {
		int type_id_ = Integer.parseInt(udr.getFileType());
		String biz_type_ = UdruldCfg._BizTypeIdMap.get(type_id_).getBiz_type();
		int uld_seq_ = _odbUtils.nextSeq(Integer.parseInt(udr._eFields[UdrFmt.E_07_A08_RCV_YMDH].substring(0, 8)),
				"ULD_" + udr.getFileType());
		String uld_fnm_ = String.format("ULD_%03d_%s_%05d_%s_%s_%s.bcp", type_id_, udr._eFields[UdrFmt.E_07_A08_RCV_YMDH], uld_seq_,
				udr.getAcntYm(), biz_type_, aux_key);
		DbUdruldFileLogRec file_log_ = new DbUdruldFileLogRec();
		file_log_.setUld_file_nm(uld_fnm_);
		file_log_.setRcv_mm(Integer.parseInt(udr._eFields[UdrFmt.E_06_A07_RCV_MM]));
		file_log_.setRcv_ymdh(Long.parseLong(udr._eFields[UdrFmt.E_07_A08_RCV_YMDH]));
		file_log_.setAcnt_ym(Integer.parseInt(udr.getAcntYm()));
		file_log_.setUld_status(0);
		file_log_.setTs_start(System.currentTimeMillis());
		file_log_.setUld_start_tm(new Timestamp(file_log_.getTs_start()));
		file_log_.setUld_end_tm(new Timestamp(0L));
		file_log_.setTs_end(file_log_.getTs_start());
		file_log_.setUld_cnt(0);
		file_log_.setErcy_cnt(0);
		file_log_.setSum_batch(0L);
		file_log_.setSpare_num1(0L);
		file_log_.setSpare_num2(0L);
		return file_log_;
	}

	private void _adjOraLog(UdrFmt udr, String aux_key) {
		String key_ = udr.getUldKey(aux_key);
		DbUdruldFileLogRec file_log_ = _fileMap.get(key_);
		if (udr.isFeedback()) {
			file_log_.setSpare_num2(file_log_.getSpare_num2() + 1 + (udr._uList == null ? 0 : udr._uList.size()));
		} else {
			file_log_.setUld_cnt(file_log_.getUld_cnt() + 1);
			file_log_.setSpare_num1(file_log_.getSpare_num1() + 1 + (udr._uList == null ? 0 : udr._uList.size()));
		}
		Map<String, DbUdruldProcLogRec> proc_map_ = _procMap.get(key_);
		if (proc_map_ == null) {
			proc_map_ = new TreeMap<String, DbUdruldProcLogRec>();
			_procMap.put(key_, proc_map_);
		}
		String proc_key_ = udr.getUldProcKey(aux_key);
		DbUdruldProcLogRec proc_log_ = proc_map_.get(proc_key_);
		if (proc_log_ == null) {
			proc_log_ = new DbUdruldProcLogRec();
			proc_log_.setUld_file_nm(file_log_.getUld_file_nm());
			proc_log_.setRcv_mm(file_log_.getRcv_mm());
			proc_log_.setRcv_ymdh(file_log_.getRcv_ymdh());
			proc_log_.setAcnt_ym(file_log_.getAcnt_ym());
			proc_log_.setFile_id(Long.parseLong(udr._eFields[UdrFmt.E_04_A05_FILE_ID]));
			proc_log_.setOrg_file_id(Long.parseLong(udr._eFields[UdrFmt.E_03_A04_ORG_FILE_ID]));
			proc_log_.setPp_file_id(Long.parseLong(udr._eFields[UdrFmt.E_21_E07_PP_FILE_ID]));
			proc_log_.setUld_cnt(0);
			proc_log_.setErcy_cnt(0);
			proc_log_.setSpare_num1(0L);
			proc_log_.setSpare_num2(0L);
			proc_map_.put(proc_key_, proc_log_);
		}
		if (udr.isFeedback()) {
			proc_log_.setSpare_num2(proc_log_.getSpare_num2() + 1 + (udr._uList == null ? 0 : udr._uList.size()));
		} else {
			proc_log_.setUld_cnt(proc_log_.getUld_cnt() + 1);
			proc_log_.setSpare_num1(proc_log_.getSpare_num1() + 1 + (udr._uList == null ? 0 : udr._uList.size()));
		}
		if (udr.isErcy()) {
			file_log_.setErcy_cnt(file_log_.getErcy_cnt() + 1);
			proc_log_.setErcy_cnt(proc_log_.getErcy_cnt() + 1);
		}
		file_log_.setTs_end(System.currentTimeMillis());
	}

	private void _adjBizLog(UdrFmt udr) throws InterruptedException {
		boolean r;
		while (true) {
			r = _adjBlockingQueue.offer(udr, 2000, TimeUnit.MILLISECONDS);
			if (!r) {
				L.info("{} offer timeout, _adjBlockingQueue.size()={}", udr.getBizlogUid(), _adjBlockingQueue.size());
			} else {
				break;
			}
		}
	}

	private void _cntCutoff(UdrFmt udr, String aux_key) {
		String key_ = udr.getUldKey(aux_key);
		DbUdruldFileLogRec file_log_ = _fileMap.get(key_);
		if (file_log_.getUld_cnt() < UdruldCfg._UldCutoffCnt) {
			return;
		}
		L.info("{} uld_cnt {} reach {}, cutoff now", file_log_.getUld_file_nm(), file_log_.getUld_cnt(), UdruldCfg._UldCutoffCnt);
		_cutoff(key_);
	}

	private void _cutoff(String key) {
		Connection conn_ = null;
		UdruldOdb odb_ = new UdruldOdb();
		DbUdruldFileLogRec file_log_ = _fileMap.get(key);
		if (file_log_ == null) {
			L.warn("key {} has no correspond udruld_file_log, can not cutoff", key);
			return;
		}
		OdbCli cli_ = OdbAgt.GetBizInstance();
		UdrFileHandle hndl_ = _hndlMap.get(key);
		UdrFileHandle json_ = _jsonMap.get(key);
		try {
			conn_ = cli_.getConnection();
			file_log_.setUld_status(1);
			file_log_.setUld_end_tm(new Timestamp(System.currentTimeMillis()));
			odb_.updUldFileLog(conn_, file_log_);
			Map<String, DbUdruldProcLogRec> proc_map_ = _procMap.get(key);
			for (Entry<String, DbUdruldProcLogRec> entry_ : proc_map_.entrySet()) {
				// L.trace("proc k:v [{}] --> {}", entry_.getKey(), entry_.getValue().toGsonStr());
				odb_.addUldProcLog(conn_, entry_.getValue());
			}
			if (!hndl_.operation())
				L.warn("{} operation error, pls chk", file_log_.getUld_file_nm());
			cli_.commit(conn_);
			L.info("transaction {} commit done", conn_.toString());
			hndl_.closeSrcFile();
			json_.closeSrcFile();
			if (!json_.operation())
				L.warn("{} operation error, pls chk", json_._srcFilePath);
			_hndlMap.remove(key);
			_jsonMap.remove(key);
			_fileMap.remove(key);
			_procMap.remove(key);
		} catch (Exception e) {
			L.warn("{} exception", file_log_.getUld_file_nm(), e);
			if (conn_ != null) {
				cli_.rollback(conn_);
				L.warn("transaction {} rollback done", conn_.toString());
			}
		} finally {
			cli_.close(conn_);
		}
	}

	private void _flush() {
		UdrFileHandle hndl_ = null;
		for (String key_ : _flushSet) {
			hndl_ = _hndlMap.get(key_);
			if (hndl_ != null) {
				hndl_.flush();
			}
			hndl_ = _jsonMap.get(key_);
			if (hndl_ != null) {
				hndl_.flush();
			}
		}
		_flushSet.clear();
	}

	private void _traceMaps() {
		StringBuilder sb_ = new StringBuilder();
		sb_.append(PubMethod.FmtArgs("total %d keys", _hndlMap.size()));
		int i = 0;
		for (String key_ : _hndlMap.keySet()) {
			++i;
			UdrFileHandle hndl_ = _hndlMap.get(key_);
			sb_.append(PubMethod.FmtArgs("%nNo. %-3d %s %s%n", i, key_, hndl_.toGsonStr()));
			UdrFileHandle json_ = _jsonMap.get(key_);
			sb_.append(PubMethod.FmtArgs("%nNo. %-3d %s %s%n", i, key_, json_.toGsonStr()));
			DbUdruldFileLogRec file_log_ = _fileMap.get(key_);
			sb_.append(PubMethod.FmtArgs("No. %-3d %s %s%n", i, key_, file_log_.toGsonStr()));
			Map<String, DbUdruldProcLogRec> proc_map_ = _procMap.get(key_);
			sb_.append(PubMethod.FmtArgs("No. %-3d %s %d elems%n", i, key_, proc_map_.size()));
			int j = 0;
			for (Entry<String, DbUdruldProcLogRec> entry_ : proc_map_.entrySet()) {
				sb_.append(PubMethod.FmtArgs("%9d %s%n", ++j, entry_.getValue().toGsonStr()));
			}
		}
		L.trace(sb_.substring(0));
	}
}
