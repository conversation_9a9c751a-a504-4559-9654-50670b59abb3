package com.hp.cmcc.bboss.app.shpmsv;

import java.io.File;
import java.util.Map;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class ShpmsvCfg {
	private static Logger L = LoggerFactory.getLogger(ShpmsvCfg.class);
	public static OdbSystemParam _SystemParam;
	public static String _InstNm;
	public static int _RestPort;
	public static String _WorkingDir;
	public static Map<String, ShpmsvAgtInfo> _AgtInfoMap;

	public static boolean Init() {
		_InstNm = AppCmdline.GetInstance()._instNm;
		_SystemParam = OdbSystemParam.GetInstance();
		boolean rc_ = _SystemParam.refresh();
		if (!rc_) {
			L.warn("init system param error");
			return false;
		}

		OdbAppParam app_param_ = new OdbAppParam();
		rc_ = app_param_.refresh("shpmsv", _InstNm);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", _InstNm);
			return false;
		}

		rc_ = app_param_.subValByEnv();//处理数据中的环境变量
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", _InstNm);
			return false;
		}

		if (!_InitCommonCfg(app_param_))
			rc_ = false;

		if (!_InitAgtInfoMap(app_param_))
			rc_ = false;

		if (!_InstNm.equals(_SystemParam._shpmsvInstNm)) {
			L.warn("_InstNm [{}] ne _SystemParam._shpmsvInstNm [{}], pls chk", _InstNm, _SystemParam._shpmsvInstNm);
			rc_ = false;
		}

		return rc_;
	}

	private static boolean _InitCommonCfg(OdbAppParam app_param) {
		boolean rc_ = true;
		Long lval_ = app_param.chkValNum("COMMON", "REST_PORT", 0L, 65535L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_RestPort = lval_.intValue();
		}

		File fval_ = app_param.chkValFile("COMMON", "WORKING_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_WorkingDir = fval_.getAbsolutePath();
		}

		return rc_;
	}

	private static boolean _InitAgtInfoMap(OdbAppParam app_param) {
		boolean rc_ = true;
		ShpmsvAgtInfo agt_info_ = null;
		Map<String, ShpmsvAgtInfo> agt_info_map_ = new TreeMap<String, ShpmsvAgtInfo>();
		for (String sec_nm_ : app_param._paramMap.keySet()) {
			if (!sec_nm_.startsWith("AGT."))
				continue;
			if (agt_info_map_.containsKey(sec_nm_)) {
				L.warn("absurd!, section [{}] already exists", sec_nm_);
				continue;
			}
			agt_info_ = new ShpmsvAgtInfo();
			agt_info_._secNm = sec_nm_;

			String sval_ = app_param.chkValStr(sec_nm_, "AGT_INST_NM", null);
			if (sval_ == null) {
				agt_info_._secNm = null;
			} else {
				agt_info_._agtInstNm = sval_;
			}

			sval_ = app_param.chkValStr(sec_nm_, "AGT_HOST", null);
			if (sval_ == null) {
				agt_info_._secNm = null;
			} else {
				agt_info_._agtHost = sval_;
			}

			Long lval_ = app_param.chkValNum(sec_nm_, "AGT_REST_PORT", 1L, 65535L);
			if (lval_ == null) {
				agt_info_._secNm = null;
			} else {
				agt_info_._agtRestPort = lval_.intValue();
			}

			if (agt_info_._secNm == null) {
				L.warn("section [{}] init error", sec_nm_);
				rc_ = false;
			} else {
				agt_info_map_.put(agt_info_._secNm, agt_info_);
			}
		}

		if (agt_info_map_.isEmpty()) {
			L.warn("no valid 'AGT.*' section found");
			rc_ = false;
		}
		if (rc_) {
			_AgtInfoMap = agt_info_map_;
			L.debug("{} 'AGT.*' sections initialized", _AgtInfoMap.size());
		}
		return rc_;
	}

	public static void Trace() {
		if (_SystemParam != null) {
			L.debug("_SttlSystemParam: {}", _SystemParam.toString());
		} else {
			L.debug("_SttlSystemParam is null");
		}
		L.debug("_InstNm=[{}],_RestPort={}", new Object[] { _InstNm, _RestPort });
		L.debug("_WorkingDir=[{}]", _WorkingDir);
		L.debug("_AgtInfoMap {} entires", _AgtInfoMap.size());
		int i = 0;
		for (ShpmsvAgtInfo info_ : _AgtInfoMap.values()) {
			L.debug("No. {}, {}", ++i, info_.toGsonStr());
		}
	}
}
