package com.hp.cmcc.bboss.app.monitr;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

public class MonitrCfg {
	private static Logger L = LoggerFactory.getLogger(MonitrCfg.class);
	public static String _InstNm;
	public static String _Jps;
	public static int _RestPort = -1;
	public static int _IntervalProc;
	public static int _IntervalLoad;
	public static int _IntervalMem;
	public static int _IntervalFs;
	public static int _IntervalDir;
	public static double _Load01Threshold;
	public static double _Load05Threshold;
	public static double _Load15Threshold;
	public static int _MemFreeKbThreshold;
	public static int _MaxProcThreshold;
	public static Map<String, DbMonitrCfgRec> _MonitrCfgMap = new TreeMap<String, DbMonitrCfgRec>();
	public static Map<String, Long> _ProgPidMap = new TreeMap<String, Long>();
	public static Map<String, MonitrFsThreshold> _FsThresholdMap = new TreeMap<String, MonitrFsThreshold>();
	public static Map<String, MonitrDirThreshold> _DirThresholdMap = new TreeMap<String, MonitrDirThreshold>();

	public static boolean Init() {
		_InstNm = AppCmdline.GetInstance()._instNm;
		String java_home_ = System.getenv("JAVA_HOME");
		if (java_home_ == null) {
			L.warn("env JAVA_HOME not set");
			return false;
		}
//		File jps_ = new File(java_home_ + "/bin/jps");
		File jps_ = new File(java_home_ + "/bin/jps.exe");
		if (!jps_.isFile() || !jps_.canExecute()) {
			L.warn("{} not exist or not executable", jps_.getAbsolutePath());
			return false;
		}
		_Jps = jps_.getAbsolutePath();
		if (!RefreshAppParam()) {
			L.warn("call _InitAppParam error");
			return false;
		}
		if (!RefreshMonitrCfg()) {
			L.warn("call _InitMonitrCfg error");
			return false;
		}
		return true;
	}

	public static boolean RefreshAppParam() {
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh("monitr", _InstNm);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", _InstNm);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", _InstNm);
			return false;
		}

		Long lval_ = null;
		if (_RestPort < 0) { // refresh only once
			lval_ = app_param_.chkValNum("COMMON", "REST_PORT", 0L, 65535L);
			if (lval_ == null) {
				rc_ = false;
			} else {
				_RestPort = lval_.intValue();
			}
		}

		int interval_proc_ = 0;
		int interval_load_ = 0;
		int interval_mem_ = 0;
		int interval_fs_ = 0;
		int interval_dir_ = 0;
		double load_01_threshold_ = 0.0;
		double load_05_threshold_ = 0.0;
		double load_15_threshold_ = 0.0;
		int mem_free_kb_threshold_ = 0;
		int max_proc_threshold_ = 0;
		Map<String, MonitrFsThreshold> fs_threshold_map_ = new TreeMap<String, MonitrFsThreshold>();
		Map<String, MonitrDirThreshold> dir_threshold_map_ = new TreeMap<String, MonitrDirThreshold>();

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_PROC", 5L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			interval_proc_ = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_LOAD", 5L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			interval_load_ = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_MEM", 5L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			interval_mem_ = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_FS", 5L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			interval_fs_ = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "INTERVAL_DIR", 5L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			interval_dir_ = lval_.intValue();
		}

		Double dval_ = app_param_.chkValDouble("COMMON", "LOAD_01_THRESHOLD", 0.001, 1000.0);
		if (dval_ == null) {
			rc_ = false;
		} else {
			load_01_threshold_ = dval_.doubleValue();
		}

		dval_ = app_param_.chkValDouble("COMMON", "LOAD_05_THRESHOLD", 0.001, 1000.0);
		if (dval_ == null) {
			rc_ = false;
		} else {
			load_05_threshold_ = dval_.doubleValue();
		}

		dval_ = app_param_.chkValDouble("COMMON", "LOAD_15_THRESHOLD", 0.001, 1000.0);
		if (dval_ == null) {
			rc_ = false;
		} else {
			load_15_threshold_ = dval_.doubleValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "MEM_FREE_KB_THRESHOLD", 10L, 1000000000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			mem_free_kb_threshold_ = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "MAX_PROC_THRESHOLD", 1L, 10000000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			max_proc_threshold_ = lval_.intValue();
		}

		String sval_ = null;
		for (String sec_nm_ : app_param_._paramMap.keySet()) {
			if (!sec_nm_.startsWith("FS."))
				continue;
			if (fs_threshold_map_.containsKey(sec_nm_)) {
				L.warn("absurd! section [{}] already exists", sec_nm_);
				continue;
			}
			MonitrFsThreshold fs_threshold_ = new MonitrFsThreshold();
			fs_threshold_._secNm = sec_nm_;

			sval_ = app_param_.chkValStr(sec_nm_, "DEVICE_NM", null);
			if (sval_ == null) {
				fs_threshold_._secNm = null;
			} else {
				fs_threshold_._deviceNm = sval_;
			}

			dval_ = app_param_.chkValDouble(sec_nm_, "THRESHOLD_USE_PCT", 0.0001, 99.9999);
			if (dval_ == null) {
				fs_threshold_._secNm = null;
			} else {
				fs_threshold_._thresholdUsePct = dval_.doubleValue();
			}

			if (fs_threshold_._secNm == null) {
				L.warn("section [{}] refresh error", sec_nm_);
				rc_ = false;
			} else {
				fs_threshold_map_.put(fs_threshold_._secNm, fs_threshold_);
			}
		}

		File fval_ = null;
		Pattern pval_ = null;
		for (String sec_nm_ : app_param_._paramMap.keySet()) {
			if (!sec_nm_.startsWith("DIR."))
				continue;
			if (dir_threshold_map_.containsKey(sec_nm_)) {
				L.warn("absurd! section [{}] already exists", sec_nm_);
				continue;
			}
			MonitrDirThreshold dir_threshold_ = new MonitrDirThreshold();
			dir_threshold_._secNm = sec_nm_;

			sval_ = app_param_.chkValStr(sec_nm_, "DIR_ALIAS", null);
			if (sval_ == null) {
				dir_threshold_._secNm = null;
			} else {
				dir_threshold_._dirAlias = sval_;
			}

			fval_ = app_param_.chkValFile(sec_nm_, "DIR_PATH", true, true, true);
			if (fval_ == null) {
				dir_threshold_._secNm = null;
			} else {
				dir_threshold_._dirPath = fval_.getAbsolutePath();
			}

			sval_ = app_param_.chkValStr(sec_nm_, "FILE_PATTERN", null);
			if (sval_ == null) {
				dir_threshold_._secNm = null;
			} else {
				if (sval_.equals("NULL")) {
					dir_threshold_._filePattern = null;
				} else {
					pval_ = app_param_.chkValPattern(sec_nm_, "FILE_PATTERN", null);
					if (pval_ == null) {
						dir_threshold_._secNm = null;
					} else {
						dir_threshold_._filePattern = pval_;
					}
				}
			}

			lval_ = app_param_.chkValNum(sec_nm_, "IS_NEWEST", 0L, 1L);
			if (lval_ == null) {
				dir_threshold_._secNm = null;
			} else {
				dir_threshold_._isNewest = lval_.intValue();
			}

			lval_ = app_param_.chkValNum(sec_nm_, "THRESHOLD_IDLE", 60L, 864000L);
			if (lval_ == null) {
				dir_threshold_._secNm = null;
			} else {
				dir_threshold_._thresholdIdle = lval_.intValue();
			}

			if (dir_threshold_._secNm == null) {
				L.warn("section [{}] refresh error", sec_nm_);
				rc_ = false;
			} else {
				dir_threshold_map_.put(dir_threshold_._secNm, dir_threshold_);
			}
		}

		if (fs_threshold_map_.isEmpty())
			L.info("no valid FS.* section found");
		if (dir_threshold_map_.isEmpty())
			L.info("no valid DIR.* section found");
		if (rc_) {
			_IntervalProc = interval_proc_;
			_IntervalLoad = interval_load_;
			_IntervalMem = interval_mem_;
			_IntervalFs = interval_fs_;
			_IntervalDir = interval_dir_;
			_Load01Threshold = load_01_threshold_;
			_Load05Threshold = load_05_threshold_;
			_Load15Threshold = load_15_threshold_;
			_MemFreeKbThreshold = mem_free_kb_threshold_;
			_MaxProcThreshold = max_proc_threshold_;
			_FsThresholdMap = fs_threshold_map_;
			_DirThresholdMap = dir_threshold_map_;
			_TraceAppParam();
		}

		return rc_;
	}

	public static boolean RefreshMonitrCfg() {
		MonitrOdb odb_ = new MonitrOdb();
		List<DbMonitrCfgRec> result_ = odb_.getMonitrCfg(_InstNm);
		if (result_ == null) {
			L.warn("call getMonitrCfg failed");
			return false;
		}
		if (result_.isEmpty()) {
			L.warn("getMonitrCfg returns empty result, pls chk cfg");
			return false;
		}

		boolean rc_ = true;
		Map<String, DbMonitrCfgRec> cfg_map_ = new TreeMap<String, DbMonitrCfgRec>();
		for (DbMonitrCfgRec rec_ : result_) {
			if (!rec_.validatePsPattern()) {
				L.warn("[{}] chk ps_pattern [{}] error", rec_.getProg_nm(), rec_.getPs_pattern());
				rc_ = false;
				continue;
			}
			if (!rec_.validateStartCmd()) {
				L.warn("[{}] chk start_cmd [{}] error", rec_.getProg_nm(), rec_.getStart_cmd());
				rc_ = false;
				continue;
			}
			if (!rec_.validateStopCmd()) {
				L.warn("[{}] chk stop_cmd [{}] error", rec_.getProg_nm(), rec_.getStop_cmd());
				rc_ = false;
				continue;
			}
			cfg_map_.put(rec_.getProg_nm(), rec_);
		}

		if (rc_) {
			_MonitrCfgMap = cfg_map_;
			L.info("{} monitr_cfg refreshed", _MonitrCfgMap.size());
			Map<String, Long> prog_pid_map_ = new TreeMap<String, Long>();
			_ProgPidMap = prog_pid_map_;
		} else {
			L.warn("monitr_cfg refresh error, original cfg is reserved");
		}
		int i = 0;
		for (DbMonitrCfgRec rec_ : _MonitrCfgMap.values()) {
			L.debug("No. {} {}", ++i, rec_.toGsonStr());
		}
		return rc_;
	}

	private static void _TraceAppParam() {
		L.debug("_IntervalProc={}, _IntervalLoad={}, _IntervalMem={}, _IntervalFs={}, _IntervalDir={}", new Object[] {
				_IntervalProc, _IntervalLoad, _IntervalMem, _IntervalFs, _IntervalDir });
		L.debug("_Load01Threshold={}, _Load05Threshold={}, _Load15Threshold={}", _Load01Threshold, _Load05Threshold,
				_Load15Threshold);
		L.debug("_MemFreeKbThreshold={}, _MaxProcThreshold={}", _MemFreeKbThreshold, _MaxProcThreshold);
		L.debug("_FsThresholdMap.size()={}", _FsThresholdMap.size());
		int i = 0;
		for (MonitrFsThreshold fs_threshold_ : _FsThresholdMap.values()) {
			L.debug("No. {}, {}", ++i, fs_threshold_.toGsonStr());
		}
		i = 0;
		L.debug("_DirThresholdMap.size()={}", _DirThresholdMap.size());
		for (MonitrDirThreshold dir_threshold_ : _DirThresholdMap.values()) {
			L.debug("No. {}, {}", ++i, dir_threshold_.toGsonStr());
		}
	}
}
