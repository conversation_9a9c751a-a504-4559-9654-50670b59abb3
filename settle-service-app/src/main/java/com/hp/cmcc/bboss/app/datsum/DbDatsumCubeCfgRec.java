package com.hp.cmcc.bboss.app.datsum;

import javax.script.Bindings;
import javax.script.CompiledScript;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbDatsumCubeCfgRec extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(DbDatsumCubeCfgRec.class);
	public static final int ATTR_0_KEY = 0;
	public static final int ATTR_1_INCR = 1;
	public static final int ATTR_2_SUM = 2;
	public static final int ATTR_3_DUMMY = 3;

	private String fmt_nm;
	private Integer sum_idx;
	private Integer src_idx;
	private String eval;
	private Integer sum_attr;
	private Integer opt_src_idx;
	private String opt_eval;
	private Integer aud_flag;

	public String eval(CompiledScript compiled_script, Bindings bindings, DatsumQElem qelem) {
		bindings.clear();
		bindings.put("FIELDS", qelem._fields);
		if (qelem._udr != null) {
			bindings.put("UDR", qelem._udr);
			bindings.put("ATTACH", qelem._udr._eFields[UdrFmt.E_25_R02_RAW_ATTACHMENT]);
		}
		String result_ = null;
		try {
			result_ = (String) compiled_script.eval(bindings);
		} catch (Exception e) {
			L.warn("eval [{}], {} exception", new Object[] { this.toGsonStr(), qelem.toGsonStr(), e });
			result_ = "";
		}
		result_ = result_ == null ? "" : result_.trim();
		return result_;
	}

	public String getFmt_nm() {
		return fmt_nm;
	}

	public void setFmt_nm(String fmt_nm) {
		this.fmt_nm = fmt_nm;
	}

	public Integer getSum_idx() {
		return sum_idx;
	}

	public void setSum_idx(Integer sum_idx) {
		this.sum_idx = sum_idx;
	}

	public Integer getSrc_idx() {
		return src_idx;
	}

	public void setSrc_idx(Integer src_idx) {
		this.src_idx = src_idx;
	}

	public String getEval() {
		return eval;
	}

	public void setEval(String eval) {
		this.eval = eval;
	}

	public Integer getSum_attr() {
		return sum_attr;
	}

	public void setSum_attr(Integer sum_attr) {
		this.sum_attr = sum_attr;
	}

	public Integer getOpt_src_idx() {
		return opt_src_idx;
	}

	public void setOpt_src_idx(Integer opt_src_idx) {
		this.opt_src_idx = opt_src_idx;
	}

	public String getOpt_eval() {
		return opt_eval;
	}

	public void setOpt_eval(String opt_eval) {
		this.opt_eval = opt_eval;
	}

	public Integer getAud_flag() {
		return aud_flag;
	}

	public void setAud_flag(Integer aud_flag) {
		this.aud_flag = aud_flag;
	}
}
