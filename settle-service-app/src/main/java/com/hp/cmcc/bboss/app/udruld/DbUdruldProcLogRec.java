package com.hp.cmcc.bboss.app.udruld;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbUdruldProcLogRec extends GsonObj {
	private String uld_file_nm;
	private Integer rcv_mm;
	private Long rcv_ymdh;
	private Integer acnt_ym;
	private Long file_id;
	private Long org_file_id;
	private Long pp_file_id;
	private Integer uld_cnt;
	private Integer ercy_cnt;
	private Long spare_num1;
	private Long spare_num2;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[11];
		o[0] = uld_file_nm;
		o[1] = rcv_mm;
		o[2] = rcv_ymdh;
		o[3] = acnt_ym;
		o[4] = file_id;
		o[5] = org_file_id;
		o[6] = pp_file_id;
		o[7] = uld_cnt;
		o[8] = ercy_cnt;
		o[9] = spare_num1;
		o[10] = spare_num2;
		return o;
	}

	public String getUld_file_nm() {
		return uld_file_nm;
	}

	public void setUld_file_nm(String uld_file_nm) {
		this.uld_file_nm = uld_file_nm;
	}

	public Integer getRcv_mm() {
		return rcv_mm;
	}

	public void setRcv_mm(Integer rcv_mm) {
		this.rcv_mm = rcv_mm;
	}

	public Long getRcv_ymdh() {
		return rcv_ymdh;
	}

	public void setRcv_ymdh(Long rcv_ymdh) {
		this.rcv_ymdh = rcv_ymdh;
	}

	public Integer getAcnt_ym() {
		return acnt_ym;
	}

	public void setAcnt_ym(Integer acnt_ym) {
		this.acnt_ym = acnt_ym;
	}

	public Long getFile_id() {
		return file_id;
	}

	public void setFile_id(Long file_id) {
		this.file_id = file_id;
	}

	public Long getOrg_file_id() {
		return org_file_id;
	}

	public void setOrg_file_id(Long org_file_id) {
		this.org_file_id = org_file_id;
	}

	public Long getPp_file_id() {
		return pp_file_id;
	}

	public void setPp_file_id(Long pp_file_id) {
		this.pp_file_id = pp_file_id;
	}

	public Integer getUld_cnt() {
		return uld_cnt;
	}

	public void setUld_cnt(Integer uld_cnt) {
		this.uld_cnt = uld_cnt;
	}

	public Integer getErcy_cnt() {
		return ercy_cnt;
	}

	public void setErcy_cnt(Integer ercy_cnt) {
		this.ercy_cnt = ercy_cnt;
	}

	public Long getSpare_num1() {
		return spare_num1;
	}

	public void setSpare_num1(Long spare_num1) {
		this.spare_num1 = spare_num1;
	}

	public Long getSpare_num2() {
		return spare_num2;
	}

	public void setSpare_num2(Long spare_num2) {
		this.spare_num2 = spare_num2;
	}
}
