package com.hp.cmcc.bboss.app.shpmsv;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.entity.DbCustomerRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbDomLdAreaCdProvRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbProvinceCdRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbServBizCodeRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmBlobRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmCtlRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmVerRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlEspRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlFlatRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlObjectRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlOfferRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlPayProvRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRepartParameterRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRepartRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlEspParameterRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRuleItemRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRuleRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlTariffParameterRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlTariffRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbSttlLinearRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbSttlTierRateRec;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmsvOdb {
	private static Logger L = LoggerFactory.getLogger(ShpmsvOdb.class);

	private static final String _Q_PROVINCE_CD = "SELECT PROV_CD, PROV_NM, PROV_WL, CMCC_OPID FROM PROVINCE_CD ORDER BY PROV_CD";

	private static final String _Q_DOM_LD_AREA_CD_PROV = "SELECT SN, LD_AREA_CD, PROV_CD, LD_AREA_NM, EFF_TM, EXP_TM FROM "
			+ "DOM_LD_AREA_CD_PROV ORDER BY LD_AREA_CD, EFF_TM DESC";

	private static final String _Q_STTL_LINEAR_RATE = "SELECT RATE_ID, BIZ_TYPE, OFFER_CODE, PRODUCT_CODE, EC_CODE, AUX_KEY, "
			+ "RATE_OBJ, RATE_UNIT, RATE, EFF_DATE, EXP_DATE, MEMO FROM STTL_LINEAR_RATE "
			+ "ORDER BY BIZ_TYPE, OFFER_CODE, PRODUCT_CODE, EC_CODE, AUX_KEY, EFF_DATE DESC";

	private static final String _Q_STTL_TIER_RATE = "SELECT RATE_ID, BIZ_TYPE, EC_CODE, AUX_KEY, RATE_OBJ, RATE_UNIT, RATE, "
			+ "TIER_OBJ, TIER_UNIT, TIER_IDX, TIER_MIN, TIER_MAX, EFF_DATE, EXP_DATE, IS_MONTHLY, MEMO "
			+ "FROM STTL_TIER_RATE ORDER BY BIZ_TYPE, EC_CODE, AUX_KEY, EFF_DATE DESC, TIER_IDX";

	private static final String _Q_SERV_BIZ_CODE = "SELECT ID, EC_CODE, SERV_CODE, BIZ_CODE, PROD_ORDER_ID, ORDER_ID, "
			+ "BIZ_CODE_APPLY, PROV_CODE, PRODUCT_CODE, SERVICE_CODE, EFFECTIVE_DATE, EXPIRY_DATE, EC_GROUP_ID, "
			+ "SERVICE_TYPE, SEND_PROV, PROD_ORDER_MODE, SUB_GROUP_FLAG, CARRY_TYPE, SIGN_ENTITY, PARENT_ORDER_ID, "
			+ "LOCON_FLAG, ORDER_LEVEL FROM SERV_BIZ_CODE ORDER BY ORDER_ID, SERV_CODE, PRODUCT_CODE, EFFECTIVE_DATE DESC";

	private static final String _Q_CUSTOMER = "SELECT ID, CUSTOMER_CODE, PROVINCE_CODE, FIRST_NAME, CUSTOMER_TYPE, "
			+ "BILLABLE_FLAG, PARENT_ID, PAID_TYPE, NVL(EFFECTIVE_DATE, TO_DATE('19700101','YYYYMMDD')) EFFECTIVE_DATE,"
			+ "NVL(EXPIRY_DATE, TO_DATE('99990101','YYYYMMDD')) EXPIRY_DATE FROM CUSTOMER ORDER BY CUSTOMER_CODE, "
			+ "EFFECTIVE_DATE DESC";

	private static final String _Q_STL_OFFER_T = "SELECT ID, DATA_SOURCE, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, "
			+ "RULE_ID, ROUTE_CODE, DEST_SOURCE, EFF_DATE, EXP_DATE FROM STL_OFFER_T ORDER BY DATA_SOURCE, OFFER_CODE, "
			+ "PRODUCT_CODE, ORDER_MODE, EFF_DATE";

	private static final String _Q_STL_RULE_T = "SELECT RULE_ID, RULE_NAME, OBJECT_ID, BALANCE, EFF_DATE, EXP_DATE "
			+ "FROM STL_RULE_T ORDER BY RULE_ID";

	private static final String _Q_STL_RULE_ITEM_T = "SELECT ID, RULE_ID, CHARGE_ITEM, ITEM_NAME, EFF_DATE, EXP_DATE "
			+ "FROM STL_RULE_ITEM_T ORDER BY ID";

	private static final String _Q_STL_OBJECT_T = "SELECT OBJECT_ID, OBJECT_NAME, OBJECT_TYPE, OWNER_NAME, FIELD_NAME, "
			+ "SEARCH_KEY, OBJECT_VALUE FROM STL_OBJECT_T ORDER BY OBJECT_ID";

	private static final String _Q_STL_PAY_PROV = "SELECT SVC_INST_ID, CHARGE_ITEM, OBJECT_VALUE, EFF_DATE, EXP_DATE "
			+ "FROM STL_PAY_PROV ORDER BY SVC_INST_ID, CHARGE_ITEM, EFF_DATE";

	private static final String _Q_STL_RATE_T = "SELECT ID, RATE_ID, RULE_ID, IN_OBJECT_ID, RATE_CODE, RATE_TYPE, "
			+ "CALC_PRIORITY, ROUND_METHOD, EFF_DATE, EXP_DATE FROM STL_RATE_T ORDER BY RATE_ID";

	private static final String _Q_STL_FLAT_RATE_T = "SELECT RATE_ID, RATE_VALUE FROM STL_FLAT_RATE_T ORDER BY RATE_ID";

	private static final String _Q_STL_TARIFF_RATE_T = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM STL_TARIFF_RATE_T "
			+ "ORDER BY RATE_ID";

	private static final String _Q_STL_TARIFF_PARAMETER_T = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID,  "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, OBJECT_VALUE, TARIFF_TYPE, CALC_PRIORITY, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE FROM STL_TARIFF_PARAMETER_T ORDER BY ID";

	private static final String _Q_STL_REPART_RATE_T = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM STL_REPART_RATE_T "
			+ "ORDER BY RATE_ID";

	private static final String _Q_STL_REPART_PARTITIONS = "SELECT DISTINCT RATE_ID, ACCT_MONTH, RULE_ID "
			+ "FROM STL_REPART_PARAMETER_T WHERE ACCT_MONTH >= ? ORDER BY ACCT_MONTH, RATE_ID, RULE_ID";

	private static final String _Q_STL_REPART_PARAMETER_YYYYMM = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, OBJECT_VALUE, TARIFF_TYPE, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE, ACCT_MONTH FROM STL_REPART_PARAMETER_T "
			+ "WHERE ACCT_MONTH = ? ORDER BY ID";

	//政企ESP局数据结构
	private static final String _Q_STL_ESP_RATE_T = "SELECT RATE_ID, MATCH_MODE FROM STL_ESP_RATE_T ORDER BY RATE_ID";
	
	private static final String _Q_STL_ESP_PARAMETER_T = "SELECT ID, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, "
			+ "OBJECT_VALUE, SETT_TYPE, RATE_VALUE, TAX_RATE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE "
       		+ "FROM STL_ESP_PARAMETER_T T ORDER BY T.ID";
	
	private static final String _Q_SHPM_VER_NEWEST = "SELECT VER_MM, INST_NM, SHPM_VER, VER_FLAG, TM_START, TM_BLOB, "
			+ "TM_FINISH, TS_START, TS_BLOB, TS_FINISH, ERR_MSG FROM SHPM_VER WHERE INST_NM = ? AND TM_START = "
			+ "(SELECT MAX(TM_START) FROM SHPM_VER WHERE INST_NM = ?)";

	private static final String _Q_SHPM_VER_FLAG_NEWEST = "SELECT VER_MM, INST_NM, SHPM_VER, VER_FLAG, TM_START, "
			+ "TM_BLOB, TM_FINISH, TS_START, TS_BLOB, TS_FINISH, ERR_MSG FROM SHPM_VER WHERE INST_NM = ? AND TM_START = "
			+ "(SELECT MAX(TM_START) FROM SHPM_VER WHERE INST_NM = ? AND VER_FLAG = ?)";

	private static final String _I_SHPM_VER = "INSERT INTO SHPM_VER (VER_MM, INST_NM, SHPM_VER, VER_FLAG, "
			+ "TM_START, TS_START) VALUES (?, ?, ?, ?, ?, ?)";

	private static final String _U_SHPM_VER_TM_BLOB = "UPDATE SHPM_VER SET TM_BLOB = ?, TS_BLOB = ? WHERE VER_MM = ? "
			+ "AND INST_NM = ? AND SHPM_VER = ?";

	private static final String _U_SHPM_VER_TM_FINISH = "UPDATE SHPM_VER SET VER_FLAG = ?, TM_FINISH = ?, TS_FINISH = ?, "
			+ "ERR_MSG = ? WHERE VER_MM = ? AND INST_NM = ? AND SHPM_VER = ?";

	private static final String _D_SHPM_VER_TEMP = "DELETE FROM SHPM_VER WHERE INST_NM = ? AND SHPM_VER = ? AND VER_FLAG = 0";

	private static final String _I_SHPM_BLOB = "INSERT INTO SHPM_BLOB (VER_MM, INST_NM, SHPM_VER, SHPM_SIZE, "
			+ "SHPM_CKSUM, SHPM_BLOB) VALUES (?, ?, ?, ?, ?, ?)";

	// field 'SHPM_BLOB' will not be fetched
	private static final String _Q_SHPM_BLOB = "SELECT VER_MM, INST_NM, SHPM_VER, SHPM_SIZE, SHPM_CKSUM FROM SHPM_BLOB "
			+ "WHERE INST_NM = ? AND SHPM_VER = ?";

	private static final String _I_SHPM_CTL = "INSERT INTO SHPM_CTL (VER_MM, INST_NM, SHPM_VER, AGT_INST, CTL_FLAG, "
			+ "TM_RQST, TS_RQST) VALUES (?, ?, ?, ?, ?, ?, ?)";

	private static final String _U_SHPM_CTL_RSPS_NML = "UPDATE SHPM_CTL SET TM_RSPS = ?, TS_RSPS = ? WHERE VER_MM = ? "
			+ "AND INST_NM = ? AND SHPM_VER = ? AND AGT_INST = ?";

	private static final String _U_SHPM_CTL_RSPS_ERR = "UPDATE SHPM_CTL SET TM_RSPS = ?, TS_RSPS = ?, ERR_MSG = ? "
			+ "WHERE VER_MM = ? AND INST_NM = ? AND SHPM_VER = ? AND AGT_INST = ?";

	private static final String _Q_SHOM_CTL = "SELECT VER_MM, INST_NM, SHPM_VER, AGT_INST, CTL_FLAG, TM_RQST, "
			+ "TM_RSPS, TM_DONE, TS_RQST, TS_RSPS, TS_DONE, ERR_MSG FROM SHPM_CTL WHERE VER_MM = ? "
			+ "AND INST_NM = ? AND SHPM_VER = ?";

	public DbShpmVerRec getShpmVerNewest(String inst_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		DbShpmVerRec rec_ = cli_.queryForObject(_Q_SHPM_VER_NEWEST, DbShpmVerRec.class, inst_nm, inst_nm);
		return rec_;
	}

	public DbShpmVerRec getShpmVerNewest(String inst_nm, int ver_flag) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		DbShpmVerRec rec_ = cli_.queryForObject(_Q_SHPM_VER_FLAG_NEWEST, DbShpmVerRec.class, inst_nm, inst_nm, ver_flag);
		return rec_;
	}

	public void addShpmVer(DbShpmVerRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		cli_.updateWithThrow(_I_SHPM_VER, rec.asInsertObjArray());
	}

	public int updShpmVerTmBlob(DbShpmVerRec rec) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		int rows_affected_ = cli_.update(_U_SHPM_VER_TM_BLOB, rec.getTm_blob(), rec.getTs_blob(), rec.getVer_mm(), rec.getInst_nm(),
				rec.getShpm_ver());
		if (rows_affected_ > 0) {
			L.debug("upd ts_blob done, {}", rec.toGsonStr());
		} else {
			L.info("upd ts_blob rows_affected_={}, {}", rows_affected_, rec.toGsonStr());
		}
		return rows_affected_;
	}

	public int updShpmVerTmFinish(DbShpmVerRec rec) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		int rows_affected_ = cli_.update(_U_SHPM_VER_TM_FINISH, rec.getVer_flag(), rec.getTm_finish(), rec.getTs_finish(),
				rec.getErr_msg(), rec.getVer_mm(), rec.getInst_nm(), rec.getShpm_ver());
		if (rows_affected_ > 0) {
			L.debug("upd ts_finish done, {}", rec.toGsonStr());
		} else {
			L.info("upd ts_finish rows_affected_={}, {}", rows_affected_, rec.toGsonStr());
		}
		return rows_affected_;
	}

	public int delShpmVerTemp(String inst_nm, String shpm_ver) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		int rows_affected_ = cli_.update(_D_SHPM_VER_TEMP, inst_nm, shpm_ver);
		if (rows_affected_ > 0) {
			L.info("del temp SHPM_VER({},{}) done", inst_nm, shpm_ver);
		} else {
			L.info("del temp SHPM_VER({},{}) rows_affected_={}", inst_nm, shpm_ver, rows_affected_);
		}
		return rows_affected_;
	}

	public List<DbProvinceCdRec> getProvinceCd() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbProvinceCdRec> result_ = cli_.queryForOListWithThrow(_Q_PROVINCE_CD, DbProvinceCdRec.class);
		return result_;
	}

	public List<DbDomLdAreaCdProvRec> getDomLdAreaCdProv() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbDomLdAreaCdProvRec> result_ = cli_.queryForOListWithThrow(_Q_DOM_LD_AREA_CD_PROV, DbDomLdAreaCdProvRec.class);
		return result_;
	}

	public List<DbSttlLinearRateRec> getSttlLinearRate() {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbSttlLinearRateRec> result_ = cli_.queryForOList(_Q_STTL_LINEAR_RATE, DbSttlLinearRateRec.class);
		return result_;
	}

	public List<DbSttlTierRateRec> getSttlTierRate() {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbSttlTierRateRec> result_ = cli_.queryForOList(_Q_STTL_TIER_RATE, DbSttlTierRateRec.class);
		return result_;
	}

	public List<DbServBizCodeRec> getServBizCode(OdbCli cli) throws SQLException {
		OdbCli cli_ = cli;
		if (cli_ == null)
			cli_ = OdbAgt.GetBizInstance();
		L.debug("SERV_BIZ_CODE init SQL: {}", _Q_SERV_BIZ_CODE);
		List<DbServBizCodeRec> result_ = cli_.queryForOListWithThrow(_Q_SERV_BIZ_CODE, DbServBizCodeRec.class);
		return result_;
	}

	public List<DbCustomerRec> getCustomer() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbCustomerRec> result_ = cli_.queryForOListWithThrow(_Q_CUSTOMER, DbCustomerRec.class);
		return result_;
	}

	public List<DbStlOfferRec> getStlOffer() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlOfferRec> result_ = cli_.queryForOListWithThrow(_Q_STL_OFFER_T, DbStlOfferRec.class);
		return result_;
	}

	public List<DbStlRuleRec> getStlRule() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlRuleRec> result_ = cli_.queryForOListWithThrow(_Q_STL_RULE_T, DbStlRuleRec.class);
		return result_;
	}

	public List<DbStlRuleItemRec> getStlRuleItem() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlRuleItemRec> result_ = cli_.queryForOListWithThrow(_Q_STL_RULE_ITEM_T, DbStlRuleItemRec.class);
		return result_;
	}

	public List<DbStlObjectRec> getStlObject() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlObjectRec> result_ = cli_.queryForOListWithThrow(_Q_STL_OBJECT_T, DbStlObjectRec.class);
		return result_;
	}

	public List<DbStlPayProvRec> getStlPayProv() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlPayProvRec> result_ = cli_.queryForOListWithThrow(_Q_STL_PAY_PROV, DbStlPayProvRec.class);
		return result_;
	}

	public List<DbStlRateRec> getStlRate() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlRateRec> result_ = cli_.queryForOListWithThrow(_Q_STL_RATE_T, DbStlRateRec.class);
		return result_;
	}

	public List<DbStlFlatRateRec> getStlFlatRate() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlFlatRateRec> result_ = cli_.queryForOListWithThrow(_Q_STL_FLAT_RATE_T, DbStlFlatRateRec.class);
		return result_;
	}

	public List<DbStlTariffRateRec> getStlTariffRate() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlTariffRateRec> result_ = cli_.queryForOListWithThrow(_Q_STL_TARIFF_RATE_T, DbStlTariffRateRec.class);
		return result_;
	}

	public List<DbStlTariffParameterRec> getStlTariffParameter() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlTariffParameterRec> result_ = cli_.queryForOListWithThrow(_Q_STL_TARIFF_PARAMETER_T,
				DbStlTariffParameterRec.class);
		return result_;
	}

	public List<DbStlRepartRateRec> getStlRepartRate() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlRepartRateRec> result_ = cli_.queryForOListWithThrow(_Q_STL_REPART_RATE_T, DbStlRepartRateRec.class);
		return result_;
	}

	public List<Object[]> getStlRepartPartitions(String min_acct_month) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<Object[]> result_ = cli_.queryForTsListWithThrow(_Q_STL_REPART_PARTITIONS, min_acct_month);
		return result_;
	}

	public List<DbStlRepartParameterRec> getStlRepartParameterYyyymm(String acct_month) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlRepartParameterRec> result_ = cli_.queryForOListWithThrow(_Q_STL_REPART_PARAMETER_YYYYMM,
				DbStlRepartParameterRec.class, acct_month);
		return result_;
	}
	
	public List<DbStlEspRateRec> getStlEspRate() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlEspRateRec> result_ = cli_.queryForOListWithThrow(_Q_STL_ESP_RATE_T, DbStlEspRateRec.class);
		return result_;
	}
	
	public List<DbStlEspParameterRec> getStlEspParameter() throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbStlEspParameterRec> result_ = cli_.queryForOListWithThrow(_Q_STL_ESP_PARAMETER_T, DbStlEspParameterRec.class);
		return result_;
	}

	public void addShpmBlob(DbShpmBlobRec rec, File gzipped_db3_file) throws Exception {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		FileInputStream fis_ = null;
		Connection conn_ = null;
//		Blob blob_ = null;
		PreparedStatement pstmt_ = null;
		try {
			fis_ = new FileInputStream(gzipped_db3_file);
			conn_ = cli_.getCanonicalConnection();
			conn_.setAutoCommit(false);
//			blob_ = oracle.sql.BLOB.createTemporary(conn_, false, oracle.sql.BLOB.DURATION_SESSION);
//			blob_ = conn_.createBlob();
//			OutputStream os_ = blob_.setBinaryStream(1);
//			byte[] bin_ = new byte[2048];
//			int total_ = 0;
//			int chunk_;
//			while ((chunk_ = fis_.read(bin_)) != -1) {
//				os_.write(bin_, 0, chunk_);
//				total_ += chunk_;
//			}
//			os_.flush();
//			rec.setShpm_blob(blob_);
			pstmt_ = conn_.prepareStatement(_I_SHPM_BLOB);
			pstmt_.setInt(1, rec.getVer_mm());
			pstmt_.setString(2, rec.getInst_nm());
			pstmt_.setString(3, rec.getShpm_ver());
			pstmt_.setInt(4, rec.getShpm_size());
			pstmt_.setString(5, rec.getShpm_cksum());
			pstmt_.setBinaryStream(6, fis_);
			int rows_affected_ = pstmt_.executeUpdate();
//			blob_.free();
			conn_.commit();
			L.debug("{} added, rows affected {}, blob length {}", rec.toString(), rows_affected_, gzipped_db3_file.length());
		} finally {
			PubMethod.Close(fis_);
			cli_.close(null, pstmt_, conn_);
		}
	}

	public DbShpmBlobRec getShpmBlob(DbShpmVerRec ver_rec) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		DbShpmBlobRec result_ = cli_.queryForObject(_Q_SHPM_BLOB, DbShpmBlobRec.class, ver_rec.getInst_nm(), ver_rec.getShpm_ver());
		return result_;
	}

	public void addShpmCtl(List<DbShpmCtlRec> rec_list) throws SQLException {
		if (rec_list == null) {
			L.warn("rec_list is null, do nothing");
			return;
		}
		if (rec_list.isEmpty()) {
			L.warn("rec_list is empty, do nothing");
			return;
		}
		OdbCli cli_ = OdbAgt.GetBizInstance();
		Connection conn_ = null;
		DbShpmCtlRec current_rec_ = null;
		try {
			conn_ = cli_.getConnection();
			for (DbShpmCtlRec rec_ : rec_list) {
				current_rec_ = rec_;
				cli_.update(conn_, _I_SHPM_CTL, rec_.asInsertObjArray());
			}
			cli_.commit(conn_);
		} catch (SQLException e) {
			if (current_rec_ != null) {
				L.warn("insert {} exception", current_rec_.toGsonStr(), e);
			}
			cli_.rollback(conn_);
			throw e;
		} finally {
			cli_.close(conn_);
		}
		L.debug("{} SHPM_CTL records inserted", rec_list.size());
	}

	public int updShpmCtlRsps(DbShpmCtlRec rec) {
		int rows_affected_ = 0;
		OdbCli cli_ = OdbAgt.GetBizInstance();
		if (rec.getCtl_flag() == DbShpmVerRec.VER_FLAG_0_INIT) {
			rows_affected_ = cli_.update(_U_SHPM_CTL_RSPS_NML, rec.getTm_rsps(), rec.getTs_rsps(), rec.getVer_mm(),
					rec.getInst_nm(), rec.getShpm_ver(), rec.getAgt_inst());
		} else {
			rows_affected_ = cli_.update(_U_SHPM_CTL_RSPS_ERR, rec.getTm_rsps(), rec.getTs_rsps(), rec.getErr_msg(),
					rec.getVer_mm(), rec.getInst_nm(), rec.getShpm_ver(), rec.getAgt_inst());
		}
		return rows_affected_;
	}

	public Map<String, DbShpmCtlRec> getShpmCtl(int ver_mm, String inst_nm, String shpm_ver) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbShpmCtlRec> db_result_ = cli_.queryForOList(_Q_SHOM_CTL, DbShpmCtlRec.class, ver_mm, inst_nm, shpm_ver);
		if (db_result_ == null) {
			return null;
		} else {
			Map<String, DbShpmCtlRec> map_ = new HashMap<String, DbShpmCtlRec>();
			for (DbShpmCtlRec rec_ : db_result_) {
				map_.put(rec_.getAgt_inst(), rec_);
			}
			return map_;
		}
	}
}
