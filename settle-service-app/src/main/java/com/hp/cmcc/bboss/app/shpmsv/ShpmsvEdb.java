package com.hp.cmcc.bboss.app.shpmsv;

import java.io.File;

public abstract class ShpmsvEdb {
	protected ShpmsvOdb _odb;
	protected int _rdbCnt; // Relational DB count
	protected int _edbCnt; // Embedded DB count

	public ShpmsvEdb(ShpmsvOdb odb) {
		_odb = odb;
	}

	public int getRdbCnt() {
		return _rdbCnt;
	}

	public int getEdbCnt() {
		return _edbCnt;
	}

	public abstract File encapsulateEdb(String shpm_ver) throws Exception;
}
