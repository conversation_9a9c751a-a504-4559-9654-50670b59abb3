package com.hp.cmcc.bboss.app;

import javax.ws.rs.NotFoundException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Provider
public class RestNotFoundExceptionHandler implements ExceptionMapper<NotFoundException> {
	private static Logger L = LoggerFactory.getLogger(RestNotFoundExceptionHandler.class);

	@Override
	public Response toResponse(NotFoundException e) {
		L.warn("encounter exception", e);
		return Response.ok().entity(e.toString()).type(MediaType.TEXT_PLAIN).build();
	}
}
