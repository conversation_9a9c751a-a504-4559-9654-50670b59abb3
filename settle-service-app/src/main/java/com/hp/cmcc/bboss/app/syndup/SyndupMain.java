package com.hp.cmcc.bboss.app.syndup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class SyndupMain {
	private static Logger L = LoggerFactory.getLogger(SyndupMain.class);
	private static SyndupBizRunnable _BizRunnable0;
	private static SyndupBizRunnable _BizRunnable1;
	private static Thread _BizThread0;
	private static Thread _BizThread1;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("syndup", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 2, 0, 3)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!SyndupCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		if (!MdbAgt.Init(OdbSystemParam.GetInstance(), 0, 0, 3)) {
			L.error("init mdb connection error, JVM exit");
			System.exit(1);
		}
		_BizRunnable0 = new SyndupBizRunnable(true);
		_BizRunnable1 = new SyndupBizRunnable(false);
		_BizThread0 = new Thread(_BizRunnable0, "SYN0");
		_BizThread1 = new Thread(_BizRunnable1, "SYN1");
		_BizThread0.start();
		_BizThread1.start();
	}

	private static void _MainLoop() throws InterruptedException {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			PubMethod.Sleep(1000);
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}
			L.info("try stop normal");
			_BizRunnable0.setTerminateFlag();
			_BizRunnable1.setTerminateFlag();
			L.debug("try join thread {}", _BizThread0.getName());
			_BizThread0.join();
			L.debug("thread {} joined", _BizThread0.getName());
			L.debug("try join thread {}", _BizThread1.getName());
			_BizThread1.join();
			L.debug("thread {} joined", _BizThread1.getName());
			break;
		}
	}
}
