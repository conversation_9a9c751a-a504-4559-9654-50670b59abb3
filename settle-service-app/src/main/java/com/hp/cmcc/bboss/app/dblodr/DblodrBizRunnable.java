package com.hp.cmcc.bboss.app.dblodr;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PatternFileFilter;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DblodrBizRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(DblodrBizRunnable.class);

	private static final Pattern _SQLLDR_TABLE_NAME_E = Pattern.compile("^Table [A-Za-z][A-Za-z0-9_]*:$");
	private static final String _SQLLDR_LOAD_ROWS_E = "successfully loaded";
	private static final String _SQLLDR_READ_ROWS_E = "Total logical records read";
	private static final String _SQLLDR_RJCT_ROWS_E = "Total logical records rejected";
	private static final String _SQLLDR_DSCD_ROWS_E = "Total logical records discarded";

	private static final Pattern _SQLLDR_TABLE_NAME_C = Pattern.compile("^表 [A-Za-z][A-Za-z0-9_]*:$");
	private static final String _SQLLDR_LOAD_ROWS_C = "行载入成功";
	private static final String _SQLLDR_READ_ROWS_C = "读取的逻辑记录总数";
	private static final String _SQLLDR_RJCT_ROWS_C = "拒绝的逻辑记录总数";
	private static final String _SQLLDR_DSCD_ROWS_C = "废弃的逻辑记录总数";

	private static final Pattern _EXP_NUM_HEAD = Pattern.compile("^ *[0-9]+ ");
	private static final Pattern _EXP_NUM_TAIL = Pattern.compile(" +[0-9]+ *$");

	private static final String _SqlldrCmd = "%s userid=%s silent=header,feedback control=%s data=%s bad=%s log=%s "
			+ "bindsize=10485760 readsize=10485760 errors=1000000000 skip_unusable_indexes=true commit_discontinued=true";
	private List<DblodrCtlSec> _ctlSecList;
	private DbDblodrLogRec _logRec;
	private DblodrFh _fh;
	private long _lastScanTs;
	private DblodrOdb _odb;
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public DblodrBizRunnable(List<DblodrCtlSec> ctl_sec_list) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_ctlSecList = ctl_sec_list;
		_lastScanTs = System.currentTimeMillis();
		_odb = new DblodrOdb();
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
				_interval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
		L.info("thread terminated");
	}

	private void _exec() throws SQLException {
		for (DblodrCtlSec ctl_sec_ : _ctlSecList) {
			_execCtlSec(ctl_sec_);
		}
	}

	private void _execCtlSec(DblodrCtlSec ctl_sec) throws SQLException {
		PatternFileFilter filter_ = new PatternFileFilter(ctl_sec._pattern, PatternFileFilter.TypeFilter.TypeFile);
		File src_dir_ = new File(ctl_sec._srcDir);
		File[] bcp_files_ = src_dir_.listFiles(filter_);
		if (bcp_files_.length == 0) {
			long now_ = System.currentTimeMillis();
			long idle_ = now_ - _lastScanTs;
			if (idle_ > 1800 * 1000) {
				L.debug("idling 1800 seconds");
				_lastScanTs = now_;
			}
			return;
		}
		Arrays.sort(bcp_files_);
		L.debug("{} file(s) scaned in [{}]", bcp_files_.length, src_dir_.getAbsolutePath());
		AppTicker ticker_ = new AppTicker();
		_lastScanTs = ticker_._tsStart;
		for (File f : bcp_files_) {
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
			ticker_.tickStart();
			_logRec = new DbDblodrLogRec();
			_logRec.setTs_start(ticker_._tsStart);
			_initLog(f);
			_procOneFile(f, ctl_sec);
			ticker_.tickEnd(_logRec.getTot_cnt());
			L.debug("{} term:cnt:pfm {}:{}:{}", new Object[] { f.getName(), ticker_._term, ticker_._recNum, ticker_._pfm });
		}
	}

	private void _initLog(File f) {
		String tm_start_ = PubMethod.Long2Str(_logRec.getTs_start(), PubMethod.TimeStrFmt.Fmt14);
		_logRec.setLog_mm(Integer.parseInt(tm_start_.substring(4, 6)));
		_logRec.setFile_nm(f.getName());
		_logRec.setProc_flag(DbDblodrLogRec._FLAG_0_SUCCESS);
		_logRec.setTot_cnt(0);
		_logRec.setDb_cnt(0);
		_logRec.setBad_cnt(0);
		_logRec.setDscd_cnt(0);
		_logRec.setTbl_nm("-");
		_logRec.setFile_sz(f.length());
		_logRec.setTs_end(0L);
	}

	private void _procOneFile(File f, DblodrCtlSec ctl_sec) throws SQLException {
		_fh = new DblodrFh(ctl_sec, f);
		_fh._badHandle._srcFile.delete();
		_fh._logHandle._srcFile.delete();
		if (ctl_sec._script == null || ctl_sec._script.equals("NULL")) {
			_procSqlldr(f, ctl_sec);
		} else {
			_procScript(f, ctl_sec);
		}
		if (!_fh._logHandle._srcFile.isFile()) {
			_logRec.setProc_flag(DbDblodrLogRec._FLAG_2_FAIL);
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341013_BCP_LOG_NONEXISTS,
					PubMethod.FmtArgs("[%s] not exists, regard as fail", _fh._logHandle._srcFilePath), ctl_sec.toGsonStr());
			alm_.setAlm_kpi(_logRec.toGsonStr());
			L.warn("{}", alm_.getAlm_msg());
			_odbUtils.addRawAlm(null, alm_);
		} else {
			if (_fh._badHandle._srcFile.isFile()) {
				_logRec.setProc_flag(DbDblodrLogRec._FLAG_1_BAD);
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205331014_BCP_BAD_DETECTED,
						PubMethod.FmtArgs("[%s] detected", _fh._badHandle._srcFilePath), ctl_sec.toGsonStr());
				alm_.setAlm_kpi(_logRec.toGsonStr());
				L.warn("{} detected", _fh._badHandle._srcFilePath);
				_odbUtils.addRawAlm(null, alm_);
			}
			_parseSqlldrLog(ctl_sec);
		}

		if (_logRec.getProc_flag() == DbDblodrLogRec._FLAG_0_SUCCESS) {
			_fh.successHandle();
		} else {
			_fh.badHandle();
		}
		_logRec.setTs_end(System.currentTimeMillis());
		_odb.addDblodrLog(_logRec);
	}

	private void _procSqlldr(File f, DblodrCtlSec ctl_sec) {
		String db_udr_login_ = OdbAgt.GetUdrInstance().getLoginStrSqlpus();
		String sqlldr_cmd_ = String.format(_SqlldrCmd, DblodrCfg._SqlldrPath, db_udr_login_, ctl_sec._ctlFile, f.getAbsolutePath(),
				_fh._badHandle._srcFilePath, _fh._logHandle._srcFilePath);
		_execShellCmd(sqlldr_cmd_, ctl_sec);
	}

	private void _procScript(File f, DblodrCtlSec ctl_sec) {
		String db_udr_login_ = OdbAgt.GetUdrInstance().getLoginStrSqlpus();
		StringBuilder sb_ = new StringBuilder();
		sb_.append(String.format("%s %s %s %s", ctl_sec._script, AppCmdline.GetInstance()._workingDir, db_udr_login_,
				f.getAbsolutePath()));
		if (ctl_sec._args != null && !ctl_sec._args.isEmpty()) {
			for (String arg_ : ctl_sec._args) {
				sb_.append(" ");
				sb_.append(arg_);
			}
		}
		_execShellCmd(sb_.substring(0), ctl_sec);
	}

	private void _execShellCmd(String shell_cmd, DblodrCtlSec ctl_sec) {
		List<String> cmd_ = PubMethod.AssembleCmd(shell_cmd);
		ProcessBuilder pb_ = new ProcessBuilder(cmd_);
		pb_.inheritIO();
		try {
			Process proc_ = pb_.start();
			int exit_code_ = proc_.waitFor();
			if (exit_code_ == 0) {
				L.trace("invoke [{}] ok", shell_cmd);
			} else {
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341011_SHELL_RCNZ,
						PubMethod.FmtArgs("invoke [%s] returns %d", shell_cmd, exit_code_), ctl_sec.toGsonStr());
				L.warn("{}", alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341012_SHELL_EXCEPTION,
					PubMethod.FmtArgs("invoke [%s] exception", shell_cmd), ctl_sec.toGsonStr());
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
		}
	}

	private void _parseSqlldrLog(DblodrCtlSec ctl_sec) {
		boolean flag_load_rows_ = false;
		boolean flag_read_rows_ = false;
		boolean flag_rjct_rows_ = false;
		boolean flag_dscd_rows_ = false;
		BufferedReader br_ = null;
		try {
			br_ = new BufferedReader(new FileReader(_fh._logHandle._srcFile));
			L.trace("[{}] opened for parsing", _fh._logHandle._srcFilePath);
			String log_line_;
			while ((log_line_ = br_.readLine()) != null) {
				if (log_line_.contains(_SQLLDR_LOAD_ROWS_E) || log_line_.contains(_SQLLDR_LOAD_ROWS_C)) {
					_logRec.setDb_cnt(_extractNum(log_line_, _EXP_NUM_HEAD));
					flag_load_rows_ = _logRec.getDb_cnt() < 0 ? false : true;
				} else if (log_line_.contains(_SQLLDR_READ_ROWS_E) || log_line_.contains(_SQLLDR_READ_ROWS_C)) {
					_logRec.setTot_cnt(_extractNum(log_line_, _EXP_NUM_TAIL));
					flag_read_rows_ = _logRec.getTot_cnt() < 0 ? false : true;
				} else if (log_line_.contains(_SQLLDR_RJCT_ROWS_E) || log_line_.contains(_SQLLDR_RJCT_ROWS_C)) {
					_logRec.setBad_cnt(_extractNum(log_line_, _EXP_NUM_TAIL));
					flag_rjct_rows_ = _logRec.getBad_cnt() < 0 ? false : true;
				} else if (log_line_.contains(_SQLLDR_DSCD_ROWS_E) || log_line_.contains(_SQLLDR_DSCD_ROWS_C)) {
					_logRec.setDscd_cnt(_extractNum(log_line_, _EXP_NUM_TAIL));
					flag_dscd_rows_ = _logRec.getDscd_cnt() < 0 ? false : true;
				} else if (_SQLLDR_TABLE_NAME_E.matcher(log_line_).find() || _SQLLDR_TABLE_NAME_C.matcher(log_line_).find()) {
					String[] args_ = log_line_.split("[ :]");
					_logRec.setTbl_nm(args_[1]);
				}
			}

			if (!flag_load_rows_ || !flag_read_rows_ || !flag_rjct_rows_ || !flag_dscd_rows_) {
				_logRec.setProc_flag(DbDblodrLogRec._FLAG_3_LOG_PARSE_ERR);
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341015_BCP_LOG_PARSE_FAIL,
						PubMethod.FmtArgs("parse [%s] error, flags load:read:rjct:dscd %b:%b:%b:%b", _fh._logHandle._srcFilePath,
								flag_load_rows_, flag_read_rows_, flag_rjct_rows_, flag_dscd_rows_),
						ctl_sec.toGsonStr());
				alm_.setAlm_kpi(_logRec.toGsonStr());
				L.warn("{}", alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
				return;
			}

			int sum_ = _logRec.getDb_cnt() + _logRec.getBad_cnt() + _logRec.getDscd_cnt();
			if (_logRec.getTot_cnt() != sum_) {
				_logRec.setProc_flag(DbDblodrLogRec._FLAG_3_LOG_PARSE_ERR);
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341016_BCP_LOG_NOT_BALANCE,
						PubMethod.FmtArgs("parse [%s] not balance, tot ne db + bad + dscd, %d ne %d, (%d + %d + %d), pls chk",
								_fh._logHandle._srcFilePath, _logRec.getTot_cnt(), sum_, _logRec.getDb_cnt(), _logRec.getBad_cnt(),
								_logRec.getDscd_cnt()),
						ctl_sec.toGsonStr());
				alm_.setAlm_kpi(_logRec.toGsonStr());
				L.warn("{}", alm_.getAlm_msg());
				_odbUtils.addRawAlm(null, alm_);
			}
		} catch (Exception e) {
			_logRec.setProc_flag(DbDblodrLogRec._FLAG_3_LOG_PARSE_ERR);
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_205341017_BCP_LOG_PARSE_EXCEPTION,
					PubMethod.FmtArgs("parse [%s] exception", _fh._logHandle._srcFilePath), ctl_sec.toGsonStr());
			L.warn("{}", alm_.getAlm_msg(), e);
			alm_.setAlm_msg(alm_.getAlm_msg() + ", " + e.toString());
			alm_.setAlm_kpi(_logRec.toGsonStr());
			_odbUtils.addRawAlm(null, alm_);
		} finally {
			PubMethod.Close(br_);
		}
	}

	private int _extractNum(String log_line, Pattern pattern) {
		Matcher m = pattern.matcher(log_line);
		if (!m.find()) {
			L.warn("[{}] not match [{}]", log_line, pattern.pattern());
			return -1;
		}
		String num_str_ = m.group();
		num_str_ = num_str_.trim();
		return (int) PubMethod.A2L(num_str_);
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		for (int i = 0; i < DblodrCfg._Interval; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}
}
