package com.hp.cmcc.bboss.app.locdis;

import java.io.File;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DbLocdisCfgRec extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(DbLocdisCfgRec.class);
	public static final int ENABLED_1_MOVE = 1;
	public static final int ENABLED_2_ARCH_YM = 2;
	public static final int ENABLED_3_ARCH_YMD = 3;
	public static final int LOG_FLAG_0_DISABLED = 0;
	public static final int LOG_FLAG_1_ENABLED = 1;

	public Pattern _patternMat;
	public Pattern _patternIgn;

	private Integer cfg_id;
	private String inst_nm;
	private String src_dir;
	private Integer priority;
	private String dst_dir;
	private String bak_dir;
	private String pattern_mat;
	private String pattern_ign;
	private Long delay_dur;
	private String act_script;
	private Integer enabled;
	private Integer log_flag;

	public boolean validateRec() {
		boolean rc_ = true;
		if (!validateSrcDir())
			rc_ = false;
		if (!validateDstDir())
			rc_ = false;
		if (!validateBakDir())
			rc_ = false;
		if (!validatePatternMat())
			rc_ = false;
		if (!validatePatternIgn())
			rc_ = false;
		if (!validateActScript())
			rc_ = false;
		return rc_;
	}

	public boolean validatePatternMat() {
		try {
			_patternMat = Pattern.compile(pattern_mat);
			return true;
		} catch (Exception e) {
			L.warn("[{}] invalid pattern_mat [{}]", cfg_id, pattern_mat);
			_patternMat = null;
			return false;
		}
	}

	public boolean validatePatternIgn() {
		if (pattern_ign == null) {
			_patternIgn = null;
			return true;
		}
		try {
			_patternIgn = Pattern.compile(pattern_ign);
			return true;
		} catch (Exception e) {
			L.warn("[{}] invalid pattern_ign [{}]", cfg_id, pattern_ign);
			_patternIgn = null;
			return false;
		}
	}

	public boolean validateActScript() {
		if (act_script == null)
			return true;
		String script_ = PubMethod.SubStrByEnv(act_script);
		if (script_ == null) {
			L.warn("[{}] act_script SubStrByEnv [{}] failed", cfg_id, act_script);
			return false;
		}
		String[] cmd_array_ = script_.split(" ");
		File cmd_ = new File(cmd_array_[0]);
		if (!cmd_.isFile()) {
			L.warn("[{}] act_script [{}], cmd [{}] not exists", cfg_id, act_script, cmd_.getAbsolutePath());
			return false;
		}
		if (!cmd_.canExecute()) {
			L.warn("[{}] act_script [{}], cmd [{}] not executable", cfg_id, act_script, cmd_.getAbsolutePath());
			return false;
		}
		act_script = script_;
		return true;
	}

	public boolean validateSrcDir() {
		String s = PubMethod.SubStrByEnv(src_dir);
		if (s == null) {
			L.warn("[{}] src_dir SubStrByEnv [{}] failed", cfg_id, src_dir);
			return false;
		}
		File f = new File(s);
		if (!f.isDirectory()) {
			L.warn("[{}] src_dir [{}] not exists or not a directory", cfg_id, s);
			return false;
		}
		src_dir = s;
		return true;
	}

	public boolean validateDstDir() {
		String s = PubMethod.SubStrByEnv(dst_dir);
		if (s == null) {
			L.warn("[{}] dst_dir SubStrByEnv [{}] failed", cfg_id, dst_dir);
			return false;
		}
		File f = new File(s);
		if (!f.isDirectory()) {
			L.warn("[{}] dst_dir [{}] not exists or not a directory", cfg_id, s);
			return false;
		}
		dst_dir = s;
		return true;
	}

	public boolean validateBakDir() {
		if (bak_dir == null)
			return true;
		String s = PubMethod.SubStrByEnv(bak_dir);
		if (s == null) {
			L.warn("[{}] bak_dir SubStrByEnv [{}] failed", cfg_id, bak_dir);
			return false;
		}
		File f = new File(s);
		if (!f.isDirectory()) {
			L.warn("[{}] bak_dir [{}] not exists or not a directory", cfg_id, s);
			return false;
		}
		bak_dir = s;
		return true;
	}

	public Integer getCfg_id() {
		return cfg_id;
	}

	public void setCfg_id(Integer cfg_id) {
		this.cfg_id = cfg_id;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getSrc_dir() {
		return src_dir;
	}

	public void setSrc_dir(String src_dir) {
		this.src_dir = src_dir;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public String getDst_dir() {
		return dst_dir;
	}

	public void setDst_dir(String dst_dir) {
		this.dst_dir = dst_dir;
	}

	public String getBak_dir() {
		return bak_dir;
	}

	public void setBak_dir(String bak_dir) {
		this.bak_dir = bak_dir;
	}

	public String getPattern_mat() {
		return pattern_mat;
	}

	public void setPattern_mat(String pattern_mat) {
		this.pattern_mat = pattern_mat;
	}

	public String getPattern_ign() {
		return pattern_ign;
	}

	public void setPattern_ign(String pattern_ign) {
		this.pattern_ign = pattern_ign;
	}

	public Long getDelay_dur() {
		return delay_dur;
	}

	public void setDelay_dur(Long delay_dur) {
		this.delay_dur = delay_dur;
	}

	public String getAct_script() {
		return act_script;
	}

	public void setAct_script(String act_script) {
		this.act_script = act_script;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Integer getLog_flag() {
		return log_flag;
	}

	public void setLog_flag(Integer log_flag) {
		this.log_flag = log_flag;
	}
}
