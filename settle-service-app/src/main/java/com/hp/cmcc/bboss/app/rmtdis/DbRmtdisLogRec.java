package com.hp.cmcc.bboss.app.rmtdis;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbRmtdisLogRec extends GsonObj {
	public static final int _TRANS_STATUS_0_SUCCESS = 0;
	public static final int _TRANS_STATUS_1_FAIL = 1;
	private Integer mmdd;
	private String inst_nm;
	private String trans_cfg_nm;
	private String login_cfg_nm;
	private String trans_mode;
	private String file_nm;
	private Long file_sz;
	private Long file_cksum;
	private String rmt_host;
	private Integer rmt_port;
	private String rmt_user;
	private String rmt_dir;
	private Timestamp rmt_modify_tm;
	private String loc_host;
	private Integer loc_port;
	private String loc_dir;
	private Timestamp loc_modify_tm;
	private Long ts_start;
	private Long ts_end;
	private Timestamp tm_end;
	private Integer trans_status;
	private String err_msg;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[22];
		o[0] = mmdd;
		o[1] = inst_nm;
		o[2] = trans_cfg_nm;
		o[3] = login_cfg_nm;
		o[4] = trans_mode;
		o[5] = file_nm;
		o[6] = file_sz;
		o[7] = file_cksum;
		o[8] = rmt_host;
		o[9] = rmt_port;
		o[10] = rmt_user;
		o[11] = rmt_dir;
		o[12] = rmt_modify_tm;
		o[13] = loc_host;
		o[14] = loc_port;
		o[15] = loc_dir;
		o[16] = loc_modify_tm;
		o[17] = ts_start;
		o[18] = ts_end;
		o[19] = tm_end;
		o[20] = trans_status;
		o[21] = err_msg;
		return o;
	}

	public Integer getMmdd() {
		return mmdd;
	}

	public void setMmdd(Integer mmdd) {
		this.mmdd = mmdd;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getTrans_cfg_nm() {
		return trans_cfg_nm;
	}

	public void setTrans_cfg_nm(String trans_cfg_nm) {
		this.trans_cfg_nm = trans_cfg_nm;
	}

	public String getLogin_cfg_nm() {
		return login_cfg_nm;
	}

	public void setLogin_cfg_nm(String login_cfg_nm) {
		this.login_cfg_nm = login_cfg_nm;
	}

	public String getTrans_mode() {
		return trans_mode;
	}

	public void setTrans_mode(String trans_mode) {
		this.trans_mode = trans_mode;
	}

	public String getFile_nm() {
		return file_nm;
	}

	public void setFile_nm(String file_nm) {
		this.file_nm = file_nm;
	}

	public Long getFile_sz() {
		return file_sz;
	}

	public void setFile_sz(Long file_sz) {
		this.file_sz = file_sz;
	}

	public Long getFile_cksum() {
		return file_cksum;
	}

	public void setFile_cksum(Long file_cksum) {
		this.file_cksum = file_cksum;
	}

	public String getRmt_host() {
		return rmt_host;
	}

	public void setRmt_host(String rmt_host) {
		this.rmt_host = rmt_host;
	}

	public Integer getRmt_port() {
		return rmt_port;
	}

	public void setRmt_port(Integer rmt_port) {
		this.rmt_port = rmt_port;
	}

	public String getRmt_user() {
		return rmt_user;
	}

	public void setRmt_user(String rmt_user) {
		this.rmt_user = rmt_user;
	}

	public String getRmt_dir() {
		return rmt_dir;
	}

	public void setRmt_dir(String rmt_dir) {
		this.rmt_dir = rmt_dir;
	}

	public Timestamp getRmt_modify_tm() {
		return rmt_modify_tm;
	}

	public void setRmt_modify_tm(Timestamp rmt_modify_tm) {
		this.rmt_modify_tm = rmt_modify_tm;
	}

	public String getLoc_host() {
		return loc_host;
	}

	public void setLoc_host(String loc_host) {
		this.loc_host = loc_host;
	}

	public Integer getLoc_port() {
		return loc_port;
	}

	public void setLoc_port(Integer loc_port) {
		this.loc_port = loc_port;
	}

	public String getLoc_dir() {
		return loc_dir;
	}

	public void setLoc_dir(String loc_dir) {
		this.loc_dir = loc_dir;
	}

	public Timestamp getLoc_modify_tm() {
		return loc_modify_tm;
	}

	public void setLoc_modify_tm(Timestamp loc_modify_tm) {
		this.loc_modify_tm = loc_modify_tm;
	}

	public Long getTs_start() {
		return ts_start;
	}

	public void setTs_start(Long ts_start) {
		this.ts_start = ts_start;
	}

	public Long getTs_end() {
		return ts_end;
	}

	public void setTs_end(Long ts_end) {
		this.ts_end = ts_end;
	}

	public Timestamp getTm_end() {
		return tm_end;
	}

	public void setTm_end(Timestamp tm_end) {
		this.tm_end = tm_end;
	}

	public Integer getTrans_status() {
		return trans_status;
	}

	public void setTrans_status(Integer trans_status) {
		this.trans_status = trans_status;
	}

	public String getErr_msg() {
		return err_msg;
	}

	public void setErr_msg(String err_msg) {
		this.err_msg = err_msg;
	}
}
