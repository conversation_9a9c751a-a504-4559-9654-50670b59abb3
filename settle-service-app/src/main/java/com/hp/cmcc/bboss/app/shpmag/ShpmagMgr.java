package com.hp.cmcc.bboss.app.shpmag;

import java.io.File;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmCtlRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbShpmVerRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmagMgr {
	private static Logger L = LoggerFactory.getLogger(ShpmagMgr.class);
	public static boolean _TriggerFlag = true;
	private List<DbShpmCtlRec> _flag0List;
	private String _agtInst;
	private ShpmagOdb _odb;
	private OdbUtils _odbUtils;

	public ShpmagMgr() {
		_odb = new ShpmagOdb();
		_agtInst = AppCmdline.GetInstance()._instNm;
		_odbUtils = new OdbUtils();
	}

	public boolean needUpdate() {
		_flag0List = _odb.getShpmCtlFlag0(_agtInst);
		if (_flag0List == null || _flag0List.isEmpty())
			return false;
		L.trace("{} _flag0List entries", _flag0List.size());
		return true;
	}

	public void update() {
		for (DbShpmCtlRec rec_ : _flag0List) {
			L.debug("process begin, {}", rec_.toGsonStr());
			File db3_ = _odb.downloadShpmBlob(rec_, new File(ShpmagCfg._WorkingDir), OdbSystemParam.GetInstance()._gzipPath);
			if (db3_ == null) {
				rec_.setCtl_flag(DbShpmVerRec.VER_FLAG_3_SHPMAG_ERR);
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_204341001_SHPMAG_DOWNLOAD_ERR, PubMethod.FmtArgs(
						"%s download error", rec_.getShpm_ver()), rec_.toGsonStr());
				L.warn("{}, {}", alm_.getAlm_msg(), rec_.getErr_msg());
				_odb.updShpmCtlDone(_agtInst, rec_.getShpm_ver(), rec_.getCtl_flag(), rec_.getErr_msg());
				_odbUtils.addRawAlm(null, alm_);
				continue;
			}
			L.info("{} downloaded to {}", rec_.getShpm_ver(), db3_.getAbsolutePath());
			File ver_dir_ = new File(ShpmagCfg._ShpmBaseDir + "/" + rec_.getShpm_ver().substring(0, 6));
			if (!ver_dir_.isDirectory()) {
				if (!ver_dir_.mkdir()) {
					rec_.setCtl_flag(DbShpmVerRec.VER_FLAG_3_SHPMAG_ERR);
					rec_.setErr_msg(PubMethod.FmtArgs("%s mkdir %s failed", rec_.getShpm_ver(), ver_dir_.getAbsolutePath()));
					DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_204341002_SHPMAG_MKDIR_FAIL, rec_.getErr_msg(),
							rec_.toGsonStr());
					L.warn("{}", rec_.getErr_msg());
					_odb.updShpmCtlDone(_agtInst, rec_.getShpm_ver(), rec_.getCtl_flag(), rec_.getErr_msg());
					_odbUtils.addRawAlm(null, alm_);
					continue;
				} else {
					L.info("{} mkdir {} ok", rec_.getShpm_ver(), ver_dir_.getAbsolutePath());
				}
			}
			String dst_path_ = ver_dir_.getAbsolutePath() + "/" + db3_.getName();
			if (!PubMethod.MoveAFile(db3_.getAbsolutePath(), dst_path_)) {
				rec_.setCtl_flag(DbShpmVerRec.VER_FLAG_3_SHPMAG_ERR);
				rec_.setErr_msg(PubMethod.FmtArgs("%s mv %s to %s failed", rec_.getShpm_ver(), db3_.getAbsolutePath(), dst_path_));
				DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_204341003_SHPMAG_MOV_FAIL, rec_.getErr_msg(), rec_.toGsonStr());
				L.warn("{}", rec_.getErr_msg());
				_odb.updShpmCtlDone(_agtInst, rec_.getShpm_ver(), rec_.getCtl_flag(), rec_.getErr_msg());
				_odbUtils.addRawAlm(null, alm_);
				continue;
			} else {
				L.info("{} mv {} to {} ok", rec_.getShpm_ver(), db3_.getAbsolutePath(), dst_path_);
			}
			rec_.setCtl_flag(DbShpmVerRec.VER_FLAG_1_SUCCESS);
			_odb.updShpmCtlDone(_agtInst, rec_.getShpm_ver(), rec_.getCtl_flag(), null);
		}
	}
}
