package com.hp.cmcc.bboss.app.udr2mq;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbBizTypeDefRec;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class Udr2mqCfg {
	private static Logger L = LoggerFactory.getLogger(Udr2mqCfg.class);
	public static final String F1000_EMPTY_FILE = "F1000";
	public static final String F1001_INVALID_FNM_TIME = "F1001";
	public static final String F1008_1ST_UDR_BAD_FMT = "F1008";
	public static final String F1009_DUP_FILE = "F1009";
	public static final String F1999_INTERNAL_ERR = "F1999";

	public static int _Interval;
	public static int _ToJsonPoolSize;
	public static int _KafkaMsgSize;
	public static String _KafkaTopic;

	// key is APP_PARAM.SECTION 'BIZ.*'
	public static Map<String, Udr2mqBizCfg> _BizCfgMap = new TreeMap<String, Udr2mqBizCfg>();
	public static Map<String, List<Udr2mqBizCfg>> _ThreadGrpMap = new TreeMap<String, List<Udr2mqBizCfg>>();
	public static Map<String, DbBizTypeDefRec> _BizTypeMap = new TreeMap<String, DbBizTypeDefRec>();//业务类型

	public static boolean Init() {
		boolean rc_ = OdbSystemParam.GetInstance().refresh(); // 初始化system参数信息
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}

		//加载业务类型
		OdbUtils odb_utils_ = new OdbUtils();
		if (!odb_utils_.getBizTypeDef(_BizTypeMap, null)) {
			L.warn("init BIZ_TYPE_DEF error");
			rc_ = false;
		}
		//应用模块参数表加载
		if (!_InitAppParam()) {
			L.warn("call _InitAppParam error");
			rc_ = false;
		}
		return rc_;
	}

	public static void Debug() {
		L.debug("_Interval={}, _ToJsonPoolSize={}", _Interval, _ToJsonPoolSize);
		L.debug("_KafkaTopic={}, _KafkaMsgSize={}", _KafkaTopic, _KafkaMsgSize);
		L.debug("{} entries in _BizCfgMap", _BizCfgMap.size());
		int i = 0;
		for (Entry<String, Udr2mqBizCfg> entry_ : _BizCfgMap.entrySet()) {
			L.debug("No. {} {}", ++i, entry_.getValue().toGsonStr());
		}
		i = 0;
		for (Entry<String, List<Udr2mqBizCfg>> entry_ : _ThreadGrpMap.entrySet()) {
			List<String> sec_list_ = new ArrayList<String>();
			for (Udr2mqBizCfg biz_cfg_ : entry_.getValue()) {
				sec_list_.add(biz_cfg_._bizNm.substring(4));
			}
			L.debug("no. {} thread [{}], {} elements, [{}]", ++i, entry_.getKey(), entry_.getValue().size(),
					PubMethod.Collection2Str(sec_list_, ","));
		}
	}

	private static boolean _InitAppParam() {
		String inst_nm_ = AppCmdline.GetInstance()._instNm;
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(AppCmdline.GetInstance()._module, inst_nm_);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		if (!_InitCommonCfg(app_param_))
			rc_ = false;

		if (!_InitBizCfg(app_param_))
			rc_ = false;

		return rc_;
	}

	private static boolean _InitCommonCfg(OdbAppParam app_param) {
		boolean rc_ = true;
		Long lval_ = app_param.chkValNum("COMMON", "INTERVAL", 1L, 3600L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_Interval = lval_.intValue();
		}

		lval_ = app_param.chkValNum("COMMON", "TO_JSON_POOL_SIZE", 2L, 32L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_ToJsonPoolSize = lval_.intValue();
		}

		lval_ = app_param.chkValNum("COMMON", "KAFKA_MSG_SIZE", 1L, 50L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_KafkaMsgSize = lval_.intValue();
		}

		String sval_ = app_param.chkValStr("COMMON", "KAFKA_TOPIC", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_KafkaTopic = sval_;
		}

		return rc_;
	}

	private static boolean _InitBizCfg(OdbAppParam app_param) {
		boolean rc_ = true;
		Udr2mqBizCfg biz_cfg_ = null;
		Map<String, String> type_uniq_map_ = new TreeMap<String, String>(); // K:'FILE_TYPE:IS_ERCY:THREAD_NM', V:'SECTION'
		Map<String, Udr2mqBizCfg> biz_cfg_map_ = new TreeMap<String, Udr2mqBizCfg>();
		for (String sec_nm_ : app_param._paramMap.keySet()) {
			if (!sec_nm_.startsWith("BIZ."))
				continue;
			if (biz_cfg_map_.containsKey(sec_nm_)) {
				L.warn("absurd!, section [{}] already exists", sec_nm_);
				continue;
			}
			biz_cfg_ = new Udr2mqBizCfg();
			biz_cfg_._bizNm = sec_nm_;
			_InitOneBizSection(sec_nm_, app_param, biz_cfg_);

			if (biz_cfg_._bizNm == null) {
				L.warn("section [{}] init error", sec_nm_);
				rc_ = false;
			} else {
				String type_uniq_ = String.format("%s:%d:%s", biz_cfg_._bizType, biz_cfg_._isErcy, biz_cfg_._threadNm);
				String section_ = type_uniq_map_.get(type_uniq_);
				if (section_ == null) {
					biz_cfg_map_.put(biz_cfg_._bizNm, biz_cfg_);
					type_uniq_map_.put(type_uniq_, sec_nm_);
				} else {
					L.warn("section [{}], BIZ_TYPE:IS_ERCY:THREAD_NM [{}] dup with section [{}], pls chk", sec_nm_, type_uniq_,
							section_);
					rc_ = false;
				}
			}
		}

		if (biz_cfg_map_.isEmpty()) {
			L.warn("no valid 'BIZ.*' section found");
			rc_ = false;
		}
		if (rc_) {
			_BizCfgMap = biz_cfg_map_;
			_ThreadGrpMap.clear();
			for (Udr2mqBizCfg cfg_ : _BizCfgMap.values()) {
				List<Udr2mqBizCfg> cfg_list_ = _ThreadGrpMap.get(cfg_._threadNm);
				if (cfg_list_ == null) {
					cfg_list_ = new ArrayList<Udr2mqBizCfg>();
					_ThreadGrpMap.put(cfg_._threadNm, cfg_list_);
				}
				cfg_list_.add(cfg_);
			}
			L.debug("{} 'BIZ.*' sections initialized, _ThreadGrpMap.size()={}", _BizCfgMap.size(), _ThreadGrpMap.size());
		}
		return rc_;
	}

	private static void _InitOneBizSection(String sec_nm, OdbAppParam app_param, Udr2mqBizCfg biz_cfg) {
		Long lval_;
		File fval_;
		Pattern pval_;
		String sval_;
		fval_ = app_param.chkValFile(sec_nm, "RAW_DIR", true, true, false);
		if (fval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._rawDir = fval_.getAbsolutePath();
		}

		fval_ = app_param.chkValFile(sec_nm, "BAK_DIR", true, true, false);
		if (fval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._bakDir = fval_.getAbsolutePath();
		}

		fval_ = app_param.chkValFile(sec_nm, "ERR_DIR", true, true, false);
		if (fval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._errDir = fval_.getAbsolutePath();
		}

		pval_ = app_param.chkValPattern(sec_nm, "RAW_PATTERN", null);
		if (pval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._rawPattern = pval_;
		}

		sval_ = app_param.chkValStr(sec_nm, "FNM_PARSER", "(RAW_FILE_NM|YMD|PROV)");
		if (sval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._fnmParser = sval_;
		}

		sval_ = app_param.chkValStr(sec_nm, "FILE_CHARSET", null);
		if (sval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._fileCharset = sval_;
		}

		sval_ = app_param.chkValStr(sec_nm, "BIZ_TYPE", null);
		if (sval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._bizType = sval_;
			DbBizTypeDefRec biz_type_def_rec_ = _BizTypeMap.get(sval_);
			if (biz_type_def_rec_ == null) {
				L.warn("{}==>BIZ_TYPE [{}] not exists in BIZ_TYPE_DEF", sec_nm, sval_);
				biz_cfg._bizNm = null;
			} else {
				biz_cfg._typeId = biz_type_def_rec_.getType_id();
			}
		}

		lval_ = app_param.chkValNum(sec_nm, "CACHE_THRESHOLD", 1L, 99999999L);
		if (lval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._cacheThreshold = lval_.intValue();
		}

		lval_ = app_param.chkValNum(sec_nm, "IS_ERCY", 0L, 1L);
		if (lval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._isErcy = lval_.intValue();
		}

		sval_ = app_param.chkValStr(sec_nm, "THREAD_NM", null);
		if (sval_ == null) {
			biz_cfg._bizNm = null;
		} else {
			biz_cfg._threadNm = sval_;
		}
	}
}
