package com.hp.cmcc.bboss.app.udruld;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import javax.script.Compilable;
import javax.script.CompiledScript;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbBizTypeDefRec;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class UdruldCfg {
	private static Logger L = LoggerFactory.getLogger(UdruldCfg.class);
	public static String _KafkaTopicNml;
	public static String _UldCharset;
	public static String _UldCacheDir;
	public static String _UldOutputDir;
	public static String _UldJsonDir;
	public static int _UldCutoffCnt;
	public static int _UldCutoffDur;
	public static int _UldDupchkHashYmd;
	public static int _UldDupchkHashTbl;
	public static ScriptEngine _Luaj;
	public static Map<String, DbBizTypeDefRec> _BizTypeNmMap = new TreeMap<String, DbBizTypeDefRec>();
	public static Map<Integer, DbBizTypeDefRec> _BizTypeIdMap = new TreeMap<Integer, DbBizTypeDefRec>();
	public static Map<Integer, CompiledScript> _AuxKeyScriptMap = new HashMap<Integer, CompiledScript>(); // key:'type_id'
	public static Map<String, List<DbUdruldFmtCfgRec>> _UldFmtCfgMap; // key:'type_id:aux_key'
	public static Map<String, CompiledScript> _UldFmtScriptMap; // key:'type_id:aux_key:uld_idx'

	public static boolean Init() {
		boolean rc_ = OdbSystemParam.GetInstance().refresh();
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}

		OdbUtils odb_utils_ = new OdbUtils();
		if (!odb_utils_.getBizTypeDef(_BizTypeNmMap, _BizTypeIdMap)) {
			L.warn("init BIZ_TYPE_DEF error");
			rc_ = false;
		}

		_Luaj = new ScriptEngineManager().getEngineByName("luaj");
		if (_Luaj == null) {
			L.warn("init luaj script engine error, pls chk classpath");
			return false;
		}

		if (!_InitAppParam()) {
			L.warn("call _InitAppParam error");
			rc_ = false;
		}
		if (!_InitFmtCfg()) {
			L.warn("call _InitFmtCfg error");
			rc_ = false;
		}
		return rc_;
	}

	public static void TraceCfg() {
		L.debug("_KafkaTopicNml=[{}], _UldCharset=[{}], _UldCacheDir=[{}]", _KafkaTopicNml, _UldCharset, _UldCacheDir);
		L.debug("_UldCutoffCnt={}, _UldCutoffDur={}, _UldOutputDir=[{}]", _UldCutoffCnt, _UldCutoffDur, _UldOutputDir);
		L.debug("_UldDupchkHashYmd={}, _UldDupchkHashTbl={}", _UldDupchkHashYmd, _UldDupchkHashTbl);
		L.debug("_UldJsonDir=[{}]", _UldJsonDir);
		L.debug("_AuxKeyScriptMap.size={}", _AuxKeyScriptMap.size());
		L.debug("_UldFmtCfgMap.size={}", _UldFmtCfgMap.size());
		int i = 0;
		for (Entry<String, List<DbUdruldFmtCfgRec>> ent_ : _UldFmtCfgMap.entrySet()) {
			L.debug("_UldFmtCfgMap entry No. {}, key={}", ++i, ent_.getKey());
			List<DbUdruldFmtCfgRec> list_ = ent_.getValue();
			for (int j = 0; j < list_.size(); j++) {
				L.debug("  no. {}, {}", j + 1, list_.get(j).toGsonStr());
			}
		}
	}

	private static boolean _InitAppParam() {
		String inst_nm_ = AppCmdline.GetInstance()._instNm;
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(AppCmdline.GetInstance()._module, inst_nm_);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		if (!_InitCommonCfg(app_param_))
			rc_ = false;

		if (!_InitAuxKeyScriptCfg(app_param_))
			rc_ = false;

		return rc_;
	}

	private static boolean _InitCommonCfg(OdbAppParam app_param) {
		boolean rc_ = true;
		String sval_ = app_param.chkValStr("COMMON", "KAFKA_TOPIC_NML", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_KafkaTopicNml = sval_;
		}

		sval_ = app_param.chkValStr("COMMON", "ULD_CHARSET", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_UldCharset = sval_;
		}

		File fval_ = app_param.chkValFile("COMMON", "ULD_CACHE_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_UldCacheDir = fval_.getAbsolutePath();
		}

		fval_ = app_param.chkValFile("COMMON", "ULD_OUTPUT_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_UldOutputDir = fval_.getAbsolutePath();
		}

		fval_ = app_param.chkValFile("COMMON", "ULD_JSON_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_UldJsonDir = fval_.getAbsolutePath();
		}

		Long lval_ = app_param.chkValNum("COMMON", "ULD_CUTOFF_CNT", 1L, 100000000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_UldCutoffCnt = lval_.intValue();
		}

		lval_ = app_param.chkValNum("COMMON", "ULD_CUTOFF_DUR", 1L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_UldCutoffDur = lval_.intValue();
		}

		lval_ = app_param.chkValNum("COMMON", "ULD_DUPCHK_HASH_YMD", 1L, 97L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			if (!PubMethod._PrimesAnd1.contains(lval_.intValue())) {
				L.warn("{}==>{} {} should in ({})", "COMMON", "ULD_DUPCHK_HASH_YMD", lval_,
						PubMethod.Collection2Str(PubMethod._PrimesAnd1, ","));
				rc_ = false;
			} else {
				_UldDupchkHashYmd = lval_.intValue();
			}
		}

		lval_ = app_param.chkValNum("COMMON", "ULD_DUPCHK_HASH_TBL", 1L, 97L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			if (!PubMethod._PrimesAnd1.contains(lval_.intValue())) {
				L.warn("{}==>{} {} should in ({})", "COMMON", "ULD_DUPCHK_HASH_TBL", lval_,
						PubMethod.Collection2Str(PubMethod._PrimesAnd1, ","));
				rc_ = false;
			} else {
				_UldDupchkHashTbl = lval_.intValue();
			}
		}

		if (_UldDupchkHashYmd == _UldDupchkHashTbl) {
			L.warn("{}==>{}:{} {}:{} can not be eq",
					new Object[] { "COMMON", "ULD_DUPCHK_HASH_YMD", "ULD_DUPCHK_HASH_TBL", _UldDupchkHashYmd, _UldDupchkHashTbl });
			rc_ = false;
		}

		return rc_;
	}

	private static boolean _InitAuxKeyScriptCfg(OdbAppParam app_param) {
		boolean rc_ = true;
		for (String sec_nm_ : app_param._paramMap.keySet()) {
			if (!sec_nm_.startsWith("BIZ."))
				continue;
			String sval_ = app_param.chkValStr(sec_nm_, "BIZ_TYPE", null);
			if (sval_ == null) {
				rc_ = false;
				continue;
			}
			DbBizTypeDefRec biz_type_def_rec_ = _BizTypeNmMap.get(sval_);
			if (biz_type_def_rec_ == null) {
				L.warn("{}==>{} [{}] not exists or disabled in BIZ_TYPE_DEF", sec_nm_, "BIZ_TYPE", sval_);
				rc_ = false;
				continue;
			}
			sval_ = app_param.chkValStr(sec_nm_, "EVAL", "return");
			if (sval_ == null) {
				rc_ = false;
				continue;
			}

			try {
				CompiledScript compiled_script_ = ((Compilable) _Luaj).compile(sval_);
				_AuxKeyScriptMap.put(biz_type_def_rec_.getType_id(), compiled_script_);
			} catch (ScriptException e) {
				L.warn("compile exception, {} [{}]", biz_type_def_rec_.getBiz_type(), sval_, e);
				rc_ = false;
			}
		}
		return rc_;
	}

	private static boolean _InitFmtCfg() {
		UdruldOdb odb_ = new UdruldOdb();
		Map<String, List<DbUdruldFmtCfgRec>> fmt_map_ = odb_.getUldFmtCfg();
		if (fmt_map_ == null) {
			L.warn("call getUldFmtCfg() returns null");
			return false;
		}
		boolean rc_ = true;
		for (List<DbUdruldFmtCfgRec> val_ : fmt_map_.values()) {
			for (int i = 0; i < val_.size(); i++) {
				DbUdruldFmtCfgRec rec_ = val_.get(i);
				if (i != rec_.getUld_idx()) {
					L.warn("[{}] ULD_IDX ne i {}, (some uld fields is missing or skipped, pls chk)", rec_.getScriptKey(), i);
					rc_ = false;
				}
				if (!rec_.validateRec())
					rc_ = false;
			}
		}
		if (!rc_) {
			L.warn("validate UDRULD_FMT_CFG error");
		} else {
			_UldFmtCfgMap = fmt_map_;
		}

		if (rc_) {
			Map<String, CompiledScript> script_map_ = new TreeMap<String, CompiledScript>();
			for (List<DbUdruldFmtCfgRec> val_ : _UldFmtCfgMap.values()) {
				for (DbUdruldFmtCfgRec rec_ : val_) {
					if (!PubMethod.IsBlank(rec_.getEval())) {
						try {
							CompiledScript compiled_script_ = ((Compilable) _Luaj).compile(rec_.getEval());
							script_map_.put(rec_.getScriptKey(), compiled_script_);
						} catch (ScriptException e) {
							L.warn("compile exception, {}", rec_.toGsonStr(), e);
							rc_ = false;
						}
					}
				}
			}
			if (!rc_) {
				L.warn("UDRULD_FMT_CFG compile eval error");
			} else {
				_UldFmtScriptMap = script_map_;
				L.debug("{} elems in _UldFmtScriptMap", _UldFmtScriptMap.size());
			}
		}
		return rc_;
	}
}
