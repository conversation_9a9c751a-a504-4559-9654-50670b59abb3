package com.hp.cmcc.bboss.app.locdis;

import javax.ws.rs.NotFoundException;

import org.jboss.resteasy.plugins.server.tjws.TJWSEmbeddedJaxrsServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.app.RestNotFoundExceptionHandler;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class LocdisMain {
	private static Logger L = LoggerFactory.getLogger(LocdisMain.class);
	private static LocdisMoveRunnable _MoveRunnable;
	private static LocdisArchRunnable _ArchRunnable;
	private static LocdisGzipRunnable _GzipRunnable;
	private static LocdisUlnkRunnable _UlnkRunnable;
	private static Thread _MoveThread;
	private static Thread _ArchThread;
	private static Thread _GzipThread;
	private static Thread _UlnkThread;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("locdis", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	private static void _Init() {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 4, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!LocdisCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		LocdisCfg.TraceCfgMaps();

		LocdisRest rest_sv_ = new LocdisRest();
		TJWSEmbeddedJaxrsServer tjws = new TJWSEmbeddedJaxrsServer();
		tjws.setPort(LocdisCfg._RestPort);
		tjws.setRootResourcePath("/");
		tjws.start();
		tjws.getDeployment().getRegistry().addSingletonResource(rest_sv_);
		tjws.getDeployment().getProviderFactory().getExceptionMappers()
				.put(NotFoundException.class, new RestNotFoundExceptionHandler());
		L.info("REST service started at port {}", LocdisCfg._RestPort);

		_MoveRunnable = new LocdisMoveRunnable();
		_ArchRunnable = new LocdisArchRunnable();
		_GzipRunnable = new LocdisGzipRunnable();
		_UlnkRunnable = new LocdisUlnkRunnable();
		_MoveThread = new Thread(_MoveRunnable, "MOVE");
		_ArchThread = new Thread(_ArchRunnable, "ARCH");
		_GzipThread = new Thread(_GzipRunnable, "GZIP");
		_UlnkThread = new Thread(_UlnkRunnable, "ULNK");
		_MoveThread.start();
		_ArchThread.start();
		_GzipThread.start();
		_UlnkThread.start();
	}

	private static void _MainLoop() throws InterruptedException {
		long now_ = System.currentTimeMillis();
		long refresh_ts_ = now_ + 180 * 1000L;
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		while (true) {
			now_ = System.currentTimeMillis();
			if (now_ > refresh_ts_) {
				if (!LocdisCfg.RefreshLocdisCfg())
					L.warn("call RefreshLocdisCfg() error");
				if (!LocdisCfg.RefreshBinmanCfg())
					L.warn("call RefreshBinmanCfg() error");
				refresh_ts_ = System.currentTimeMillis() + 180 * 1000L;
			} else if (LocdisCfg._DebugFlag) {
				LocdisCfg._DebugFlag = false;
				LocdisCfg.TraceCfgMaps();
			} else {
				PubMethod.Sleep(1000);
			}
			if (!ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				continue;
			}

			L.info("try stop normal");
			_MoveRunnable.setTerminateFlag();
			_ArchRunnable.setTerminateFlag();
			_GzipRunnable.setTerminateFlag();
			_UlnkRunnable.setTerminateFlag();
			L.debug("try join thread {}", _MoveThread.getName());
			_MoveThread.join();
			L.debug("thread {} joined", _MoveThread.getName());
			L.debug("try join thread {}", _ArchThread.getName());
			_ArchThread.join();
			L.debug("thread {} joined", _ArchThread.getName());
			L.debug("try join thread {}", _GzipThread.getName());
			_GzipThread.join();
			L.debug("thread {} joined", _GzipThread.getName());
			L.debug("try join thread {}", _UlnkThread.getName());
			_UlnkThread.join();
			L.debug("thread {} joined", _UlnkThread.getName());
			break;
		}
	}
}
