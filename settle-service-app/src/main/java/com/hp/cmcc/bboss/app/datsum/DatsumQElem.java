package com.hp.cmcc.bboss.app.datsum;

import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DatsumQElem extends GsonObj {
	public static final int CMD_ENUM_REC = 0; // RECord
	public static final int CMD_ENUM_BOB = 1; // Begin Of Batch
	public static final int CMD_ENUM_EOB = 2; // End Of Batch
	public int _cmdEnum;
	public int _keyHash;
	public boolean _isOptRec = false;
	public String _keyStr;
	public String[] _fields;
	public UdrFmt _udr;

	public DatsumQElem() {
		_cmdEnum = CMD_ENUM_REC;
	}

	public DatsumQElem(int cmd_enum) {
		_cmdEnum = cmd_enum;
	}

	public void setKeyStr(String key_str) {
		_keyStr = key_str;
		_keyHash = _keyStr.hashCode();
	}

	public int calcHashRemainder(int mod) {
		int remainder_ = _keyHash % mod;
		return (remainder_ + mod) % mod; // ensure positive value
	}
}
