package com.hp.cmcc.bboss.app.syndup;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbDupchkUdrRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.AppTicker;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.Transaction;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class SyndupBizRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(SyndupBizRunnable.class);
	private static final String _KEY_MATCH_ODD = "[1-9][0-9][0-9][0-9][0-1][0-9][0-3][0-9][0-2][0-9][0-5][0-9][0-5][13579]";
	private static final String _KEY_MATCH_EVEN = "[1-9][0-9][0-9][0-9][0-1][0-9][0-3][0-9][0-2][0-9][0-5][0-9][0-5][02468]";
	//private static final String _KEY_MATCH_ALL = "[1-9][0-9][0-9][0-9][0-1][0-9][0-3][0-9][0-2][0-9][0-5][0-9][0-5][0-9]";
	private static final int _KEY_LIST_LIMIT = 50000000;
	private boolean _isEven;
	private boolean _exceedLimit;
	private String _expireTm;
	private List<String> _expireKeyList;
	private List<String> _batchKeyList;
	private List<DbDupchkUdrRec> _batchRecList;
	private OdbUtils _odbUtils;
	private int _mergeCnt;
	private boolean _terminateFlag = false;

	SyndupBizRunnable(boolean is_even) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_isEven = is_even;
		_expireKeyList = new LinkedList<String>();
		_batchKeyList = new ArrayList<String>();
		_batchRecList = new LinkedList<DbDupchkUdrRec>();
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
				_interval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
		L.info("thread end");
	}

	private void _exec() {
		AppTicker ticker_ = new AppTicker();
		if (!_scanExpireKeys()) {
			L.warn("_scanExpireKeys error");
			return;
		}
		if (_expireKeyList.isEmpty())
			return;

		boolean ok_ = true;
		_mergeCnt = 0;
		int key_cnt_ = _expireKeyList.size();
		int rec_cnt_ = 0;
		while (true) {
			if (!_prepareBatch()) {
				L.warn("_prepareBatch error");
				ok_ = false;
				break;
			}
			if (_batchKeyList.isEmpty())
				break;
			if (!_merge2Odb()) {
				L.warn("_merge2Odb error");
				ok_ = false;
				break;
			}
			rec_cnt_ += _batchRecList.size();
			if (!_cleanBatch()) {
				L.warn("_cleanBatch error");
				ok_ = false;
				break;
			}
		}

		if (ok_) {
			ticker_.tickEnd(rec_cnt_);
			L.info("_expireTm {}, term:keys:tot:merged:pfm {}:{}:{}:{}:{}", new Object[] { _expireTm, ticker_._term, key_cnt_,
					rec_cnt_, _mergeCnt, ticker_._pfm });
		}

		_expireKeyList.clear();
		_batchKeyList.clear();
		_batchRecList.clear();
	}

	private boolean _scanExpireKeys() {
		_exceedLimit = false;
		_expireKeyList.clear();
		MdbCli cli_ = MdbAgt.GetDupchkInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		long expire_ts_ = System.currentTimeMillis();
		expire_ts_ -= 3600L * 1000L * OdbSystemParam.GetInstance()._redisDupchkHoursHybrid;
		_expireTm = PubMethod.Long2Str(expire_ts_, PubMethod.TimeStrFmt.Fmt14);
		ScanParams scan_params_ = new ScanParams();
		scan_params_.match(_isEven ? _KEY_MATCH_EVEN : _KEY_MATCH_ODD);
		try {
			String cursor_ = ScanParams.SCAN_POINTER_START;
			while (true) {
				ScanResult<String> scan_result_ = jedis_.scan(cursor_, scan_params_);
				for (String k : scan_result_.getResult()) {
					if (_expireTm.compareTo(k) > 0) {
						_expireKeyList.add(k);
						if (_expireKeyList.size() >= _KEY_LIST_LIMIT)
							break;
					}
				}
				if (_expireKeyList.size() >= _KEY_LIST_LIMIT)
					break;
				cursor_ = scan_result_.getCursor();
				if (cursor_.equals(ScanParams.SCAN_POINTER_START)) {
					break;
				}
			}

			if (_expireKeyList.size() >= _KEY_LIST_LIMIT) {
				_exceedLimit = true;
			}
			L.debug("{} expire keys scaned, expire criterion {}, _exceedLimit={}", _expireKeyList.size(), _expireTm, _exceedLimit);
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_206341001_SCAN_EXPIRE_EXCEPTION, PubMethod.FmtArgs(
					"scan expire keys exception, _expireTm=%s, _expireKeyList.size()=%d", _expireTm, _expireKeyList.size()), null);
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
			return false;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return true;
	}

	private boolean _prepareBatch() {
		_batchKeyList.clear();
		_batchRecList.clear();
		MdbCli cli_ = MdbAgt.GetDupchkInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		try {
			while (!_expireKeyList.isEmpty()) {
				String key_ = _expireKeyList.remove(0);
				_batchKeyList.add(key_);
				int mmdd_ = Integer.parseInt(key_.substring(4, 8));
				Map<String, String> hmap_ = jedis_.hgetAll(key_);
				for (Entry<String, String> entry_ : hmap_.entrySet()) {
					String[] fid_lno_ = entry_.getValue().split(":");
					long fid_ = Long.parseLong(fid_lno_[0]);
					int lno_ = Integer.parseInt(fid_lno_[1]);
					DbDupchkUdrRec rec_ = new DbDupchkUdrRec();
					rec_.setMmdd(mmdd_);
					rec_.setEvent_time(PubMethod.Str2Timestamp(key_, PubMethod.TimeStrFmt.Fmt14));
					rec_.setHash_key(entry_.getKey());
					rec_.setAux_key("-");
					rec_.setFile_id(fid_);
					rec_.setLine_num(lno_);
					_batchRecList.add(rec_);
				}
				if (_batchRecList.size() >= SyndupCfg._BatchSize)
					break;
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_206341002_PREPARE_BATCH_EXCEPTION, PubMethod.FmtArgs(
					"prepare batch exception, _expireTm=%s, _expireKeyList.size()=%d, _batchRecList.size()=%d", _expireTm,
					_expireKeyList.size(), _batchRecList.size()), null);
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
			return false;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return true;
	}

	private boolean _merge2Odb() {
		AppTicker ticker_ = new AppTicker();
		int merge_cnt_ = 0;
		Connection conn_ = null;
		SyndupOdb odb_ = new SyndupOdb();
		OdbCli cli_ = OdbAgt.GetUdrInstance();
		try {
			conn_ = cli_.getConnection();
			L.info("transaction {} begin", conn_.toString());
			odb_.addSyndup(conn_, _batchRecList);
			merge_cnt_ = odb_.merge2SttlDupchk(conn_);
			_mergeCnt += merge_cnt_;
			odb_.truncateSyndup(conn_);
			cli_.commit(conn_);
			ticker_.tickEnd(_batchRecList.size());
			L.info("transaction {} commit done, term:cnt:merged:pfm {}:{}:{}:{}", new Object[] { conn_.toString(), ticker_._term,
					ticker_._recNum, merge_cnt_, ticker_._pfm });
		} catch (SQLException e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_206341003_MERGE_EXCEPTION, PubMethod.FmtArgs(
					"merge exception, _expireTm=%s, _batchRecList.size()=%d", _expireTm, _batchRecList.size()), null);
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			if (conn_ != null) {
				cli_.rollback(conn_);
				L.warn("transaction {} rollback done", conn_.toString());
			}
			return false;
		} finally {
			cli_.close(conn_);
		}
		return true;
	}

	private boolean _cleanBatch() {
		MdbCli cli_ = MdbAgt.GetDupchkInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		Transaction tx_ = jedis_.multi();
		try {
			for (String k : _batchKeyList) {
				tx_.del(k);
			}
			List<Object> response_ = tx_.exec();
			L.info("{} expire keys deleted", response_.size());
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_206341003_MERGE_EXCEPTION, PubMethod.FmtArgs(
					"clear expire keys exception, _expireTm=%s, _batchKeyList.size()=%d", _expireTm, _batchKeyList.size()), null);
			alm_.setAlm_kpi(e.toString());
			L.warn("{}", alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			cli_.returnBrokenResource(tx_, pool_, jedis_);
			jedis_ = null;
			return false;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
		return true;
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		if (_exceedLimit) {
			L.trace("_exceedLimit={}, no wait", _exceedLimit);
			return;
		}
		for (int i = 0; i < SyndupCfg._Interval; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}
}
