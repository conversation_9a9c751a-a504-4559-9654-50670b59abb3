package com.hp.cmcc.bboss.app.datsum;

import java.io.File;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

import javax.script.Compilable;
import javax.script.CompiledScript;
import javax.script.ScriptEngine;
import javax.script.ScriptException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DatsumCfg {
	private static Logger L = LoggerFactory.getLogger(DatsumCfg.class);
	public static int _Interval;
	public static int _WorkerThreadNum;
	public static int _WorkerHashNum;
	public static int _TriggerInterval;
	public static int _TriggerLines;
	public static String _TriggerCron;
	public static String _InputDir;
	public static String _BakDir;
	public static String _OutputDir;
	public static String _WorkingDir;
	public static String _InputDelimiterExp;
	//public static String _OutputDelimiterExp;
	public static String _InputDelimiterRaw;
	public static String _OutputDelimiterRaw;
	public static String _InputCharset;
	public static String _OutputCharset;
	public static String _InputFnmPattern;
	public static String _InputFnmParser;
	public static String _OutputFnmPfx;
	public static boolean _InputIsJson;
	public static String _FmtNm;
	public static int _MaxSrcIdx;
	public static DbDatsumCubeCfgRec[] _SumCubeCfg;
	public static DbDatsumCubeCfgRec _AudFlagRec;
	public static List<DbDatsumCubeCfgRec> _SumSrcKeyCfgList;
	public static List<Integer> _SumOptKeyIdxList;
	public static List<Integer> _OptSrcKeyIdxList;
	public static int[] _DumpMappingKeyStr;
	public static int[] _DumpMappingOthers;
	public static AtomicBoolean _TriggerFlag = new AtomicBoolean(false);

	public static boolean Init() {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(cmdline_._module, cmdline_._instNm);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", cmdline_._instNm);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", cmdline_._instNm);
			return false;
		}

		rc_ = _InitAppParam(app_param_);
		if (rc_) {
			rc_ = _InitSumCubeCfg();
		}

		if (rc_)
			_Trace();

		return rc_;
	}

	public static Map<String, CompiledScript> PreCompileEvalScripts(ScriptEngine luaj) throws ScriptException {
		Map<String, CompiledScript> cs_map_ = new HashMap<String, CompiledScript>();
		for (int i = 0; i < _SumCubeCfg.length; i++) {
			DbDatsumCubeCfgRec rec_ = _SumCubeCfg[i];
			if (!PubMethod.IsEmpty(rec_.getEval()) && !cs_map_.containsKey(rec_.getEval())) {
				CompiledScript cs_ = ((Compilable) luaj).compile(rec_.getEval());
				cs_map_.put(rec_.getEval(), cs_);
			}
			if (!PubMethod.IsEmpty(rec_.getOpt_eval()) && !cs_map_.containsKey(rec_.getOpt_eval())) {
				CompiledScript cs_ = ((Compilable) luaj).compile(rec_.getOpt_eval());
				cs_map_.put(rec_.getOpt_eval(), cs_);
			}
		}
		return cs_map_;
	}

	private static boolean _InitAppParam(OdbAppParam app_param) {
		boolean rc_ = true;
		Long lval_ = app_param.chkValNum("COMMON", "INTERVAL", 1L, 3600L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_Interval = lval_.intValue();
		}

		File fval_ = app_param.chkValFile("COMMON", "INPUT_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_InputDir = fval_.getAbsolutePath();
		}

		fval_ = app_param.chkValFile("COMMON", "BAK_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_BakDir = fval_.getAbsolutePath();
		}

		fval_ = app_param.chkValFile("COMMON", "OUTPUT_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_OutputDir = fval_.getAbsolutePath();
		}

		fval_ = app_param.chkValFile("COMMON", "WORKING_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_WorkingDir = fval_.getAbsolutePath();
		}

		lval_ = app_param.chkValNum("COMMON", "TRIGGER_INTERVAL", -1L, 86400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_TriggerInterval = lval_.intValue();
		}

		lval_ = app_param.chkValNum("COMMON", "TRIGGER_LINES", 1L, 1000000000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_TriggerLines = lval_.intValue();
		}

		lval_ = app_param.chkValNum("COMMON", "WORKER_THREAD_NUM", 1L, 97L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			if (!PubMethod._PrimesAnd1.contains(lval_.intValue())) {
				L.warn("{}==>{} {} should in ({})",
						new Object[] { "COMMON", "WORKER_THREAD_NUM", lval_, PubMethod.Collection2Str(PubMethod._PrimesAnd1, ",") });
				rc_ = false;
			} else {
				_WorkerThreadNum = lval_.intValue();
			}
		}

		lval_ = app_param.chkValNum("COMMON", "WORKER_HASH_NUM", 1L, 97L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			if (!PubMethod._PrimesAnd1.contains(lval_.intValue())) {
				L.warn("{}==>{} {} should in ({})",
						new Object[] { "COMMON", "WORKER_HASH_NUM", lval_, PubMethod.Collection2Str(PubMethod._PrimesAnd1, ",") });
				rc_ = false;
			} else {
				_WorkerHashNum = lval_.intValue();
			}
		}

		Pattern pval_ = app_param.chkValPattern("COMMON", "INPUT_DELIMITER_EXP", null);
		if (pval_ == null) {
			rc_ = false;
		} else {
			_InputDelimiterExp = pval_.pattern();
		}

		//		pval_ = app_param.chkValPattern("COMMON", "OUTPUT_DELIMITER_EXP", null);
		//		if (pval_ == null) {
		//			rc_ = false;
		//		} else {
		//			_OutputDelimiterExp = pval_.pattern();
		//		}

		pval_ = app_param.chkValPattern("COMMON", "INPUT_FNM_PATTERN", null);
		if (pval_ == null) {
			rc_ = false;
		} else {
			_InputFnmPattern = pval_.pattern();
		}

		Charset cval_ = app_param.chkValCharset("COMMON", "INPUT_CHARSET");
		if (cval_ == null) {
			rc_ = false;
		} else {
			_InputCharset = cval_.name();
		}

		cval_ = app_param.chkValCharset("COMMON", "OUTPUT_CHARSET");
		if (cval_ == null) {
			rc_ = false;
		} else {
			_OutputCharset = cval_.name();
		}

		String sval_ = app_param.chkValStr("COMMON", "FMT_NM", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_FmtNm = sval_;
		}

		sval_ = app_param.chkValStr("COMMON", "INPUT_FNM_PARSER", "RAW_FILE_NM");
		if (sval_ == null) {
			rc_ = false;
		} else {
			_InputFnmParser = sval_;
		}

		sval_ = app_param.chkValStr("COMMON", "INPUT_DELIMITER_RAW", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_InputDelimiterRaw = sval_;
		}

		sval_ = app_param.chkValStr("COMMON", "OUTPUT_DELIMITER_RAW", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_OutputDelimiterRaw = sval_;
		}

		sval_ = app_param.chkValStr("COMMON", "OUTPUT_FNM_PFX", "^[0-9a-zA-Z]+$");
		if (sval_ == null) {
			rc_ = false;
		} else {
			_OutputFnmPfx = sval_;
		}

		lval_ = app_param.chkValNum("COMMON", "INPUT_IS_JSON", 0L, 1L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			if (lval_ == 1)
				_InputIsJson = true;
			else
				_InputIsJson = false;
		}

		sval_ = app_param.chkValStr("COMMON", "TRIGGER_CRON",
				"(-1)|((\\*|([0-1][0-9]|2[0-3])(,([0-1][0-9]|2[0-3]))*):[0-5][0-9](,[0-5][0-9])*)");
		if (sval_ == null) {
			rc_ = false;
		} else {
			_TriggerCron = sval_;
		}

		if (rc_) {
			if (_WorkerHashNum != 1 && _WorkerHashNum > _WorkerThreadNum) {
				L.warn("{}==>{} {} gt {} {}", new Object[] { "COMMON", "WORKER_HASH_NUM", _WorkerHashNum, "WORKER_THREAD_NUM",
						_WorkerThreadNum });
				rc_ = false;
			}
		}

		return rc_;
	}

	private static boolean _InitSumCubeCfg() {
		DatsumOdb odb_ = new DatsumOdb();
		List<DbDatsumCubeCfgRec> db_list_ = odb_.getSumCubeCfg(_FmtNm);
		if (db_list_ == null) {
			L.warn("getSumCubeCfg({}) returns null", _FmtNm);
			return false;
		}
		if (db_list_.isEmpty()) {
			L.warn("getSumCubeCfg({}) returns empty, pls chk", _FmtNm);
			return false;
		}
		DbDatsumCubeCfgRec[] sum_cube_cfg_ = db_list_.toArray(new DbDatsumCubeCfgRec[db_list_.size()]);
		_MaxSrcIdx = 0;
		boolean rc_ = true;
		int aud_flag_cnt_ = 0;
		for (int i = 0; i < sum_cube_cfg_.length; i++) {
			DbDatsumCubeCfgRec cfg_rec_ = sum_cube_cfg_[i];
			if (cfg_rec_.getAud_flag() == 1) {
				++aud_flag_cnt_;
			}
			if (cfg_rec_.getSum_idx() != i) {
				L.warn("_FmtNm [{}] sum_idx {} ne {}, pls chk", _FmtNm, cfg_rec_.getSum_idx(), i);
				rc_ = false;
			}
			if (cfg_rec_.getSrc_idx() > _MaxSrcIdx) {
				_MaxSrcIdx = cfg_rec_.getSrc_idx();
			}
		}
		if (aud_flag_cnt_ != 1) {
			L.warn("_FmtNm [{}] count of aud_flag is {} ne 1, pls chk", _FmtNm, aud_flag_cnt_);
			rc_ = false;
		}
		if (rc_) {
			_SumCubeCfg = sum_cube_cfg_;
			_SumSrcKeyCfgList = new ArrayList<DbDatsumCubeCfgRec>();
			_SumOptKeyIdxList = new ArrayList<Integer>();
			_OptSrcKeyIdxList = new ArrayList<Integer>();
			List<Integer> dump_keystr_list_ = new ArrayList<Integer>();
			List<Integer> dump_others_list_ = new ArrayList<Integer>();
			for (DbDatsumCubeCfgRec rec_ : _SumCubeCfg) {
				if (rec_.getAud_flag() == 1)
					_AudFlagRec = rec_;
				if (rec_.getSum_attr() == DbDatsumCubeCfgRec.ATTR_0_KEY) {
					_SumSrcKeyCfgList.add(rec_);
					if (rec_.getOpt_src_idx() == null)
						_SumOptKeyIdxList.add(rec_.getSrc_idx());
					else
						_SumOptKeyIdxList.add(rec_.getOpt_src_idx());
					dump_keystr_list_.add(rec_.getSum_idx());
				} else {
					dump_others_list_.add(rec_.getSum_idx());
				}
				if (rec_.getOpt_src_idx() != null) {
					_OptSrcKeyIdxList.add(rec_.getOpt_src_idx());
				}
			}
			_DumpMappingKeyStr = new int[dump_keystr_list_.size()];
			for (int i = 0; i < dump_keystr_list_.size(); i++)
				_DumpMappingKeyStr[i] = dump_keystr_list_.get(i);
			_DumpMappingOthers = new int[dump_others_list_.size()];
			for (int i = 0; i < dump_others_list_.size(); i++)
				_DumpMappingOthers[i] = dump_others_list_.get(i);
		}
		return rc_;
	}

	private static void _Trace() {
		L.debug("_InputDir={}", _InputDir);
		L.debug("_OutputDir={}", _OutputDir);
		L.debug("_BakDir={}", _BakDir);
		L.debug("_WorkingDir={}", _WorkingDir);
		L.debug("_Interval={}, _OutputFnmPfx={}, _InputIsJson", _Interval, _OutputFnmPfx, _InputIsJson);
		L.debug("_TriggerLines={}, _TriggerInterval={}, _TriggerCron={}", _TriggerLines, _TriggerInterval, _TriggerCron);
		L.debug("_InputDelimiterRaw={}, _OutputDelimiterRaw={}", _InputDelimiterRaw, _OutputDelimiterRaw);
		//L.debug("_InputDelimiterExp={}, _OutputDelimiterExp={}", _InputDelimiterExp, _OutputDelimiterExp);
		L.debug("_InputDelimiterExp={}", _InputDelimiterExp);
		L.debug("_InputCharset={}, _OutputCharset={}", _InputCharset, _OutputCharset);
		L.debug("_InputFnmPattern={}", _InputFnmPattern);
		L.debug("_InputFnmParser={}", _InputFnmParser);
		L.debug("_WorkerThreadNum={}, _WorkerHashNum={}", _WorkerThreadNum, _WorkerHashNum);
		L.debug("_FmtNm={}, _MaxSrcIdx={}, _SumCubeCfg.length={}", _FmtNm, _MaxSrcIdx, _SumCubeCfg.length);
		for (int i = 0; i < _SumCubeCfg.length; i++)
			L.debug("Idx {}, {}", i, _SumCubeCfg[i].toGsonStr());
		L.debug("_AudFlagRec {}", _AudFlagRec.toGsonStr());
		L.debug("_SumSrcKeyCfgList.size()={}", _SumSrcKeyCfgList.size());
		L.debug("_SumOptKeyIdxList.size()={}", _SumOptKeyIdxList.size());
		L.debug("{}", PubMethod.Collection2Str(_SumOptKeyIdxList, ","));
		L.debug("_OptSrcKeyIdxList.size()={}", _OptSrcKeyIdxList.size());
		L.debug("{}", PubMethod.Collection2Str(_OptSrcKeyIdxList, ","));
		L.debug("_DumpMappingKeyStr.length={}, _DumpMappingOthers.length={}", _DumpMappingKeyStr.length, _DumpMappingOthers.length);
		StringBuilder sb_ = new StringBuilder();
		for (int i = 0; i < _DumpMappingKeyStr.length; i++) {
			sb_.append(i > 0 ? " " + i : i);
			sb_.append(":");
			sb_.append(_DumpMappingKeyStr[i]);
		}
		L.debug("_DumpMappingKeyStr: {}", sb_.substring(0));
		sb_.delete(0, sb_.length());
		for (int i = 0; i < _DumpMappingOthers.length; i++) {
			sb_.append(i > 0 ? " " + i : i);
			sb_.append(":");
			sb_.append(_DumpMappingOthers[i]);
		}
		L.debug("_DumpMappingKeyOthers: {}", sb_.substring(0));
	}
}
