package com.hp.cmcc.bboss.app.erruld;

import java.sql.Connection;
import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class ErruldOdb {
	private static Logger L = LoggerFactory.getLogger(ErruldOdb.class);
	private static final String _Q_ERRULD_LOG = "SELECT ULD_FILE_NM, RCV_MM, RCV_YMDH, ACNT_YM, ULD_STATUS, "
			+ "ULD_START_TM, ULD_END_TM, FILE_ID, ULD_CNT, ERCY_CNT, TS_START, TS_END, SPARE_NUM1, SPARE_NUM2 "
			+ "FROM ERRULD_LOG WHERE ULD_FILE_NM = ?";
	private static final String _I_ERRULD_LOG = "INSERT INTO ERRULD_LOG (ULD_FILE_NM, RCV_MM, RCV_YMDH, ACNT_YM, "
			+ "ULD_STATUS, ULD_START_TM, ULD_END_TM, FILE_ID, ULD_CNT, ERCY_CNT, TS_START, TS_END, SPARE_NUM1, "
			+ "SPARE_NUM2) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String _U_ERRULD_LOG = "UPDATE ERRULD_LOG SET ULD_STATUS = ?, ULD_END_TM = ?, "
			+ "ULD_CNT = ?, ERCY_CNT = ?, TS_END = ?, SPARE_NUM1 = ?, SPARE_NUM2 = ? "
			+ "WHERE RCV_MM = ? AND RCV_YMDH = ? AND ULD_FILE_NM = ? AND FILE_ID = ?";

	public DbErruldLogRec getErrUldLog(String uld_file_nm) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		DbErruldLogRec result_ = cli_.queryForObject(_Q_ERRULD_LOG, DbErruldLogRec.class, uld_file_nm);
		return result_;
	}

	public void addErrUldLog(Connection conn, DbErruldLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		if (conn == null) {
			cli_.update(_I_ERRULD_LOG, rec.asInsertObjArray());
			L.info("add {}", rec.toGsonStr());
		} else {
			cli_.update(conn, _I_ERRULD_LOG, rec.toGsonStr());
			L.info("transaction {}, add {}", conn.toString(), rec.toGsonStr());
		}
	}

	public void updErrUldLog(Connection conn, DbErruldLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		Object[] o = new Object[11];
		o[0] = rec.getUld_status();
		o[1] = rec.getUld_end_tm();
		o[2] = rec.getUld_cnt();
		o[3] = rec.getErcy_cnt();
		o[4] = rec.getTs_end();
		o[5] = rec.getSpare_num1();
		o[6] = rec.getSpare_num2();
		o[7] = rec.getRcv_mm();
		o[8] = rec.getRcv_ymdh();
		o[9] = rec.getUld_file_nm();
		o[10] = rec.getFile_id();
		if (conn == null) {
			cli_.update(_U_ERRULD_LOG, o);
			L.info("upd {}", rec.toGsonStr());
		} else {
			cli_.update(conn, _U_ERRULD_LOG, o);
			L.info("transaction {}, upd {}", conn.toString(), rec.toGsonStr());
		}
	}
}
