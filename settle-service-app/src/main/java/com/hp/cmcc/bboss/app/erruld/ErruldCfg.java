package com.hp.cmcc.bboss.app.erruld;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ErruldCfg {
	public static Logger L = LoggerFactory.getLogger(ErruldCfg.class);
	public static String _KafkaTopicErr;
	public static String _UldCharset;
	public static String _UldCacheDir;
	public static String _UldOutputDir;
	// public static String _UldEdatDir;
	public static int _UldCutoffDur;
	public static int _UldDupchkHashYmd;
	public static int _UldDupchkHashTbl;

	public static boolean Init() {
		boolean rc_ = OdbSystemParam.GetInstance().refresh();
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}
		if (!_InitAppParam()) {
			L.warn("call _InitAppParam error");
			rc_ = false;
		}
		return rc_;
	}

	private static boolean _InitAppParam() {
		String inst_nm_ = AppCmdline.GetInstance()._instNm;
		OdbAppParam app_param_ = new OdbAppParam();
		boolean rc_ = app_param_.refresh(AppCmdline.GetInstance()._module, inst_nm_);
		if (!rc_) {
			L.warn("init app_param for inst_nm [{}] error", inst_nm_);
			return false;
		}

		rc_ = app_param_.subValByEnv();
		if (!rc_) {
			L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
			return false;
		}

		String sval_ = app_param_.chkValStr("COMMON", "KAFKA_TOPIC_ERR", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_KafkaTopicErr = sval_;
		}

		sval_ = app_param_.chkValStr("COMMON", "ULD_CHARSET", null);
		if (sval_ == null) {
			rc_ = false;
		} else {
			_UldCharset = sval_;
		}

		File fval_ = app_param_.chkValFile("COMMON", "ULD_CACHE_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_UldCacheDir = fval_.getAbsolutePath();
		}

		fval_ = app_param_.chkValFile("COMMON", "ULD_OUTPUT_DIR", true, true, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_UldOutputDir = fval_.getAbsolutePath();
		}

		// fval_ = app_param_.chkValFile("COMMON", "ULD_EDAT_DIR", true, true, true);
		// if (fval_ == null) {
		// rc_ = false;
		// } else {
		// _UldEdatDir = fval_.getAbsolutePath();
		// }

		Long lval_ = app_param_.chkValNum("COMMON", "ULD_CUTOFF_DUR", 1L, 864000L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_UldCutoffDur = lval_.intValue();
		}

		lval_ = app_param_.chkValNum("COMMON", "ULD_DUPCHK_HASH_YMD", 1L, 97L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			if (!PubMethod._PrimesAnd1.contains(lval_.intValue())) {
				L.warn("{}==>{} {} should in ({})", "COMMON", "ULD_DUPCHK_HASH_YMD", lval_,
						PubMethod.Collection2Str(PubMethod._PrimesAnd1, ","));
				rc_ = false;
			} else {
				_UldDupchkHashYmd = lval_.intValue();
			}
		}

		lval_ = app_param_.chkValNum("COMMON", "ULD_DUPCHK_HASH_TBL", 1L, 97L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			if (!PubMethod._PrimesAnd1.contains(lval_.intValue())) {
				L.warn("{}==>{} {} should in ({})", "COMMON", "ULD_DUPCHK_HASH_TBL", lval_,
						PubMethod.Collection2Str(PubMethod._PrimesAnd1, ","));
				rc_ = false;
			} else {
				_UldDupchkHashTbl = lval_.intValue();
			}
		}

		if (_UldDupchkHashYmd == _UldDupchkHashTbl) {
			L.warn("{}==>{}:{} {}:{} can not be eq",
					new Object[] { "COMMON", "ULD_DUPCHK_HASH_YMD", "ULD_DUPCHK_HASH_TBL", _UldDupchkHashYmd, _UldDupchkHashTbl });
			rc_ = false;
		}

		return rc_;
	}
}
