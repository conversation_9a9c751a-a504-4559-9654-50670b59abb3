package com.hp.cmcc.bboss.app.datsum;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.ProcessLock;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DatsumMain {
	private static Logger L = LoggerFactory.getLogger(DatsumMain.class);
	private static BlockingQueue<DatsumQElem>[] _SumQueues;
	private static AtomicBoolean[] _EobFlags;
	private static DatsumSumRunnable[] _SumRunnables;
	private static Thread[] _SumThreads;
	private static DatsumCronRunnable _CronRunnable;
	private static Thread _CronThread;

	public static void main(String[] args) {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("datsum", args);
		try {
			ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, true);
			ProcessLock.TryLock(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, 1);
			_Init();
			_MainLoop();
			L.info("stopped");
			System.exit(0);
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}

	@SuppressWarnings("unchecked")
	private static void _Init() throws Exception {
		if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 3, 0, 0)) {
			L.error("init odb connection failed, JVM exit");
			System.exit(1);
		}
		if (!DatsumCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		_SumQueues = new BlockingQueue[DatsumCfg._WorkerThreadNum];
		_EobFlags = new AtomicBoolean[DatsumCfg._WorkerThreadNum];
		_SumRunnables = new DatsumSumRunnable[DatsumCfg._WorkerThreadNum];
		_SumThreads = new Thread[DatsumCfg._WorkerThreadNum];
		for (int i = 0; i < DatsumCfg._WorkerThreadNum; i++) {
			_SumQueues[i] = new LinkedBlockingQueue<DatsumQElem>();
			_EobFlags[i] = new AtomicBoolean();
			_SumRunnables[i] = new DatsumSumRunnable(i, _SumQueues[i], _EobFlags[i]);
			_SumThreads[i] = new Thread(_SumRunnables[i], String.format("SM%02d", i));
		}
		for (int i = 0; i < DatsumCfg._WorkerThreadNum; i++) {
			_SumThreads[i].start();
		}
		if (!DatsumCfg._TriggerCron.equals("-1")) {
			_CronRunnable = new DatsumCronRunnable(DatsumCfg._TriggerFlag, DatsumCfg._TriggerCron);
			_CronThread = new Thread(_CronRunnable, "CRON");
			_CronThread.start();
		}
	}

	private static void _MainLoop() throws Exception {
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		DatsumSumMgr sum_mgr_ = new DatsumSumMgr(_SumQueues, _EobFlags);
		if (!sum_mgr_.init()) {
			L.error("init mgr error,  JVM exit");
			System.exit(1);
		}
		long last_run_ts_ = 0;
		long now_ = 0;
		while (true) {
			PubMethod.Sleep(1000);
			if (ProcessLock.IsStopRequested(cmdline_._module, cmdline_._instNm, cmdline_._rootEnv, false)) {
				L.info("try stop normal");
				if (_CronRunnable != null)
					_CronRunnable.setTerminateFlag();
				for (DatsumSumRunnable r : _SumRunnables)
					r.setTerminateFlag();
				if (_CronThread != null) {
					L.debug("try join thread {}", _CronThread.getName());
					_CronThread.join();
					L.debug("thread {} joined", _CronThread.getName());
				}
				for (Thread t : _SumThreads) {
					L.debug("try join thread {}", t.getName());
					t.join();
					L.debug("thread {} joined", t.getName());
				}
				break;
			}
			now_ = System.currentTimeMillis();
			if (!DatsumCfg._TriggerFlag.get()) {
				if (now_ - last_run_ts_ < DatsumCfg._Interval * 1000)
					continue;
			}
			last_run_ts_ = now_;
			sum_mgr_.run();
			last_run_ts_ = System.currentTimeMillis();
		}
	}
}
