package com.hp.cmcc.bboss.app.shpmsv;

import java.io.File;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlEspParameterRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlEspRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlFlatRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlObjectRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlOfferRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlPayProvRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRepartParameterRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRepartRateRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRuleItemRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRuleRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlTariffParameterRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlTariffRateRec;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class ShpmsvEdbSidd extends ShpmsvEdb {
	private static Logger L = LoggerFactory.getLogger(ShpmsvEdbSidd.class);

	private static final String _C_TBL_OFFER = "CREATE TABLE IF NOT EXISTS OFFER (ID INTEGER NOT NULL, "
			+ "DATA_SOURCE INTEGER NOT NULL, OFFER_CODE TEXT NOT NULL, PRODUCT_CODE TEXT NOT NULL, "
			+ "ORDER_MODE TEXT NOT NULL, RULE_ID INTEGER NOT NULL, ROUTE_CODE TEXT NOT NULL, DEST_SOURCE TEXT NOT NULL, "
			+ "EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, UNIQUE (ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_OFFER = "CREATE INDEX IF NOT EXISTS IX1_OFFER ON OFFER ("
			+ "DATA_SOURCE, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, EFF_DATE)";
	private static final String _I_OFFER = "INSERT INTO OFFER (ID, DATA_SOURCE, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, "
			+ "RULE_ID, ROUTE_CODE, DEST_SOURCE, EFF_DATE, EXP_DATE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_RULE = "CREATE TABLE IF NOT EXISTS RULE (RULE_ID INTEGER NOT NULL, "
			+ "RULE_NAME TEXT NOT NULL, OBJECT_ID INTEGER NOT NULL, BALANCE INTEGER NOT NULL, EFF_DATE TEXT NOT NULL, "
			+ "EXP_DATE TEXT NOT NULL, UNIQUE (RULE_ID) ON CONFLICT REPLACE)";
	private static final String _I_RULE = "INSERT INTO RULE (RULE_ID, RULE_NAME, OBJECT_ID, BALANCE, EFF_DATE, "
			+ "EXP_DATE) VALUES (?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_RULE_ITEM = "CREATE TABLE IF NOT EXISTS RULE_ITEM (ID INTEGER NOT NULL, "
			+ "RULE_ID INTEGER NOT NULL, CHARGE_ITEM TEXT NOT NULL, ITEM_NAME TEXT NOT NULL, EFF_DATE TEXT NOT NULL, "
			+ "EXP_DATE TEXT NOT NULL, UNIQUE (ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_RULE_ITEM = "CREATE INDEX IF NOT EXISTS IX1_RULE_ITEM ON RULE_ITEM (RULE_ID)";
	private static final String _I_RULE_ITEM = "INSERT INTO RULE_ITEM (ID, RULE_ID, CHARGE_ITEM, ITEM_NAME, EFF_DATE, "
			+ "EXP_DATE) VALUES (?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_OBJECT = "CREATE TABLE IF NOT EXISTS OBJECT (OBJECT_ID INTEGER NOT NULL, "
			+ "OBJECT_NAME TEXT NOT NULL, OBJECT_TYPE INTEGER NOT NULL, OWNER_NAME TEXT, FIELD_NAME TEXT, "
			+ "SEARCH_KEY TEXT, OBJECT_VALUE TEXT, UNIQUE (OBJECT_ID) ON CONFLICT REPLACE)";
	private static final String _I_OBJECT = "INSERT INTO OBJECT (OBJECT_ID, OBJECT_NAME, OBJECT_TYPE, OWNER_NAME, "
			+ "FIELD_NAME, SEARCH_KEY, OBJECT_VALUE) VALUES (?, ?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_PAY_PROV = "CREATE TABLE IF NOT EXISTS PAY_PROV (SVC_INST_ID INTEGER NOT NULL, "
			+ "CHARGE_ITEM TEXT NOT NULL, OBJECT_VALUE TEXT NOT NULL, EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, "
			+ "UNIQUE (SVC_INST_ID, CHARGE_ITEM, EFF_DATE) ON CONFLICT REPLACE)";
	private static final String _I_PAY_PROV = "INSERT INTO PAY_PROV (SVC_INST_ID, CHARGE_ITEM, OBJECT_VALUE, EFF_DATE, "
			+ "EXP_DATE) VALUES (?, ?, ?, ?, ?)";

	private static final String _C_TBL_RATE = "CREATE TABLE IF NOT EXISTS RATE (ID INTEGER NOT NULL, "
			+ "RATE_ID INTEGER NOT NULL, RULE_ID INTEGER NOT NULL, IN_OBJECT_ID INTEGER NOT NULL, "
			+ "RATE_CODE TEXT NOT NULL, RATE_TYPE INTEGER NOT NULL, CALC_PRIORITY INTEGER NOT NULL, "
			+ "ROUND_METHOD INTEGER NOT NULL, EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, UNIQUE (ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_RATE = "CREATE INDEX IF NOT EXISTS IX1_RATE ON RATE (RATE_ID)";
	private static final String _C_IX2_RATE = "CREATE INDEX IF NOT EXISTS IX2_RATE ON RATE (RULE_ID)";
	private static final String _I_RATE = "INSERT INTO RATE (ID, RATE_ID, RULE_ID, IN_OBJECT_ID, RATE_CODE, "
			+ "RATE_TYPE, CALC_PRIORITY, ROUND_METHOD, EFF_DATE, EXP_DATE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_FLAT_RATE = "CREATE TABLE IF NOT EXISTS FLAT_RATE (RATE_ID INTEGER NOT NULL, "
			+ "RATE_VALUE TEXT NOT NULL, UNIQUE (RATE_ID) ON CONFLICT REPLACE)";
	private static final String _I_FLAT_RATE = "INSERT INTO FLAT_RATE (RATE_ID, RATE_VALUE) VALUES (?, ?)";

	private static final String _C_TBL_TARIFF_RATE = "CREATE TABLE IF NOT EXISTS TARIFF_RATE (RATE_ID INTEGER NOT NULL, "
			+ "TARIFF_TYPE INTEGER NOT NULL, MATCH_MODE INTEGER NOT NULL, UNIQUE (RATE_ID) ON CONFLICT REPLACE)";
	private static final String _I_TARIFF_RATE = "INSERT INTO TARIFF_RATE (RATE_ID, TARIFF_TYPE, MATCH_MODE) VALUES (?, ?, ?)";

	private static final String _C_TBL_TARIFF_PARAMETER = "CREATE TABLE IF NOT EXISTS TARIFF_PARAMETER ("
			+ "ID INTEGER NOT NULL, OFFER_CODE TEXT NOT NULL, PRODUCT_CODE TEXT, PROD_INST_ID INTEGER NOT NULL, "
			+ "SVC_INST_ID INTEGER, ORDER_MODE TEXT NOT NULL, RULE_ID INTEGER NOT NULL, RATE_ID INTEGER NOT NULL, "
			+ "CHARGE_ITEM TEXT NOT NULL, OBJECT_VALUE TEXT NOT NULL, TARIFF_TYPE INTEGER NOT NULL, "
			+ "CALC_PRIORITY INTEGER NOT NULL, RATE_VALUE TEXT NOT NULL, DEST_SOURCE TEXT NOT NULL, "
			+ "ROUTE_FLAG TEXT NOT NULL, EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, UNIQUE (ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_TARIFF_PARAMETER = "CREATE INDEX IF NOT EXISTS IX1_TARIFF_PARAMETER "
			+ "ON TARIFF_PARAMETER (SVC_INST_ID, CHARGE_ITEM, RULE_ID, RATE_ID, TARIFF_TYPE)";
	private static final String _C_IX2_TARIFF_PARAMETER = "CREATE INDEX IF NOT EXISTS IX2_TARIFF_PARAMETER "
			+ "ON TARIFF_PARAMETER (PROD_INST_ID, CHARGE_ITEM, RULE_ID, RATE_ID, TARIFF_TYPE)";
	private static final String _I_TARIFF_PARAMETER = "INSERT INTO TARIFF_PARAMETER (ID, OFFER_CODE, PRODUCT_CODE, "
			+ "PROD_INST_ID, SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, OBJECT_VALUE, TARIFF_TYPE, "
			+ "CALC_PRIORITY, RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE) VALUES "
			+ "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String _C_TBL_REPART_RATE = "CREATE TABLE IF NOT EXISTS REPART_RATE (RATE_ID INTEGER NOT NULL, "
			+ "TARIFF_TYPE INTEGER NOT NULL, MATCH_MODE INTEGER NOT NULL, UNIQUE (RATE_ID) ON CONFLICT REPLACE)";
	private static final String _I_REPART_RATE = "INSERT INTO REPART_RATE (RATE_ID, TARIFF_TYPE, MATCH_MODE) VALUES (?, ?, ?)";

	private static final String _C_TBL_REPART_PARTITION = "CREATE TABLE IF NOT EXISTS REPART_PARTITION ("
			+ "RATE_ID INTEGER NOT NULL, ACCT_MONTH TEXT NOT NULL, RULE_ID INTEGER NOT NULL, "
			+ "UNIQUE (RATE_ID, ACCT_MONTH, RULE_ID) ON CONFLICT REPLACE)";
	private static final String _I_REPART_PARTITION = "INSERT INTO REPART_PARTITION (RATE_ID, ACCT_MONTH, RULE_ID) "
			+ "VALUES (?, ?, ?)";

	private static final String _C_TBL_REPART_YYYYMM = "CREATE TABLE IF NOT EXISTS REPART_%s (ID INTEGER NOT NULL, "
			+ "OFFER_CODE TEXT NOT NULL, PRODUCT_CODE TEXT, PROD_INST_ID INTEGER NOT NULL, "
			+ "SVC_INST_ID INTEGER, ORDER_MODE TEXT NOT NULL, RULE_ID INTEGER NOT NULL, "
			+ "RATE_ID INTEGER NOT NULL, CHARGE_ITEM TEXT NOT NULL, CALC_PRIORITY INTEGER NOT NULL, "
			+ "OBJECT_VALUE TEXT NOT NULL, TARIFF_TYPE INTEGER NOT NULL, RATE_VALUE TEXT NOT NULL, "
			+ "DEST_SOURCE TEXT NOT NULL, ROUTE_FLAG TEXT NOT NULL, EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, "
			+ "UNIQUE (ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_REPART_YYYYMM = "CREATE INDEX IF NOT EXISTS IX1_REPART_%s ON REPART_%s "
			+ "(SVC_INST_ID, CHARGE_ITEM, RULE_ID, RATE_ID, TARIFF_TYPE)";
	private static final String _C_IX2_REPART_YYYYMM = "CREATE INDEX IF NOT EXISTS IX2_REPART_%s ON REPART_%s "
			+ "(PROD_INST_ID, CHARGE_ITEM, RULE_ID, RATE_ID, TARIFF_TYPE)";
	private static final String _I_REPART_YYYYMM = "INSERT INTO REPART_%s (ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, OBJECT_VALUE, TARIFF_TYPE, RATE_VALUE, "
			+ "DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	
	private static final String _C_TBL_ESP_RATE = "CREATE TABLE IF NOT EXISTS ESP_RATE ( RATE_ID INTEGER NOT NULL, MATCH_MODE INTEGER NOT NULL, UNIQUE (RATE_ID) ON CONFLICT REPLACE)";
	private static final String _I_ESP_RATE = "INSERT INTO ESP_RATE (RATE_ID, MATCH_MODE) VALUES (?, ?)";
	private static final String _C_TBL_ESP_PARAMETER = "CREATE TABLE IF NOT EXISTS ESP_PARAMETER (ID INTEGER NOT NULL, "
					+ "OFFER_CODE TEXT NOT NULL, PRODUCT_CODE TEXT, ORDER_MODE TEXT NOT NULL, RULE_ID INTEGER NOT NULL, "
					+ "RATE_ID INTEGER NOT NULL, CHARGE_ITEM TEXT NOT NULL, CALC_PRIORITY INTEGER NOT NULL, "
					+ "OBJECT_VALUE TEXT NOT NULL, SETT_TYPE INTEGER NOT NULL, RATE_VALUE TEXT NOT NULL, TAX_RATE TEXT NOT NULL, "
					+ "DEST_SOURCE TEXT NOT NULL, ROUTE_FLAG TEXT NOT NULL, EFF_DATE TEXT NOT NULL, EXP_DATE TEXT NOT NULL, "
					+ "UNIQUE (ID) ON CONFLICT REPLACE)";
	private static final String _C_IX1_ESP_PARAMETER = "CREATE INDEX IF NOT EXISTS IX1_ESP_PARAMETER ON ESP_PARAMETER(OFFER_CODE, RULE_ID, RATE_ID, CHARGE_ITEM, SETT_TYPE)";
	private static final String _C_IX2_ESP_PARAMETER = "CREATE INDEX IF NOT EXISTS IX2_ESP_PARAMETER ON ESP_PARAMETER(PRODUCT_CODE, RULE_ID, RATE_ID, CHARGE_ITEM, SETT_TYPE)";
	private static final String _I_ESP_PARAMETER = "INSERT INTO ESP_PARAMETER (ID, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, "
			+ "RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, OBJECT_VALUE, SETT_TYPE, RATE_VALUE, TAX_RATE, DEST_SOURCE, "
			+ "ROUTE_FLAG, EFF_DATE, EXP_DATE) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private String _minAcctMonth;

	public ShpmsvEdbSidd(ShpmsvOdb odb) {
		super(odb);
		long ts_ = System.currentTimeMillis();
		ts_ -= 86400L * 1000L * DbStlRepartParameterRec._MAX_DAYS_CACHED_IN_SQLITE; // about half a year before
		_minAcctMonth = PubMethod.Long2Str(ts_, PubMethod.TimeStrFmt.Fmt8).substring(0, 6);
		L.debug("_minAcctMonth is {}", _minAcctMonth);
	}

	@Override
	public File encapsulateEdb(String shpm_ver) throws Exception {
		File fdb3_ = new File(ShpmsvCfg._WorkingDir + "/" + shpm_ver + ".db3");
		Edb edb_ = null;
		Set<String> acct_month_set_;
		ShpmsvEdbCommon edb_common_ = new ShpmsvEdbCommon(_odb);
		try {
			edb_ = new Edb(fdb3_.getAbsolutePath(), false);
			edb_common_.initEdbCommon(edb_);
			_initEdbSchema(edb_);
			_fillEdbOffer(edb_);
			_fillEdbRule(edb_);
			_fillEdbRuleItem(edb_);
			_fillEdbObject(edb_);
			_fillEdbPayProv(edb_);
			_fillEdbRate(edb_);
			_fillEdbFlatRate(edb_);
			_fillEdbTariffRate(edb_);
			_fillEdbTariffParameter(edb_);
			_fillEdbRepartRate(edb_);
			acct_month_set_ = _fillEdbRepartPartition(edb_);
			L.debug("{} acct_month to be loaded", acct_month_set_.size());
			for (String acct_month_ : acct_month_set_) {
				_fillEdbRepartParameter(edb_, acct_month_);
			}
			_fillEdbEspRate(edb_);
			_fillEdbEspParameter(edb_);
			
			_rdbCnt += edb_common_.getRdbCnt();
			_edbCnt += edb_common_.getEdbCnt();
		} finally {
			if (edb_ != null)
				edb_.destroy();
		}
		return fdb3_;
	}

	private void _initEdbSchema(Edb edb) throws SQLException {
		_rdbCnt = 0;
		_edbCnt = 0;
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(_C_TBL_OFFER);
			stmt_.executeUpdate(_C_IX1_OFFER);
			stmt_.executeUpdate(_C_TBL_RULE);
			stmt_.executeUpdate(_C_TBL_RULE_ITEM);
			stmt_.executeUpdate(_C_IX1_RULE_ITEM);
			stmt_.executeUpdate(_C_TBL_OBJECT);
			stmt_.executeUpdate(_C_TBL_PAY_PROV);
			stmt_.executeUpdate(_C_TBL_RATE);
			stmt_.executeUpdate(_C_IX1_RATE);
			stmt_.executeUpdate(_C_IX2_RATE);
			stmt_.executeUpdate(_C_TBL_FLAT_RATE);
			stmt_.executeUpdate(_C_TBL_TARIFF_RATE);
			stmt_.executeUpdate(_C_TBL_TARIFF_PARAMETER);
			stmt_.executeUpdate(_C_IX1_TARIFF_PARAMETER);
			stmt_.executeUpdate(_C_IX2_TARIFF_PARAMETER);
			stmt_.executeUpdate(_C_TBL_REPART_RATE);
			stmt_.executeUpdate(_C_TBL_REPART_PARTITION);
			
			stmt_.executeUpdate(_C_TBL_ESP_RATE);
			stmt_.executeUpdate(_C_TBL_ESP_PARAMETER);
			stmt_.executeUpdate(_C_IX1_ESP_PARAMETER);
			stmt_.executeUpdate(_C_IX2_ESP_PARAMETER);
		} finally {
			Edb.Close(null, stmt_, null);
		}
	}

	private void _fillEdbOffer(Edb edb) throws SQLException {
		List<DbStlOfferRec> db_list_ = _odb.getStlOffer();
		L.debug("{} records fetched from STL_OFFER_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_OFFER);
			for (DbStlOfferRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getId());
				pstmt_.setInt(++idx_, rec_.getData_source());
				pstmt_.setString(++idx_, rec_.getOffer_code());
				pstmt_.setString(++idx_, rec_.getProduct_code());
				pstmt_.setString(++idx_, rec_.getOrder_mode());
				pstmt_.setLong(++idx_, rec_.getRule_id());
				pstmt_.setString(++idx_, rec_.getRoute_code());
				pstmt_.setString(++idx_, rec_.getDest_source());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("OFFER", null);
		L.debug("{} records inserted into OFFER", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbRule(Edb edb) throws SQLException {
		List<DbStlRuleRec> db_list_ = _odb.getStlRule();
		L.debug("{} records fetched from STL_RULE_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_RULE);
			for (DbStlRuleRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getRule_id());
				pstmt_.setString(++idx_, rec_.getRule_name());
				pstmt_.setLong(++idx_, rec_.getObject_id());
				pstmt_.setInt(++idx_, rec_.getBalance());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("RULE", null);
		L.debug("{} records inserted into RULE", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbRuleItem(Edb edb) throws SQLException {
		List<DbStlRuleItemRec> db_list_ = _odb.getStlRuleItem();
		L.debug("{} records fetched from STL_RULE_ITEM_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_RULE_ITEM);
			for (DbStlRuleItemRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getId());
				pstmt_.setLong(++idx_, rec_.getRule_id());
				pstmt_.setString(++idx_, rec_.getCharge_item());
				pstmt_.setString(++idx_, rec_.getItem_name());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("RULE_ITEM", null);
		L.debug("{} records inserted into RULE_ITEM", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbObject(Edb edb) throws SQLException {
		List<DbStlObjectRec> db_list_ = _odb.getStlObject();
		L.debug("{} records fetched from STL_OBJECT_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_OBJECT);
			for (DbStlObjectRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getObject_id());
				pstmt_.setString(++idx_, rec_.getObject_name());
				pstmt_.setInt(++idx_, rec_.getObject_type());
				pstmt_.setString(++idx_, rec_.getOwner_name());
				pstmt_.setString(++idx_, rec_.getField_name());
				pstmt_.setString(++idx_, rec_.getSearch_key());
				pstmt_.setString(++idx_, rec_.getObject_value());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("OBJECT", null);
		L.debug("{} records inserted into OBJECT", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbPayProv(Edb edb) throws SQLException {
		List<DbStlPayProvRec> db_list_ = _odb.getStlPayProv();
		L.debug("{} records fetched from STL_PAY_PROV", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_PAY_PROV);
			for (DbStlPayProvRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getSvc_inst_id());
				pstmt_.setString(++idx_, rec_.getCharge_item());
				pstmt_.setString(++idx_, rec_.getObject_value());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("PAY_PROV", null);
		L.debug("{} records inserted into PAY_PROV", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbRate(Edb edb) throws SQLException {
		List<DbStlRateRec> db_list_ = _odb.getStlRate();
		L.debug("{} records fetched from STL_RATE_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_RATE);
			for (DbStlRateRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getId());
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setLong(++idx_, rec_.getRule_id());
				pstmt_.setLong(++idx_, rec_.getIn_object_id());
				pstmt_.setString(++idx_, rec_.getRate_code());
				pstmt_.setInt(++idx_, rec_.getRate_type());
				pstmt_.setInt(++idx_, rec_.getCalc_priority());
				pstmt_.setInt(++idx_, rec_.getRound_method());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("RATE", null);
		L.debug("{} records inserted into RATE", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbFlatRate(Edb edb) throws SQLException {
		List<DbStlFlatRateRec> db_list_ = _odb.getStlFlatRate();
		L.debug("{} records fetched from STL_FLAT_RATE_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_FLAT_RATE);
			for (DbStlFlatRateRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setString(++idx_, rec_.getRate_value());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("FLAT_RATE", null);
		L.debug("{} records inserted into FLAT_RATE", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbTariffRate(Edb edb) throws SQLException {
		List<DbStlTariffRateRec> db_list_ = _odb.getStlTariffRate();
		L.debug("{} records fetched from STL_TARIFF_RATE_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_TARIFF_RATE);
			for (DbStlTariffRateRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setInt(++idx_, rec_.getTariff_type());
				pstmt_.setInt(++idx_, rec_.getMatch_mode());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("TARIFF_RATE", null);
		L.debug("{} records inserted into TARIFF_RATE", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbTariffParameter(Edb edb) throws SQLException {
		List<DbStlTariffParameterRec> db_list_ = _odb.getStlTariffParameter();
		L.debug("{} records fetched from STL_TARIFF_PARAMETER_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_TARIFF_PARAMETER);
			for (DbStlTariffParameterRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getId());
				pstmt_.setString(++idx_, rec_.getOffer_code());
				pstmt_.setString(++idx_, rec_.getProduct_code());
				pstmt_.setLong(++idx_, rec_.getProd_inst_id());
				if (rec_.getSvc_inst_id() == null) {
					pstmt_.setNull(++idx_, java.sql.Types.INTEGER);
				} else {
					pstmt_.setLong(++idx_, rec_.getSvc_inst_id());
				}
				pstmt_.setString(++idx_, rec_.getOrder_mode());
				pstmt_.setLong(++idx_, rec_.getRule_id());
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setString(++idx_, rec_.getCharge_item());
				pstmt_.setString(++idx_, rec_.getObject_value());
				pstmt_.setInt(++idx_, rec_.getTariff_type());
				pstmt_.setInt(++idx_, rec_.getCalc_priority());
				pstmt_.setString(++idx_, rec_.getRate_value());
				pstmt_.setString(++idx_, rec_.getDest_source());
				pstmt_.setString(++idx_, rec_.getRoute_flag());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("TARIFF_PARAMETER", null);
		L.debug("{} records inserted into TARIFF_PARAMETER", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private void _fillEdbRepartRate(Edb edb) throws SQLException {
		List<DbStlRepartRateRec> db_list_ = _odb.getStlRepartRate();
		L.debug("{} records fetched from STL_REPART_RATE_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_REPART_RATE);
			for (DbStlRepartRateRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setInt(++idx_, rec_.getTariff_type());
				pstmt_.setInt(++idx_, rec_.getMatch_mode());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("REPART_RATE", null);
		L.debug("{} records inserted into REPART_RATE", edb_cnt_);
		_edbCnt += edb_cnt_;
	}

	private Set<String> _fillEdbRepartPartition(Edb edb) throws SQLException {
		Set<String> acct_month_set_ = new TreeSet<String>();
		List<Object[]> db_list_ = _odb.getStlRepartPartitions(_minAcctMonth);
		L.debug("{} records fetched from STL_REPART_PARTITION", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_REPART_PARTITION);
			for (Object[] rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, ((BigDecimal) rec_[0]).longValue());
				String acct_month_ = (String) rec_[1];
				if (!acct_month_set_.contains(acct_month_)) {
					acct_month_set_.add(acct_month_);
				}
				pstmt_.setString(++idx_, acct_month_);
				pstmt_.setLong(++idx_, ((BigDecimal) rec_[2]).longValue());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("REPART_PARTITION", null);
		L.debug("{} records inserted into REPART_PARTITION", edb_cnt_);
		_edbCnt += edb_cnt_;
		return acct_month_set_;
	}

	private void _fillEdbRepartParameter(Edb edb, String acct_month) throws SQLException {
		List<DbStlRepartParameterRec> db_list_ = _odb.getStlRepartParameterYyyymm(acct_month);
		L.debug("{} records fetched from STL_REPART_PARAMETER_T for {}", db_list_.size(), acct_month);
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(PubMethod.FmtArgs(_C_TBL_REPART_YYYYMM, acct_month));
			stmt_.executeUpdate(PubMethod.FmtArgs(_C_IX1_REPART_YYYYMM, acct_month, acct_month));
			stmt_.executeUpdate(PubMethod.FmtArgs(_C_IX2_REPART_YYYYMM, acct_month, acct_month));
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(PubMethod.FmtArgs(_I_REPART_YYYYMM, acct_month));
			for (DbStlRepartParameterRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getId());
				pstmt_.setString(++idx_, rec_.getOffer_code());
				pstmt_.setString(++idx_, rec_.getProduct_code());
				pstmt_.setLong(++idx_, rec_.getProd_inst_id());
				if (rec_.getSvc_inst_id() == null) {
					pstmt_.setNull(++idx_, java.sql.Types.INTEGER);
				} else {
					pstmt_.setLong(++idx_, rec_.getSvc_inst_id());
				}
				pstmt_.setString(++idx_, rec_.getOrder_mode());
				pstmt_.setLong(++idx_, rec_.getRule_id());
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setString(++idx_, rec_.getCharge_item());
				pstmt_.setInt(++idx_, rec_.getCalc_priority());
				pstmt_.setString(++idx_, rec_.getObject_value());
				pstmt_.setInt(++idx_, rec_.getTariff_type());
				pstmt_.setString(++idx_, rec_.getRate_value());
				pstmt_.setString(++idx_, rec_.getDest_source());
				pstmt_.setString(++idx_, rec_.getRoute_flag());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("REPART_" + acct_month, null);
		L.debug("{} records inserted into REPART_{}", edb_cnt_, acct_month);
		_edbCnt += edb_cnt_;
	}
	
	private void _fillEdbEspRate(Edb edb) throws SQLException {
		List<DbStlEspRateRec> db_list_ = _odb.getStlEspRate();
		L.debug("{} records fetched from STL_ESP_RATE_T", db_list_.size());
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_ESP_RATE);
			for (DbStlEspRateRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setInt(++idx_, rec_.getMatch_mode());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("ESP_RATE", null);
		L.debug("{} records inserted into ESP_RATE", edb_cnt_);
		_edbCnt += edb_cnt_;
	}
	
	private void _fillEdbEspParameter(Edb edb) throws SQLException {
		List<DbStlEspParameterRec> db_list_ = _odb.getStlEspParameter();
		L.debug("{} records fetched from STL_ESP_PARAMETER_T for {}", db_list_.size(), _minAcctMonth);
		_rdbCnt += db_list_.size();
		Connection conn_ = edb.getConnection();
		Statement stmt_ = null;
		PreparedStatement pstmt_ = null;
		try {
			stmt_ = conn_.createStatement();
			stmt_.executeUpdate(Edb.TRANSACTION_BEGIN);
			pstmt_ = conn_.prepareStatement(_I_ESP_PARAMETER);
			for (DbStlEspParameterRec rec_ : db_list_) {
				int idx_ = 0;
				pstmt_.setLong(++idx_, rec_.getId());
				pstmt_.setString(++idx_, rec_.getOffer_code());
				pstmt_.setString(++idx_, rec_.getProduct_code());
				pstmt_.setString(++idx_, rec_.getOrder_mode());
				pstmt_.setLong(++idx_, rec_.getRule_id());
				pstmt_.setLong(++idx_, rec_.getRate_id());
				pstmt_.setString(++idx_, rec_.getCharge_item());
				pstmt_.setInt(++idx_, rec_.getCalc_priority());
				pstmt_.setString(++idx_, rec_.getObject_value());
				pstmt_.setInt(++idx_, rec_.getSett_type());
				pstmt_.setString(++idx_, rec_.getRate_value());
				pstmt_.setString(++idx_, rec_.getTax_rate());
				pstmt_.setString(++idx_, rec_.getDest_source());
				pstmt_.setString(++idx_, rec_.getRoute_flag());
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getEff_date(), PubMethod.TimeStrFmt.Fmt14));
				pstmt_.setString(++idx_, PubMethod.Timestamp2Str(rec_.getExp_date(), PubMethod.TimeStrFmt.Fmt14));
				//pstmt_.setString(++idx_, rec_.getAcct_month());
				pstmt_.executeUpdate();
			}
			stmt_.executeUpdate(Edb.TRANSACTION_COMMIT);
		} finally {
			Edb.Close(null, stmt_, null);
			Edb.Close(null, pstmt_, null);
		}
		int edb_cnt_ = edb.count("ESP_PARAMETER", null);
		L.debug("{} records inserted into ESP_PARAMETER", edb_cnt_);
		_edbCnt += edb_cnt_;
		
	}
}
