package com.hp.cmcc.bboss.app.rmtdis;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

/**
 * @Classname ConnPoolConfig
 * @Description 连接池配置
 * @Date 2023/2/24 17:19
 * <AUTHOR>
 * @Version 1.0.0
 */
public class ConnPoolConfig extends GenericObjectPoolConfig {

    public ConnPoolConfig() {
        // 对象池中最少需要有几个空闲对象
        setMinIdle(4);
        // 对象池的最大容量。池里最多存放多少个对象
        setMaxTotal(50);
        // 当从对象池里借走一个对象的时候，是否校验该对象的有效性
        setTestOnBorrow(true);
        // 回收器线程多久执行一次空闲对象回收（轮询间隔时间，单位毫秒）
        setTimeBetweenEvictionRunsMillis(60 * 60000);
        // 当回收器在扫描空闲对象时，是否校验对象的有效性。
        // 如果某个对象空闲时间还没达到规定的阈值，如果testWhileIdle配置为true，
        // 那么就会检查该对象是否还有效，如果该对象的资源已经失效（例如：连接断开），那么他就可以被回收。
        setTestWhileIdle(true);
    }
}
