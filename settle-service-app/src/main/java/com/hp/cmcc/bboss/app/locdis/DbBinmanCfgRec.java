package com.hp.cmcc.bboss.app.locdis;

import java.io.File;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class DbBinmanCfgRec extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(DbBinmanCfgRec.class);
	public static final int ENABLED_1_GZIP = 1;
	public static final int ENABLED_2_ULNK = 2;
	public Pattern _patternMat;
	public Pattern _patternIgn;
	public Pattern _patternSub;

	private Integer cfg_id;
	private String inst_nm;
	private String src_dir;
	private Integer priority;
	private String pattern_mat;
	private String pattern_ign;
	private String pattern_sub;
	private Long delay_dur;
	private Integer enabled;

	public boolean validateRec(int min_delay) {
		boolean rc_ = true;
		if (!validateSrcDir())
			rc_ = false;
		if (!validatePatternMat())
			rc_ = false;
		if (!validatePatternIgn())
			rc_ = false;
		if (!validatePatternSub())
			rc_ = false;
		if (delay_dur < min_delay) {
			L.warn("[{}] delay_dur {} lt min_delay {}, reset as min_delay", cfg_id, delay_dur, min_delay);
			delay_dur = (long) min_delay;
		}
		return rc_;
	}

	public boolean validatePatternMat() {
		try {
			_patternMat = Pattern.compile(pattern_mat);
			return true;
		} catch (Exception e) {
			L.warn("[{}] invalid pattern_mat [{}]", cfg_id, pattern_mat);
			_patternMat = null;
			return false;
		}
	}

	public boolean validatePatternIgn() {
		if (pattern_ign == null) {
			_patternIgn = null;
			return true;
		}
		try {
			_patternIgn = Pattern.compile(pattern_ign);
			return true;
		} catch (Exception e) {
			L.warn("[{}] invalid pattern_ign [{}]", cfg_id, pattern_ign);
			_patternIgn = null;
			return false;
		}
	}

	public boolean validatePatternSub() {
		if (pattern_sub == null) {
			_patternSub = null;
			return true;
		}
		try {
			_patternSub = Pattern.compile(pattern_sub);
			return true;
		} catch (Exception e) {
			L.warn("[{}] invalid pattern_sub [{}]", cfg_id, pattern_sub);
			_patternSub = null;
			return false;
		}
	}

	public boolean validateSrcDir() {
		String s = PubMethod.SubStrByEnv(src_dir);
		if (s == null) {
			L.warn("[{}] src_dir SubStrByEnv [{}] failed", cfg_id, src_dir);
			return false;
		}
		File f = new File(s);
		if (!f.isDirectory()) {
			L.warn("[{}] src_dir [{}] not exists or not a directory", cfg_id, s);
			return false;
		}
		src_dir = s;
		return true;
	}

	public Integer getCfg_id() {
		return cfg_id;
	}

	public void setCfg_id(Integer cfg_id) {
		this.cfg_id = cfg_id;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getSrc_dir() {
		return src_dir;
	}

	public void setSrc_dir(String src_dir) {
		this.src_dir = src_dir;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public String getPattern_mat() {
		return pattern_mat;
	}

	public void setPattern_mat(String pattern_mat) {
		this.pattern_mat = pattern_mat;
	}

	public String getPattern_ign() {
		return pattern_ign;
	}

	public void setPattern_ign(String pattern_ign) {
		this.pattern_ign = pattern_ign;
	}

	public String getPattern_sub() {
		return pattern_sub;
	}

	public void setPattern_sub(String pattern_sub) {
		this.pattern_sub = pattern_sub;
	}

	public Long getDelay_dur() {
		return delay_dur;
	}

	public void setDelay_dur(Long delay_dur) {
		this.delay_dur = delay_dur;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}
}
