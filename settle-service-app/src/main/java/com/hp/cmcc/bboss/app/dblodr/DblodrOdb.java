package com.hp.cmcc.bboss.app.dblodr;

import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class DblodrOdb {
	private static Logger L = LoggerFactory.getLogger(DblodrOdb.class);
	private static final String _I_DBLODR_LOG = "INSERT INTO DBLODR_LOG (LOG_MM, FILE_NM, PROC_FLAG, "
			+ "TOT_CNT, DB_CNT, BAD_CNT, DSCD_CNT, TBL_NM, FILE_SZ, TS_START, TS_END) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	public void addDblodrLog(DbDblodrLogRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		cli_.updateWithThrow(_I_DBLODR_LOG, rec.asInsertObjArray());
		L.info("add {}", rec.toGsonStr());
	}
}
