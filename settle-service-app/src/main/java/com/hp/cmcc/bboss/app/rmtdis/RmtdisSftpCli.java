package com.hp.cmcc.bboss.app.rmtdis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;

public class RmtdisSftpCli {
	private static Logger L = LoggerFactory.getLogger(RmtdisSftpCli.class);
	public JSch _jsch;
	public Session _session;
	public ChannelSftp _sftp;

	public RmtdisSftpCli() {
		_jsch = new JSch();
	}

	public void login(DbRmtdisLoginCfgRec login_cfg) throws Exception {
		/*this.close();
		_session = _jsch.getSession(login_cfg.getRmt_user(), login_cfg.getRmt_host(), login_cfg.getRmt_port());
		_session.setConfig("StrictHostKeyChecking", "no");
		_session.setPassword(login_cfg.getRmt_passwd_plain());
		_session.connect(30000);
		// L.debug("session {}@{}:{} connect ok, loc ip:port {}:{}", new Object[] { login_cfg.getRmt_user(),
		// login_cfg.getRmt_host(),
		// login_cfg.getRmt_port(), _session.getLocalIpAddress(), _session.getLocalPort() });
		L.debug("session {}@{}:{} connect ok",
				new Object[] { login_cfg.getRmt_user(), login_cfg.getRmt_host(), login_cfg.getRmt_port() });
		_sftp = (ChannelSftp) _session.openChannel("sftp");
		_sftp.connect();*/

		ConnPool connPool = ConnPool.getConnPool(
				login_cfg.getRmt_host(), login_cfg.getRmt_port(), login_cfg.getRmt_user(), login_cfg.getRmt_passwd_plain());
		try {
			_sftp = connPool.borrowObject();
		} catch (Exception e) {
			L.error("ConnPool is Login error:{}", e.getMessage(), e);
		} finally {
			connPool.returnObject(_sftp);
		}
	}

	public void close() {
		if (_sftp != null) {
			_sftp.disconnect();
			_sftp = null;
		}
		if (_session != null) {
			_session.disconnect();
			_session = null;
		}
	}

	public ChannelSftp borrowChannel(DbRmtdisLoginCfgRec cfgRec) {
		try {
			ConnPool connPool = ConnPool.getConnPool(cfgRec.getRmt_host(), cfgRec.getRmt_port(), cfgRec.getRmt_user(), cfgRec.getRmt_passwd_plain());
			return connPool.borrowObject();
		} catch (Exception e) {
			L.error("Get channelSftp from pool fail", e);
			return null;
		}
	}

	public void returnChannel(DbRmtdisLoginCfgRec cfgRec, ChannelSftp channel) {
		ConnPool connPool = ConnPool.getConnPool(cfgRec.getRmt_host(), cfgRec.getRmt_port(), cfgRec.getRmt_user(), cfgRec.getRmt_passwd_plain());
		try {
			connPool.returnObject(channel);
		} catch (Exception e) {
			L.error("Return channelSftp to pool fail", e);
		}
	}

}
