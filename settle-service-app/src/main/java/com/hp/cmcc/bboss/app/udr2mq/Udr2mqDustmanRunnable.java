package com.hp.cmcc.bboss.app.udr2mq;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class Udr2mqDustmanRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(Udr2mqDustmanRunnable.class);
	private Udr2mqOdb _odb;
	private List<String> _bizTypeList;
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public Udr2mqDustmanRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_odb = new Udr2mqOdb();
		_bizTypeList = new ArrayList<String>();
		_odbUtils = new OdbUtils();
		for (Entry<String, Udr2mqBizCfg> entry_ : Udr2mqCfg._BizCfgMap.entrySet()) {
			if (!_bizTypeList.contains(entry_.getValue()._bizType)) {
				_bizTypeList.add(entry_.getValue()._bizType);
			}
		}
		L.debug("_bizTypeList [{}]", PubMethod.Collection2Str(_bizTypeList, ","));
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				_exec();
				_interval();
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
		L.info("thread terminated");
	}

	private void _exec() {
		for (String biz_type_ : _bizTypeList) {
			_clearHisCount(biz_type_);
			_clearHisBizlog(biz_type_);
		}
	}

	private void _clearHisCount(String biz_type) {
		int type_id_ = Udr2mqCfg._BizTypeMap.get(biz_type).getType_id();
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		long two_hours_ago_ = System.currentTimeMillis();
		two_hours_ago_ -= (2 * 3600 * 1000);
		String expire_ymdh_ = PubMethod.Long2Str(two_hours_ago_, PubMethod.TimeStrFmt.Fmt14).substring(0, 10);
		String key_ = String.format(MdbConst.BIZLOG_KEY_CACHE_CNT, type_id_);
		ScanParams scan_params_ = new ScanParams();
		scan_params_.match("[1-9][0-9][0-9][0-9][0-1][0-9][0-3][0-9][0-2][0-9]");
		int del_cnt_ = 0;
		try {
			String cursor_ = ScanParams.SCAN_POINTER_START;
			while (true) {
				ScanResult<Entry<String, String>> scan_result_ = jedis_.hscan(key_, cursor_, scan_params_);
				for (Entry<String, String> entry_ : scan_result_.getResult()) {
					if (expire_ymdh_.compareTo(entry_.getKey()) < 0) {
						// L.trace("{} expire_ymdh_ {}, {} not expire yet", key_, expire_ymdh_, entry_.getKey());
						continue;
					}
					if (!"0".equals(entry_.getValue())) {
						L.trace("{} {} {} not 0, skip", key_, entry_.getKey(), entry_.getValue());
						continue;
					}
					jedis_.hdel(key_, entry_.getKey());
					L.trace("{} {} is 0, deleted", key_, entry_.getKey());
					del_cnt_++;
				}
				cursor_ = scan_result_.getCursor();
				if (cursor_.equals(ScanParams.SCAN_POINTER_START)) {
					break;
				}
			}
			if (del_cnt_ > 0) {
				L.debug("{}, {} expire and 0 value hkeys deleted", key_, del_cnt_);
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_210341005_CLEAR_HIS_COUNT_EXCEPTION,
					PubMethod.FmtArgs("clear his count exception, key=[%s] expire_ymdh=[%s]", key_, expire_ymdh_), null);
			alm_.setAlm_kpi(e.toString());
			L.warn(alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}

	private void _clearHisBizlog(String biz_type) {
		int type_id_ = Udr2mqCfg._BizTypeMap.get(biz_type).getType_id();
		MdbCli cli_ = MdbAgt.GetBizlogInstance();
		JedisSentinelPool pool_ = cli_.getPool();
		Jedis jedis_ = pool_.getResource();
		long ten_min_ago_ = System.currentTimeMillis();
		ten_min_ago_ -= (10 * 60 * 1000);
		String expire_tm_ = PubMethod.Long2Str(ten_min_ago_, PubMethod.TimeStrFmt.Fmt14);
		ScanParams scan_params_ = new ScanParams();
		scan_params_.match("L:[1-9][0-9][0-9][0-9][0-1][0-9][0-3][0-9][0-2][0-9]:[1-9][0-9][0-9][0-9][0-1][0-9][0-3][0-9]"
				+ String.format("%03d", type_id_) + "[0-9][0-9][0-9][0-9][0-9][0-9]");
		int del_cnt_ = 0;
		try {
			String cursor_ = ScanParams.SCAN_POINTER_START;
			while (true) {
				ScanResult<String> scan_result_ = jedis_.scan(cursor_, scan_params_);
				for (String k : scan_result_.getResult()) {
					Map<String, String> biz_log_map_ = jedis_.hgetAll(k);
					if (!_needDelete(k, expire_tm_, biz_log_map_))
						continue;
					jedis_.del(k);
					L.trace("{} deleted", k);
					_updOraLog(k, biz_log_map_);
					del_cnt_++;
				}
				cursor_ = scan_result_.getCursor();
				if (cursor_.equals(ScanParams.SCAN_POINTER_START)) {
					break;
				}
			}
			if (del_cnt_ > 0) {
				L.debug("{} expire and balanced bizlog deleted", del_cnt_);
			}
		} catch (Exception e) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_210341006_CLEAR_HIS_BIZLOG_EXCEPTION,
					PubMethod.FmtArgs("clear his bizlog exception, expire_tm=[%s], biz_type=[%s]", expire_tm_, biz_type), null);
			L.warn(alm_.getAlm_msg(), e);
			_odbUtils.addRawAlm(null, alm_);
			cli_.returnBrokenResource(null, pool_, jedis_);
			jedis_ = null;
		} finally {
			cli_.returnResource(pool_, jedis_);
		}
	}

	private boolean _needDelete(String log_key, String expire_tm, Map<String, String> biz_log_map) {
		String rcv_tm_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_RCV_TM);
		if (rcv_tm_ == null) {
			L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_RCV_TM);
			return false;
		}
		if (expire_tm.compareTo(rcv_tm_) < 0) {
			// L.trace("{}, {}={}, newer than {}, not expire yet", log_key, MdbConst.BIZLOG_HK_LOG_RCV_TM, rcv_tm_, expire_tm);
			return false;
		}

		String cnt_tot_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_TOT);
		if (cnt_tot_str_ == null) {
			L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_CNT_TOT);
			return false;
		}

		String cnt_fmt_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_FMT);
		if (cnt_fmt_str_ == null) {
			L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_CNT_FMT);
			return false;
		}

		String cnt_rat_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_RAT);
		if (cnt_rat_str_ == null) {
			L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_CNT_RAT);
			return false;
		}

		String cnt_dup_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_DUP);
		if (cnt_dup_str_ == null) {
			L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_CNT_DUP);
			return false;
		}

		String cnt_nml_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_NML);
		if (cnt_nml_str_ == null) {
			L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_CNT_NML);
			return false;
		}

		int cnt_tot_ = Integer.parseInt(cnt_tot_str_);
		int cnt_fmt_ = Integer.parseInt(cnt_fmt_str_);
		int cnt_rat_ = Integer.parseInt(cnt_rat_str_);
		int cnt_dup_ = Integer.parseInt(cnt_dup_str_);
		int cnt_nml_ = Integer.parseInt(cnt_nml_str_);
		int cnt_sum_ = cnt_fmt_ + cnt_rat_ + cnt_dup_ + cnt_nml_;
		if (cnt_tot_ != cnt_sum_) {
			L.trace("{}, tot {} ne sum {}, fmt:rat:dup:nml {}:{}:{}:{}, skip", log_key, cnt_tot_, cnt_sum_, cnt_fmt_, cnt_rat_,
					cnt_dup_, cnt_nml_);
			return false;
		}

		String fbk_tot_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_FBK_TOT);
		if (fbk_tot_str_ != null) {
			String fbk_fmt_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_FBK_FMT);
			if (fbk_fmt_str_ == null) {
				L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_FBK_FMT);
				return false;
			}

			String fbk_rat_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_FBK_RAT);
			if (fbk_rat_str_ == null) {
				L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_FBK_RAT);
				return false;
			}

			String fbk_dup_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_FBK_DUP);
			if (fbk_dup_str_ == null) {
				L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_FBK_DUP);
				return false;
			}

			String fbk_nml_str_ = biz_log_map.get(MdbConst.BIZLOG_HK_LOG_FBK_NML);
			if (fbk_nml_str_ == null) {
				L.warn("{}, hkey {} not exists", log_key, MdbConst.BIZLOG_HK_LOG_FBK_NML);
				return false;
			}

			int fbk_tot_ = Integer.parseInt(fbk_tot_str_);
			int fbk_fmt_ = Integer.parseInt(fbk_fmt_str_);
			int fbk_rat_ = Integer.parseInt(fbk_rat_str_);
			int fbk_dup_ = Integer.parseInt(fbk_dup_str_);
			int fbk_nml_ = Integer.parseInt(fbk_nml_str_);
			int fbk_sum_ = fbk_fmt_ + fbk_rat_ + fbk_dup_ + fbk_nml_;
			if (fbk_tot_ != fbk_sum_) {
				L.trace("{}, fbk {} ne sum {}, fmt:rat:dup:nml {}:{}:{}:{}, skip", log_key, fbk_tot_, fbk_sum_, fbk_fmt_, fbk_rat_,
						fbk_dup_, fbk_nml_);
				return false;
			}
		}

		L.trace("{}, balanced tot {}, fmt:rat:dup:nml {}:{}:{}:{}",
				new Object[] { log_key, cnt_tot_, cnt_fmt_, cnt_rat_, cnt_dup_, cnt_nml_ });
		return true;
	}

	private void _updOraLog(String log_key, Map<String, String> biz_log_map) {
		long file_id_ = Long.parseLong(log_key.substring(13));
		int fmt_err_cnt_ = Integer.parseInt(biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_FMT));
		int rat_err_cnt_ = Integer.parseInt(biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_RAT));
		int dup_cnt_ = Integer.parseInt(biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_DUP));
		int nml_cnt_ = Integer.parseInt(biz_log_map.get(MdbConst.BIZLOG_HK_LOG_CNT_NML));
		_odb.updUdr2mqLog(file_id_, fmt_err_cnt_, rat_err_cnt_, dup_cnt_, nml_cnt_);
	}

	private void _interval() {
		if (_terminateFlag)
			return;
		for (int i = 0; i < 60; i++) {
			PubMethod.Sleep(1000);
			if (_terminateFlag) {
				L.debug("_terminateFlag detected");
				break;
			}
		}
	}
}
