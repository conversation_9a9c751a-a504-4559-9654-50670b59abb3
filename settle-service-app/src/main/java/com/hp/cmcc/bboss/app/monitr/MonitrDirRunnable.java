package com.hp.cmcc.bboss.app.monitr;

import java.io.File;
import java.util.Arrays;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.hp.cmcc.bboss.pub.odb.OdbUtils;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AlmConst;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class MonitrDirRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(MonitrDirRunnable.class);
	private long _tsScan;
	private OdbUtils _odbUtils;
	private boolean _terminateFlag = false;

	public MonitrDirRunnable() {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_odbUtils = new OdbUtils();
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		L.info("thread started");
		long now_;
		while (!_terminateFlag) {
			try {
				PubMethod.Sleep(1000);
				if (_terminateFlag) {
					L.debug("_terminateFlag detected");
					continue;
				}
				now_ = System.currentTimeMillis();
				if (now_ > _tsScan) {
					_tsScan = now_ + MonitrCfg._IntervalDir * 1000;
					_scan();
					now_ = System.currentTimeMillis();
					_tsScan = now_ + MonitrCfg._IntervalDir * 1000;
				}
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5000);
			}
		}
		L.info("thread end");
	}

	private void _scan() {
		Map<String, MonitrDirThreshold> dir_threshold_map_ = MonitrCfg._DirThresholdMap;
		for (MonitrDirThreshold threshold_ : dir_threshold_map_.values()) {
			_scanOneDir(threshold_);
		}
	}

	private void _scanOneDir(MonitrDirThreshold threshold) {
		File dir_ = new File(threshold._dirPath);
		if (!dir_.isDirectory()) {
			L.warn("dir {} not exists any more", threshold._dirPath);
			return;
		}
		File target_ = dir_;
		if (threshold._filePattern != null) {
			File[] file_list_ = dir_.listFiles();
			if (file_list_ != null && file_list_.length > 0)
				Arrays.sort(file_list_);
			for (File f : file_list_) {
				if (!threshold._filePattern.matcher(f.getName()).find()) {
					continue;
				}
				if (target_ == dir_)
					target_ = f;
				if (threshold._isNewest == 0) {
					if (f.lastModified() < target_.lastModified())
						target_ = f;
				} else {
					if (f.lastModified() > target_.lastModified())
						target_ = f;
				}
			}
		}
		String last_modified_ = PubMethod.Long2Str(target_.lastModified(), PubMethod.TimeStrFmt.Fmt19);
		L.trace("{} {} {} {}", new Object[] { threshold._secNm, target_.getAbsolutePath(), target_.isDirectory(), last_modified_ });
		long now_ = System.currentTimeMillis();
		long diff_ = now_ - target_.lastModified();
		if (diff_ > threshold._thresholdIdle * 1000L) {
			DbRawAlmRec alm_ = new DbRawAlmRec(AlmConst.ALM_200331009_DIR_IDLE, PubMethod.FmtArgs("%s%s, idle %d > %d, since %s",
					target_.getAbsolutePath(), target_.isDirectory() ? "/" : "", diff_ / 1000, threshold._thresholdIdle,
					last_modified_), threshold.toGsonStr());
			_odbUtils.addRawAlm(null, alm_);
		}
	}
}
