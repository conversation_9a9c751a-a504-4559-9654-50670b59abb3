package com.hp.cmcc.bboss.app.bizrcy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;

public class BizrcyOdb {
	private static Logger L = LoggerFactory.getLogger(BizrcyOdb.class);
	private static final String _I_BIZRCY_LOG = "INSERT INTO BIZRCY_LOG (LOG_MM, RCY_TM, DMP_FILE_NM, UDR_TOT_CNT, UDR_IGN_CNT,"
			+ "UDR_INI_CNT, UDR_RAT_CNT, UDR_DUP_CNT, UDR_ERR_CNT, UDR_RRT_CNT, UDR_RWK_CNT, UDR_BAD_CNT, KEY_PATTERN) VALUES "
			+ "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	public void addBizrcyLog(DbBizrcyLogRec rec) {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		cli_.update(_I_BIZRCY_LOG, rec.asInsertObjArray());
		L.info("add {}", rec.toGsonStr());
	}
}
