<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
	<id>distribution</id>
	<formats>
		<format>zip</format>
	</formats>
	<fileSets>
		<fileSet>
			<directory>${project.basedir}/src/main/java</directory>
			<outputDirectory>/</outputDirectory>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<useProjectArtifact>true</useProjectArtifact>
			<outputDirectory>lib</outputDirectory>
			<!-- 将scope为runtime的依赖包打包到lib目录下。 -->
			<scope>runtime</scope>
		</dependencySet>
		<dependencySet>
			<useProjectArtifact>true</useProjectArtifact>
			<outputDirectory>lib</outputDirectory>
			<!-- 将scope为runtime的依赖包打包到lib目录下。 -->
			<scope>system</scope>
			<includes>
				<include>*:jar</include>
			</includes>
		</dependencySet>
	</dependencySets>
</assembly>