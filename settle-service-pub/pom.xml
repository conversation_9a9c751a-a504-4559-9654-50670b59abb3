<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.hp.cmcc.bboss</groupId>
		<artifactId>settle-service</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>settle-service-pub</artifactId>
	<name>settle-service-pub</name>
	<version>1.0.0</version>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>12.2.0.1</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.22</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.20</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>



		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.3</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>1.2.23</version>
		</dependency>
		<!--<dependency>-->
			<!--<groupId>com.oracle.database.jdbc</groupId>-->
			<!--<artifactId>ojdbc6</artifactId>-->
			<!--<version>11.2.0.4</version>-->
		<!--</dependency>-->

		<!--<dependency>-->
			<!--<groupId>com.oracle</groupId>-->
			<!--<artifactId>ojdbc6</artifactId>-->
			<!--<version>********</version>-->
		<!--</dependency>-->
		<dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>kryo</artifactId>
			<version>3.0.3</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<id>settle-pub-jar</id>
						<configuration>
							<descriptors>
								<descriptor>src/assembly/asm-pub.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<!--<finalName>settle-pub</finalName>-->
	</build>
</project>
