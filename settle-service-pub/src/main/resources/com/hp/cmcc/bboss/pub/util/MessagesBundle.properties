/**************************************************************************
/* MessagesBundle.properties -- English language error messages
/*
/* Copyright (c) 1998 by <PERSON> (<EMAIL>) and
/*                       <PERSON> (<EMAIL>)
/*
/* This program is free software; you can redistribute it and/or modify
/* it under the terms of the GNU Library General Public License as published
/* by  the Free Software Foundation; either version 2 of the License or
/* (at your option) any later version.
/*
/* This program is distributed in the hope that it will be useful, but
/* WITHOUT ANY WARRANTY; without even the implied warranty of
/* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
/* GNU Library General Public License for more details.
/*
/* You should have received a copy of the GNU Library General Public License
/* along with this program; see the file COPYING.LIB.  If not, write to
/* the Free Software Foundation Inc., 59 Temple Place - Suite 330,
/* Boston, MA  02111-1307 USA
/**************************************************************************/

getopt.ambigious={0}: option ''{1}'' is ambiguous
getopt.arguments1={0}: option ''--{1}'' doesn't allow an argument
getopt.arguments2={0}: option ''{1}{2}'' doesn't allow an argument
getopt.requires={0}: option ''{1}'' requires an argument
getopt.unrecognized={0}: unrecognized option ''--{1}''
getopt.unrecognized2={0}: unrecognized option ''{1}{2}''
getopt.illegal={0}: illegal option -- {1}
getopt.invalid={0}: invalid option -- {1}
getopt.requires2={0}: option requires an argument -- {1}
getopt.invalidValue=Invalid value {0} for parameter 'has_arg' 

