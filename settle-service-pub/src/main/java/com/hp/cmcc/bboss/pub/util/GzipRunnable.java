package com.hp.cmcc.bboss.pub.util;

import java.io.File;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

public class GzipRunnable implements Runnable {
	private static Logger L = LoggerFactory.getLogger(GzipRunnable.class);
	private BlockingQueue<String> _queue = null;
	private String _gzipPath = null;
	private boolean _bakByYmd = false;
	private boolean _terminateFlag = false;

	public GzipRunnable(BlockingQueue<String> queue, String gzip_path, boolean bak_by_ymd) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		_queue = queue;
		_gzipPath = gzip_path;
		_bakByYmd = bak_by_ymd;
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	public void run() {
		StringBuilder ymd_ = new StringBuilder();
		L.info("thread started");
		while (!_terminateFlag) {
			try {
				String path_ = _queue.poll(2, TimeUnit.SECONDS);
				if (path_ == null)
					continue;
				File f = new File(path_);
				if (!f.isFile()) {
					L.warn("[{}] not exists, can not gzip, just skip", path_);
					continue;
				}
				boolean mkdir_status_ = _mkdirYmd(f, ymd_);
				boolean gzip_status_ = PubMethod.Gzip(_gzipPath, f, true);
				if (!gzip_status_) {
					L.warn("call gzip({}) failed, just skip", path_);
					continue;
				}
				if (mkdir_status_ && _bakByYmd) {
					String src_ = path_;
					String dst_ = f.getAbsoluteFile().getParent() + "/" + ymd_.substring(0) + "/" + f.getName();
					if (!f.getName().endsWith(".gz")) {
						src_ = path_ + ".gz";
						dst_ = dst_ + ".gz";
					}
					if (PubMethod.MoveAFile(src_, dst_)) {
						L.debug("mv {} to {} ok", src_, dst_);
					} else {
						L.warn("mv {} to {} error", src_, dst_);
					}
				}
			} catch (Exception e) {
				L.warn("encounter exception, sleep 5 seconds", e);
				PubMethod.Sleep(5 * 1000);
			}
		}
		L.info("thread terminated");
	}

	private boolean _mkdirYmd(File f, StringBuilder ymd) {
		ymd.delete(0, ymd.length());
		if (_bakByYmd) {
			ymd.append(PubMethod.Long2Str(f.lastModified(), PubMethod.TimeStrFmt.Fmt8));
			File d = new File(f.getAbsoluteFile().getParent() + "/" + ymd.substring(0));
			if (!d.isDirectory()) {
				if (d.mkdir()) {
					L.debug("mkdir({}) done", d.getAbsolutePath());
				} else if (d.isDirectory()) {
					L.warn("mkdir({}) error", d.getAbsolutePath());
					return false;
				}
			}
		}
		return true;
	}
}
