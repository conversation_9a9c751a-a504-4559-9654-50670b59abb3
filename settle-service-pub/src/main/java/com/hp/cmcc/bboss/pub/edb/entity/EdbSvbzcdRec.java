package com.hp.cmcc.bboss.pub.edb.entity;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbSvbzcdRec extends GsonObj implements Effectivable {
	public Long _id;
	public String _ecCode;
	public String _servCode;
	public String _bizCode;
	public String _prodOrderId;
	public String _orderId;
	public Integer _bizCodeApply;
	public String _provCode;
	public String _productCode;
	public String _serviceCode;
	public String _effTm;
	public String _expTm;
	public String _ecGroupId;
	public String _serviceType;
	public String _sendProv;
	public String _prodOrderMode;
	public Integer _subGroupFlag;
	public String _carryType;
	public String _signEntity;
	public String _parentOrderId;
	public Integer _loconFlag;
	public Integer _orderLevel;

	@Override
	public boolean isEffective(String tm14) {
		if (tm14 == null || tm14.compareTo(_effTm) < 0 || tm14.compareTo(_expTm) > 0) {
			return false;
		} else {
			return true;
		}
	}
}
