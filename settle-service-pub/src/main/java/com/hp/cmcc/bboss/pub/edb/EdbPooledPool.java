package com.hp.cmcc.bboss.pub.edb;

import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

public class EdbPooledPool extends GenericObjectPool<Edb> {
	public EdbPooledPool(PooledObjectFactory<Edb> factory, GenericObjectPoolConfig config) {
		super(factory, config);
	}

	public EdbPooledPool(PooledObjectFactory<Edb> factory) {
		super(factory);
	}
}
