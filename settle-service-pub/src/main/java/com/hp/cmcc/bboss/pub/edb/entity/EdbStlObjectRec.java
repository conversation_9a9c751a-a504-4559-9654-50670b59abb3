package com.hp.cmcc.bboss.pub.edb.entity;

import java.io.Serializable;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbStlObjectRec extends GsonObj implements Serializable {
	private static final long serialVersionUID = 7307864161161814716L;

	public static final int OBJECT_TYPE_1_FIX = 1; //固定值， 取对象的默认值
	public static final int OBJECT_TYPE_2_DEF = 2; //定义值， 根据owner_name,field_name,search_key定义取值
	public static final int OBJECT_TYPE_3_GRP = 3; //GROUP对象

	public static final String OBJECT_OWNER_IDD = "Idd"; //Field_Name = IDD中的字段名称
	public static final String OBJECT_OWNER_MEM = "Mem";
	public static final String OBJECT_OWNER_ORDER = "Order";
	public static final String OBJECT_OWNER_RULE = "Rule";  //Field_Name = rule_prov
	public static final String OBJECT_OWNER_REPART = "Repart";  //Field_Name = report_prov
	public static final String OBJECT_OWNER_PAY = "Pay";  //Field_Name = pay_prov
	public static final String OBJECT_OWNER_ESP = "Esp"; //Field_Name = esp_prov

	public Long _objectId;
	public String _objectName;
	public Integer _objectType;
	public String _ownerName;
	public String _fieldName;
	public String _searchKey;
	public String _objectValue;
}
