package com.hp.cmcc.bboss.pub.udr;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class UdrKafkaProducerFactory {
	private static Logger L = LoggerFactory.getLogger(UdrKafkaProducerFactory.class);

	/**
	 *
	 * @param kafkaCluster
	 * @return
	 */
	public static Producer<String, String> GetKafkaProducuer(String kafkaCluster) {
		Properties props = new Properties();
//		props.put("broker.list", kafkaCluster);
//		props.put("request.required.acks", "1");
//		props.put("producer.type", "sync");
//		props.put("serializer.class", "kafka.serializer.StringEncoder");
//		props.put("compression.codec", "snappy");

		props.put("bootstrap.servers", kafkaCluster);
		props.put("acks", "1");
//		props.put("producer.type", "sync");
		props.put("retries", 0);
		props.put("batch.size", 16384);
		props.put("linger.ms", 0);
		props.put("buffer.memory", 33554432);
		props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
		props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
//		props.put("compression.type", "snappy");

		org.apache.kafka.clients.producer.Producer<String, String> kafkaProducer = new KafkaProducer<>(props);
		L.info("init kafka producer with url [{}] done", kafkaCluster);
		return kafkaProducer;
	}
}
