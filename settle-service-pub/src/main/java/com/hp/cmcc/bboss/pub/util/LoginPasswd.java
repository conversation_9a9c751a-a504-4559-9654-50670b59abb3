package com.hp.cmcc.bboss.pub.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

public class LoginPasswd {
	private static Logger L = LoggerFactory.getLogger(LoginPasswd.class);

	public static final int _LOGIN_STR_TYPE_PLAIN = 0;
	public static final int _LOGIN_STR_TYPE_CRYPT = 1;
	public static final int _LOGIN_STR_TYPE_REDIS = 2;
	public static final int _LOGIN_STR_TYPE_BBOSS = 3;
	public static final int _LOGIN_STR_TYPE_REDISCLUSTER = 4;
	public static final String[] _LoginStrType = { "plain", "crypt", "redis", "bboss" ,"rediscluster"};

	private static final byte[] _PASS_SEED_AES = "java.lang.System".getBytes();
	private static final byte[] _PASS_SALT_AES = "FEDCBA9876543210".getBytes();
	private static final char[] _PASS_SEED_PBE = "java.lang.System".toCharArray();
	private static final byte[] _PASS_SALT_PBE = { (byte) 0xfe, (byte) 0xdc, (byte) 0xba, (byte) 0x98, (byte) 0x76, (byte) 0x54,
			(byte) 0x32, (byte) 0x10, };
	private static final byte[] _PASS_BBOSS_KEY = { '`', 'A', 'd', '0', '&', '8', '_', 'j', '[', '}', '1' }; //"`Ad0&8_j[}1"

	public static String PasswdEncryptAES(String plain_str) { // AES128, has C libmcrypt counterpart
		try {
			Cipher aes_cipher_ = Cipher.getInstance("AES/CBC/NoPadding", "SunJCE");
			SecretKeySpec key_spec_ = new SecretKeySpec(_PASS_SALT_AES, "AES");
			aes_cipher_.init(Cipher.ENCRYPT_MODE, key_spec_, new IvParameterSpec(_PASS_SEED_AES));
			byte rand_byte_ = (byte) ThreadLocalRandom.current().nextInt(255);
			++rand_byte_; // prevent rand_byte_ equals 0
			byte[] raw_bytes_ = plain_str.getBytes();
			byte[] padding_ = null;
			int mod_ = (1 + raw_bytes_.length) % 16;
			if (mod_ == 0) {
				padding_ = new byte[1 + raw_bytes_.length];
				padding_[0] = rand_byte_;
				System.arraycopy(raw_bytes_, 0, padding_, 1, raw_bytes_.length);
			} else {
				padding_ = new byte[1 + raw_bytes_.length + (16 - mod_)];
				padding_[0] = rand_byte_;
				System.arraycopy(raw_bytes_, 0, padding_, 1, raw_bytes_.length);
			}
			byte[] cipher_bytes_ = aes_cipher_.doFinal(padding_);
			return Base64.encodeBytes(cipher_bytes_);
		} catch (Exception e) {
			L.warn("encrypt [{}] exception, return null", plain_str, e);
			return null;
		}
	}

	public static String PasswdDecryptAES(String cipher_base64_str) { // AES128, has C libmcrypt counterpart
		try {
			Cipher aes_cipher_ = Cipher.getInstance("AES/CBC/NoPadding", "SunJCE");
			SecretKeySpec key_spec_ = new SecretKeySpec(_PASS_SALT_AES, "AES");
			aes_cipher_.init(Cipher.DECRYPT_MODE, key_spec_, new IvParameterSpec(_PASS_SEED_AES));
			byte[] cipher_bytes_ = Base64.decode(cipher_base64_str);
			byte[] padding_ = aes_cipher_.doFinal(cipher_bytes_);
			int pos_ = 1;
			for (pos_ = 1; pos_ < padding_.length; pos_++) {
				if (padding_[pos_] == 0x00)
					break;
			}
			byte[] raw_bytes_ = new byte[pos_ - 1];
			System.arraycopy(padding_, 1, raw_bytes_, 0, pos_ - 1);
			return new String(raw_bytes_);
		} catch (Exception e) {
			L.warn("decrypt [{}] exception, return null", cipher_base64_str, e);
			return null;
		}
	}

	public static String PasswdEncryptPBE(String plain_str) {
		try {
			SecretKeyFactory key_factory_ = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
			SecretKey key_ = key_factory_.generateSecret(new PBEKeySpec(_PASS_SEED_PBE));
			Cipher pbe_cipher_ = Cipher.getInstance("PBEWithMD5AndDES");
			pbe_cipher_.init(Cipher.ENCRYPT_MODE, key_, new PBEParameterSpec(_PASS_SALT_PBE, 20));
			byte[] cipher_bytes_ = pbe_cipher_.doFinal(plain_str.getBytes());
			return Base64.encodeBytes(cipher_bytes_);
		} catch (Exception e) {
			L.warn("encrypt [{}] exception, return null", plain_str, e);
			return null;
		}
	}

	public static String PasswdDecryptPBE(String cipher_base64_str) {
		try {
			SecretKeyFactory key_factory_ = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
			SecretKey key = key_factory_.generateSecret(new PBEKeySpec(_PASS_SEED_PBE));
			Cipher pbe_cipher_ = Cipher.getInstance("PBEWithMD5AndDES");
			pbe_cipher_.init(Cipher.DECRYPT_MODE, key, new PBEParameterSpec(_PASS_SALT_PBE, 20));
			byte[] cipher_bytes_ = Base64.decode(cipher_base64_str);
			return new String(pbe_cipher_.doFinal(cipher_bytes_));
		} catch (Exception e) {
			L.warn("decrypt [{}] exception, return null", cipher_base64_str, e);
			return null;
		}
	}

	public static String PasswdDecryptBBOSS(String cipher_bboss_str) { // migrated from BBOSS dbpasswd.cc
		byte[] l_sCipher = cipher_bboss_str.getBytes();
		byte[] l_szPass = new byte[13];
		byte[] l_cPassHign = new byte[12];
		byte[] l_cPassLow = new byte[12];
		byte l_ch;
		int i;
		int ki;

		if (l_sCipher.length != 12) {
			L.warn("cipher_bboss_str.getBytes({}) length {} ne 12", cipher_bboss_str, l_sCipher.length);
			return "";
		}

		for (i = 0; i < 12; ++i) {
			if (l_sCipher[i] == '&') {
				l_sCipher[i] = ':';
			}

			if (l_sCipher[i] == '+') {
				l_sCipher[i] = 127;
			}

			l_cPassHign[i] = (byte) (l_sCipher[i] >> 4);
			l_cPassLow[i] = (byte) (l_sCipher[i] & 0x0f);
		}

		for (i = 0, ki = 0; i < 12; ++i) { // swap 0 <-> 11, 1 <-> 10, ...
			l_cPassHign[i] = (byte) ((l_cPassHign[i] - 3 + 5 - (_PASS_BBOSS_KEY[ki] % 5)) % 5 + 3);
			l_cPassLow[i] = (byte) (l_cPassLow[i] ^ (_PASS_BBOSS_KEY[ki] & 0x0f));
			ki = (ki == (_PASS_BBOSS_KEY.length - 1)) ? 0 : ki + 1;
		}

		for (i = 0; i < 6; ++i) { // swap 0 <-> 6, 1 <-> 7, ...
			l_ch = l_cPassLow[i];
			l_cPassLow[i] = l_cPassLow[11 - i];
			l_cPassLow[11 - i] = l_ch;
			l_ch = l_cPassHign[i];
			l_cPassHign[i] = l_cPassHign[6 + i];
			l_cPassHign[6 + i] = l_ch;
		}

		for (i = 0; i < 12; ++i) {
			l_szPass[i] = (byte) ((l_cPassHign[i] << 4) | l_cPassLow[i]);

			if (l_szPass[i] == '~') {
				l_szPass[i] = 0;
				break;
			}
		}

		byte[] result_ = new byte[20];
		for (i = 0; i < l_szPass.length; ++i) {
			if (l_szPass[i] == '\0') {
				return new String(result_, 0, i);
			}
			result_[i] = l_szPass[i];
		}
		return null;
	}

	public static String ParseBbossLogin(String passwd_file, String db_user, String db_sid, String db_host, int db_port) {
		String passwd_file_path_ = PubMethod.SubStrByEnv(passwd_file);
		if (passwd_file_path_ == null) {
			L.warn("passwd file [{}] SubStrByEnv failed", passwd_file);
			return null;
		}
		String result_ = null;
		BufferedReader br_ = null;
		try {
			br_ = new BufferedReader(new FileReader(passwd_file_path_));
			int line_cnt_ = 0;
			String cfg_line_;
			while ((cfg_line_ = br_.readLine()) != null) {
				++line_cnt_;
				if (cfg_line_.length() == 0 || cfg_line_.startsWith("#"))
					continue;
				String[] ucs_ = cfg_line_.trim().split(":"); // u:user, c:cipher, s:sid
				if (ucs_.length != 3) {
					L.warn("[{}] line {} [{}] has {} fields while expecting 3, skip", passwd_file_path_, line_cnt_, cfg_line_,
							ucs_.length);
					continue;
				}
				if (ucs_[0].equals(db_user) && ucs_[2].equals(db_sid)) {
					L.trace("[{}] line {} [{}] matches user [{}] and sid [{}]", passwd_file_path_, line_cnt_, cfg_line_, db_user,
							db_sid);
					String plain_ = PasswdDecryptBBOSS(ucs_[1]);
					if (plain_ == null) {
						L.warn("call PasswdDecryptBBOSS({}) returns null", ucs_[1]);
					} else {
						result_ = PubMethod.FmtArgs("%s/%s@%s:%s:%d", db_user, plain_, db_sid, db_host, db_port);
					}
					break;
				}
			}
		} catch (Exception e) {
			L.warn("access {} exception", passwd_file_path_, e);
		} finally {
			PubMethod.Close(br_);
		}
		if (result_ == null) {
			L.warn("passwd file [{}] no records match [{}:{}:*] or decrypt failed", passwd_file_path_, db_user, db_sid);
		}
		return result_;
	}

	public static String DecryptLogin(String key, String instance) {
		String env_ = System.getenv("DB_LOGIN_FILE");
		if (PubMethod.IsEmpty(env_)) {
			L.warn("env $DB_LOGIN_FILE not set");
			return null;
		}
		File db_login_file_ = new File(env_);
		if (!db_login_file_.isFile()) {
			L.warn("$DB_LOGIN_FILE [{}] do not exists", env_);
			return null;
		}
		BufferedReader br_ = null;
		try {
			br_ = new BufferedReader(new FileReader(db_login_file_));
			return _ParseLogin(key, instance, br_);
		} catch (Exception e) {
			L.warn("parse $DB_LOGIN_FILE [{}] exception", env_, e);
			return null;
		} finally {
			PubMethod.Close(br_);
		}
	}

	private static String _ParseLogin(String key, String instance, BufferedReader br) throws IOException {
		int line_num_ = 0;
		String[] kitv_; // key, instance, type, value
		String raw_line_;
		while ((raw_line_ = br.readLine()) != null) {
			++line_num_;
			if (raw_line_.startsWith("#"))
				continue;
			if (raw_line_.matches("^\\s*$"))
				continue;
			raw_line_ = raw_line_.trim();
			kitv_ = raw_line_.split("\\s+", 4);
			if (kitv_.length != 4) {
				L.trace("line {} [{}] insufficient fields, skip it", line_num_, raw_line_);
				continue;
			}
			if (!kitv_[0].equals(key)) {
				//L.trace("line {} [{}] key [{}] not match [{}]", new Object[] { line_num_, raw_line_, kitv_[0], key });
				continue;
			}
			if (!kitv_[1].equals(instance)) {
				//L.trace("line {} [{}] instance [{}] not match [{}]", new Object[] { line_num_, raw_line_, kitv_[1], instance });
				continue;
			}
			L.trace("line {} [{}] matches [{},{}]", new Object[] { line_num_, raw_line_, key, instance });
			int login_str_type_ = -1;
			for (int i = 0; i < _LoginStrType.length; i++) {
				if (_LoginStrType[i].equals(kitv_[2])) {
					login_str_type_ = i;
					break;
				}
			}
			if (login_str_type_ < 0) {
				L.warn("line {} [{}], invalid type [{}], should in ({}), skip it", new Object[] { line_num_, raw_line_, kitv_[2],
						PubMethod.Collection2Str(Arrays.asList(_LoginStrType), ",") });
				continue;
			}
			switch (login_str_type_) {
			case _LOGIN_STR_TYPE_REDIS:
				return _ParseLoginViaRedis(line_num_, raw_line_, key, instance, kitv_[3]);
			case _LOGIN_STR_TYPE_CRYPT:
				return PasswdDecryptAES(kitv_[3]);
			case _LOGIN_STR_TYPE_BBOSS:
				return _ParseLoginViaBboss(line_num_, raw_line_, key, instance, kitv_[3]);
			case _LOGIN_STR_TYPE_REDISCLUSTER:
				return _ParseLoginViaRedisCluster(line_num_, raw_line_, key, instance, kitv_[3]);
			case _LOGIN_STR_TYPE_PLAIN:
			default:
				return kitv_[3];
			}
		}
		L.warn("key [{}] instance [{}] not exists in $DB_LOGIN_FILE [{}]", key, instance, System.getenv("DB_LOGIN_FILE"));
		return null;
	}



	private static String _ParseLoginViaBboss(int line_num, String raw_line, String key, String instance, String bboss_fusip) {
		String[] fusip_ = bboss_fusip.split(":", 5); // f:file, u:user, s:sid, i:ip, p:port
		if (fusip_.length < 3) {
			L.warn("line {} [{}], value has {} fields while expecting 3-5", line_num, raw_line, fusip_.length);
			return null;
		}
		String passwd_file_path_ = PubMethod.SubStrByEnv(fusip_[0]);
		if (passwd_file_path_ == null) {
			L.warn("line {} [{}], passwd file [{}] SubStrByEnv failed", line_num, raw_line, fusip_[0]);
			return null;
		}
		String host_ = null;
		if (fusip_.length < 4 || fusip_[3].length() == 0) {
			host_ = "127.0.0.1";
		} else {
			host_ = fusip_[3];
		}
		String port_ = null;
		if (fusip_.length < 5 || fusip_[4].length() == 0) {
			port_ = "1521";
		} else {
			port_ = fusip_[4];
		}
		String result_ = null;
		BufferedReader br_ = null;
		try {
			br_ = new BufferedReader(new FileReader(passwd_file_path_));
			int line_cnt_ = 0;
			String cfg_line_;
			while ((cfg_line_ = br_.readLine()) != null) {
				++line_cnt_;
				if (cfg_line_.length() == 0 || cfg_line_.startsWith("#"))
					continue;
				String[] ucs_ = cfg_line_.trim().split(":"); // u:user, c:cipher, s:sid
				if (ucs_.length != 3) {
					L.warn("[{}] line {} [{}] has {} fields while expecting 3, skip", passwd_file_path_, line_cnt_, cfg_line_,
							ucs_.length);
					continue;
				}
				if (ucs_[0].equals(fusip_[1]) && ucs_[2].equals(fusip_[2])) {
					L.trace("[{}] line {} [{}] matches user [{}] and sid [{}]", passwd_file_path_, line_cnt_, cfg_line_, fusip_[1],
							fusip_[2]);
					String plain_ = PasswdDecryptBBOSS(ucs_[1]);
					result_ = PubMethod.FmtArgs("%s/%s@%s:%s:%s", fusip_[1], plain_, fusip_[2], host_, port_);
					break;
				}
			}
		} catch (Exception e) {
			L.warn("access {} exception", passwd_file_path_, e);
		} finally {
			PubMethod.Close(br_);
		}
		if (result_ == null)
			L.warn("line {} [{}], passwd file [{}] no records match", line_num, raw_line, passwd_file_path_);
		return result_;
	}
	private static String _ParseLoginViaRedisCluster(int line_num, String raw_line, String key, String instance, String redis_hpa) {
		String[] hpa_ = redis_hpa.split("(?<!\\\\):", 3); // host, port, auth
		if (hpa_.length != 3) {
			L.info("line {} [{}], value has {} fields while expecting 3, try Base64/AES decoding", line_num, raw_line, hpa_.length);
			String plain_hpa_ = PasswdDecryptAES(redis_hpa);
			if (plain_hpa_ == null) {
				L.warn("line {} [{}], Base64/AES decoding failed", line_num, raw_line);
				return null;
			}
			hpa_ = plain_hpa_.split("(?<!\\\\):", 3);
			if (hpa_.length != 3) {
				L.warn("line {} [{}], plain value [{}] has {} fields while expecting 3", line_num, raw_line, plain_hpa_,
						hpa_.length);
				return null;
			}
		}
		for (int i = 0; i < hpa_.length; ++ i) {
			hpa_[i] = hpa_[i].replaceAll("\\\\:", ":");
		}
		JedisCluster jedisCluster = null;
		try {
			JedisPoolConfig _poolCfg = new JedisPoolConfig();
			_poolCfg.setMaxTotal(5);
			_poolCfg.setMaxIdle(5);
			_poolCfg.setMinIdle(5);
			_poolCfg.setTestOnReturn(false);
			_poolCfg.setTestOnBorrow(false);
			Set<HostAndPort> redisCluster = new HashSet<>();
			redisCluster.add(new HostAndPort(hpa_[0], Integer.parseInt(hpa_[1])));
			if (!PubMethod.IsEmpty(hpa_[2])) {
				jedisCluster = new JedisCluster(redisCluster, 6000, 6000, 5, hpa_[2],"db_login",_poolCfg);
				L.debug("redis {}:{} auth OK", hpa_[0], hpa_[1]);
			} else {
				jedisCluster = new JedisCluster(redisCluster, 6000, 6000, 5,_poolCfg);
			}
			String redis_key_ = String.format("login:%s:%s:crypt", key, instance);
			String redis_val_ = jedisCluster.get(redis_key_);
			if (!PubMethod.IsBlank(redis_val_)) {
				L.trace("[{}] got from redis", redis_key_);
				return PasswdDecryptAES(redis_val_);
			} else {
				L.info("line {} [{}], no correspond val for [{}] in redis, try plain", line_num, raw_line, redis_key_);
				redis_key_ = String.format("login:%s:%s:plain", key, instance);
				redis_val_ = jedisCluster.get(redis_key_);
				if (PubMethod.IsBlank(redis_val_)) {
					L.warn("line {} [{}], no correspond val for [{}] in redis", line_num, raw_line, redis_key_);
					return null;
				}
				L.trace("[{}] got from redis", redis_key_);
				return redis_val_;
			}
		} finally {
			if (jedisCluster != null)
				jedisCluster.close();
		}
	}
	private static String _ParseLoginViaRedis(int line_num, String raw_line, String key, String instance, String redis_hpa) {
		String[] hpa_ = redis_hpa.split("(?<!\\\\):", 3); // host, port, auth
		if (hpa_.length != 3) {
			L.info("line {} [{}], value has {} fields while expecting 3, try Base64/AES decoding", line_num, raw_line, hpa_.length);
			String plain_hpa_ = PasswdDecryptAES(redis_hpa);
			if (plain_hpa_ == null) {
				L.warn("line {} [{}], Base64/AES decoding failed", line_num, raw_line);
				return null;
			}
			hpa_ = plain_hpa_.split("(?<!\\\\):", 3);
			if (hpa_.length != 3) {
				L.warn("line {} [{}], plain value [{}] has {} fields while expecting 3", line_num, raw_line, plain_hpa_,
						hpa_.length);
				return null;
			}
		}
		for (int i = 0; i < hpa_.length; ++ i) {
			hpa_[i] = hpa_[i].replaceAll("\\\\:", ":");
		}
		Jedis jedis_ = null;
		JedisSentinelPool _pool = null;
		try {
			Set<String> sentinels = new HashSet<>();
			sentinels.add(new HostAndPort(hpa_[0], Integer.parseInt(hpa_[1])).toString());
			if (!PubMethod.IsEmpty(hpa_[2])) {
				_pool = new JedisSentinelPool("mymaster", sentinels, hpa_[2]);
				L.debug("redis {}:{} auth OK", hpa_[0], hpa_[1]);
			} else {
				_pool = new JedisSentinelPool("mymaster", sentinels);
			}
			jedis_ = _pool.getResource();
			String redis_key_ = String.format("login:%s:%s:crypt", key, instance);
			String redis_val_ = jedis_.get(redis_key_);
			if (!PubMethod.IsBlank(redis_val_)) {
				L.trace("[{}] got from redis", redis_key_);
				return PasswdDecryptAES(redis_val_);
			} else {
				L.info("line {} [{}], no correspond val for [{}] in redis, try plain", line_num, raw_line, redis_key_);
				redis_key_ = String.format("login:%s:%s:plain", key, instance);
				redis_val_ = jedis_.get(redis_key_);
				if (PubMethod.IsBlank(redis_val_)) {
					L.warn("line {} [{}], no correspond val for [{}] in redis", line_num, raw_line, redis_key_);
					return null;
				}
				L.trace("[{}] got from redis", redis_key_);
				return redis_val_;
			}
		} finally {
			if (jedis_ != null)
				jedis_.close();
		}
	}
}
