package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbStlEspParameterRec extends GsonObj {

	private Long id;
	private String offer_code;
	private String product_code;
	private String order_mode;
	private Long rule_id;
	private Long rate_id;
	private String charge_item;
	private Integer calc_priority;
	private String object_value;
	private Integer sett_type;
	private String rate_value;
	private String tax_rate;
	private String dest_source;
	private String route_flag;
	private Timestamp eff_date;
	private Timestamp exp_date;
	private String acct_month;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getOffer_code() {
		return offer_code;
	}

	public void setOffer_code(String offer_code) {
		this.offer_code = offer_code;
	}

	public String getProduct_code() {
		return product_code;
	}

	public void setProduct_code(String product_code) {
		this.product_code = product_code;
	}

	public String getOrder_mode() {
		return order_mode;
	}

	public void setOrder_mode(String order_mode) {
		this.order_mode = order_mode;
	}

	public Long getRule_id() {
		return rule_id;
	}

	public void setRule_id(Long rule_id) {
		this.rule_id = rule_id;
	}

	public Long getRate_id() {
		return rate_id;
	}

	public void setRate_id(Long rate_id) {
		this.rate_id = rate_id;
	}

	public String getCharge_item() {
		return charge_item;
	}

	public void setCharge_item(String charge_item) {
		this.charge_item = charge_item;
	}

	public Integer getCalc_priority() {
		return calc_priority;
	}

	public void setCalc_priority(Integer calc_priority) {
		this.calc_priority = calc_priority;
	}

	public String getObject_value() {
		return object_value;
	}

	public void setObject_value(String object_value) {
		this.object_value = object_value;
	}

	public Integer getSett_type() {
		return sett_type;
	}

	public void setSett_type(Integer sett_type) {
		this.sett_type = sett_type;
	}

	public String getRate_value() {
		return rate_value;
	}

	public void setRate_value(String rate_value) {
		this.rate_value = rate_value;
	}

	public String getTax_rate() {
		return tax_rate;
	}

	public void setTax_rate(String tax_rate) {
		this.tax_rate = tax_rate;
	}

	public String getDest_source() {
		return dest_source;
	}

	public void setDest_source(String dest_source) {
		this.dest_source = dest_source;
	}

	public String getRoute_flag() {
		return route_flag;
	}

	public void setRoute_flag(String route_flag) {
		this.route_flag = route_flag;
	}

	public Timestamp getEff_date() {
		return eff_date;
	}

	public void setEff_date(Timestamp eff_date) {
		this.eff_date = eff_date;
	}

	public Timestamp getExp_date() {
		return exp_date;
	}

	public void setExp_date(Timestamp exp_date) {
		this.exp_date = exp_date;
	}

	public String getAcct_month() {
		return acct_month;
	}

	public void setAcct_month(String acct_month) {
		this.acct_month = acct_month;
	}

}
