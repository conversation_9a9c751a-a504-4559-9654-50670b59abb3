package com.hp.cmcc.bboss.pub.odb;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

public class OdbSystemParam {
	private static Logger L = LoggerFactory.getLogger(OdbSystemParam.class);
	public static final int SUB_SYSTEM_TYPE_1_UIDD = 1; //详单子系统
	public static final int SUB_SYSTEM_TYPE_2_SIDD = 2; //结算单子系统

	public int _subSystemType; //1:详单子系统;2:结算单子系统
	public String _shpmsvInstNm; //shpmsv的实例名,查shpm_ver表要用到
	public String _gzipPath; //压缩工具路径(如果主机CPU够快,可以配置成bzip2)

	public String _redisShparmCluster; //前主后备,逗号分隔记录,冒号分隔主机和端口
	public String _redisShparmAuth; //公参Redis登录口令,”NULL”空口令
	public int _redisShparmTimeout; //公参Redis访问超时秒数

	public String _redisBizlogCluster;//前主后备,逗号分隔记录,冒号分隔主机和端口
	public String _redisBizlogAuth; //日志Redis登录口令,”NULL”空口令
	public int _redisBizlogTimeout; //日志Redis访问超时秒数

	public String _redisDupchkCluster;
	public String _redisDupchkAuth; //查重Redis登录口令,”NULL”空口令
	public int _redisDupchkTimeout; //查重Redis访问超时秒数
	public int _redisDupchkHoursDirect; //内存查重范围(小时)
	public int _redisDupchkHoursHybrid; //内存及DB混合查重范围(小时)

	public String _kafkaCluster; //逗号分隔记录,冒号分隔主机和端口
	public String _kafkaGroup; //消费队列状态信息缓存在ZooKeeper的/consumers/<KAFKA_GROUP>/<队列名>
	public String _zookeeperCluster; //逗号分隔记录,冒号分隔主机和端口

	public String redisMode = "sentinel";


	// private static final String _IpPfx3 = "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}";
	// private static final String _IpSfx1 = "([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$";
	// private static final String _IpAddr = _IpPfx3 + _IpSfx1;
	private static final String _ClusterPattern = "^([^:]+:[0-9]+)(,[^:]+:[0-9]+)*";
	private static OdbSystemParam _Instance;

	static {
		_Instance = new OdbSystemParam();
	}

	public static OdbSystemParam GetInstance() {
		return _Instance;
	}

	public String toString() {
		StringBuilder builder_ = new StringBuilder();
		builder_.append(
				String.format("sub_system_type %d, shpmsv_inst_nm [%s], gzip_path [%s]", _subSystemType, _shpmsvInstNm, _gzipPath));
		builder_.append(String.format("cluster:auth:timeout, shparm [%s:%s:%d], ", _redisShparmCluster, _redisShparmAuth,
				_redisShparmTimeout));
		builder_.append(String.format("bizlog [%s:%s:%d], ", _redisBizlogCluster, _redisBizlogAuth, _redisBizlogTimeout));
		builder_.append(String.format("dupchk [%s:%s:%d], hours direct:hybird [%d:%d], ", _redisDupchkCluster, _redisDupchkAuth,
				_redisDupchkTimeout, _redisDupchkHoursDirect, _redisDupchkHoursHybrid));
		builder_.append(String.format("kafka cluster,group [%s, %s], zookeeper cluster [%s]", _kafkaCluster, _kafkaGroup,
				_zookeeperCluster));
		return builder_.substring(0);
	}

	public boolean refresh() {
		OdbAppParam app_param_ = new OdbAppParam();
		OdbCli cli_ = OdbAgt.GetBizInstance();
		String module_ = cli_.getLoginKey();
		String inst_nm_ = "system";
		if (!app_param_.refresh(module_, inst_nm_)) {
			L.warn("refresh({},{}) error", module_, inst_nm_);
			return false;
		}
		if (!app_param_.subValByEnv()) {
			L.warn("[{},{}] subValByEnv error", module_, inst_nm_);
			return false;
		}
		String section_ = "COMMON";
		boolean rc_ = true;

		Long lval_ = app_param_.chkValNum(section_, "SUB_SYSTEM_TYPE", 1L, 2L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_subSystemType = lval_.intValue();
		}

		String val_ = app_param_.chkValStr(section_, "SHPMSV_INST_NM", null);
		if (val_ == null) {
			rc_ = false;
		} else {
			_shpmsvInstNm = val_;
		}

		File fval_ = app_param_.chkValFile(section_, "GZIP_PATH", true, false, true);
		if (fval_ == null) {
			rc_ = false;
		} else {
			_gzipPath = fval_.getAbsolutePath();
		}

		val_ = app_param_.chkValStr(section_, "REDIS_SHPARM_CLUSTER", _ClusterPattern);
		if (val_ == null) {
			rc_ = false;
		} else {
			_redisShparmCluster = val_;
		}

		val_ = app_param_.chkValStr(section_, "REDIS_SHPARM_AUTH", null);
		if (val_ == null) {
			rc_ = false;
		} else {
			_redisShparmAuth = val_;
		}

		lval_ = app_param_.chkValNum(section_, "REDIS_SHPARM_TIMEOUT", 1L, 3600L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_redisShparmTimeout = lval_.intValue();
		}

		val_ = app_param_.chkValStr(section_, "REDIS_BIZLOG_CLUSTER", _ClusterPattern);
		if (val_ == null) {
			rc_ = false;
		} else {
			_redisBizlogCluster = val_;
		}

		val_ = app_param_.chkValStr(section_, "REDIS_BIZLOG_AUTH", null);
		if (val_ == null) {
			rc_ = false;
		} else {
			_redisBizlogAuth = val_;
		}

		lval_ = app_param_.chkValNum(section_, "REDIS_BIZLOG_TIMEOUT", 1L, 3600L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_redisBizlogTimeout = lval_.intValue();
		}

		val_ = app_param_.chkValStr(section_, "REDIS_DUPCHK_CLUSTER", _ClusterPattern);
		if (val_ == null) {
			rc_ = false;
		} else {
			_redisDupchkCluster = val_;
		}

		val_ = app_param_.chkValStr(section_, "REDIS_DUPCHK_AUTH", null);
		if (val_ == null) {
			rc_ = false;
		} else {
			_redisDupchkAuth = val_;
		}

		lval_ = app_param_.chkValNum(section_, "REDIS_DUPCHK_TIMEOUT", 1L, 3600L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_redisDupchkTimeout = lval_.intValue();
		}

		lval_ = app_param_.chkValNum(section_, "REDIS_DUPCHK_HOURS_DIRECT", 0L, 2400L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_redisDupchkHoursDirect = lval_.intValue();
		}

		lval_ = app_param_.chkValNum(section_, "REDIS_DUPCHK_HOURS_HYBRID", 0L, 4800L);
		if (lval_ == null) {
			rc_ = false;
		} else {
			_redisDupchkHoursHybrid = lval_.intValue();
		}

		if (rc_ && _redisDupchkHoursHybrid <= _redisDupchkHoursDirect) {
			L.warn("_redisDupchkHours Direct<=Hybrid but {}>{}", _redisDupchkHoursDirect, _redisDupchkHoursHybrid);
			rc_ = false;
		}

		val_ = app_param_.chkValStr(section_, "KAFKA_CLUSTER", _ClusterPattern);
		if (val_ == null) {
			rc_ = false;
		} else {
			_kafkaCluster = val_;
		}

		val_ = app_param_.chkValStr(section_, "KAFKA_GROUP", null);
		if (val_ == null) {
			rc_ = false;
		} else {
			_kafkaGroup = val_;
		}

		val_ = app_param_.chkValStr(section_, "ZOOKEEPER_CLUSTER", _ClusterPattern);
		if (val_ == null) {
			rc_ = false;
		} else {
			_zookeeperCluster = val_;
		}

		val_ = app_param_.chkValStr(section_, "REDIS_MODE", null);
		if (StrUtil.isNotBlank(val_)) {
			redisMode = val_;
		}

		if (!rc_) {
			L.warn("refresh sttl system app_param error, pls chk");
		}
		return rc_;
	}

	private OdbSystemParam() {
		// do nothing explicitly
	}

	public boolean isRedisCluster() {
        return "cluster".equals(redisMode);
    }
}
