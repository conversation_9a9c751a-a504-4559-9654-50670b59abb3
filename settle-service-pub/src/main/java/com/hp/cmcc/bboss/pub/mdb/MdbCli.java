package com.hp.cmcc.bboss.pub.mdb;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import redis.clients.jedis.*;

import java.util.HashSet;
import java.util.Set;

public class MdbCli {
	private static Logger L = LoggerFactory.getLogger(MdbCli.class);
	private String _name;
	private String _ip;
	private int _port;
	//private String _ipSlave;
	//private int _portSlave;
	private String _auth;
	private String masterName;
	private int _timeout;
	private int _poolMax;
	private JedisSentinelPool _pool;
	//private JedisPool _poolSlave;
	private JedisPoolConfig _poolCfg;

	public void returnResource(JedisSentinelPool pool, Jedis jedis) {
		if (jedis == null) {
			return;
		}
		try {
			jedis.close();
		} catch (Exception e) {
			L.warn("returnResource exception, ignore", e);
		}
	}

	public void returnBrokenResource(Transaction tx, JedisSentinelPool pool, Jedis jedis) {
		if (tx != null) {
			try {
				tx.discard();
			} catch (Exception e) {
				L.warn("transaction discard exception, ignore", e);
			}
		}
		if (jedis == null) {
			return;
		}
		try {
			jedis.close();
		} catch (Exception e) {
			L.warn("returnBrokenResource exception, ignore", e);
		}
	}

	public MdbCli(String name, String cluster, String auth, int timeout, int pool_max) {
		_name = name;
		String[] master_slave_ = cluster.split(",");
		String[] ip_port_ = master_slave_[0].split(":");
		_ip = ip_port_[0];
		_port = Integer.parseInt(ip_port_[1]);
		if (master_slave_.length > 1) {
			ip_port_ = master_slave_[1].split(":");
			//_ipSlave = ip_port_[0];
			//_portSlave = Integer.parseInt(ip_port_[1]);
		}
		_auth = auth;
		_timeout = timeout * 1000;
		_poolMax = pool_max;
		_poolCfg = new JedisPoolConfig();
		_poolCfg.setMaxTotal(pool_max);
		_poolCfg.setMaxIdle(pool_max);
		_poolCfg.setMinIdle(pool_max);
		_poolCfg.setTestOnReturn(false);
		_poolCfg.setTestOnBorrow(false);
	}

	public JedisSentinelPool getPool() {
		return _pool;
	}

	public boolean init() {
		if (_poolMax <= 0) {
			L.info("_poolMax of {} is {}, no real init", _name, _poolMax);
			return true;
		}
		if (_pool != null) {
			L.warn("_pool is not null, re-init");
		}
		L.debug("try init jedis connection [{}] [{}:{}]", _name, _ip, _port);
//		_pool = new JedisPool(_poolCfg, _ip, _port, _timeout, _auth.equalsIgnoreCase("NULL") ? null : _auth);
		Set<String> sentinels = new HashSet<>();
		sentinels.add(new HostAndPort(_ip,_port).toString());
		_pool = new JedisSentinelPool("mymaster", sentinels, _poolCfg, _timeout, _auth);
		boolean rc_ = true;
		Jedis jedis_ = _pool.getResource();
		try {
			String pong_ = jedis_.ping();
			if (pong_.equalsIgnoreCase("PONG")) {
				L.info("ping {} [{}:{}] ok", new Object[] { _name, _ip, _port });
			} else {
				rc_ = false;
				L.warn("ping {} [{}:{}] returns [{}]", new Object[] { _name, _ip, _port, pong_ });
			}
		} catch (Exception e) {
			rc_ = false;
			L.warn("jedis encounter exception", e);
		} finally {
			if (jedis_ != null) {
				jedis_.close();
			}
		}
		return rc_;
	}
}
