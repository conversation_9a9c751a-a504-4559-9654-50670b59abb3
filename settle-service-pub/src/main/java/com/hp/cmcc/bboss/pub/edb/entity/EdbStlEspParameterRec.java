package com.hp.cmcc.bboss.pub.edb.entity;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbStlEspParameterRec extends GsonObj implements Effectivable{

	public static final int SETT_TYPE_1_STANDARD_PRICE = 1; //标准价
	public static final int SETT_TYPE_2_PRICE = 2; //售价
	public static final int SETT_TYPE_3_FIXED_VALUE = 3; //固定值1
	
	public Long _id;
	public String _offerCode;
	public String _productCode;
	public String _orderMode;
	public Long _ruleId;
	public Long _rateId;
	public String _chargeItem;
	public Integer _calcPriority;
	public String _objectValue;
	public Integer _settType;
	public String _rateValue;
	public String _taxRate;
	public String _destSource;
	public String _routeFlag;
	public String _effDate;
	public String _expDate;

	@Override
	public boolean isEffective(String tm14) {
		if (tm14 == null || tm14.compareTo(_effDate) < 0 || tm14.compareTo(_expDate) > 0) {
			return false;
		} else {
			return true;
		}
	}

}
