package com.hp.cmcc.bboss.pub.util;

import java.io.File;
import java.io.FileFilter;
import java.util.regex.Pattern;

public class PatternFileFilter implements FileFilter {
	public enum TypeFilter {
		TypeFile, TypeDirectory, TypeIgnore
	}

	private Pattern _patternFilter;
	private TypeFilter _typeFilter;

	public PatternFileFilter(Pattern pattern_filter, TypeFilter type_filter) {
		_patternFilter = pattern_filter;
		_typeFilter = type_filter;
	}

	public boolean accept(File f) {
		switch (_typeFilter) {
		case TypeFile:
			if (!f.isFile()) {
				return false;
			}
			break;
		case TypeDirectory:
			if (!f.isDirectory()) {
				return false;
			}
			break;
		default:
			break;
		}
		return _patternFilter.matcher(f.getName()).find();
	}
}
