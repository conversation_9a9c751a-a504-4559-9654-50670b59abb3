package com.hp.cmcc.bboss.pub.mdb;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;
import redis.clients.jedis.util.JedisClusterCRC16;

import java.util.HashSet;
import java.util.Set;

public class MdbCli2 {
	private static Logger L = LoggerFactory.getLogger(MdbCli2.class);
	private String _name;
	private String _auth;
	private int _timeout;
	private int _poolMax;
	private JedisPoolConfig _poolCfg;
	private JedisCluster jedisCluster;

	private Set<HostAndPort> redisCluster = new HashSet<>();
	public void returnResource( Jedis jedis) {
		if (jedis != null) {
			jedis.close();
		}
	}

	public void returnBrokenResource(Transaction tx, JedisSentinelPool pool, Jedis jedis) {
		returnBrokenResource(tx);
	}
	public void returnBrokenResource(Transaction tx) {
		if (tx != null) {
			try {
				tx.discard();
			} catch (Exception e) {
				L.warn("transaction discard exception, ignore", e);
			}
		}
	}

	public MdbCli2(String name, String cluster, String auth, int timeout, int pool_max) {
		_name = name;
		String[] clusterNodes = cluster.split(",");
		for (String clusterNode : clusterNodes) {
			String[] ip_port_ = clusterNode.split(":");
			redisCluster.add(new HostAndPort(ip_port_[0], Integer.parseInt(ip_port_[1])));
		}

		_auth = auth;
		_timeout = timeout * 1000;
		_poolMax = pool_max;
		_poolCfg = new JedisPoolConfig();
		_poolCfg.setMaxTotal(pool_max);
		_poolCfg.setMaxIdle(pool_max);
		_poolCfg.setMinIdle(pool_max);
		_poolCfg.setTestOnReturn(false);
		_poolCfg.setTestOnBorrow(false);
	}

	public Jedis getJedis(String key) {
		return jedisCluster.getConnectionFromSlot(JedisClusterCRC16.getSlot(key));
	}


	public JedisCluster getJedisCluster() {
		return jedisCluster;
	}

	public boolean init() {
		if (_poolMax <= 0) {
			L.info("_poolMax of {} is {}, no real init", _name, _poolMax);
			return true;
		}

		L.debug("try init redis cluster :{}", redisCluster);

		jedisCluster = new JedisCluster(redisCluster, _timeout, _timeout, 5, _auth.equalsIgnoreCase("NULL") ? null : _auth, _name, _poolCfg);
		boolean rc_ = true;
		try {
			String key = "cluster:init:test:ping";
			jedisCluster.set(key, "PONG");
			String pong_ = jedisCluster.get(key);
			if (pong_.equalsIgnoreCase("PONG")) {
				L.info("ping {} is ok", redisCluster);
			} else {
				rc_ = false;
				L.warn("ping {}  returns [{}]",  redisCluster, pong_ );
			}
		} catch (Exception e) {
			rc_ = false;
			L.warn("jedis encounter exception", e);
		}
		return rc_;
	}
}
