package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbStlRuleItemRec extends GsonObj {
	private Long id;
	private Long rule_id;
	private String charge_item;
	private String item_name;
	private Timestamp eff_date;
	private Timestamp exp_date;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRule_id() {
		return rule_id;
	}

	public void setRule_id(Long rule_id) {
		this.rule_id = rule_id;
	}

	public String getCharge_item() {
		return charge_item;
	}

	public void setCharge_item(String charge_item) {
		this.charge_item = charge_item;
	}

	public String getItem_name() {
		return item_name;
	}

	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}

	public Timestamp getEff_date() {
		return eff_date;
	}

	public void setEff_date(Timestamp eff_date) {
		this.eff_date = eff_date;
	}

	public Timestamp getExp_date() {
		return exp_date;
	}

	public void setExp_date(Timestamp exp_date) {
		this.exp_date = exp_date;
	}
}
