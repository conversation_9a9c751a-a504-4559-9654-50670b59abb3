package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbStlPayProvRec extends GsonObj {
	private Long svc_inst_id;
	private String charge_item;
	private String object_value;
	private Timestamp eff_date;
	private Timestamp exp_date;

	public Long getSvc_inst_id() {
		return svc_inst_id;
	}

	public void setSvc_inst_id(Long svc_inst_id) {
		this.svc_inst_id = svc_inst_id;
	}

	public String getCharge_item() {
		return charge_item;
	}

	public void setCharge_item(String charge_item) {
		this.charge_item = charge_item;
	}

	public String getObject_value() {
		return object_value;
	}

	public void setObject_value(String object_value) {
		this.object_value = object_value;
	}

	public Timestamp getEff_date() {
		return eff_date;
	}

	public void setEff_date(Timestamp eff_date) {
		this.eff_date = eff_date;
	}

	public Timestamp getExp_date() {
		return exp_date;
	}

	public void setExp_date(Timestamp exp_date) {
		this.exp_date = exp_date;
	}
}
