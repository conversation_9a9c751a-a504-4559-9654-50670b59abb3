package com.hp.cmcc.bboss.pub.dto;


import com.hp.cmcc.bboss.pub.edb.entity.EdbStlObjectRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlOfferRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRuleRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.GsonUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @author: zhanglei
 * @version: 1.0
 * @date: 2021/12/28 17:28
 */

public class RequestParam {

    private String logTag;
    private UdrFmt udr;
    private String[] fields;
    private Map<Long, EdbStlRuleRec> ruleMap = new HashMap<>();
    private Map<Long, EdbStlOfferRec> offerMap = new HashMap<>(); // K:_ruleId
    private Map<Long, List<EdbStlRateRec>> rateMap = new HashMap<>(); // K:_ruleId
    private Map<Long, EdbStlObjectRec> oObjMap = new HashMap<>(); // K:rule_id
    private Map<Long, EdbStlObjectRec> iObjMap = new HashMap<>(); // K:_objectId;
    private EdbStlOfferRec offer;

    private Map<String, Object> guidingMap = new HashMap<>();
    private Map<Long, List<String[]>> inSiddRuleMap = new HashMap<>(); // K:rule_id V:idd_by_rate
    private Map<String, List<String[]>> inSiddSeqMap = new HashMap<>(); // K:'rule_id:RECORD_ID_29' V:idd_by_parameter
    private Map<Integer, List<String[]>> paramSiddMap = new HashMap<>(); // K:RECORD_ID_29

    private List<EdbStlRateRec> stl_rate_list = new ArrayList<>();
    private EdbStlRateRec rate_rec;
    private String[] in_sidd;
    private long rule_id;

    private String shpmBaseDir;
    private Integer shpmCacheSize;

    private int udrExpireDays;

    private int udrAheadMinutes;

    private int udrDurationMax;

    public UdrFmt getUdr() {
        return udr;
    }

    public void setUdr(UdrFmt udr) {
        this.udr = udr;
    }

    public String[] getFields() {
        return fields;
    }

    public void setFields(String[] fields) {
        this.fields = fields;
    }

    public String getLogTag() {
        return logTag;
    }

    public void setLogTag(String logTag) {
        this.logTag = logTag;
    }

    public Map<Long, EdbStlRuleRec> getRuleMap() {
        return ruleMap;
    }

    public void setRuleMap(Map<Long, EdbStlRuleRec> ruleMap) {
        this.ruleMap = ruleMap;
    }

    public Map<Long, EdbStlOfferRec> getOfferMap() {
        return offerMap;
    }

    public void setOfferMap(Map<Long, EdbStlOfferRec> offerMap) {
        this.offerMap = offerMap;
    }

    public Map<Long, List<EdbStlRateRec>> getRateMap() {
        return rateMap;
    }

    public void setRateMap(Map<Long, List<EdbStlRateRec>> rateMap) {
        this.rateMap = rateMap;
    }

    public Map<Long, EdbStlObjectRec> getoObjMap() {
        return oObjMap;
    }

    public void setoObjMap(Map<Long, EdbStlObjectRec> oObjMap) {
        this.oObjMap = oObjMap;
    }

    public Map<Long, EdbStlObjectRec> getiObjMap() {
        return iObjMap;
    }

    public void setiObjMap(Map<Long, EdbStlObjectRec> iObjMap) {
        this.iObjMap = iObjMap;
    }

    public EdbStlOfferRec getOffer() {
        return offer;
    }

    public void setOffer(EdbStlOfferRec offer) {
        this.offer = offer;
    }

    public Map<String, Object> getGuidingMap() {
        return guidingMap;
    }

    public void setGuidingMap(Map<String, Object> guidingMap) {
        this.guidingMap = guidingMap;
    }

    public Map<Long, List<String[]>> getInSiddRuleMap() {
        return inSiddRuleMap;
    }

    public void setInSiddRuleMap(Map<Long, List<String[]>> inSiddRuleMap) {
        this.inSiddRuleMap = inSiddRuleMap;
    }

    public Map<String, List<String[]>> getInSiddSeqMap() {
        return inSiddSeqMap;
    }

    public void setInSiddSeqMap(Map<String, List<String[]>> inSiddSeqMap) {
        this.inSiddSeqMap = inSiddSeqMap;
    }

    public Map<Integer, List<String[]>> getParamSiddMap() {
        return paramSiddMap;
    }

    public void setParamSiddMap(Map<Integer, List<String[]>> paramSiddMap) {
        this.paramSiddMap = paramSiddMap;
    }

    public List<EdbStlRateRec> getStl_rate_list() {
        return stl_rate_list;
    }

    public void setStl_rate_list(List<EdbStlRateRec> stl_rate_list) {
        this.stl_rate_list = stl_rate_list;
    }

    public EdbStlRateRec getRate_rec() {
        return rate_rec;
    }

    public void setRate_rec(EdbStlRateRec rate_rec) {
        this.rate_rec = rate_rec;
    }

    public String[] getIn_sidd() {
        return in_sidd;
    }

    public void setIn_sidd(String[] in_sidd) {
        this.in_sidd = in_sidd;
    }

    public long getRule_id() {
        return rule_id;
    }

    public void setRule_id(long rule_id) {
        this.rule_id = rule_id;
    }

    public String getShpmBaseDir() {
        return shpmBaseDir;
    }

    public void setShpmBaseDir(String shpmBaseDir) {
        this.shpmBaseDir = shpmBaseDir;
    }

    public Integer getShpmCacheSize() {
        return shpmCacheSize;
    }

    public void setShpmCacheSize(Integer shpmCacheSize) {
        this.shpmCacheSize = shpmCacheSize;
    }

    public int getUdrExpireDays() {
        return udrExpireDays;
    }

    public void setUdrExpireDays(int udrExpireDays) {
        this.udrExpireDays = udrExpireDays;
    }

    public int getUdrAheadMinutes() {
        return udrAheadMinutes;
    }

    public void setUdrAheadMinutes(int udrAheadMinutes) {
        this.udrAheadMinutes = udrAheadMinutes;
    }

    public int getUdrDurationMax() {
        return udrDurationMax;
    }

    public void setUdrDurationMax(int udrDurationMax) {
        this.udrDurationMax = udrDurationMax;
    }

    @Override
    public String toString() {
        return "RequestParam{" +
                "logTag='" + logTag + '\'' +
                ", udr=" + udr.toGsonStr() +
                ", fields=" + GsonUtil.toJsonString(fields) +
                '}';
    }
}
