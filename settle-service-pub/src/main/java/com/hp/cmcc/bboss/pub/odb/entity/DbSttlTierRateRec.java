package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbSttlTierRateRec extends GsonObj {
	public static final int K_0_VER = 0;
	public static final int K_1_STTRRT = 1;
	public static final int K_2_BIZ_TYPE = 2;
	public static final int K_3_EC_CODE = 3;
	public static final int K_4_AUX_KEY = 4;

	public static final int V_00_EFF_DATE = 0;
	public static final int V_01_EXP_DATE = 1;
	public static final int V_02_TIER_IDX = 2;
	public static final int V_03_TIER_OBJ = 3;
	public static final int V_04_TIER_UNIT = 4;
	public static final int V_05_TIER_MIN = 5;
	public static final int V_06_TIER_MAX = 6;
	public static final int V_07_RATE_OBJ = 7;
	public static final int V_08_RATE_UNIT = 8;
	public static final int V_09_RATE = 9;
	public static final int V_10_RATE_ID = 10;
	public static final int V_11_BIZ_TYPE = 11;
	public static final int V_12_EC_CODE = 12;
	public static final int V_13_AUX_KEY = 13;

	private Integer rate_id;
	private String biz_type;
	private String ec_code;
	private String aux_key;
	private Integer rate_obj;
	private Integer rate_unit;
	private Integer rate;
	private Integer tier_obj;
	private Integer tier_unit;
	private Integer tier_idx;
	private Long tier_min;
	private Long tier_max;
	private Timestamp eff_date;
	private Timestamp exp_date;
	private Integer is_monthly;
	private String memo;

	public Integer getRate_id() {
		return rate_id;
	}

	public void setRate_id(Integer rate_id) {
		this.rate_id = rate_id;
	}

	public String getBiz_type() {
		return biz_type;
	}

	public void setBiz_type(String biz_type) {
		this.biz_type = biz_type;
	}

	public String getEc_code() {
		return ec_code;
	}

	public void setEc_code(String ec_code) {
		this.ec_code = ec_code;
	}

	public String getAux_key() {
		return aux_key;
	}

	public void setAux_key(String aux_key) {
		this.aux_key = aux_key;
	}

	public Integer getRate_obj() {
		return rate_obj;
	}

	public void setRate_obj(Integer rate_obj) {
		this.rate_obj = rate_obj;
	}

	public Integer getRate_unit() {
		return rate_unit;
	}

	public void setRate_unit(Integer rate_unit) {
		this.rate_unit = rate_unit;
	}

	public Integer getRate() {
		return rate;
	}

	public void setRate(Integer rate) {
		this.rate = rate;
	}

	public Integer getTier_obj() {
		return tier_obj;
	}

	public void setTier_obj(Integer tier_obj) {
		this.tier_obj = tier_obj;
	}

	public Integer getTier_unit() {
		return tier_unit;
	}

	public void setTier_unit(Integer tier_unit) {
		this.tier_unit = tier_unit;
	}

	public Integer getTier_idx() {
		return tier_idx;
	}

	public void setTier_idx(Integer tier_idx) {
		this.tier_idx = tier_idx;
	}

	public Long getTier_min() {
		return tier_min;
	}

	public void setTier_min(Long tier_min) {
		this.tier_min = tier_min;
	}

	public Long getTier_max() {
		return tier_max;
	}

	public void setTier_max(Long tier_max) {
		this.tier_max = tier_max;
	}

	public Timestamp getEff_date() {
		return eff_date;
	}

	public void setEff_date(Timestamp eff_date) {
		this.eff_date = eff_date;
	}

	public Timestamp getExp_date() {
		return exp_date;
	}

	public void setExp_date(Timestamp exp_date) {
		this.exp_date = exp_date;
	}

	public Integer getIs_monthly() {
		return is_monthly;
	}

	public void setIs_monthly(Integer is_monthly) {
		this.is_monthly = is_monthly;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
}
