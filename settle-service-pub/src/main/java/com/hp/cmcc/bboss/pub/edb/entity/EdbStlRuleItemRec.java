package com.hp.cmcc.bboss.pub.edb.entity;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbStlRuleItemRec extends GsonObj implements Effectivable {
	public Long _id;
	public Long _ruleId;
	public String _chargeItem;
	public String _itemName;
	public String _effDate;
	public String _expDate;

	@Override
	public boolean isEffective(String tm14) {
		if (tm14 == null || tm14.compareTo(_effDate) < 0 || tm14.compareTo(_expDate) > 0) {
			return false;
		} else {
			return true;
		}
	}
}
