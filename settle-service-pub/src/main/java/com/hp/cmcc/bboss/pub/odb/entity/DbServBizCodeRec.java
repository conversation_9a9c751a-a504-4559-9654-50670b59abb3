package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbServBizCodeRec extends GsonObj {
	public static final int K1_0_VER = 0;
	public static final int K1_1_SVBZCD = 1;
	public static final int K1_2_PRODUCT_CODE = 2;

	public static final int K2_0_VER = 0;
	public static final int K2_1_SVBZCD = 1;
	public static final int K2_2_SERV_CODE = 2;
	public static final int K2_3_BIZ_CODE = 3;

	public static final int V_00_EFFECTIVE_DATE = 0;
	public static final int V_01_EXPIRY_DATE = 1;
	public static final int V_02_EC_CODE = 2;
	public static final int V_03_SERV_CODE = 3;
	public static final int V_04_BIZ_CODE = 4;
	public static final int V_05_RPOD_ORDER_ID = 5;
	public static final int V_06_ORDER_ID = 6;
	public static final int V_07_BIZ_CODE_APPLY = 7;
	public static final int V_08_PROV_CODE = 8;
	public static final int V_09_PRODUCT_CODE = 9;
	public static final int V_10_SERVICE_CODE = 10;
	public static final int V_11_EC_GROUP_ID = 11;
	public static final int V_12_SERVICE_TYPE = 12;
	public static final int V_13_SEND_PROV = 13;
	public static final int V_14_PROD_ORDER_MODE = 14;
	public static final int V_15_SUB_GROUP_FLAG = 15;
	public static final int V_16_CARRY_TYPE = 16;
	public static final int V_17_SIGN_ENTITY = 17;
	public static final int V_18_PARENT_ORDER_ID = 18;
	public static final int V_19_LOCON_FLAG = 19;
	public static final int V_20_ORDER_LEVEL = 20;

	private Long id;
	private String ec_code;
	private String serv_code;
	private String biz_code;
	private String prod_order_id;
	private String order_id;
	private Long biz_code_apply;
	private String prov_code;
	private String product_code;
	private String service_code;
	private Timestamp effective_date;
	private Timestamp expiry_date;
	private String ec_group_id;
	private String service_type;
	private String send_prov;
	private String prod_order_mode;
	private Integer sub_group_flag;
	private String carry_type;
	private String sign_entity;
	private String parent_order_id;
	private Integer locon_flag;
	private Integer order_level;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getEc_code() {
		return ec_code;
	}

	public void setEc_code(String ec_code) {
		this.ec_code = ec_code;
	}

	public String getServ_code() {
		return serv_code;
	}

	public void setServ_code(String serv_code) {
		this.serv_code = serv_code;
	}

	public String getBiz_code() {
		return biz_code;
	}

	public void setBiz_code(String biz_code) {
		this.biz_code = biz_code;
	}

	public String getProd_order_id() {
		return prod_order_id;
	}

	public void setProd_order_id(String prod_order_id) {
		this.prod_order_id = prod_order_id;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public Long getBiz_code_apply() {
		return biz_code_apply;
	}

	public void setBiz_code_apply(Long biz_code_apply) {
		this.biz_code_apply = biz_code_apply;
	}

	public String getProv_code() {
		return prov_code;
	}

	public void setProv_code(String prov_code) {
		this.prov_code = prov_code;
	}

	public String getProduct_code() {
		return product_code;
	}

	public void setProduct_code(String product_code) {
		this.product_code = product_code;
	}

	public String getService_code() {
		return service_code;
	}

	public void setService_code(String service_code) {
		this.service_code = service_code;
	}

	public Timestamp getEffective_date() {
		return effective_date;
	}

	public void setEffective_date(Timestamp effective_date) {
		this.effective_date = effective_date;
	}

	public Timestamp getExpiry_date() {
		return expiry_date;
	}

	public void setExpiry_date(Timestamp expiry_date) {
		this.expiry_date = expiry_date;
	}

	public String getEc_group_id() {
		return ec_group_id;
	}

	public void setEc_group_id(String ec_group_id) {
		this.ec_group_id = ec_group_id;
	}

	public String getService_type() {
		return service_type;
	}

	public void setService_type(String service_type) {
		this.service_type = service_type;
	}

	public String getSend_prov() {
		return send_prov;
	}

	public void setSend_prov(String send_prov) {
		this.send_prov = send_prov;
	}

	public String getProd_order_mode() {
		return prod_order_mode;
	}

	public void setProd_order_mode(String prod_order_mode) {
		this.prod_order_mode = prod_order_mode;
	}

	public Integer getSub_group_flag() {
		return sub_group_flag;
	}

	public void setSub_group_flag(Integer sub_group_flag) {
		this.sub_group_flag = sub_group_flag;
	}

	public String getCarry_type() {
		return carry_type;
	}

	public void setCarry_type(String carry_type) {
		this.carry_type = carry_type;
	}

	public String getSign_entity() {
		return sign_entity;
	}

	public void setSign_entity(String sign_entity) {
		this.sign_entity = sign_entity;
	}

	public String getParent_order_id() {
		return parent_order_id;
	}

	public void setParent_order_id(String parent_order_id) {
		this.parent_order_id = parent_order_id;
	}

	public Integer getLocon_flag() {
		return locon_flag;
	}

	public void setLocon_flag(Integer locon_flag) {
		this.locon_flag = locon_flag;
	}

	public Integer getOrder_level() {
		return order_level;
	}

	public void setOrder_level(Integer order_level) {
		this.order_level = order_level;
	}
}
