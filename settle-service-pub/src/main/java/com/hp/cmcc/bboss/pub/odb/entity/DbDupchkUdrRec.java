package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbDupchkUdrRec extends GsonObj {
	private Integer mmdd;
	private String hash_key;
	private Timestamp event_time;
	private String aux_key;
	private Long file_id;
	private Integer line_num;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[6];
		o[0] = mmdd;
		o[1] = hash_key;
		o[2] = event_time;
		o[3] = aux_key;
		o[4] = file_id;
		o[5] = line_num;
		return o;
	}

	public Integer getMmdd() {
		return mmdd;
	}

	public void setMmdd(Integer mmdd) {
		this.mmdd = mmdd;
	}

	public String getHash_key() {
		return hash_key;
	}

	public void setHash_key(String hash_key) {
		this.hash_key = hash_key;
	}

	public Timestamp getEvent_time() {
		return event_time;
	}

	public void setEvent_time(Timestamp event_time) {
		this.event_time = event_time;
	}

	public String getAux_key() {
		return aux_key;
	}

	public void setAux_key(String aux_key) {
		this.aux_key = aux_key;
	}

	public Long getFile_id() {
		return file_id;
	}

	public void setFile_id(Long file_id) {
		this.file_id = file_id;
	}

	public Integer getLine_num() {
		return line_num;
	}

	public void setLine_num(Integer line_num) {
		this.line_num = line_num;
	}
}
