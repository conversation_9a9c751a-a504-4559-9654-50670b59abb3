package com.hp.cmcc.bboss.pub.util;

import java.util.concurrent.Callable;

public class GsonStrObjCallable implements Callable<GsonStrObj> {
	private String _id;
	private GsonObj _gsonObj;

	public GsonStrObjCallable(String id, GsonObj gson_obj) {
		_id = id;
		_gsonObj = gson_obj;
	}

	@Override
	public GsonStrObj call() throws Exception {
		GsonStrObj result_ = new GsonStrObj();
		result_._id = _id;
		result_._gsonStr = _gsonObj.toGsonStr();
		return result_;
	}
	
	// Usage example:
	//
	// *** NOTICE:
	// *** Since Google's gson library (tested with gson-2.7.jar) is buggy, the following code may throw
	// *** java.util.ConcurrentModificationException. So the paradigm is only ideal and can not be used
	// *** in production environment. (What we can do is just waiting and pray Google's bug-fix)
	//
	// List<Future<GsonStrObj>> future_list_ = new ArrayList<Future<GsonStrObj>>();
	// for (UdrFmtMsg msg_ : _msgList) {
	//		GsonStrObjCallable callable_ = new GsonStrObjCallable(msg_._rndmId, msg_);
	//		Future<GsonStrObj> future_ = Main._ExecutorService.submit(callable_);
	//		future_list_.add(future_);
	// }
	// for (Future<GsonStrObj> future_ : future_list_) {
	//		GsonStrObj gson_str_obj_ = future_.get();
	//		... do biz logic such as send to message queue ...
	// }
}
