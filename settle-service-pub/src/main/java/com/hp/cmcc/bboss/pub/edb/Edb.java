package com.hp.cmcc.bboss.pub.edb;

import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.sqlite.SQLiteConfig;

import java.io.File;
import java.io.FileNotFoundException;
import java.sql.*;

public class Edb {
	private static Logger L = LoggerFactory.getLogger(Edb.class);
	public static final String TRANSACTION_BEGIN = "BEGIN TRANSACTION";
	public static final String TRANSACTION_COMMIT = "COMMIT TRANSACTION";
	public static final String TRANSACTION_ROLLBACK = "ROLLBACK TRANSACTION";
	private static final String _Q_DUAL = "SELECT 'X'";
	private String _edbPath;
	private boolean _readOnly;
	private int _cacheSize;
	private Connection _sqlite;

	public Edb(String edb_path, boolean read_only) throws Exception {
		_edbPath = edb_path;
		_readOnly = read_only;
		_initConnection();
	}

	public Edb(String edb_path, boolean read_only, int cache_size) throws Exception {
		_edbPath = edb_path;
		_readOnly = read_only;
		_cacheSize = cache_size;
		_initConnection();
	}

	public void destroy() {
		Close(null, null, _sqlite);
		_sqlite = null;
	}

	public boolean isConnected() {
		if (_sqlite == null) {
			L.info("_sqlite is null");
			return false;
		}
		boolean connected_ = false;
		Statement stmt_ = null;
		ResultSet rs_ = null;
		try {
			stmt_ = _sqlite.createStatement();
			rs_ = stmt_.executeQuery(_Q_DUAL);
			while (rs_.next()) {
				String dual_ = rs_.getString(1);
				if (dual_.equals("X"))
					connected_ = true;
			}
			if (!connected_) {
				L.info("test _sqlite with [{}] failed", _Q_DUAL);
			}
			return connected_;
		} catch (Exception e) {
			L.info("test _sqlite exception", e);
			return false;
		} finally {
			Close(rs_, stmt_, null);
		}
	}

	public String getEdbPath() {
		return _edbPath;
	}

	public boolean getReadOnly() {
		return _readOnly;
	}

	public Connection getConnection() {
		return _sqlite;
	}

	public void begin() throws SQLException {
		PreparedStatement pstmt_ = null;
		try {
			pstmt_ = _sqlite.prepareStatement(TRANSACTION_BEGIN);
			pstmt_.executeUpdate();
		} finally {
			Close(null, pstmt_, null);
		}
	}

	public void commit() throws SQLException {
		PreparedStatement pstmt_ = null;
		try {
			pstmt_ = _sqlite.prepareStatement(TRANSACTION_COMMIT);
			pstmt_.executeUpdate();
		} finally {
			Close(null, pstmt_, null);
		}
	}

	public void rollback() throws SQLException {
		PreparedStatement pstmt_ = null;
		try {
			pstmt_ = _sqlite.prepareStatement(TRANSACTION_ROLLBACK);
			pstmt_.executeUpdate();
		} finally {
			Close(null, pstmt_, null);
		}
	}

	public int count(String tbl_name, String condition) throws SQLException {
		String sql_ = null;
		if (PubMethod.IsEmpty(condition)) {
			sql_ = PubMethod.FmtArgs("SELECT COUNT(*) FROM %s", tbl_name);
		} else {
			sql_ = PubMethod.FmtArgs("SELECT COUNT(*) FROM %s WHERE %s", tbl_name, condition);
		}
		int cnt_ = 0;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			pstmt_ = _sqlite.prepareStatement(sql_);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				cnt_ = rs_.getInt(1);
				break;
			}
		} finally {
			Close(rs_, pstmt_, null);
		}
		return cnt_;
	}

	public static void Close(ResultSet rs, Statement stmt, Connection conn) {
		if (rs != null) {
			try {
				rs.close();
			} catch (Exception e) {
				L.warn("close ResultSet exception, pls ignore", e);
			}
		}
		if (stmt != null) {
			try {
				stmt.close();
			} catch (Exception e) {
				L.warn("close Statement exception, pls ignore", e);
			}
		}
		if (conn != null) {
			try {
				conn.close();
			} catch (Exception e) {
				L.warn("close Connection exception, pls ignore", e);
			}
		}
	}

	private void _initConnection() throws FileNotFoundException, ClassNotFoundException, SQLException {
		Class.forName("org.sqlite.JDBC");
		File f = new File(_edbPath);
		SQLiteConfig cfg_ = new SQLiteConfig();
		cfg_.setSharedCache(true);
		if (_readOnly) {
			if (!f.isFile()) {
				L.info("_edbPath [{}], file not exists", f.getAbsolutePath());
				throw new FileNotFoundException("file not exists [" + _edbPath + "]");
			}
			cfg_.setReadOnly(true);
		}
		if (_cacheSize < -2000 || _cacheSize > 2000) {
			cfg_.setCacheSize(_cacheSize);
		}
		_sqlite = DriverManager.getConnection("jdbc:sqlite:" + f.getAbsolutePath(), cfg_.toProperties());
	}
}
