package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbStlRateRec extends GsonObj {
	private Long id;
	private Long rate_id;
	private Long rule_id;
	private Long in_object_id;
	private String rate_code;
	private Integer rate_type;
	private Integer calc_priority;
	private Integer round_method;
	private Timestamp eff_date;
	private Timestamp exp_date;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRate_id() {
		return rate_id;
	}

	public void setRate_id(Long rate_id) {
		this.rate_id = rate_id;
	}

	public Long getRule_id() {
		return rule_id;
	}

	public void setRule_id(Long rule_id) {
		this.rule_id = rule_id;
	}

	public Long getIn_object_id() {
		return in_object_id;
	}

	public void setIn_object_id(Long in_object_id) {
		this.in_object_id = in_object_id;
	}

	public String getRate_code() {
		return rate_code;
	}

	public void setRate_code(String rate_code) {
		this.rate_code = rate_code;
	}

	public Integer getRate_type() {
		return rate_type;
	}

	public void setRate_type(Integer rate_type) {
		this.rate_type = rate_type;
	}

	public Integer getCalc_priority() {
		return calc_priority;
	}

	public void setCalc_priority(Integer calc_priority) {
		this.calc_priority = calc_priority;
	}

	public Integer getRound_method() {
		return round_method;
	}

	public void setRound_method(Integer round_method) {
		this.round_method = round_method;
	}

	public Timestamp getEff_date() {
		return eff_date;
	}

	public void setEff_date(Timestamp eff_date) {
		this.eff_date = eff_date;
	}

	public Timestamp getExp_date() {
		return exp_date;
	}

	public void setExp_date(Timestamp exp_date) {
		this.exp_date = exp_date;
	}
}
