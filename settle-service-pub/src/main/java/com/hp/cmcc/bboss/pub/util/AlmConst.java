package com.hp.cmcc.bboss.pub.util;

public class AlmConst {
	// 9 digits alarm code:
	// 1-3: <PERSON><PERSON><PERSON> (100-199:public, 200-399:application, 000-099/400-999:reserved)
	//      200:monitr; 201:locdis; 202:rmtdis; 203:shpmsv; 204:shpmag; 205:dblodr;
	//      206:syndup; 207:datsum; 208:erorcy; 209:prdbil; 210:udr2mq; 211:udruld;
	//      212:erruld; 213:worker; 214:bizrcy
	// 4-4: Level (1:DEBUG/TEST, 2:INFO, 3:WARN, 4:ERROR)
	// 5-5: Category (1:configuration, 2:performance, 3:event, 4:fault/error)
	// 6-9: Code (fire at will)

	// 100:public
	public static final int ALM_100131000_DUMMY_TEST = 100131000;

	// 200:monitr
	public static final int ALM_200321001_LOAD_15_HEAVY = 200321001;
	public static final int ALM_200321002_LOAD_05_HEAVY = 200321002;
	public static final int ALM_200221003_LOAD_01_HEAVY = 200221003;
	public static final int ALM_200321004_MEM_FREE_LOW = 200321004;
	public static final int ALM_200321005_FS_FREE_LOW = 200321005;
	public static final int ALM_200321006_TOO_MANY_PROC = 200321006;
	public static final int ALM_200331007_PROC_NOT_RUNNING = 200331007;
	public static final int ALM_200321008_PROC_PID_CHG = 200321008;
	public static final int ALM_200331009_DIR_IDLE = 200331009;

	// 201:locdis
	public static final int ALM_201341001_MOVE_BAK_FAIL = 201341001;
	public static final int ALM_201341002_MOVE_ACT_FAIL = 201341002;
	public static final int ALM_201341003_MOVE_ACT_LOST = 201341003;
	public static final int ALM_201341004_MOVE_MOV_FAIL = 201341004;
	public static final int ALM_201341011_ARCH_MKDIR_FAIL = 201341011;
	public static final int ALM_201341012_ARCH_FAIL = 201341012;
	public static final int ALM_201341021_GZIP_FAIL = 201341021;
	public static final int ALM_201341022_GZIP_EXCEPTION = 201341022;
	public static final int ALM_201341031_ULNK_DIR_FAIL = 201341031;
	public static final int ALM_201341032_ULNK_FAIL = 201341032;

	// 202:rmtdis
	public static final int ALM_202341001_TRANS_EXCEPTION = 202341001;
	public static final int ALM_202341011_PUT_EXCEPTION = 202341011;
	public static final int ALM_202341012_PUT_SIZE_NE = 202341012;
	public static final int ALM_202341013_PUT_LOC_DEL_FAIL = 202341013;
	public static final int ALM_202341014_PUT_LOC_BAK_FAIL = 202341014;
	public static final int ALM_202341021_GET_EXCEPTION = 202341021;
	public static final int ALM_202341022_GET_LOC_NONEXISTS = 202341022;
	public static final int ALM_202341023_GET_SIZE_NE = 202341023;
	public static final int ALM_202341024_GET_LOC_MOV_FAIL = 202341024;

	// 203:shpmsv
	public static final int ALM_203231000_SHPMSV_REFRESH_OK = 203231000;
	public static final int ALM_203341001_SHPMSV_TRIGGER_EXCEPTION = 203341001;
	public static final int ALM_203341002_SHPMSV_REFRESH_FAIL = 203341002;

	// 204:shpmag
	public static final int ALM_204341001_SHPMAG_DOWNLOAD_ERR = 204341001;
	public static final int ALM_204341002_SHPMAG_MKDIR_FAIL = 204341002;
	public static final int ALM_204341003_SHPMAG_MOV_FAIL = 204341003;

	// 205:dblodr
	public static final int ALM_205341001_FH_ULNK_LOG_FAIL = 205341001;
	public static final int ALM_205341002_FH_MOV_BAD_FAIL = 205341002;
	public static final int ALM_205341003_FH_MOV_LOG_FAIL = 205341003;
	public static final int ALM_205341004_FH_BAK_BCP_FAIL = 205341004;
	public static final int ALM_205341011_SHELL_RCNZ = 205341011; // RCNZ: Return Code Not Zero
	public static final int ALM_205341012_SHELL_EXCEPTION = 205341012;
	public static final int ALM_205341013_BCP_LOG_NONEXISTS = 205341013;
	public static final int ALM_205331014_BCP_BAD_DETECTED = 205331014;
	public static final int ALM_205341015_BCP_LOG_PARSE_FAIL = 205341015;
	public static final int ALM_205341016_BCP_LOG_NOT_BALANCE = 205341016;
	public static final int ALM_205341017_BCP_LOG_PARSE_EXCEPTION = 205341017;

	// 206:syndup
	public static final int ALM_206341001_SCAN_EXPIRE_EXCEPTION = 206341001;
	public static final int ALM_206341002_PREPARE_BATCH_EXCEPTION = 206341002;
	public static final int ALM_206341003_MERGE_EXCEPTION = 206341003;
	public static final int ALM_206341004_CLEAR_BATCH_EXCEPTION = 206341004;

	// 207:datsum
	public static final int ALM_207341001_PARSE_FNM_EXCEPTION = 207341001;
	public static final int ALM_207341002_COUNT_LINES_EXCEPTION = 207341002;
	public static final int ALM_207341003_JSON_BAK_FAIL = 207341003;
	public static final int ALM_207341004_SUM_MOV_FAIL = 207341004;
	public static final int ALM_207341005_DBLOG_EXCEPTION = 207341005;
	
	// 208:erorcy
	public static final int ALM_208341001_ERORCY_EXCEPTION = 208341001;
	
	// 209:prdbil
	public static final int ALM_209341001_PRDBIL_EXCEPTION = 209341001;
	
	// 210:udr2mq
	public static final int ALM_210341001_UKN_EXCEPTION = 210341001;
	public static final int ALM_210341002_PROC_EXCEPTION = 210341002;
	public static final int ALM_210341003_BAK_FAIL = 210341003;
	public static final int ALM_210341004_PROC_FAIL = 210341004;
	public static final int ALM_210341005_CLEAR_HIS_COUNT_EXCEPTION = 210341005;
	public static final int ALM_210341006_CLEAR_HIS_BIZLOG_EXCEPTION = 210341006;
	
	// 211:udruld
	public static final int ALM_211341001_UKN_EXCEPTION = 211341001;
	public static final int ALM_211341002_ADJ_EXCEPTION = 211341002;
	
	// 212:erruld
	public static final int ALM_212341001_UKN_EXCEPTION = 212341001;
	
	// 213:worker
	public static final int ALM_213441001_PING_RATER_FAIL = 213441001;
	
	// 214:bizrcy
	public static final int ALM_214341001_SCAN_BIZLOG_EXCEPTION = 214341001;
}
