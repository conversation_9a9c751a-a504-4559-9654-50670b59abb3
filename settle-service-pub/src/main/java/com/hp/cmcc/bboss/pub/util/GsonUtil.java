package com.hp.cmcc.bboss.pub.util;

import com.google.gson.*;

import java.lang.reflect.Type;
import java.util.Map;


/*
 * Gson工具类
 * 
 * @author: zhanglei
 * @version: 1.0
 * @date: 2021-02-07 11:25:05
 */
public class GsonUtil {
    private static final Gson gson = new Gson();
    private static final JsonParser jsonParser = new JsonParser();

    private GsonUtil() {
        throw new AssertionError();
    }

    public static String toJsonString(Object object) {
        return gson.toJson(object);
    }

    public static String toJsonString(Object object, Type typeOfT) {
        return gson.toJson(object, typeOfT);
    }

    public static Object fromJsonString(String json) {
        return gson.fromJson(json, Object.class);
    }

    public static <T> T fromJsonString(String json, Type typeOfT) {
        return gson.fromJson(json, typeOfT);
    }

    public static <T> T fromMap(Map<String, Object> map, Class<T> type) {
        return gson.fromJson(gson.toJson(map), type);
    }
    /**
     * @author: zhanglei
     * @date: 2021-02-18 15:18:59
     * @param {Object} object
     * @return {*}
     * @exception: 
     * @update: 
     * @updatePerson: 
     */
    public static Map<String, Object> toMap(Object object) {
        return (Map)gson.fromJson(gson.toJson(object), Map.class);
    }

    /**
     * @Author: zhanglei
     * @Date: 2021-02-07 11:35:01
     * @update: 
     * @updatePerson: zhanglei
     * @Return: 
     * @param {String} jsonStringObject
     */
    public static JsonObject toJsonObject(String jsonStringObject) throws JsonSyntaxException, IllegalStateException {
        return jsonParser.parse(jsonStringObject).getAsJsonObject();
    }

    /**
     * @Description: 字符串（数组） 转 JsonArray
     * @Return: 
     * @Author: zhanglei
     * @Date: 2021-02-07 11:34:14
     * @update: 
     * @updatePerson: zhanglei
     * @param {String} jsonStringArray
     */
    public static JsonArray toJsonArray(String jsonStringArray) throws JsonSyntaxException, IllegalStateException {
        return jsonParser.parse(jsonStringArray).getAsJsonArray();
    }
    /**
     * @Description: 字符串（jsonStringElement）转 JsonElement
     * @Return: 
     * @Author: zhanglei
     * @Date: 2021-02-07 11:31:52
     * @update: 
     * @updatePerson: zhanglei
     * @param {String} jsonStringElement
     */
    public static JsonElement toJsonElement(String jsonStringElement) throws JsonSyntaxException, IllegalStateException {
        return jsonParser.parse(jsonStringElement);
    }
    /**
     * @Description: 字符串转JsonElement 
     * @Return: 
     * @Author: zhanglei
     * @Date: 2021-02-07 11:31:28
     * @update:
     * @updatePerson: zhanglei
     * @param {String} jsonString
     */
    public static JsonElement jsonElementFrom(String jsonString) {
        return jsonParser.parse(jsonString);
    }
    /**
     * @Description: 验证是否为对象
     * @Return: 
     * @Author: zhanglei
     * @Date: 2021-02-07 11:28:07
     * @update: 
     * @updatePerson: zhanglei
     * @param {String} jsonStringObject
     */
    public static boolean isJsonObject(String jsonStringObject) {
        try {
            return toJsonObject(jsonStringObject).isJsonObject();
        } catch (IllegalStateException var2) {
            return false;
        }
    }
    /**
     * @Description: 验证是否为数组
     * @Return: 
     * @Author: zhanglei
     * @Date: 2021-02-07 11:28:23
     * @update:
     * @updatePerson: zhanglei
     * @param {String} jsonStringArray
     */
    public static boolean isJsonArray(String jsonStringArray) {
        try {
            return toJsonArray(jsonStringArray).isJsonArray();
        } catch (IllegalStateException var2) {
            return false;
        }
    }
}