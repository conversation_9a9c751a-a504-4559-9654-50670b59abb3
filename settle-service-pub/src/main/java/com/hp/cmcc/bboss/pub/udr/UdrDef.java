package com.hp.cmcc.bboss.pub.udr;

import java.util.HashMap;
import java.util.Map;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class UdrDef extends GsonObj {
	public int _idx;
	public boolean _isNum;
	public int _maxLen;
	public String _dftVal;

	public static UdrDef[] E_DEF;
	public static UdrDef[] U_DEF;
	public static UdrDef[] S_DEF;
	public static Map<String, Integer> S_NM2IDX_MAP;

	public UdrDef(int idx, boolean is_num, int max_len, String dft_val) {
		_idx = idx;
		_isNum = is_num;
		_maxLen = max_len;
		_dftVal = dft_val;
	}

	static {
		_InitE();
		_InitU();
		_InitS();
		_InitSNm2IdxMap();
	}

	private static void _InitE() {
		E_DEF = new UdrDef[UdrFmt.E_24_R01_RAW_UDR + 1];
		E_DEF[UdrFmt.E_00_A01_RAW_FILE_NM] = new UdrDef(UdrFmt.E_00_A01_RAW_FILE_NM, false, 100, null);
		E_DEF[UdrFmt.E_01_A02_RAW_LINE_NUM] = new UdrDef(UdrFmt.E_01_A02_RAW_LINE_NUM, true, 9, null);
		E_DEF[UdrFmt.E_02_A03_ERR_CODE] = new UdrDef(UdrFmt.E_02_A03_ERR_CODE, false, 5, null);
		E_DEF[UdrFmt.E_03_A04_ORG_FILE_ID] = new UdrDef(UdrFmt.E_03_A04_ORG_FILE_ID, true, 18, null);
		E_DEF[UdrFmt.E_04_A05_FILE_ID] = new UdrDef(UdrFmt.E_04_A05_FILE_ID, true, 18, null);
		E_DEF[UdrFmt.E_05_A06_LINE_NUM] = new UdrDef(UdrFmt.E_05_A06_LINE_NUM, true, 9, null);
		E_DEF[UdrFmt.E_06_A07_RCV_MM] = new UdrDef(UdrFmt.E_06_A07_RCV_MM, true, 2, null);
		E_DEF[UdrFmt.E_07_A08_RCV_YMDH] = new UdrDef(UdrFmt.E_07_A08_RCV_YMDH, true, 10, null);
		E_DEF[UdrFmt.E_08_A09_RCV_TM] = new UdrDef(UdrFmt.E_08_A09_RCV_TM, false, 14, null);
		E_DEF[UdrFmt.E_09_A10_FILE_YMD] = new UdrDef(UdrFmt.E_09_A10_FILE_YMD, true, 8, null);
		E_DEF[UdrFmt.E_10_A11_FILE_PROV] = new UdrDef(UdrFmt.E_10_A11_FILE_PROV, true, 3, null);
		E_DEF[UdrFmt.E_11_A12_ERCY_TIMES] = new UdrDef(UdrFmt.E_11_A12_ERCY_TIMES, true, 5, null);
		E_DEF[UdrFmt.E_12_A13_ERCY_TIME] = new UdrDef(UdrFmt.E_12_A13_ERCY_TIME, false, 14, null);
		E_DEF[UdrFmt.E_13_A14_PROC_FLAG] = new UdrDef(UdrFmt.E_13_A14_PROC_FLAG, false, 10, null);
		E_DEF[UdrFmt.E_14_A15_ACUMLT] = new UdrDef(UdrFmt.E_14_A15_ACUMLT, true, 20, "0");
		E_DEF[UdrFmt.E_15_E01_ERR_IDX] = new UdrDef(UdrFmt.E_15_E01_ERR_IDX, true, 5, "-1");
		E_DEF[UdrFmt.E_16_E02_ERR_VAL] = new UdrDef(UdrFmt.E_16_E02_ERR_VAL, false, 500, null);
		E_DEF[UdrFmt.E_17_E03_START_TM] = new UdrDef(UdrFmt.E_17_E03_START_TM, false, 30, null);
		E_DEF[UdrFmt.E_18_E04_EC_CODE] = new UdrDef(UdrFmt.E_18_E04_EC_CODE, false, 100, null);
		E_DEF[UdrFmt.E_19_E05_PROV] = new UdrDef(UdrFmt.E_19_E05_PROV, false, 30, null);
		E_DEF[UdrFmt.E_20_E06_ACCT_DAY] = new UdrDef(UdrFmt.E_20_E06_ACCT_DAY, false, 30, null);
		E_DEF[UdrFmt.E_21_E07_PP_FILE_ID] = new UdrDef(UdrFmt.E_21_E07_PP_FILE_ID, false, 30, null);
		E_DEF[UdrFmt.E_22_E08_ERR_REASON] = new UdrDef(UdrFmt.E_22_E08_ERR_REASON, false, 500, null);
		E_DEF[UdrFmt.E_23_E09_SPARE1] = new UdrDef(UdrFmt.E_23_E09_SPARE1, false, 100, null);
		E_DEF[UdrFmt.E_24_R01_RAW_UDR] = new UdrDef(UdrFmt.E_24_R01_RAW_UDR, false, 4000, null);
	}

	private static void _InitU() {
		U_DEF = new UdrDef[UdrFmt.U_FIELD_CNT_57];
		U_DEF[UdrFmt.U_00_ERROR_CODE_01] = new UdrDef(UdrFmt.U_00_ERROR_CODE_01, false, 4, null);
		U_DEF[UdrFmt.U_01_ERROR_LINE_02] = new UdrDef(UdrFmt.U_01_ERROR_LINE_02, true, 10, null);
		U_DEF[UdrFmt.U_02_HOME_PARTY_03] = new UdrDef(UdrFmt.U_02_HOME_PARTY_03, false, 3, null);
		U_DEF[UdrFmt.U_03_FEE1_04] = new UdrDef(UdrFmt.U_03_FEE1_04, true, 12, "0");
		U_DEF[UdrFmt.U_04_FEE2_05] = new UdrDef(UdrFmt.U_04_FEE2_05, true, 12, "0");
		U_DEF[UdrFmt.U_05_FEE3_06] = new UdrDef(UdrFmt.U_05_FEE3_06, true, 12, "0");
		U_DEF[UdrFmt.U_06_FEE4_07] = new UdrDef(UdrFmt.U_06_FEE4_07, true, 12, "0");
		U_DEF[UdrFmt.U_07_CHARGE_CODE1_08] = new UdrDef(UdrFmt.U_07_CHARGE_CODE1_08, false, 64, null);
		U_DEF[UdrFmt.U_08_CHARGE_CODE2_09] = new UdrDef(UdrFmt.U_08_CHARGE_CODE2_09, false, 64, null);
		U_DEF[UdrFmt.U_09_CHARGE_CODE3_10] = new UdrDef(UdrFmt.U_09_CHARGE_CODE3_10, false, 64, null);
		U_DEF[UdrFmt.U_10_CHARGE_CODE4_11] = new UdrDef(UdrFmt.U_10_CHARGE_CODE4_11, false, 64, null);
		// add gya start 20230216
		U_DEF[UdrFmt.U_11_DISCOUNT1_12] = new UdrDef(UdrFmt.U_11_DISCOUNT1_12, false, 64, null);
		U_DEF[UdrFmt.U_12_DISCOUNT2_13] = new UdrDef(UdrFmt.U_12_DISCOUNT2_13, false, 64, null);
		U_DEF[UdrFmt.U_13_DISCOUNT3_14] = new UdrDef(UdrFmt.U_13_DISCOUNT3_14, false, 64, null);
		U_DEF[UdrFmt.U_14_DISCOUNT4_15] = new UdrDef(UdrFmt.U_14_DISCOUNT4_15, false, 64, null);
		U_DEF[UdrFmt.U_15_IS_MEM_CHARGE_PLAN1_16] = new UdrDef(UdrFmt.U_15_IS_MEM_CHARGE_PLAN1_16, false, 16, null);
		U_DEF[UdrFmt.U_16_IS_MEM_CHARGE_PLAN2_17] = new UdrDef(UdrFmt.U_16_IS_MEM_CHARGE_PLAN2_17, false, 16, null);
		U_DEF[UdrFmt.U_17_IS_MEM_CHARGE_PLAN3_18] = new UdrDef(UdrFmt.U_17_IS_MEM_CHARGE_PLAN3_18, false, 16, null);
		U_DEF[UdrFmt.U_18_IS_MEM_CHARGE_PLAN4_19] = new UdrDef(UdrFmt.U_18_IS_MEM_CHARGE_PLAN4_19, false, 16, null);
		U_DEF[UdrFmt.U_19_PRODUCT_OFFER1_20] = new UdrDef(UdrFmt.U_19_PRODUCT_OFFER1_20, false, 64, null);
		U_DEF[UdrFmt.U_20_PRODUCT_OFFER2_21] = new UdrDef(UdrFmt.U_20_PRODUCT_OFFER2_21, false, 64, null);
		U_DEF[UdrFmt.U_21_PRODUCT_OFFER3_22] = new UdrDef(UdrFmt.U_21_PRODUCT_OFFER3_22, false, 64, null);
		U_DEF[UdrFmt.U_22_PRODUCT_OFFER4_23] = new UdrDef(UdrFmt.U_22_PRODUCT_OFFER4_23, false, 64, null);
		// add gya end 20230216
		U_DEF[UdrFmt.U_23_EC_CODE_24] = new UdrDef(UdrFmt.U_23_EC_CODE_24, false, 32, null);
		U_DEF[UdrFmt.U_24_EC_PROV_CODE_25] = new UdrDef(UdrFmt.U_24_EC_PROV_CODE_25, false, 3, null);
		U_DEF[UdrFmt.U_25_PRODUCT_CODE_26] = new UdrDef(UdrFmt.U_25_PRODUCT_CODE_26, false, 64, null);
		U_DEF[UdrFmt.U_26_MEMBER_CODE_27] = new UdrDef(UdrFmt.U_26_MEMBER_CODE_27, false, 32, null);
		U_DEF[UdrFmt.U_27_MEMBER_PROV_CODE_28] = new UdrDef(UdrFmt.U_27_MEMBER_PROV_CODE_28, false, 4, null); // false, 3, null
		U_DEF[UdrFmt.U_28_START_TIME_29] = new UdrDef(UdrFmt.U_28_START_TIME_29, false, 14, null);
		U_DEF[UdrFmt.U_29_END_TIME_30] = new UdrDef(UdrFmt.U_29_END_TIME_30, false, 14, null);
		U_DEF[UdrFmt.U_30_DURATION_31] = new UdrDef(UdrFmt.U_30_DURATION_31, true, 9, null);
		U_DEF[UdrFmt.U_31_VOLUME_UP_32] = new UdrDef(UdrFmt.U_31_VOLUME_UP_32, true, 18, null);
		U_DEF[UdrFmt.U_32_VOLUME_DOWN_33] = new UdrDef(UdrFmt.U_32_VOLUME_DOWN_33, true, 18, null);
		U_DEF[UdrFmt.U_33_TICKET_ID_34] = new UdrDef(UdrFmt.U_33_TICKET_ID_34, true, 16, null);
		U_DEF[UdrFmt.U_34_PP_FILE_ID_35] = new UdrDef(UdrFmt.U_34_PP_FILE_ID_35, true, 12, null);
		U_DEF[UdrFmt.U_35_ACCT_DAY_36] = new UdrDef(UdrFmt.U_35_ACCT_DAY_36, true, 8, null);
		U_DEF[UdrFmt.U_36_BIZ_TYPE_37] = new UdrDef(UdrFmt.U_36_BIZ_TYPE_37, false, 10, null);
		U_DEF[UdrFmt.U_37_PROCESS_FLAG_38] = new UdrDef(UdrFmt.U_37_PROCESS_FLAG_38, false, 10, null);
		U_DEF[UdrFmt.U_38_BILL_FLAG_39] = new UdrDef(UdrFmt.U_38_BILL_FLAG_39, true, 1, null);
		U_DEF[UdrFmt.U_39_ACCUMULATION_KEY_40] = new UdrDef(UdrFmt.U_39_ACCUMULATION_KEY_40, false, 64, null);
		U_DEF[UdrFmt.U_40_DUP_TIME_41] = new UdrDef(UdrFmt.U_40_DUP_TIME_41, false, 14, null);
		U_DEF[UdrFmt.U_41_EC_GROUP_42] = new UdrDef(UdrFmt.U_41_EC_GROUP_42, false, 2, null);
		U_DEF[UdrFmt.U_42_OFFER_CODE_43] = new UdrDef(UdrFmt.U_42_OFFER_CODE_43, false, 64, null);
		U_DEF[UdrFmt.U_43_OFFER_ORDER_ID_44] = new UdrDef(UdrFmt.U_43_OFFER_ORDER_ID_44, true, 12, null);
		U_DEF[UdrFmt.U_44_PRODUCT_ORDER_ID_45] = new UdrDef(UdrFmt.U_44_PRODUCT_ORDER_ID_45, true, 12, null);
		U_DEF[UdrFmt.U_45_PROD_ORDER_MODE_46] = new UdrDef(UdrFmt.U_45_PROD_ORDER_MODE_46, false, 32, null); // true, 2, null
		U_DEF[UdrFmt.U_46_RATE_BACK_ID_47] = new UdrDef(UdrFmt.U_46_RATE_BACK_ID_47, false, 256, null);
		U_DEF[UdrFmt.U_47_SUB_GROUP_NUM_48] = new UdrDef(UdrFmt.U_47_SUB_GROUP_NUM_48, false, 20, null);
		U_DEF[UdrFmt.U_48_PRODUCT_ORDER_ID2_49] = new UdrDef(UdrFmt.U_48_PRODUCT_ORDER_ID2_49, true, 12, null);
//		U_DEF[UdrFmt.U_49_IS_MEM_CP_50] = new UdrDef(UdrFmt.U_49_IS_MEM_CP_50, false, 16, null); // true, 2, null
		U_DEF[UdrFmt.U_49_IS_SUB_GROUP_FLAG_50] = new UdrDef(UdrFmt.U_49_IS_SUB_GROUP_FLAG_50, true, 1, null);
		U_DEF[UdrFmt.U_50_SERVICE_51] = new UdrDef(UdrFmt.U_50_SERVICE_51, false, 256, null);
		U_DEF[UdrFmt.U_51_SERVICE_OPTION_52] = new UdrDef(UdrFmt.U_51_SERVICE_OPTION_52, false, 64, null);
		U_DEF[UdrFmt.U_52_FREE_RESOURCE_53] = new UdrDef(UdrFmt.U_52_FREE_RESOURCE_53, false, 64, null);
//		U_DEF[UdrFmt.U_54_DISCOUNT_55] = new UdrDef(UdrFmt.U_54_DISCOUNT_55, false, 64, null);
//		U_DEF[UdrFmt.U_55_PRODUCT_OFFER_56] = new UdrDef(UdrFmt.U_55_PRODUCT_OFFER_56, false, 64, null);
		U_DEF[UdrFmt.U_53_QUATITLY_54] = new UdrDef(UdrFmt.U_53_QUATITLY_54, false, 64, null);
		U_DEF[UdrFmt.U_54_RES1_55] = new UdrDef(UdrFmt.U_54_RES1_55, false, 64, null);
		U_DEF[UdrFmt.U_55_RES2_56] = new UdrDef(UdrFmt.U_55_RES2_56, false, 64, null);
		U_DEF[UdrFmt.U_56_ATTACHEMENT_57] = new UdrDef(UdrFmt.U_56_ATTACHEMENT_57, false, 2048, null);
		// U_DEF[UdrFmt.U_47_RESERVE8_48] = new UdrDef(UdrFmt.U_47_RESERVE8_48, false, 64, null);
		// U_DEF[UdrFmt.U_48_SHPARM_VER_49] = new UdrDef(UdrFmt.U_48_SHPARM_VER_49, false, 10, null);
		// U_DEF[UdrFmt.U_49_STTL_FILE_ID_50] = new UdrDef(UdrFmt.U_49_STTL_FILE_ID_50, true, 18, null);
		// U_DEF[UdrFmt.U_50_STTL_RAW_FILE_ID_51] = new UdrDef(UdrFmt.U_50_STTL_RAW_FILE_ID_51, true, 18, null);
		// U_DEF[UdrFmt.U_51_STTL_RAW_LINE_NUM_52] = new UdrDef(UdrFmt.U_51_STTL_RAW_LINE_NUM_52, true, 9, null);
		// U_DEF[UdrFmt.U_52_FILE_TYPE_53] = new UdrDef(UdrFmt.U_52_FILE_TYPE_53, true, 2, null);
		// U_DEF[UdrFmt.U_53_ERCY_FLAG_54] = new UdrDef(UdrFmt.U_53_ERCY_FLAG_54, true, 1, null);
		// U_DEF[UdrFmt.U_54_ERCY_TM_55] = new UdrDef(UdrFmt.U_54_ERCY_TM_55, false, 14, null);
		// U_DEF[UdrFmt.U_55_RCV_TM_56] = new UdrDef(UdrFmt.U_55_RCV_TM_56, false, 14, null);
		// U_DEF[UdrFmt.U_56_FILE_YMD_57] = new UdrDef(UdrFmt.U_56_FILE_YMD_57, true, 8, null);
		// U_DEF[UdrFmt.U_57_STTL_DUR_58] = new UdrDef(UdrFmt.U_57_STTL_DUR_58, true, 9, "0");
		// U_DEF[UdrFmt.U_58_STTL_VOL_59] = new UdrDef(UdrFmt.U_58_STTL_VOL_59, true, 18, "0");
		// U_DEF[UdrFmt.U_59_STTL_FEE_60] = new UdrDef(UdrFmt.U_59_STTL_FEE_60, true, 12, "0");
		// U_DEF[UdrFmt.U_60_SERV_FEE_61] = new UdrDef(UdrFmt.U_60_SERV_FEE_61, true, 12, "0");
		// U_DEF[UdrFmt.U_61_MISC_FEE_62] = new UdrDef(UdrFmt.U_61_MISC_FEE_62, true, 12, "0");
		// U_DEF[UdrFmt.U_62_VATX_63] = new UdrDef(UdrFmt.U_62_VATX_63, true, 12, "0");
		// U_DEF[UdrFmt.U_63_ACNT_YM_64] = new UdrDef(UdrFmt.U_63_ACNT_YM_64, true, 6, null);
		// U_DEF[UdrFmt.U_64_PART_DD_65] = new UdrDef(UdrFmt.U_64_PART_DD_65, true, 2, null);
		// U_DEF[UdrFmt.U_65_SPARE1_66] = new UdrDef(UdrFmt.U_65_SPARE1_66, false, 64, null);
		// U_DEF[UdrFmt.U_66_SPARE2_67] = new UdrDef(UdrFmt.U_66_SPARE2_67, false, 64, null);
		// U_DEF[UdrFmt.U_67_SPARE3_68] = new UdrDef(UdrFmt.U_67_SPARE3_68, false, 64, null);
		// U_DEF[UdrFmt.U_68_SPARE4_69] = new UdrDef(UdrFmt.U_68_SPARE4_69, false, 64, null);
		// U_DEF[UdrFmt.U_69_SPARE5_70] = new UdrDef(UdrFmt.U_69_SPARE5_70, false, 64, null);
	}

	private static void _InitS() {
		S_DEF = new UdrDef[UdrFmt.S_FIELD_CNT_40];
		S_DEF[UdrFmt.S_00_ERROR_CODE_01] = new UdrDef(UdrFmt.S_00_ERROR_CODE_01, false, 4, null);
		S_DEF[UdrFmt.S_01_ERROR_LINE_02] = new UdrDef(UdrFmt.S_01_ERROR_LINE_02, true, 10, null);
		S_DEF[UdrFmt.S_02_BIZ_TYPE_03] = new UdrDef(UdrFmt.S_02_BIZ_TYPE_03, false, 10, null);
		S_DEF[UdrFmt.S_03_DATA_SOURCE_04] = new UdrDef(UdrFmt.S_03_DATA_SOURCE_04, true, 10, null);
		S_DEF[UdrFmt.S_04_STREAM_ID_05] = new UdrDef(UdrFmt.S_04_STREAM_ID_05, false, 18, null);
		S_DEF[UdrFmt.S_05_EC_CODE_06] = new UdrDef(UdrFmt.S_05_EC_CODE_06, false, 32, null);
		S_DEF[UdrFmt.S_06_EC_PROV_CODE_07] = new UdrDef(UdrFmt.S_06_EC_PROV_CODE_07, false, 3, null);
		S_DEF[UdrFmt.S_07_OFFER_CODE_08] = new UdrDef(UdrFmt.S_07_OFFER_CODE_08, false, 64, null);
		S_DEF[UdrFmt.S_08_PRODUCT_CODE_09] = new UdrDef(UdrFmt.S_08_PRODUCT_CODE_09, false, 64, null);
		S_DEF[UdrFmt.S_09_OFFER_ORDER_ID_10] = new UdrDef(UdrFmt.S_09_OFFER_ORDER_ID_10, false, 64, null);
		S_DEF[UdrFmt.S_10_PRODUCT_ORDER_ID_11] = new UdrDef(UdrFmt.S_10_PRODUCT_ORDER_ID_11, false, 64, null);
		S_DEF[UdrFmt.S_11_ORDER_PROV_12] = new UdrDef(UdrFmt.S_11_ORDER_PROV_12, false, 3, null);
		S_DEF[UdrFmt.S_12_ACCOUNT_ID_13] = new UdrDef(UdrFmt.S_12_ACCOUNT_ID_13, false, 32, null);
		S_DEF[UdrFmt.S_13_MEM_NUMBER_14] = new UdrDef(UdrFmt.S_13_MEM_NUMBER_14, false, 64, null);
		S_DEF[UdrFmt.S_14_MEM_PROV_15] = new UdrDef(UdrFmt.S_14_MEM_PROV_15, false, 3, null);
		S_DEF[UdrFmt.S_15_ORDER_MODE_16] = new UdrDef(UdrFmt.S_15_ORDER_MODE_16, false, 10, null);
		S_DEF[UdrFmt.S_16_SIGN_ENTITY_17] = new UdrDef(UdrFmt.S_16_SIGN_ENTITY_17, false, 64, null);
		S_DEF[UdrFmt.S_17_CHARGE_ITEM_18] = new UdrDef(UdrFmt.S_17_CHARGE_ITEM_18, false, 32, null);
		S_DEF[UdrFmt.S_18_CHARGE_19] = new UdrDef(UdrFmt.S_18_CHARGE_19, true, 18, null);
		S_DEF[UdrFmt.S_19_AMOUNT_NOTAX_20] = new UdrDef(UdrFmt.S_19_AMOUNT_NOTAX_20, true, 18, null);
		S_DEF[UdrFmt.S_20_AMOUNT_TAX_21] = new UdrDef(UdrFmt.S_20_AMOUNT_TAX_21, true, 18, null);
		S_DEF[UdrFmt.S_21_TAX_RATE_22] = new UdrDef(UdrFmt.S_21_TAX_RATE_22, false, 32, null);
		S_DEF[UdrFmt.S_22_ORG_MONTH_23] = new UdrDef(UdrFmt.S_22_ORG_MONTH_23, false, 14, null);
		S_DEF[UdrFmt.S_23_PAID_MONTH_24] = new UdrDef(UdrFmt.S_23_PAID_MONTH_24, false, 14, null);
		S_DEF[UdrFmt.S_24_TICKET_ID_25] = new UdrDef(UdrFmt.S_24_TICKET_ID_25, false, 32, null);
		S_DEF[UdrFmt.S_25_SETTLE_MONTH_26] = new UdrDef(UdrFmt.S_25_SETTLE_MONTH_26, false, 14, null);
		S_DEF[UdrFmt.S_26_OUT_OBJECT_27] = new UdrDef(UdrFmt.S_26_OUT_OBJECT_27, false, 64, null);
		S_DEF[UdrFmt.S_27_IN_OBJECT_28] = new UdrDef(UdrFmt.S_27_IN_OBJECT_28, false, 64, null);
		S_DEF[UdrFmt.S_28_RECORD_ID_29] = new UdrDef(UdrFmt.S_28_RECORD_ID_29, false, 32, null);
		S_DEF[UdrFmt.S_29_SETTLE_NOTAX_30] = new UdrDef(UdrFmt.S_29_SETTLE_NOTAX_30, true, 18, null);
		S_DEF[UdrFmt.S_30_SETTLE_TAX_31] = new UdrDef(UdrFmt.S_30_SETTLE_TAX_31, true, 18, null);
		S_DEF[UdrFmt.S_31_FILE_ID_32] = new UdrDef(UdrFmt.S_31_FILE_ID_32, false, 32, null);
		S_DEF[UdrFmt.S_32_RULE_ID_33] = new UdrDef(UdrFmt.S_32_RULE_ID_33, false, 32, null);
		S_DEF[UdrFmt.S_33_DEST_SOURCE_34] = new UdrDef(UdrFmt.S_33_DEST_SOURCE_34, false, 32, null);
		S_DEF[UdrFmt.S_34_PHASE_35] = new UdrDef(UdrFmt.S_34_PHASE_35, false, 32, null);
		S_DEF[UdrFmt.S_35_START_TIME_36] = new UdrDef(UdrFmt.S_35_START_TIME_36, false, 14, null);
		S_DEF[UdrFmt.S_36_RES1_37] = new UdrDef(UdrFmt.S_36_RES1_37, false, 64, null);
		S_DEF[UdrFmt.S_37_RES2_38] = new UdrDef(UdrFmt.S_37_RES2_38, false, 64, null);
		S_DEF[UdrFmt.S_38_RES3_39] = new UdrDef(UdrFmt.S_38_RES3_39, false, 64, null);
		S_DEF[UdrFmt.S_39_ATTACHMENT_40] = new UdrDef(UdrFmt.S_39_ATTACHMENT_40, false, 2048, null);
	}

	private static void _InitSNm2IdxMap() {
		S_NM2IDX_MAP = new HashMap<String, Integer>();
		S_NM2IDX_MAP.put(UdrFmt.S_NM_ERROR_CODE_01, UdrFmt.S_00_ERROR_CODE_01);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_ERROR_LINE_02, UdrFmt.S_01_ERROR_LINE_02);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_BIZ_TYPE_03, UdrFmt.S_02_BIZ_TYPE_03);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_DATA_SOURCE_04, UdrFmt.S_03_DATA_SOURCE_04);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_STREAM_ID_05, UdrFmt.S_04_STREAM_ID_05);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_EC_CODE_06, UdrFmt.S_05_EC_CODE_06);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_EC_PROV_CODE_07, UdrFmt.S_06_EC_PROV_CODE_07);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_OFFER_CODE_08, UdrFmt.S_07_OFFER_CODE_08);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_PRODUCT_CODE_09, UdrFmt.S_08_PRODUCT_CODE_09);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_OFFER_ORDER_ID_10, UdrFmt.S_09_OFFER_ORDER_ID_10);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_PRODUCT_ORDER_ID_11, UdrFmt.S_10_PRODUCT_ORDER_ID_11);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_ORDER_PROV_12, UdrFmt.S_11_ORDER_PROV_12);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_ACCOUNT_ID_13, UdrFmt.S_12_ACCOUNT_ID_13);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_MEM_NUMBER_14, UdrFmt.S_13_MEM_NUMBER_14);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_MEM_PROV_15, UdrFmt.S_14_MEM_PROV_15);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_ORDER_MODE_16, UdrFmt.S_15_ORDER_MODE_16);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_SIGN_ENTITY_17, UdrFmt.S_16_SIGN_ENTITY_17);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_CHARGE_ITEM_18, UdrFmt.S_17_CHARGE_ITEM_18);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_CHARGE_19, UdrFmt.S_18_CHARGE_19);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_AMOUNT_NOTAX_20, UdrFmt.S_19_AMOUNT_NOTAX_20);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_AMOUNT_TAX_21, UdrFmt.S_20_AMOUNT_TAX_21);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_TAX_RATE_22, UdrFmt.S_21_TAX_RATE_22);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_ORG_MONTH_23, UdrFmt.S_22_ORG_MONTH_23);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_PAID_MONTH_24, UdrFmt.S_23_PAID_MONTH_24);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_TICKET_ID_25, UdrFmt.S_24_TICKET_ID_25);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_SETTLE_MONTH_26, UdrFmt.S_25_SETTLE_MONTH_26);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_OUT_OBJECT_27, UdrFmt.S_26_OUT_OBJECT_27);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_IN_OBJECT_28, UdrFmt.S_27_IN_OBJECT_28);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_RECORD_ID_29, UdrFmt.S_28_RECORD_ID_29);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_SETTLE_NOTAX_30, UdrFmt.S_29_SETTLE_NOTAX_30);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_SETTLE_TAX_31, UdrFmt.S_30_SETTLE_TAX_31);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_FILE_ID_32, UdrFmt.S_31_FILE_ID_32);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_RULE_ID_33, UdrFmt.S_32_RULE_ID_33);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_DEST_SOURCE_34, UdrFmt.S_33_DEST_SOURCE_34);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_PHASE_35, UdrFmt.S_34_PHASE_35);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_START_TIME_36, UdrFmt.S_35_START_TIME_36);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_RES1_37, UdrFmt.S_36_RES1_37);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_RES2_38, UdrFmt.S_37_RES2_38);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_RES3_39, UdrFmt.S_38_RES3_39);
		S_NM2IDX_MAP.put(UdrFmt.S_NM_ATTACHMENT_40, UdrFmt.S_39_ATTACHMENT_40);
	}
}
