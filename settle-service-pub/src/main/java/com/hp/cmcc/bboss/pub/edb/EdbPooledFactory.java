package com.hp.cmcc.bboss.pub.edb;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EdbPooledFactory extends BasePooledObjectFactory<Edb> {
	private static Logger L = LoggerFactory.getLogger(EdbPooledFactory.class);
	private String _edbPath;

	public EdbPooledFactory(String edb_path) {
		_edbPath = edb_path;
	}

	public String getEdbPath() {
		return _edbPath;
	}

	@Override
	public Edb create() throws Exception {
		Edb edb_ = new Edb(_edbPath, false);
		L.trace("edb {} created, {}", _edbPath, edb_);
		return edb_;
	}

	@Override
	public void destroyObject(PooledObject<Edb> edb) {
		L.trace("destory edb {}, {}", edb.getObject().getEdbPath(), edb.getObject());
		edb.getObject().destroy();
	}

	public boolean validateObject(PooledObject<Edb> edb) {
		return edb.getObject().isConnected();
	}

	@Override
	public PooledObject<Edb> wrap(Edb edb) {
		return new DefaultPooledObject<Edb>(edb);
	}
}
