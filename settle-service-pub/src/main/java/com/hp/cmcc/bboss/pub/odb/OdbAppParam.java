package com.hp.cmcc.bboss.pub.odb;

import java.io.File;
import java.nio.charset.Charset;
import java.nio.charset.UnsupportedCharsetException;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class OdbAppParam {
	private static Logger L = LoggerFactory.getLogger(OdbAppParam.class);
	private static final String _Q_APP_PARAM = "SELECT SECTION, PARAM_KEY, PARAM_VAL FROM APP_PARAM WHERE MODULE = ? "
			+ "AND INST_NM = ? ORDER BY SECTION, PARAM_KEY, PARAM_VAL";
	private String _module;
	private String _instNm;
	public Map<String, TreeMap<String, String>> _paramMap = new TreeMap<String, TreeMap<String, String>>();

	public boolean refresh(String module, String inst_nm) {
		_module = module;
		_instNm = inst_nm;
		List<Object[]> q_result_ = OdbAgt.GetBizInstance().queryForTsList(_Q_APP_PARAM, module, inst_nm);
		if (q_result_ == null) {
			L.warn("refresh app_param for [{},{}] failed", module, inst_nm);
			return false;
		}
		if (_paramMap == null) {
			_paramMap = new TreeMap<String, TreeMap<String, String>>();
		} else {
			_paramMap.clear();
		}
		for (Object[] row_ : q_result_) {
			String section_ = (String) row_[0];
			String key_ = (String) row_[1];
			String value_ = (String) row_[2];
			TreeMap<String, String> sec_map_ = _paramMap.get(section_);
			if (sec_map_ == null) {
				sec_map_ = new TreeMap<String, String>();
				_paramMap.put(section_, sec_map_);
			}
			sec_map_.put(key_, value_);
		}
		L.info("{} rows fetched from app_param for [{},{}]", new Object[] { q_result_.size(), module, inst_nm });
		return true;
	}

	public String getValByKeyInSection(String section, String key) {
		TreeMap<String, String> sec_ = _paramMap.get(section);
		if (sec_ == null) {
			L.warn("section [{}] not exists for [{},{}] in app_param", new Object[] { section, _module, _instNm });
			return null;
		}
		String val_ = sec_.get(key);
		if (val_ == null) {
			L.warn("key [{}] not exists in section [{}] for [{},{}] in app_param", new Object[] { key, section, _module, _instNm });
			return null;
		}
		return val_;
	}

	public boolean subValByEnv() {
		boolean rc_ = true;
		TreeMap<String, String> sec_ = null;
		for (String sec_nm_ : _paramMap.keySet()) {
			sec_ = _paramMap.get(sec_nm_);
			for (String key_ : sec_.keySet()) {
				String raw_val_ = sec_.get(key_);
				String sub_val_ = PubMethod.SubStrByEnv(raw_val_);
				if (sub_val_ == null) {
					L.warn("[{}]-->[{}]=[{}], sub env failed", new Object[] { sec_nm_, key_, raw_val_ });
					rc_ = false;
				} else if (!raw_val_.equals(sub_val_)) {
					sec_.put(key_, sub_val_);
				}
			}
		}
		return rc_;
	}

	public String chkValStr(String section, String key, String pattern) {
		String val_ = getValByKeyInSection(section, key);
		if (val_ == null) {
			L.warn("{}==>{} not set", section, key);
			return null;
		}
		if (pattern != null) {
			Pattern p = Pattern.compile(pattern);
			Matcher m = p.matcher(val_);
			if (!m.find()) {
				L.warn("{}==>{} [{}] not match [{}]", new Object[] { section, key, val_, pattern });
				return null;
			}
		}
		return val_;
	}

	public Pattern chkValPattern(String section, String key, Integer flags) {
		String val_ = getValByKeyInSection(section, key);
		if (val_ == null) {
			L.warn("{}==>{} not set", section, key);
			return null;
		}
		Pattern p = null;
		try {
			if (flags == null) {
				p = Pattern.compile(val_);
			} else {
				p = Pattern.compile(val_, flags);
			}
		} catch (Exception e) {
			L.warn("invalid pattern [{}]", val_, e);
			p = null;
		}
		return p;
	}

	public Long chkValNum(String section, String key, Long val_min, Long val_max) {
		String val_ = getValByKeyInSection(section, key);
		if (val_ == null) {
			L.warn("{}==>{} not set", section, key);
			return null;
		}
		Long lval_ = Long.parseLong(val_);
		if (val_min != null && val_min != Long.MIN_VALUE) {
			if (lval_ < val_min) {
				L.warn("{}==>{} {} lt val_min {}", new Object[] { section, key, lval_, val_min });
				return null;
			}
		}
		if (val_max != null && val_max != Long.MAX_VALUE) {
			if (lval_ > val_max) {
				L.warn("{}==>{} {} gt val_max {}", new Object[] { section, key, lval_, val_max });
				return null;
			}
		}
		return lval_;
	}

	public Double chkValDouble(String section, String key, Double val_min, Double val_max) {
		String val_ = getValByKeyInSection(section, key);
		if (val_ == null) {
			L.warn("{}==>{} not set", section, key);
			return null;
		}
		Double dval_ = Double.parseDouble(val_);
		if (val_min != null && val_min != Double.NaN && val_min != Double.NEGATIVE_INFINITY) {
			if (dval_ < val_min) {
				L.warn("{}==>{} {} lt val_min {}", new Object[] { section, key, dval_, val_min });
				return null;
			}
		}
		if (val_max != null && val_max != Double.NaN && val_max != Double.POSITIVE_INFINITY) {
			if (dval_ > val_max) {
				L.warn("{}==>{} {} gt val_max {}", new Object[] { section, key, dval_, val_max });
				return null;
			}
		}
		return dval_;
	}

	public File chkValFile(String section, String key, boolean must_exists, boolean is_dir, boolean must_executable) {
		String val_ = getValByKeyInSection(section, key);
		if (val_ == null) {
			L.warn("{}==>{} not set", section, key);
			return null;
		}
		File f = new File(val_);
		if (!must_exists) {
			return f;
		}
		if (is_dir) {
			if (!f.isDirectory()) {
				L.warn("{}==>{} dir [{}] not exists", new Object[] { section, key, val_ });
				return null;
			}
		} else if (!f.isFile()) {
			L.warn("{}==>{} file [{}] not exists", new Object[] { section, key, val_ });
			return null;
		} else if (must_executable && !f.canExecute()) {
			L.warn("{}==>{} file [{}] not executable", new Object[] { section, key, val_ });
		}
		return f;
	}

	public File chkValScript(String section, String key, List<String> script_args) {
		String val_ = getValByKeyInSection(section, key);
		if (val_ == null) {
			L.warn("{}==>{} not set", section, key);
			return null;
		}
		script_args.clear();
		String[] argv_ = val_.split("(?<!\\\\) "); // prevent path & args with blank/space
		String script_ = argv_[0].replaceAll("\\\\ ", " ");
		File f = new File(script_);
		if (!f.isFile()) {
			L.warn("{}==>{} file [{}] not exists", section, key, argv_[0]);
			return null;
		}
		if (!f.canExecute()) {
			L.warn("{}==>{} file [{}] not executable", section, key, argv_[0]);
			return null;
		}
		for (int i = 0; i < argv_.length; i++) {
			script_args.add(argv_[i]);
		}
		return f;
	}

	public Charset chkValCharset(String section, String key) {
		String val_ = getValByKeyInSection(section, key);
		if (val_ == null) {
			L.warn("{}==>{} not set", section, key);
			return null;
		}
		try {
			Charset charset_ = Charset.forName(val_);
			return charset_;
		} catch (UnsupportedCharsetException e) {
			L.warn("{}==>{} charset [{}] not supported", new Object[] { section, key, val_ }, e);
			return null;
		}
	}

	public String toString() {
		int i = 0;
		StringBuilder sb_ = new StringBuilder();
		TreeMap<String, String> sec_ = null;
		for (String sec_nm_ : _paramMap.keySet()) {
			sec_ = _paramMap.get(sec_nm_);
			for (String key_ : sec_.keySet()) {
				String val_ = sec_.get(key_);
				sb_.append(String.format("%-4d: [%s]|[%s]|[%s]%n", ++i, sec_nm_, key_, val_));
			}
		}
		sb_.append(String.format("Total %d sections %d records for [%s,%s]", _paramMap.size(), i, _module, _instNm));
		return sb_.substring(0);
	}
}
