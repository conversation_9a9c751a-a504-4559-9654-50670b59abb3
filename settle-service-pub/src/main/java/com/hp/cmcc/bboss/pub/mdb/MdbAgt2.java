package com.hp.cmcc.bboss.pub.mdb;

import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MdbAgt2 {
	private static Logger L = LoggerFactory.getLogger(MdbAgt2.class);
	private static MdbCli2 _MdbCliShparmInstance;
	private static MdbCli2 _MdbCliBizlogInstance;
	private static MdbCli2 _MdbCliDupchkInstance;
	private static OdbSystemParam _SystemParam;

	private MdbAgt2() {
		// do nothing explicitlly
	}

	public static MdbCli2 GetShparmInstance() {
		return _MdbCliShparmInstance;
	}

	public static MdbCli2 GetBizlogInstance() {
		return _MdbCliBizlogInstance;
	}

	public static MdbCli2 GetDupchkInstance() {
		return _MdbCliDupchkInstance;
	}

	public static boolean Init(OdbSystemParam system_param, int pool_max_shparm, int pool_max_bizlog, int pool_max_dupchk) {
		if (system_param == null) {
			L.warn("sttl_system_param can not be null, init failed");
			return false;
		}
		_SystemParam = system_param;
		boolean rc_ = true;
		if (pool_max_shparm > 0) {
			_MdbCliShparmInstance = new MdbCli2("shparm", _SystemParam._redisShparmCluster, _SystemParam._redisShparmAuth,
					_SystemParam._redisShparmTimeout, pool_max_shparm);
			if (!_MdbCliShparmInstance.init()) {
				L.warn("init MdbCli shparm error");
				rc_ = false;
			}
		}
		if (pool_max_bizlog > 0) {
			_MdbCliBizlogInstance = new MdbCli2("bizlog", _SystemParam._redisBizlogCluster, _SystemParam._redisBizlogAuth,
					_SystemParam._redisBizlogTimeout, pool_max_bizlog);
			if (!_MdbCliBizlogInstance.init()) {
				L.warn("init MdbCli bizlog error");
				rc_ = false;
			}
		}
		if (pool_max_dupchk > 0) {
			_MdbCliDupchkInstance = new MdbCli2("dupchk", _SystemParam._redisDupchkCluster, _SystemParam._redisDupchkAuth,
					_SystemParam._redisDupchkTimeout, pool_max_dupchk);
			if (!_MdbCliDupchkInstance.init()) {
				L.warn("init MdbCli dupchk error");
				rc_ = false;
			}
		}
		return rc_;
	}
}
