package com.hp.cmcc.bboss.pub.util;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CfgFile {
	public static final String _DftSection = "_DUMMY_";
	private static Logger L = LoggerFactory.getLogger(CfgFile.class);
	private static Pattern _ExpTailComments = Pattern.compile("^.*\\s+##");
	private static Pattern _ExpComments = Pattern.compile("^#");
	private static Pattern _ExpEmptyLine = Pattern.compile("^\\s*$");
	private static Pattern _ExpSection = Pattern.compile("^\\s*\\[.+\\]");
	private static Pattern _ExpItem = Pattern.compile(".+=");
	private String _cfgFnm = "";
	private String _curSection = _DftSection;
	public TreeMap<String, TreeMap<String, String>> _cfg = new TreeMap<String, TreeMap<String, String>>();

	public void trace() {
		L.debug("trace CfgFile [{}] begin, {} sections", _cfgFnm, _cfg.size());
		int tot_cnt_ = 0;
		int sec_cnt_ = 0;
		int key_cnt_ = 0;
		TreeMap<String, String> sec_ = null;
		for (String sec_nm_ : _cfg.keySet()) {
			sec_ = _cfg.get(sec_nm_);
			key_cnt_ = 0;
			L.debug(" sec_cnt_={}, [{}], {} items", new Object[] { ++sec_cnt_, sec_nm_, sec_.size() });
			for (String key_ : sec_.keySet()) {
				++tot_cnt_;
				L.debug(String.format("%4d:[%s]-->[%s]=[%s]", ++key_cnt_, sec_nm_, key_, sec_.get(key_)));
			}
		}
		L.debug("trace CfgFile [{}] end, {} items total", _cfgFnm, tot_cnt_);
	}
	
	public List<String> getSectionListByPattern(String regex) {
		List<String> result_ = new ArrayList<String>();
		for (String k : _cfg.keySet()) {
			if (k.matches(regex)) {
				result_.add(k);
			}
		}
		return result_;
	}

	public String getValByKeyInSection(String section, String key) {
		TreeMap<String, String> sec_ = _cfg.get(section);
		if (sec_ == null) {
			L.warn("section [{}] not exists in [{}]", section, _cfgFnm);
			return null;
		}
		String val_ = sec_.get(key);
		if (val_ == null) {
			L.warn("key [{}] not exists in section [{}] of [{}]", new Object[] { key, section, _cfgFnm });
			return null;
		}
		return val_;
	}

	public boolean subValByEnv() {
		boolean rc_ = true;
		TreeMap<String, String> sec_ = null;
		for (String sec_nm_ : _cfg.keySet()) {
			sec_ = _cfg.get(sec_nm_);
			for (String key_ : sec_.keySet()) {
				String raw_val_ = sec_.get(key_);
				String sub_val_ = PubMethod.SubStrByEnv(raw_val_);
				if (sub_val_ == null) {
					L.warn("[{}]-->[{}]=[{}], sub env failed", new Object[] { sec_nm_, key_, raw_val_ });
					rc_ = false;
				} else if (!raw_val_.equals(sub_val_)) {
					sec_.put(key_, sub_val_);
				}
			}
		}
		return rc_;
	}

	public boolean init(String cfg_fnm) {
		try {
			BufferedReader br_ = new BufferedReader(new FileReader(cfg_fnm));
			L.info("[{}] opened for parsing", cfg_fnm);
			_cfgFnm = cfg_fnm;
			_cfg.clear();
			_curSection = _DftSection;
			_cfg.put(_curSection, new TreeMap<String, String>());
			String raw_line_;
			int line_cnt_ = 0;
			while ((raw_line_ = br_.readLine()) != null) {
				_parseRawLine(raw_line_, ++line_cnt_);
			}
			br_.close();
		} catch (IOException e) {
			L.error(String.format("read [%s] error", cfg_fnm), e);
			return false;
		}
		L.info("[{}] parsed, {} sections", cfg_fnm, _cfg.size());
		return true;
	}
	
	public String getCfgFnm() {
		return _cfgFnm;
	}

	private void _parseRawLine(String raw_line, int line_cnt) {
		int m_start_;
		int m_end_;
		Matcher m = _ExpTailComments.matcher(raw_line);
		if (m.find()) {
			m_end_ = m.end();
			raw_line = raw_line.substring(0, m_end_ - 2);
		}

		m = _ExpComments.matcher(raw_line);
		if (m.find()) {
			return;
		}

		m = _ExpEmptyLine.matcher(raw_line);
		if (m.find()) {
			return;
		}

		m = _ExpSection.matcher(raw_line);
		if (m.find()) {
			m_start_ = raw_line.indexOf('[');
			m_end_ = raw_line.indexOf(']');
			String sec_ = (raw_line.substring(m_start_ + 1, m_end_)).toUpperCase().trim();
			if (_cfg.containsKey(sec_)) {
				L.warn("dup section name [{}], line_cnt {}, ignore", sec_, line_cnt);
			} else {
				_cfg.put(sec_, new TreeMap<String, String>());
				_curSection = sec_;
			}
			return;
		}

		m = _ExpItem.matcher(raw_line);
		if (m.find()) {
			m_start_ = raw_line.indexOf('=');
			String key_ = raw_line.substring(0, m_start_).toUpperCase().trim();
			String val_ = raw_line.substring(m_start_ + 1).trim();
			val_ = _parseValDQ(val_);
			TreeMap<String, String> sec_ = _cfg.get(_curSection);
			if (sec_ == null) {
				L.warn("section [{}] not initialized, line_cnt {}", _curSection, line_cnt);
			} else {
				sec_.put(key_, val_);
			}
			return;
		}
		L.warn("unrecognized, line_cnt {}, [{}] skip", line_cnt, raw_line);
	}

	private String _parseValDQ(String item_val) { // DQ: double quoted
		if (item_val.length() <= 2) {
			return item_val;
		}
		if (item_val.startsWith("\"") && item_val.endsWith("\"")) {
			L.debug("item_val [{}] is double quoted, extract it", item_val);
			return item_val.substring(1, item_val.length() - 1);
		} else {
			return item_val;
		}
	}
}
