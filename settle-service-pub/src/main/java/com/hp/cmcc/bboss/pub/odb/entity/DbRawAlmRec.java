package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbRawAlmRec extends GsonObj {
	public static final String ALM_LEVEL_UKN = "UKN";
	public static final String ALM_LEVEL_DBG = "DBG";
	public static final String ALM_LEVEL_INF = "INF";
	public static final String ALM_LEVEL_WRN = "WRN";
	public static final String ALM_LEVEL_ERO = "ERO";

	private Integer mmdd;
	private String module;
	private String inst_nm;
	private String host_nm;
	private Integer pid;
	private Long alm_ts;
	private Timestamp alm_tm;
	private String alm_level;
	private Integer alm_code;
	private String alm_msg;
	private String alm_json;
	private String alm_kpi;

	public DbRawAlmRec(Integer alm_code, String alm_msg, String alm_json) {
		int level_digit_ = (alm_code / 100000) % 10;
		switch (level_digit_) {
		case 3:
			this.alm_level = ALM_LEVEL_WRN;
			break;
		case 4:
			this.alm_level = ALM_LEVEL_ERO;
			break;
		case 2:
			this.alm_level = ALM_LEVEL_INF;
			break;
		case 1:
			this.alm_level = ALM_LEVEL_DBG;
			break;
		default:
			this.alm_level = ALM_LEVEL_UKN;
			break;
		}
		this.alm_code = alm_code;
		this.alm_msg = alm_msg;
		this.alm_json = alm_json;
	}

	public Object[] asInsertObjArray() {
		Object[] o = new Object[12];
		o[0] = mmdd;
		o[1] = module;
		o[2] = inst_nm;
		o[3] = host_nm;
		o[4] = pid;
		o[5] = alm_ts;
		o[6] = alm_tm;
		o[7] = alm_level;
		o[8] = alm_code;
		o[9] = alm_msg;
		o[10] = alm_json;
		o[11] = alm_kpi;
		return o;
	}

	public Integer getMmdd() {
		return mmdd;
	}

	public void setMmdd(Integer mmdd) {
		this.mmdd = mmdd;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getHost_nm() {
		return host_nm;
	}

	public void setHost_nm(String host_nm) {
		this.host_nm = host_nm;
	}

	public Integer getPid() {
		return pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	public Long getAlm_ts() {
		return alm_ts;
	}

	public void setAlm_ts(Long alm_ts) {
		this.alm_ts = alm_ts;
	}

	public Timestamp getAlm_tm() {
		return alm_tm;
	}

	public void setAlm_tm(Timestamp alm_tm) {
		this.alm_tm = alm_tm;
	}

	public String getAlm_level() {
		return alm_level;
	}

	public void setAlm_level(String alm_level) {
		this.alm_level = alm_level;
	}

	public Integer getAlm_code() {
		return alm_code;
	}

	public void setAlm_code(Integer alm_code) {
		this.alm_code = alm_code;
	}

	public String getAlm_msg() {
		return alm_msg;
	}

	public void setAlm_msg(String alm_msg) {
		this.alm_msg = alm_msg;
	}

	public String getAlm_json() {
		return alm_json;
	}

	public void setAlm_json(String alm_json) {
		this.alm_json = alm_json;
	}

	public String getAlm_kpi() {
		return alm_kpi;
	}

	public void setAlm_kpi(String alm_kpi) {
		this.alm_kpi = alm_kpi;
	}
}
