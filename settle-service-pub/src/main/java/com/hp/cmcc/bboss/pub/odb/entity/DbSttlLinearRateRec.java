package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbSttlLinearRateRec extends GsonObj {
	public static final int K_0_VER = 0;
	public static final int K_1_STLNRT = 1;
	public static final int K_2_BIZ_TYPE = 2;
	public static final int K_3_OFFER_CODE = 3;
	public static final int K_4_PRODUCT_CODE = 4;
	public static final int K_5_EC_CODE = 5;
	public static final int K_6_AUX_KEY = 6;

	public static final int V_00_EFF_DATE = 0;
	public static final int V_01_EXP_DATE = 1;
	public static final int V_02_RATE_OBJ = 2;
	public static final int V_03_RATE_UNIT = 3;
	public static final int V_04_RATE = 4;
	public static final int V_05_RATE_ID = 5;
	public static final int V_06_BIZ_TYPE = 6;
	public static final int V_07_OFFER_CODE = 7;
	public static final int V_08_PRODUCT_CODE = 8;
	public static final int V_09_EC_CODE = 9;
	public static final int V_10_AUX_KEY = 10;

	private Integer rate_id;
	private String biz_type;
	private String offer_code;
	private String product_code;
	private String ec_code;
	private String aux_key;
	private Integer rate_obj;
	private Integer rate_unit;
	private Integer rate;
	private Timestamp eff_date;
	private Timestamp exp_date;
	private String memo;

	public Integer getRate_id() {
		return rate_id;
	}

	public void setRate_id(Integer rate_id) {
		this.rate_id = rate_id;
	}

	public String getBiz_type() {
		return biz_type;
	}

	public void setBiz_type(String biz_type) {
		this.biz_type = biz_type;
	}

	public String getOffer_code() {
		return offer_code;
	}

	public void setOffer_code(String offer_code) {
		this.offer_code = offer_code;
	}

	public String getProduct_code() {
		return product_code;
	}

	public void setProduct_code(String product_code) {
		this.product_code = product_code;
	}

	public String getEc_code() {
		return ec_code;
	}

	public void setEc_code(String ec_code) {
		this.ec_code = ec_code;
	}

	public String getAux_key() {
		return aux_key;
	}

	public void setAux_key(String aux_key) {
		this.aux_key = aux_key;
	}

	public Integer getRate_obj() {
		return rate_obj;
	}

	public void setRate_obj(Integer rate_obj) {
		this.rate_obj = rate_obj;
	}

	public Integer getRate_unit() {
		return rate_unit;
	}

	public void setRate_unit(Integer rate_unit) {
		this.rate_unit = rate_unit;
	}

	public Integer getRate() {
		return rate;
	}

	public void setRate(Integer rate) {
		this.rate = rate;
	}

	public Timestamp getEff_date() {
		return eff_date;
	}

	public void setEff_date(Timestamp eff_date) {
		this.eff_date = eff_date;
	}

	public Timestamp getExp_date() {
		return exp_date;
	}

	public void setExp_date(Timestamp exp_date) {
		this.exp_date = exp_date;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
}
