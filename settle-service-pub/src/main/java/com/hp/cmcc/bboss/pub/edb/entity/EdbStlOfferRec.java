package com.hp.cmcc.bboss.pub.edb.entity;

import java.io.Serializable;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbStlOfferRec extends GsonObj implements Serializable, Effectivable {
	private static final long serialVersionUID = 820229733325215172L;

	public static final int DATA_SOURCE_1_RECEIVEABLE = 1;
	public static final int DATA_SOURCE_2_RECEIVED = 2;
	public static final int DATA_SOURCE_3_EC_RECEIVEABLE = 3;
	public static final int DATA_SOURCE_4_EC_RECEIVED = 4;
	public static final int DATA_SOURCE_5_BASE_CP = 5;

	public static final String ROUTE_CODE_0_LANDING = "0";
	public static final String ROUTE_CODE_3_EC_RECEIVEABLE = "3";
	public static final String ROUTE_CODE_4_EC_RECEIVED = "4";

	public static final String DEST_SOURCE_0_NORMAL = "0";
	public static final String DEST_SOURCE_1_USER_DEFINED = "1";

	public Long _id;
	public Integer _dataSource;
	public String _offerCode;
	public String _productCode;
	public String _orderMode;
	public Long _ruleId;
	public String _routeCode;
	public String _destSource;
	public String _effDate;
	public String _expDate;

	@Override
	public boolean isEffective(String tm14) {
		if (tm14 == null || tm14.compareTo(_effDate) < 0 || tm14.compareTo(_expDate) > 0) {
			return false;
		} else {
			return true;
		}
	}
}
