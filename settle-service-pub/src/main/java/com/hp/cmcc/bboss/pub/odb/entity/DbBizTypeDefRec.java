package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbBizTypeDefRec extends GsonObj {
	private String biz_type;
	private Integer type_id;
	private Integer enabled;
	private Timestamp eff_date;
	private Timestamp exp_date;

	public String getBiz_type() {
		return biz_type;
	}

	public void setBiz_type(String biz_type) {
		this.biz_type = biz_type;
	}

	public Integer getType_id() {
		return type_id;
	}

	public void setType_id(Integer type_id) {
		this.type_id = type_id;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Timestamp getEff_date() {
		return eff_date;
	}

	public void setEff_date(Timestamp eff_date) {
		this.eff_date = eff_date;
	}

	public Timestamp getExp_date() {
		return exp_date;
	}

	public void setExp_date(Timestamp exp_date) {
		this.exp_date = exp_date;
	}
}
