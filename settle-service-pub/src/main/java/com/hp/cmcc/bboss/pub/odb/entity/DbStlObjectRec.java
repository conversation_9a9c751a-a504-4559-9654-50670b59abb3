package com.hp.cmcc.bboss.pub.odb.entity;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbStlObjectRec extends GsonObj {
	private Long object_id;
	private String object_name;
	private Integer object_type;
	private String owner_name;
	private String field_name;
	private String search_key;
	private String object_value;

	public Long getObject_id() {
		return object_id;
	}

	public void setObject_id(Long object_id) {
		this.object_id = object_id;
	}

	public String getObject_name() {
		return object_name;
	}

	public void setObject_name(String object_name) {
		this.object_name = object_name;
	}

	public Integer getObject_type() {
		return object_type;
	}

	public void setObject_type(Integer object_type) {
		this.object_type = object_type;
	}

	public String getOwner_name() {
		return owner_name;
	}

	public void setOwner_name(String owner_name) {
		this.owner_name = owner_name;
	}

	public String getField_name() {
		return field_name;
	}

	public void setField_name(String field_name) {
		this.field_name = field_name;
	}

	public String getSearch_key() {
		return search_key;
	}

	public void setSearch_key(String search_key) {
		this.search_key = search_key;
	}

	public String getObject_value() {
		return object_value;
	}

	public void setObject_value(String object_value) {
		this.object_value = object_value;
	}
}
