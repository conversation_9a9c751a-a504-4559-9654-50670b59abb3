package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbShpmVerRec extends GsonObj {
	public static final int VER_FLAG_0_INIT = 0;
	public static final int VER_FLAG_1_SUCCESS = 1;
	public static final int VER_FLAG_2_SHPMSV_ERR = 2;
	public static final int VER_FLAG_3_SHPMAG_ERR = 3;

	private Integer ver_mm;
	private String inst_nm;
	private String shpm_ver;
	private Integer ver_flag;
	private Timestamp tm_start;
	private Timestamp tm_blob;
	private Timestamp tm_finish;
	private Long ts_start;
	private Long ts_blob;
	private Long ts_finish;
	private String err_msg;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[6];
		o[0] = ver_mm;
		o[1] = inst_nm;
		o[2] = shpm_ver;
		o[3] = ver_flag;
		o[4] = tm_start;
		o[5] = ts_start;
		return o;
	}

	public Integer getVer_mm() {
		return ver_mm;
	}

	public void setVer_mm(Integer ver_mm) {
		this.ver_mm = ver_mm;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getShpm_ver() {
		return shpm_ver;
	}

	public void setShpm_ver(String shpm_ver) {
		this.shpm_ver = shpm_ver;
	}

	public Integer getVer_flag() {
		return ver_flag;
	}

	public void setVer_flag(Integer ver_flag) {
		this.ver_flag = ver_flag;
	}

	public Timestamp getTm_start() {
		return tm_start;
	}

	public void setTm_start(Timestamp tm_start) {
		this.tm_start = tm_start;
	}

	public Timestamp getTm_blob() {
		return tm_blob;
	}

	public void setTm_blob(Timestamp tm_blob) {
		this.tm_blob = tm_blob;
	}

	public Timestamp getTm_finish() {
		return tm_finish;
	}

	public void setTm_finish(Timestamp tm_finish) {
		this.tm_finish = tm_finish;
	}

	public Long getTs_start() {
		return ts_start;
	}

	public void setTs_start(Long ts_start) {
		this.ts_start = ts_start;
	}

	public Long getTs_blob() {
		return ts_blob;
	}

	public void setTs_blob(Long ts_blob) {
		this.ts_blob = ts_blob;
	}

	public Long getTs_finish() {
		return ts_finish;
	}

	public void setTs_finish(Long ts_finish) {
		this.ts_finish = ts_finish;
	}

	public String getErr_msg() {
		return err_msg;
	}

	public void setErr_msg(String err_msg) {
		this.err_msg = err_msg;
	}
}
