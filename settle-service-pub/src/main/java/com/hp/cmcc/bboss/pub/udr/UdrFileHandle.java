package com.hp.cmcc.bboss.pub.udr;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

public class UdrFileHandle extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(UdrFileHandle.class);
	public static final int OP_MODE_NONE = 0;
	public static final int OP_MODE_MOVE = 1;
	public static final int OP_MODE_DELETE = 2;
	public static final int OP_MODE_COPY = 3;

	@Expose
	public int _opMode;
	@Expose
	public boolean _deleteEmptyFile;
	public File _srcFile;
	@Expose
	public String _srcFilePath;
	@Expose
	public String _dstDir;
	@Expose
	public String _dstBasenm;
	public PrintWriter _pw;
	public String _logMsg;

	@Override
	public String toGsonStr() {
		Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().setDateFormat(GsonObj._DATE_TIME_FMT).create();
		return gson.toJson(this);
	}

	public UdrFileHandle(File src_file, int op_mode, boolean delete_empty_file) {
		_srcFile = src_file;
		_srcFilePath = _srcFile.getAbsolutePath();
		_opMode = op_mode;
		_deleteEmptyFile = delete_empty_file;
	}

	public void openSrcFile(String charset, boolean append) throws Exception {

		closeSrcFile();
		FileOutputStream fos_ = new FileOutputStream(_srcFile, append);
		OutputStreamWriter osw_ = null;
		if (charset == null) {
			osw_ = new OutputStreamWriter(fos_);
		} else {
			osw_ = new OutputStreamWriter(fos_, charset);
		}
		_pw = new PrintWriter(osw_);
		L.trace("{}, charset={}, append={} opened", _srcFile.getAbsolutePath(), charset, append);
	}

	public void flush() {
		if (_pw != null)
			_pw.flush();
	}

	public boolean operation() {
		switch (_opMode) {
		case OP_MODE_MOVE:
		case OP_MODE_DELETE:
		case OP_MODE_COPY:
			if (_srcFile == null) {
				_logMsg = PubMethod.FmtArgs("_srcFile is null, _opMode %d can not operation", _opMode);
				L.warn("{}", _logMsg);
				return false;
			}
			break;
		default: // OP_MODE_NONE
			return true;
		}

		if (!_srcFile.isFile()) {
			_logMsg = PubMethod.FmtArgs("[%s] not exists, _opMode %d no need operation", _srcFile.getAbsolutePath(), _opMode);
			L.info("{}", _logMsg);
			return true;
		}
		if (_opMode == OP_MODE_DELETE) {
			if (!_srcFile.delete()) {
				_logMsg = PubMethod.FmtArgs("[%s] delete failed", _srcFile.getAbsolutePath());
				L.warn("{}", _logMsg);
				return false;
			} else {
				_logMsg = PubMethod.FmtArgs("[%s] deleted", _srcFile.getAbsolutePath());
				L.debug("{}", _logMsg);
				return true;
			}
		} else if (_deleteEmptyFile) {
			if (_srcFile.length() == 0) {
				_logMsg = PubMethod.FmtArgs("[%s] zero size, _opMode %d delete directly", _srcFile.getAbsolutePath(), _opMode);
				L.debug("{}", _logMsg);
				_srcFile.delete();
				return true;
			}
		}

		if (_dstDir == null) {
			_logMsg = PubMethod.FmtArgs("[%s], _dstDir is null, _opMode %d can not operation", _srcFile.getAbsolutePath(), _opMode);
			L.warn("{}", _logMsg);
			return false;
		}
		File dst_dir_ = new File(_dstDir);
		if (!dst_dir_.isDirectory()) {
			_logMsg = PubMethod.FmtArgs("[%s], _dstDir [%s] not exists, _opMode %d can not operation", _srcFile.getAbsolutePath(),
					_dstDir, _opMode);
			L.warn("{}", _logMsg);
			return false;
		}
		String dst_basenm_ = PubMethod.IsBlank(_dstBasenm) ? _srcFile.getName() : _dstBasenm;
		String dst_pathnm_ = dst_dir_.getAbsolutePath() + "/" + dst_basenm_;

		boolean rc_;
		if (_opMode == OP_MODE_MOVE) {
			rc_ = PubMethod.MoveAFile(_srcFile.getAbsolutePath(), dst_pathnm_);
			if (rc_) {
				_logMsg = PubMethod.FmtArgs("mv [%s] to [%s] ok", _srcFile.getAbsolutePath(), dst_pathnm_);
				L.info("{}", _logMsg);
			} else {
				_logMsg = PubMethod.FmtArgs("mv [%s] to [%s] error", _srcFile.getAbsolutePath(), dst_pathnm_);
				L.warn("{}", _logMsg);
			}
		} else {
			rc_ = PubMethod.CopyAFile(_srcFile.getAbsolutePath(), dst_pathnm_);
			if (rc_) {
				_logMsg = PubMethod.FmtArgs("cp [%s] to [%s] ok", _srcFile.getAbsolutePath(), dst_pathnm_);
				L.info("{}", _logMsg);
			} else {
				_logMsg = PubMethod.FmtArgs("cp [%s] to [%s] error", _srcFile.getAbsolutePath(), dst_pathnm_);
				L.warn("{}", _logMsg);
			}
		}
		return rc_;
	}

	public void closeSrcFile() {
		PubMethod.Close(_pw);
		_pw = null;
	}
}
