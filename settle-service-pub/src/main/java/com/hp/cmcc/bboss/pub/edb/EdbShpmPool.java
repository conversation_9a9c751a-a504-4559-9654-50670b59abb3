package com.hp.cmcc.bboss.pub.edb;

import org.apache.commons.pool2.KeyedPooledObjectFactory;
import org.apache.commons.pool2.impl.GenericKeyedObjectPool;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;

public class EdbShpmPool extends GenericKeyedObjectPool<String, Edb> {
	public EdbShpmPool(KeyedPooledObjectFactory<String, Edb> factory, GenericKeyedObjectPoolConfig config) {
		super(factory, config);
	}

	public EdbShpmPool(KeyedPooledObjectFactory<String, Edb> factory) {
		super(factory);
	}
}
