package com.hp.cmcc.bboss.pub.edb.entity;

import java.io.Serializable;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbStlRateRec extends GsonObj implements Serializable, Effectivable {
	private static final long serialVersionUID = 1651495878347230849L;

	public static final int RATE_TYPE_1_FLAT = 1; //固定值费率
	public static final int RATE_TYPE_2_TARIFF = 2; //订购级别费率
	public static final int RATE_TYPE_3_REPART = 3; //订购级别分摊费率
	public static final int RATE_TYPE_4_SCRIPT = 4;
	public static final int RATE_TYEP_5_ESP = 5; //政企ESP局数据结算

	public static final int ROUND_METHOD_1_ROUND = 1;
	public static final int ROUND_METHOD_2_FLOOR = 2;

	public Long _id;
	public Long _rateId;
	public Long _ruleId;
	public Long _inObjectId;
	public String _rateCode;
	public Integer _rateType;
	public Integer _calcPriority;
	public Integer _roundMethod;
	public String _effDate;
	public String _expDate;

	@Override
	public boolean isEffective(String tm14) {
		if (tm14 == null || tm14.compareTo(_effDate) < 0 || tm14.compareTo(_expDate) > 0) {
			return false;
		} else {
			return true;
		}
	}
}
