package com.hp.cmcc.bboss.pub.udr;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class UdrErrInfo extends GsonObj {
	public String _errCode;
	public String _errVal;
	public String _errReason;

	public void fromUdr(UdrFmt udr) {
		_errCode = udr._eFields[UdrFmt.E_02_A03_ERR_CODE];
		_errVal = udr._eFields[UdrFmt.E_16_E02_ERR_VAL];
		_errReason = udr._eFields[UdrFmt.E_22_E08_ERR_REASON];
	}

	public void toUdr(UdrFmt udr) {
		udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = _errCode;
		udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = _errVal;
		udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = _errReason;
	}
}
