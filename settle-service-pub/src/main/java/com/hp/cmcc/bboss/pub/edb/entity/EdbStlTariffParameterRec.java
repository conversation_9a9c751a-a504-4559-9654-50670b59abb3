package com.hp.cmcc.bboss.pub.edb.entity;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbStlTariffParameterRec extends GsonObj implements Effectivable {
	public Long _id;
	public String _offerCode;
	public String _productCode;
	public Long _prodInstId;
	public Long _svcInstId;
	public String _orderMode;
	public Long _ruleId;
	public Long _rateId;
	public String _chargeItem;
	public String _objectValue;
	public Integer _tariffType;
	public Integer _calcPriority;
	public String _rateValue;
	public String _destSource;
	public String _routeFlag;
	public String _effDate;
	public String _expDate;

	@Override
	public boolean isEffective(String tm14) {
		if (tm14 == null || tm14.compareTo(_effDate) < 0 || tm14.compareTo(_expDate) > 0) {
			return false;
		} else {
			return true;
		}
	}
}
