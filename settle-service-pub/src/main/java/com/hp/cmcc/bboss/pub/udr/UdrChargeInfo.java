package com.hp.cmcc.bboss.pub.udr;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class UdrChargeInfo extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(UdrChargeInfo.class);
	public static Pattern _AngleBrackets = Pattern.compile("(<[^>]+>)");

	public class ChargeItem {
		public String _chargeCode;
		public String _productOfferCode;
		public Integer _poScopeType;
		public String _feeName;
		public Long _feeValue;
		public Long _discount;
	}

	public List<ChargeItem> _itemList;
	public List<String> _poList; // distinct _productOfferCode list
	public long _sumDiscount;

	public UdrChargeInfo() {
		_itemList = new ArrayList<ChargeItem>();
		_poList = new ArrayList<String>();
	}

	public static UdrChargeInfo ParseChargeInfo(String charge_info) {
		UdrChargeInfo result_ = null;
		ChargeItem item_ = null;
		List<String> brackets_ = new ArrayList<String>();
		String[] info_array_ = charge_info.split(";");
		for (int i = 0; i < info_array_.length; i++) {
			String info_item_ = info_array_[i];
			if (PubMethod.IsBlank(info_item_))
				continue;
			Matcher m = _AngleBrackets.matcher(info_item_);
			brackets_.clear();
			while (m.find()) {
				String s = m.group(1);
				brackets_.add(s.substring(1, s.length() - 1));
			}
			if (brackets_.size() < 2) {
				L.warn("charge_info {} [{}] insufficent angle-bracket pairs, expecting 2 but only {}", i, charge_info,
						brackets_.size());
				return null;
			}
			if (result_ == null) {
				result_ = new UdrChargeInfo();
			}
			String[] pair_ = brackets_.get(1).split(":");
			if (pair_.length < 4) {
				L.warn("charge_info {} [{}] item [{}] split by ':' insufficent fields", i, charge_info, brackets_.get(1));
				return null;
			}

			item_ = null;
			for (int j = 0; j < result_._itemList.size(); j++) {
				if (brackets_.get(0).equals(result_._itemList.get(j)._chargeCode)) {
					item_ = result_._itemList.get(j);
					break;
				}
			}
			if (item_ == null) {
				item_ = result_.new ChargeItem();
				result_._itemList.add(item_);
			}
			if (!result_._poList.contains(pair_[0]))
				result_._poList.add(pair_[0]);

			if (item_._chargeCode == null) {
				item_._chargeCode = brackets_.get(0);
				item_._productOfferCode = pair_[0];
				if (pair_.length == 4) {
					item_._poScopeType = 0;
					item_._feeName = pair_[1];
					item_._feeValue = Long.parseLong(pair_[2]);
					item_._discount = Long.parseLong(pair_[3]);
				} else { // 2015.07.08 new format
					item_._poScopeType = Integer.parseInt(pair_[1]);
					item_._feeName = pair_[2];
					item_._feeValue = Long.parseLong(pair_[3]);
					item_._discount = Long.parseLong(pair_[4]);
				}
				result_._sumDiscount += item_._discount;
			} else {
				if (pair_.length == 4) {
					item_._feeValue += Long.parseLong(pair_[2]);
					item_._discount += Long.parseLong(pair_[3]);
					result_._sumDiscount += Long.parseLong(pair_[3]);
				} else { // 2015.07.08 new format
					item_._feeValue += Long.parseLong(pair_[3]);
					item_._discount += Long.parseLong(pair_[4]);
					result_._sumDiscount += Long.parseLong(pair_[4]);
				}
			}
		}

		if (result_ == null) {
			L.warn("charge_info [{}] no valid fields", charge_info);
		}
		return result_;
	}
}
