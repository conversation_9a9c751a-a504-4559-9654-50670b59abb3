package com.hp.cmcc.bboss.pub.edb;

import org.apache.commons.pool2.BaseKeyedPooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EdbShpmFactory extends BaseKeyedPooledObjectFactory<String, Edb> {
	private static Logger L = LoggerFactory.getLogger(EdbShpmFactory.class);
	private String _baseDir;
	private int _cacheSize;

	public EdbShpmFactory(String base_dir, int cache_size) {
		_baseDir = base_dir;
		_cacheSize = cache_size;
	}

	public String getBaseDir() {
		return _baseDir;
	}

	@Override
	public void destroyObject(String shpm_ver, PooledObject<Edb> edb) {
		L.trace("edb {} {} detached, {}", shpm_ver, edb.getObject().getEdbPath(), edb.getObject());
		edb.getObject().destroy();
	}

	@Override
	public boolean validateObject(String shpm_ver, PooledObject<Edb> edb) {
		return edb.getObject().isConnected();
	}

	@Override
	public Edb create(String shpm_ver) throws Exception {
		String month_dir_ = _baseDir + "/" + shpm_ver.substring(0, 6);
		String edb_path_ = month_dir_ + "/" + shpm_ver + ".db3";
		Edb edb_ = new Edb(edb_path_, true, _cacheSize);
		L.info("edb {} attached, {}", edb_path_, edb_);
		return edb_;
	}

	@Override
	public PooledObject<Edb> wrap(Edb edb) {
		return new DefaultPooledObject<Edb>(edb);
	}
}
