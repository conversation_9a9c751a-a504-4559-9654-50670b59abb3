package com.hp.cmcc.bboss.pub.mdb;

public class MdbConst {
	public static final String SHPARM_KEY_VER_CMT = "shparm:ver"; // committed version
	public static final String SHPARM_KEY_VER_TMP = "shparm:tmp"; // temporary version
	public static final String BIZLOG_KEY_CACHE_CNT = "C:%03d"; // C:[TYPE_ID]

	public static final String BIZLOG_HK_UDR_INIT = "INIT"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_RAW = "RAW"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_SPOUT = "SPOUT"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_VALIDATION = "VALIDATION"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_CHKDUP = "CHKDUP"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_GUIDING = "GUIDING"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_RATING = "RATING"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_GSON = "GSON"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_ERHNDL = "ERHNDL"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_ACK = "ACK"; // yyyymmddHHMMSS.sss
	public static final String BIZLOG_HK_UDR_FAIL = "FAIL"; // yyyymmddHHMMSS.sss

	public static final String BIZLOG_HK_LOG_RAW_FILE_NM = "RAW_FILE_NM"; //原始文件名称
	public static final String BIZLOG_HK_LOG_RCV_TM = "RCV_TM"; //接收时间
	public static final String BIZLOG_HK_LOG_SHPARM_VER = "SHPARM_VER"; //参数版本号
	public static final String BIZLOG_HK_LOG_CNT_TOT = "CNT_TOT"; //输入的话单数
	public static final String BIZLOG_HK_LOG_CNT_FMT = "CNT_FMT"; //错单数
	public static final String BIZLOG_HK_LOG_CNT_RAT = "CNT_RAT"; //批价错单数
	public static final String BIZLOG_HK_LOG_CNT_DUP = "CNT_DUP"; //重单数
	public static final String BIZLOG_HK_LOG_CNT_NML = "CNT_NML"; //正常话单数
	public static final String BIZLOG_HK_LOG_FBK_TOT = "FBK_TOT";
	public static final String BIZLOG_HK_LOG_FBK_FMT = "FBK_FMT";
	public static final String BIZLOG_HK_LOG_FBK_RAT = "FBK_RAT";
	public static final String BIZLOG_HK_LOG_FBK_DUP = "FBK_DUP";
	public static final String BIZLOG_HK_LOG_FBK_NML = "FBK_NML";
}
