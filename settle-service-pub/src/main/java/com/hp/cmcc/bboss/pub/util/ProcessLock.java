package com.hp.cmcc.bboss.pub.util;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.lang.management.ManagementFactory;
import java.nio.channels.FileLock;

public class ProcessLock {
	private static File _LockFileObj = null;
	private static RandomAccessFile _LockRAF = null;
	private static FileLock _FLock = null;
	private static String _Pid = null;

	static {
		_Pid = ManagementFactory.getRuntimeMXBean().getName();
		if (_Pid.indexOf('@') > 0) {
			_Pid = _Pid.substring(0, _Pid.indexOf('@'));
		}
	}

	public static boolean TryLock(String module, String inst_nm, String env, int exit_on_fail) throws Exception {
		String lock_file_nm_ = _GenFileNm(module, inst_nm, env, "lock");
		boolean ok_ = _TryLock(lock_file_nm_);
		if (!ok_ && exit_on_fail >= 0) {
			System.exit(exit_on_fail);
		}
		return ok_;
	}

	public static boolean IsStopRequested(String module, String inst_nm, String env, boolean delete_stop_file) {
		String stop_file_nm_ = _GenFileNm(module, inst_nm, env, "stop");
		File stop_file_obj_ = new File(stop_file_nm_);
		boolean rc_ = stop_file_obj_.isFile();
		if (rc_ && delete_stop_file) {
			stop_file_obj_.delete();
		}
		return rc_;
	}

	public static void GenStopRequestFile(String module, String inst_nm, String env) {
		String stop_file_nm_ = _GenFileNm(module, inst_nm, env, "stop");
		RandomAccessFile raf_ = null;
		try {
			raf_ = new RandomAccessFile(stop_file_nm_, "rw");
		} catch (FileNotFoundException e) {
			PubMethod.P(PubMethod.WRN, "open [%s] exception, %s", stop_file_nm_, e.toString());
		} finally {
			if (raf_ != null) {
				try {
					raf_.close();
				} catch (IOException e) {
					// ignore
				}
			}
		}
	}

	private static String _GenFileNm(String module, String inst_nm, String env, String tag) {
		String env_root_ = "";
		if (env == null || env.length() == 0) {
			env_root_ = "";
		} else {
			env_root_ = System.getenv(env);
			if (env_root_ != null) {
				File root_obj_ = new File(env_root_);
				if (!root_obj_.isDirectory()) {
					PubMethod.P(PubMethod.WRN, "dir $%s [%s] not exists use sys /tmp/lock", env, env_root_);
				}
			} else {
				env_root_ = "";
			}
		}

		String path_;
		if (env_root_.length() == 0) {
			String os_nm_ = System.getProperty("os.name");
			if (os_nm_ != null && os_nm_.startsWith("Windows")) {
				path_ = System.getProperty("java.io.tmpdir") + "/lock";
			} else {
				path_ = "/tmp/lock";
			}
		} else {
			path_ = env_root_ + "/tmp/lock";
		}
		File lock_dir_ = new File(path_);
		if (!lock_dir_.exists()) {
			lock_dir_.mkdirs();
		}
		long cksum_ = PubMethod.BuffCksum(env_root_.getBytes());
		path_ = String.format("%s/%s.%s.%d.%s", path_, module, inst_nm, cksum_, tag);
		return path_;
	}

	private static boolean _TryLock(String lock_file_nm) throws Exception {
		if (_LockFileObj != null) {
			PubMethod.P(PubMethod.WRN, "_LockFileObj not null, TryLock already initialized");
			return false;
		}
		_LockFileObj = new File(lock_file_nm);

		if (_LockRAF != null) {
			PubMethod.P(PubMethod.WRN, "_LockRAF not null, TryLock already initialized");
			return false;
		}
		_LockRAF = new RandomAccessFile(lock_file_nm, "rw");
		_FLock = _LockRAF.getChannel().tryLock(); //非阻塞的方法，当文件锁不可用时，tryLock()会得到null值
		if (_FLock == null) {
			PubMethod.P(PubMethod.ERO, "TryLock(%s) failed, perhaps another instance running", lock_file_nm);
			return false;
		}
		_LockFileObj.deleteOnExit(); //在JVM进程退出的时候删除文件,通常用在临时文件的删除.
		String vm_pid_ = ManagementFactory.getRuntimeMXBean().getName();
		String str_ = String.format("%-59s\n", vm_pid_);
		_LockRAF.writeBytes(str_);
		_LockRAF.getFD().sync();
		return true;
	}

	public static void main(String[] args) throws Exception {
		boolean rc_;
		rc_ = ProcessLock.TryLock("m", "i", "STTL_HOME", -1);
		PubMethod.P(PubMethod.DBG, "rc_=%b", rc_);
		if (!rc_) {
			System.exit(1);
		}
		while (true) {
			PubMethod.Sleep(2000);
			rc_ = ProcessLock.IsStopRequested("m", "i", "STTL_HOME", true);
			PubMethod.P(PubMethod.DBG, "rc_=%b", rc_);
			if (rc_) {
				System.exit(0);
			}
		}
	}
}
