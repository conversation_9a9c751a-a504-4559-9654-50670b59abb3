package com.hp.cmcc.bboss.pub.util;

import java.util.ArrayList;
import java.util.concurrent.ThreadLocalRandom;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RandomObjSelector<T> {
	private static Logger L = LoggerFactory.getLogger(RandomObjSelector.class);
	private ArrayList<T> _objList;
	private ArrayList<Integer> _weightList;
	private double[] _rangedIdx;

	public RandomObjSelector() {
		_objList = new ArrayList<T>();
		_weightList = new ArrayList<Integer>();
	}

	public void clear() {
		_objList.clear();
		_weightList.clear();
	}

	public void addObj(T obj, int weight) {
		_objList.add(obj);
		_weightList.add(weight);
	}

	public void addObjDone() {
		int sz_ = _objList.size();
		_rangedIdx = new double[sz_];
		long isum_ = 0;
		for (Integer weight_ : _weightList) {
			isum_ += weight_;
		}
		double dsum_ = 0.0;
		double dtmp_ = 0.0;
		for (int i = 0; i < sz_; ++i) {
			dtmp_ = (double) _weightList.get(i) / (double) isum_;
			dsum_ += dtmp_;
			_rangedIdx[i] = dsum_;
		}
		_rangedIdx[sz_ - 1] = 1.0;
	}

	public void trace() {
		for (int i = 0; i < _objList.size(); i++) {
			L.trace(String.format("No.%5d %.6f %5d %s", i + 1, _rangedIdx[i], _weightList.get(i), _objList.get(i)));
		}
	}

	public T randomObj() {
		// binary search
		double rand_ = ThreadLocalRandom.current().nextDouble();
		int sz_ = _rangedIdx.length;
		int idx_dn_ = 0;
		int idx_up_ = sz_ - 1;
		int idx_diff_ = idx_up_ - idx_dn_;
		int idx_mid_ = idx_dn_ + idx_diff_ / 2;
		while (idx_diff_ > 5) {
			if (rand_ > _rangedIdx[idx_mid_]) {
				idx_dn_ = idx_mid_;
			} else {
				idx_up_ = idx_mid_;
			}
			idx_diff_ = idx_up_ - idx_dn_;
			idx_mid_ = idx_dn_ + idx_diff_ / 2;
		}

		for (int i = idx_dn_; i < sz_; ++i) {
			if (rand_ <= _rangedIdx[i]) {
				L.trace(String.format("i:obj:dn:up:diff:mid:rand:[dn]:[up]:[mid] %d %s %d %d %d %d %g %g %g %g", i,
						_objList.get(i), idx_dn_, idx_up_, idx_diff_, idx_mid_, rand_, _rangedIdx[idx_dn_], _rangedIdx[idx_up_],
						_rangedIdx[idx_mid_]));
				return _objList.get(i);
			}
		}

		L.warn("should not happened!!!");
		return _objList.get(sz_ - 1);
	}

	public static void main(String args[]) {
		// for unit test
		// |gawk -F":" '{print $3}'|sort|uniq -c|sort -n
		RandomObjSelector<String> stub_ = new RandomObjSelector<String>();
		stub_.addObj("HQ", 5);
		stub_.addObj("HR", 5);
		stub_.addObj("FN", 5);
		stub_.addObj("ET", 5);
		stub_.addObj("RD", 35);
		stub_.addObj("DV", 20);
		stub_.addObj("MT", 15);
		stub_.addObj("IT", 10);

		stub_.addObj("hq", 5);
		stub_.addObj("hr", 5);
		stub_.addObj("fn", 5);
		stub_.addObj("et", 5);
		stub_.addObj("rd", 35);
		stub_.addObj("dv", 20);
		stub_.addObj("mt", 15);
		stub_.addObj("it", 10);

		stub_.addObjDone();
		for (int i = 0; i < 1000; ++i) {
			String s = stub_.randomObj();
			s = s + ""; //eliminate 'value not used' warning
		}
		stub_.trace();
	}
}
