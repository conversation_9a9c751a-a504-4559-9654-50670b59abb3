package com.hp.cmcc.bboss.pub.udr;

import com.google.gson.Gson;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.annotation.PreDestroy;
import java.util.Arrays;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

public class UdrKafkaConsumerRunnable2<E extends GsonObj> implements Runnable {
	private static Logger L = LoggerFactory.getLogger(UdrKafkaConsumerRunnable2.class);
	public AtomicBoolean _pauseFlag;
	private BlockingQueue<E> _blockingQueue;
	private int _maxQueueSize;
	private long _sleepMillis;
	private String _kafkaTopic;
	private KafkaConsumer<String, String> _consumer;
	private Gson _gson = new Gson();
	private Class<E> _gsonClazz = null;
	private long _totCnt = 0;
	private int pollTimeOut = 3000;

	public UdrKafkaConsumerRunnable2(BlockingQueue<E> blocking_queue, int max_queue_size, long sleep_millis, Class<E> gson_clazz,
                                     String kafka_topic) {
		_blockingQueue = blocking_queue;
		_maxQueueSize = max_queue_size;
		_sleepMillis = sleep_millis;
		_gsonClazz = gson_clazz;
		_kafkaTopic = kafka_topic;
		_pauseFlag = new AtomicBoolean(false);
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		OdbSystemParam sys_param_ = OdbSystemParam.GetInstance();
		Properties properties_ = new Properties();
//		properties_.put("zookeeper.connect", sys_param_._zookeeperCluster);
		properties_.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, sys_param_._kafkaCluster);
		properties_.put(ConsumerConfig.GROUP_ID_CONFIG, sys_param_._kafkaGroup);
		properties_.put("request.timeout.ms", "30000");
		//该参数用来配置 Consumer 在一次拉取请求（调用 poll() 方法）中能从 Kafka 中拉取的最小数据量，
		// 默认值为1（B）。Kafka 在收到 Consumer 的拉取请求时，如果返回给 Consumer 的数据量小于这个参数所配置的值，那么它就需要进行等待，直到数据量满足这个参数的配置大小。
		// 可以适当调大这个参数的值以提高一定的吞吐量，不过也会造成额外的延迟（latency），对于延迟敏感的应用可能就不可取了。
		properties_.put("fetch.min.bytes", "102400");
		//fetch.max.wait.ms 参数用于指定 Kafka 的等待时间，默认值为500（ms）。
		// 如果 Kafka 中没有足够多的消息而满足不了 fetch.min.bytes 参数的要求，那么最终会等待500ms。
		// 这个参数的设定和 Consumer 与 Kafka 之间的延迟也有关系，如果业务应用对延迟敏感，那么可以适当调小这个参数。
		properties_.put("fetch.max.wait.ms", "200");
		//用来配置 Consumer 在一次拉取请求中从Kafka中拉取的最大数据量，默认值为52428800（B），也就是50MB
		properties_.put("fetch.max.bytes", "52428800");
		//用来配置从每个分区里返回给 Consumer 的最大数据量，默认值为1048576（B），即1MB
		properties_.put("max.partition.fetch.bytes", "2097152");
		//用来配置 Consumer 在一次拉取请求中拉取的最大消息数，默认值为500（条）
		properties_.put("max.poll.records", "500");
		properties_.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);

		properties_.put("auto.commit.interval.ms", "2000");
		properties_.put("auto.offset.reset", "earliest");
		properties_.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
				StringDeserializer.class.getName());
		properties_.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
				StringDeserializer.class.getName());

		_consumer = new KafkaConsumer<>(properties_);
		_consumer.subscribe(Arrays.asList(_kafkaTopic));
		L.info("consumer subscribe topic:{}", _kafkaTopic);


	}


	@Override
	public void run() {
		int partition_ = -1;
		long offset_ = -1;
		int pending_ = 0;
		String gson_str_ = null;
		boolean first = true;
		L.info("thread started");
		ConsumerRecords<String, String> first_records = _consumer.poll(0);
		Set<TopicPartition> assignment = _consumer.assignment();
		L.info("当前消费者分区分配情况：{}", assignment);
		while (true) {
			try {
				if (first) {
					if (!first_records.isEmpty()) {
						L.info("first_records count is {}", first_records.count());
						for (ConsumerRecord<String, String> msg_ : first_records) {
							partition_ = msg_.partition();
							offset_ = msg_.offset();
							gson_str_ = msg_.value();
							L.debug("获取kafka记录：{}", gson_str_);
							E elem_ = _gson.fromJson(gson_str_, _gsonClazz);
							_blockingQueue.add(elem_);
							++_totCnt;
						}
						first_records = null;
					}
					first = false;
				}
				pending_ = _blockingQueue.size();
				L.info("memory_BlockingQueue size:{}", pending_);
				if (pending_ > _maxQueueSize) {
					L.info("_blockingQueue has {} pending elems, gt _maxQueueSize {},  _totCnt {}, sleep {} millis", pending_,
							_maxQueueSize, _totCnt, _sleepMillis);
					PubMethod.Sleep(_sleepMillis);
					continue;
				}
				ConsumerRecords<String, String> records = _consumer.poll(pollTimeOut);
				if (records.isEmpty()) {
					L.warn("poll records is empty, sleep 1 second");
					PubMethod.Sleep(1000);
					continue;
				}
				L.info("records count is {}", records.count());
				for (ConsumerRecord<String, String> msg_ : records) {
					partition_ = msg_.partition();
					offset_ = msg_.offset();
					gson_str_ = msg_.value();
					L.debug("获取kafka记录：{}", gson_str_);
					E elem_ = _gson.fromJson(gson_str_, _gsonClazz);
					_blockingQueue.add(elem_);
					++_totCnt;
				}
			} catch (Exception e) {
				L.warn("exception, partition:offset " + partition_ + ":" + offset_ + " gson_str " + gson_str_
						+ ", sleep 2 second", e);
				PubMethod.Sleep(2000);
			}
		}
	}
//	@Override
//	public void run() {
//		long latest_ = System.currentTimeMillis();
//		int partition_ = -1;
//		long offset_ = -1;
//		int pending_ = 0;
//		String gson_str_ = null;
//		L.info("thread started");
//		try {
//			if (records.count() <= 0) {
//				L.warn("records is null, can not run");
//				return;
//			}
//			pending_ = _blockingQueue.size();
//			L.info("memory_BlockingQueue size:{}",pending_);
//			if (pending_ > _maxQueueSize) {
//				L.info("_blockingQueue has {} pending elems, gt _maxQueueSize {},  _totCnt {}, sleep {} millis", pending_,
//						_maxQueueSize, _totCnt, _sleepMillis);
//				PubMethod.Sleep(_sleepMillis);
//				return;
//			}
//			if (records.count() > 0) {
//				for (ConsumerRecord<String, String> msg_ : records) {
//					partition_ = msg_.partition();
//					offset_ = msg_.offset();
//					gson_str_ = msg_.value();
//					L.debug("获取kakfa记录：{}", gson_str_);
//					E elem_ = _gson.fromJson(gson_str_, _gsonClazz);
//					_blockingQueue.add(elem_);
//					++_totCnt;
//					latest_ = System.currentTimeMillis();
//					_consumer.commitAsync();
//				}
//			}
//			if (Thread.currentThread().isInterrupted()) {
//				L.info("thread is interrupted");
//				return;
//			}
//		} catch (ConsumerTimeoutException cte) {
//			long now_ = System.currentTimeMillis();
//			if (now_ - latest_ > 300 * 1000) {
//				L.trace("idling 5 minutes");
//				latest_ = now_;
//			}
//		} catch (Exception e) {
//			if (e.getClass().equals(InterruptedException.class)) {
//				if (gson_str_ == null) {
//					L.info("interrupted, partition:offset {}:{}", partition_, offset_);
//				} else {
//					L.warn("exception, partition:offset " + partition_ + ":" + offset_ + " gson_str " + gson_str_, e);
//				}
//				PubMethod.Sleep(10);
//			} else {
//				L.warn("exception, partition:offset " + partition_ + ":" + offset_ + " gson_str " + gson_str_
//						+ ", sleep 3 second", e);
//				PubMethod.Sleep(1000);
//			}
//		} finally {
//			records = _consumer.poll(3000);
//			L.info("records count is {}", records.count());
//		}
//		L.debug("break out, _terminateFlag {}", _terminateFlag);
//		L.info("thread end");
//	}

	@PreDestroy
	public void destroy() {
		if (null != _consumer) {
			_consumer.close();
		}
	}

}
