package com.hp.cmcc.bboss.pub.dto;

import com.hp.cmcc.bboss.pub.edb.entity.EdbStlObjectRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlOfferRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRuleRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.GsonUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @author: zhanglei
 * @version: 1.0
 * @date: 2021/12/30 15:42
 */
public class ResponseMsg {

    private UdrFmt udr;
    private String[] fields = new String[1] ;
    private Map<Long, EdbStlRuleRec> ruleMap = new HashMap<>();
    private Map<Long, List<EdbStlRateRec>> rateMap = new HashMap<>();
    private Map<Long, EdbStlOfferRec> offerMap = new HashMap<>();
    private Map<Long, EdbStlObjectRec> oObjMap = new HashMap<>();
    private Map<Long, EdbStlObjectRec> iObjMap = new HashMap<>();
    private Boolean result;
    private List<EdbStlRateRec> stl_rate_list = new ArrayList<>();
    private Map<String, List<String[]>> inSiddSeqMap= new HashMap<>();
    private Map<Long, List<String[]>> inSiddRuleMap= new HashMap<>();

    EdbStlRateRec rate_rec;
    String[] in_sidd = new String[1];
    Map<Integer, List<String[]>> paramSiddMap = new HashMap<>();

    public UdrFmt getUdr() {
        return udr;
    }

    public void setUdr(UdrFmt udr) {
        this.udr = udr;
    }

    public String[] getFields() {
        return fields;
    }

    public void setFields(String[] fields) {
        this.fields = fields;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public Map<Long, EdbStlRuleRec> getRuleMap() {
        return ruleMap;
    }

    public void setRuleMap(Map<Long, EdbStlRuleRec> ruleMap) {
        this.ruleMap = ruleMap;
    }

    public List<EdbStlRateRec> getStl_rate_list() {
        return stl_rate_list;
    }

    public void setStl_rate_list(List<EdbStlRateRec> stl_rate_list) {
        this.stl_rate_list = stl_rate_list;
    }

    public Map<String, List<String[]>> getInSiddSeqMap() {
        return inSiddSeqMap;
    }

    public void setInSiddSeqMap(Map<String, List<String[]>> inSiddSeqMap) {
        this.inSiddSeqMap = inSiddSeqMap;
    }

    public Map<Long, List<String[]>> getInSiddRuleMap() {
        return inSiddRuleMap;
    }

    public void setInSiddRuleMap(Map<Long, List<String[]>> inSiddRuleMap) {
        this.inSiddRuleMap = inSiddRuleMap;
    }

    public Map<Long, EdbStlOfferRec> getOfferMap() {
        return offerMap;
    }

    public void setOfferMap(Map<Long, EdbStlOfferRec> offerMap) {
        this.offerMap = offerMap;
    }

    public Map<Long, EdbStlObjectRec> getoObjMap() {
        return oObjMap;
    }

    public void setoObjMap(Map<Long, EdbStlObjectRec> oObjMap) {
        this.oObjMap = oObjMap;
    }

    public Map<Long, List<EdbStlRateRec>> getRateMap() {
        return rateMap;
    }

    public void setRateMap(Map<Long, List<EdbStlRateRec>> rateMap) {
        this.rateMap = rateMap;
    }

    public Map<Long, EdbStlObjectRec> getiObjMap() {
        return iObjMap;
    }

    public void setiObjMap(Map<Long, EdbStlObjectRec> iObjMap) {
        this.iObjMap = iObjMap;
    }

    public EdbStlRateRec getRate_rec() {
        return rate_rec;
    }

    public void setRate_rec(EdbStlRateRec rate_rec) {
        this.rate_rec = rate_rec;
    }

    public String[] getIn_sidd() {
        return in_sidd;
    }

    public void setIn_sidd(String[] in_sidd) {
        this.in_sidd = in_sidd;
    }

    public Map<Integer, List<String[]>> getParamSiddMap() {
        return paramSiddMap;
    }

    public void setParamSiddMap(Map<Integer, List<String[]>> paramSiddMap) {
        this.paramSiddMap = paramSiddMap;
    }

    @Override
    public String toString() {
        return "ResponseMsg{" +
                "udr=" + udr.toGsonStr() +
                ", fields=" + GsonUtil.toJsonString(fields) +
                ", result=" + result +
                '}';
    }
}
