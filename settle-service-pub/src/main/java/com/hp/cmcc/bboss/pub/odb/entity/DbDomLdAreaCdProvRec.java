package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbDomLdAreaCdProvRec extends GsonObj {
	private Long sn;
	private String ld_area_cd;
	private Integer prov_cd;
	private String ld_area_nm;
	private Timestamp eff_tm;
	private Timestamp exp_tm;

	public Long getSn() {
		return sn;
	}

	public void setSn(Long sn) {
		this.sn = sn;
	}

	public String getLd_area_cd() {
		return ld_area_cd;
	}

	public void setLd_area_cd(String ld_area_cd) {
		this.ld_area_cd = ld_area_cd;
	}

	public Integer getProv_cd() {
		return prov_cd;
	}

	public void setProv_cd(Integer prov_cd) {
		this.prov_cd = prov_cd;
	}

	public String getLd_area_nm() {
		return ld_area_nm;
	}

	public void setLd_area_nm(String ld_area_nm) {
		this.ld_area_nm = ld_area_nm;
	}

	public Timestamp getEff_tm() {
		return eff_tm;
	}

	public void setEff_tm(Timestamp eff_tm) {
		this.eff_tm = eff_tm;
	}

	public Timestamp getExp_tm() {
		return exp_tm;
	}

	public void setExp_tm(Timestamp exp_tm) {
		this.exp_tm = exp_tm;
	}
}
