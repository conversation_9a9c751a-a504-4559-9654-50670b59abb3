package com.hp.cmcc.bboss.pub.odb;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.PubMethod;

public class OdbAgt {
	private static Logger L = LoggerFactory.getLogger(OdbAgt.class);
	public static final String _InstanceBizToken = "biz"; // business
	public static final String _InstanceRatToken = "rat"; // RATer
	public static final String _InstanceUdrToken = "udr"; // Usage Detail Record
	private static OdbCli _OdbCliBizInstance;
	private static OdbCli _OdbCliRatInstance;
	private static OdbCli _OdbCliUdrInstance;

	private OdbAgt() {
		// do nothing explicitly
	}

	public static OdbCli GetBizInstance() {
		return _OdbCliBizInstance;
	}

	public static OdbCli GetRatInstance() {
		return _OdbCliRatInstance;
	}

	public static OdbCli GetUdrInstance() {
		return _OdbCliUdrInstance;
	}

	public static boolean Init(String login_key, int pool_max_biz, int pool_max_rat, int pool_max_udr) {
		if (PubMethod.IsBlank(login_key)) {
			L.warn("Hey dude! login_key is blank, can not init");
			return false;
		}
		boolean rc_ = true;
		if (pool_max_biz > 0) {
			if (_OdbCliBizInstance != null) {
				L.warn("_OdbCliBizInstance not null, force re-init");
			}

			_OdbCliBizInstance = new OdbCli();

			if (!_OdbCliBizInstance.init(login_key, _InstanceBizToken, pool_max_biz)) {
				L.warn("init biz db connection pool failed, login key [{}], pool size {}", login_key, pool_max_biz);
				rc_ = false;
			}
		}
		if (pool_max_rat > 0) {
			if (_OdbCliRatInstance != null)
				L.warn("_OdbCliRatInstance not null, force re-init");
			_OdbCliRatInstance = new OdbCli();
			if (!_OdbCliRatInstance.init(login_key, _InstanceRatToken, pool_max_rat)) {
				L.warn("init rat db connection pool failed, login key [{}], pool size {}", login_key, pool_max_rat);
				rc_ = false;
			}
		}
		if (pool_max_udr > 0) {
			if (_OdbCliUdrInstance != null)
				L.warn("_OdbCliUdrInstance not null, force re-init");
			_OdbCliUdrInstance = new OdbCli();
			if (!_OdbCliUdrInstance.init(login_key, _InstanceUdrToken, pool_max_udr)) {
				L.warn("init rat db connection pool failed, login key [{}], pool size {}", login_key, pool_max_udr);
				rc_ = false;
			}
		}
		return rc_;
	}
}
