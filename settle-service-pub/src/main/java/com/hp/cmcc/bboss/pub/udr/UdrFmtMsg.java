package com.hp.cmcc.bboss.pub.udr;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class UdrFmtMsg extends GsonObj implements Serializable {
	private static final long serialVersionUID = -2377858844226392328L;

	public List<UdrFmt> _udrFmtList;
	public String _rndmId;

	public UdrFmtMsg() {
		_udrFmtList = new ArrayList<>();
		_rndmId = "";
	}

	public void renewRndmId() {
		_rndmId = PubMethod.RandomStr(12);
	}
}
