package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Blob;

public class DbShpmBlobRec {
	private Integer ver_mm;
	private String inst_nm;
	private String shpm_ver;
	private Integer shpm_size;
	private String shpm_cksum;
	private Blob shpm_blob;

	@Override
	public String toString() {
		StringBuilder sb_ = new StringBuilder();
		sb_.append("ver_mm=");
		sb_.append(ver_mm);
		sb_.append(",inst_nm=");
		sb_.append(inst_nm);
		sb_.append(",shpm_ver=");
		sb_.append(shpm_ver);
		sb_.append(",shpm_size=");
		sb_.append(shpm_size);
		sb_.append(",shpm_cksum=");
		sb_.append(shpm_cksum);
		return sb_.substring(0);
	}

	public Integer getVer_mm() {
		return ver_mm;
	}

	public void setVer_mm(Integer ver_mm) {
		this.ver_mm = ver_mm;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getShpm_ver() {
		return shpm_ver;
	}

	public void setShpm_ver(String shpm_ver) {
		this.shpm_ver = shpm_ver;
	}

	public Integer getShpm_size() {
		return shpm_size;
	}

	public void setShpm_size(Integer shpm_size) {
		this.shpm_size = shpm_size;
	}

	public String getShpm_cksum() {
		return shpm_cksum;
	}

	public void setShpm_cksum(String shpm_cksum) {
		this.shpm_cksum = shpm_cksum;
	}

	public Blob getShpm_blob() {
		return shpm_blob;
	}

	public void setShpm_blob(Blob shpm_blob) {
		this.shpm_blob = shpm_blob;
	}
}
