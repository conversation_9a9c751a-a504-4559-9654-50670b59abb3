package com.hp.cmcc.bboss.pub.mdb;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;

public class MdbAgt {
	private static Logger L = LoggerFactory.getLogger(MdbAgt.class);
	private static MdbCli _MdbCliShparmInstance;
	private static MdbCli _MdbCliBizlogInstance;
	private static MdbCli _MdbCliDupchkInstance;
	private static OdbSystemParam _SystemParam;

	private MdbAgt() {
		// do nothing explicitlly
	}

	public static MdbCli GetShparmInstance() {
		return _MdbCliShparmInstance;
	}

	public static MdbCli GetBizlogInstance() {
		return _MdbCliBizlogInstance;
	}

	public static MdbCli GetDupchkInstance() {
		return _MdbCliDupchkInstance;
	}

	public static boolean Init(OdbSystemParam system_param, int pool_max_shparm, int pool_max_bizlog, int pool_max_dupchk) {
		if (system_param == null) {
			L.warn("sttl_system_param can not be null, init failed");
			return false;
		}
		_SystemParam = system_param;
		boolean rc_ = true;
		if (pool_max_shparm > 0) {
			_MdbCliShparmInstance = new MdbCli("shparm", _SystemParam._redisShparmCluster, _SystemParam._redisShparmAuth,
					_SystemParam._redisShparmTimeout, pool_max_shparm);
			if (!_MdbCliShparmInstance.init()) {
				L.warn("init MdbCli shparm error");
				rc_ = false;
			}
		}
		if (pool_max_bizlog > 0) {
			_MdbCliBizlogInstance = new MdbCli("bizlog", _SystemParam._redisBizlogCluster, _SystemParam._redisBizlogAuth,
					_SystemParam._redisBizlogTimeout, pool_max_bizlog);
			if (!_MdbCliBizlogInstance.init()) {
				L.warn("init MdbCli bizlog error");
				rc_ = false;
			}
		}
		if (pool_max_dupchk > 0) {
			_MdbCliDupchkInstance = new MdbCli("dupchk", _SystemParam._redisDupchkCluster, _SystemParam._redisDupchkAuth,
					_SystemParam._redisDupchkTimeout, pool_max_dupchk);
			if (!_MdbCliDupchkInstance.init()) {
				L.warn("init MdbCli dupchk error");
				rc_ = false;
			}
		}
		return rc_;
	}
}
