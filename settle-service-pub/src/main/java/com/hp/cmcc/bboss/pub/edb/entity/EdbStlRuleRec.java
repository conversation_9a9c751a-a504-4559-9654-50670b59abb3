package com.hp.cmcc.bboss.pub.edb.entity;

import java.io.Serializable;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbStlRuleRec extends GsonObj implements Serializable, Effectivable {
	private static final long serialVersionUID = -6529117115902905204L;

	public Long _ruleId;
	public String _ruleName;
	public Long _objectId;
	public Integer _balance;
	public String _effDate;
	public String _expDate;

	@Override
	public boolean isEffective(String tm14) {
		if (tm14 == null || tm14.compareTo(_effDate) < 0 || tm14.compareTo(_expDate) > 0) {
			return false;
		} else {
			return true;
		}
	}
}
