package com.hp.cmcc.bboss.pub.util;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CodingErrorAction;
import java.util.concurrent.ConcurrentHashMap;

public class StrTruncator {
	private static ConcurrentHashMap<String, StrTruncator> _ObjMap = new ConcurrentHashMap<String, StrTruncator>();
	private Charset _charset;
	private CharsetDecoder _decoder;

	public static StrTruncator GetStrTruncator(String charset) {
		String key_ = Thread.currentThread().getName() + ":" + charset;
		StrTruncator obj_ = _ObjMap.get(key_);
		if (obj_ == null) {
			obj_ = new StrTruncator(charset);
			_ObjMap.putIfAbsent(key_, obj_);
		}
		return obj_;
	}

	public StrTruncator(String charset) {
		_charset = Charset.forName(charset);
		_decoder = _charset.newDecoder();
		_decoder.onMalformedInput(CodingErrorAction.IGNORE);
	}

	public String truncate(String input, int max_bytes) {
		if (input == null || input.length() == 0)
			return "";
		byte[] bytes_ = input.getBytes(_charset);
		if (bytes_.length <= max_bytes)
			return input;

		ByteBuffer byte_buf_ = ByteBuffer.wrap(bytes_, 0, max_bytes);
		CharBuffer char_buf_ = CharBuffer.allocate(max_bytes);
		_decoder.reset();
		_decoder.decode(byte_buf_, char_buf_, true);
		_decoder.flush(char_buf_);
		return new String(char_buf_.array(), 0, char_buf_.position());
	}

	public static void main(String[] args) {
		StrTruncator st_ = new StrTruncator("utf-8");
		String input_ = "中文2测试";
		for (int i = 0; i < 16; ++i) {
			String output_ = st_.truncate(input_, i);
			System.out.println(i + ",[" + output_ + "]");
		}
	}
}
