package com.hp.cmcc.bboss.pub.udr;

import com.google.gson.Gson;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import kafka.consumer.ConsumerTimeoutException;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.annotation.PreDestroy;
import java.util.Arrays;
import java.util.Properties;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

public class UdrKafkaConsumerRunnable<E extends GsonObj> implements Runnable {
	private static Logger L = LoggerFactory.getLogger(UdrKafkaConsumerRunnable.class);
	public AtomicBoolean _pauseFlag;
	private BlockingQueue<E> _blockingQueue;
	private int _maxQueueSize;
	private long _sleepMillis;
	private String _kafkaTopic;
	private KafkaConsumer<String, String> _consumer;
//	private List<KafkaStream<byte[], byte[]>> _streams;

	private ConsumerRecords<String, String> records;

//	private ConsumerIterator<byte[], byte[]> _iterator;
	private Gson _gson = new Gson();
	private Class<E> _gsonClazz = null;
	private long _totCnt = 0;
	private boolean _terminateFlag = false;


	public UdrKafkaConsumerRunnable(BlockingQueue<E> blocking_queue, int max_queue_size, long sleep_millis, Class<E> gson_clazz,
			String kafka_topic) {
		_blockingQueue = blocking_queue;
		_maxQueueSize = max_queue_size;
		_sleepMillis = sleep_millis;
		_gsonClazz = gson_clazz;
		_kafkaTopic = kafka_topic;
		_pauseFlag = new AtomicBoolean(false);
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		OdbSystemParam sys_param_ = OdbSystemParam.GetInstance();
		Properties properties_ = new Properties();
//		properties_.put("zookeeper.connect", sys_param_._zookeeperCluster);
		properties_.put("bootstrap.servers", sys_param_._kafkaCluster);
		properties_.put("group.id", sys_param_._kafkaGroup);
		properties_.put("consumer.timeout.ms", "1000");
		properties_.put("socket.receive.buffer.bytes", "1048576");
		properties_.put("fetch.message.max.bytes", "10485760");
		properties_.put("enable.auto.commit", "false");
		properties_.put("auto.commit.interval.ms", "3000");
		properties_.put("auto.offset.reset", "earliest");
		properties_.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
		properties_.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
//		_consumer = Consumer.createJavaConsumerConnector(new ConsumerConfig(properties_));
		_consumer = new KafkaConsumer<>(properties_);
		_consumer.subscribe(Arrays.asList(_kafkaTopic));
		L.info("consumer connector created");
//		Map<String, Integer> topic_cnt_ = new HashMap<String, Integer>();
//		topic_cnt_.put(_kafkaTopic, 1);
//		Map<String, List<KafkaStream<byte[], byte[]>>> stream_map_ = _consumer.createMessageStreams(topic_cnt_);
		records = _consumer.poll(3000);
//		_streams = stream_map_.get(_kafkaTopic);
		L.info("{} count in records", records.count());
		if (records.count() <= 0) {
			L.warn("records is empty, pls chk");
		}
	}

	public void setTerminateFlag() {
		_terminateFlag = true;
		L.info("_terminateFlag is set");
	}

	@Override
	public void run() {
		long latest_ = System.currentTimeMillis();
		int partition_ = -1;
		long offset_ = -1;
		int pending_ = 0;
		String gson_str_ = null;
		L.info("thread started");
		try {
			if (records.count() <= 0) {
				L.warn("records is null, can not run");
				return;
			}
			pending_ = _blockingQueue.size();
			L.info("memory_BlockingQueue size:{}",pending_);
			if (pending_ > _maxQueueSize) {
				L.info("_blockingQueue has {} pending elems, gt _maxQueueSize {},  _totCnt {}, sleep {} millis", pending_,
						_maxQueueSize, _totCnt, _sleepMillis);
				PubMethod.Sleep(_sleepMillis);
				return;
			}
			if (records.count() > 0) {
				for (ConsumerRecord<String, String> msg_ : records) {
					partition_ = msg_.partition();
					offset_ = msg_.offset();
					gson_str_ = msg_.value();
					L.debug("获取kakfa记录：{}", gson_str_);
					E elem_ = _gson.fromJson(gson_str_, _gsonClazz);
					_blockingQueue.add(elem_);
					++_totCnt;
					latest_ = System.currentTimeMillis();
					_consumer.commitAsync();
				}
			}
			if (Thread.currentThread().isInterrupted()) {
				L.info("thread is interrupted");
				return;
			}
		} catch (ConsumerTimeoutException cte) {
			long now_ = System.currentTimeMillis();
			if (now_ - latest_ > 300 * 1000) {
				L.trace("idling 5 minutes");
				latest_ = now_;
			}
		} catch (Exception e) {
			if (e.getClass().equals(InterruptedException.class)) {
				if (gson_str_ == null) {
					L.info("interrupted, partition:offset {}:{}", partition_, offset_);
				} else {
					L.warn("exception, partition:offset " + partition_ + ":" + offset_ + " gson_str " + gson_str_, e);
				}
				PubMethod.Sleep(10);
			} else {
				L.warn("exception, partition:offset " + partition_ + ":" + offset_ + " gson_str " + gson_str_
						+ ", sleep 3 second", e);
				PubMethod.Sleep(1000);
			}
		} finally {
			records = _consumer.poll(3000);
			L.info("records count is {}", records.count());
		}
		L.debug("break out, _terminateFlag {}", _terminateFlag);
		L.info("thread end");
	}

	@PreDestroy
	public void destroy() {
		if (null != _consumer) {
			_consumer.close();
		}
	}

}
