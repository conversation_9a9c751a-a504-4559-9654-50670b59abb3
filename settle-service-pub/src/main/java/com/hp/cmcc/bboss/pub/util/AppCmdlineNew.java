package com.hp.cmcc.bboss.pub.util;

import org.slf4j.MDC;

import java.io.File;

public class AppCmdlineNew {

    public String _module;
    public String _rootEnv;
    public String _dbloginKey;
    public String _instNm;
    public String _workingDir;


    public AppCmdlineNew() {
        // do nothing explicitly
    }

    public void init(String module, String[] args) {
        _module = module;
        MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
        MDC.put(PubMethod.MDC_MODULE, _module);
        _getopt(args);
        _check();
    }

    protected void _getopt(String[] args) {
        Getopt g = new Getopt(_module, args, ":e:k:i:w:");
        int c;
        while ((c = g.getopt()) != -1) {
            switch (c) {
                case 'e':
                    _rootEnv = g.getOptarg();
                    break;
                case 'k':
                    _dbloginKey = g.getOptarg();
                    break;
                case 'i':
                    _instNm = g.getOptarg();
                    MDC.put(PubMethod.MDC_INST_NM, _instNm);
                    break;
                case 'w':
                    _workingDir = g.getOptarg();
                    break;
                case '?':
                    break; // getopt() already printed an error
                default:
                    PubMethod.P(PubMethod.WRN, "getopt() returned [%c]", c);
                    break;
            }
        }
    }

    protected void _check() {
        if (PubMethod.IsBlank(_rootEnv)) {
            PubMethod.P(PubMethod.ERO, "root_env not specified");
            _usage(1);
        }

        String root_str_ = System.getenv(_rootEnv);
        if (PubMethod.IsBlank(root_str_)) {
            PubMethod.P(PubMethod.ERO, "root_env [%s] not set", _rootEnv);
            _usage(1);
        }
        File root_dir_ = new File(root_str_);
        if (!root_dir_.isDirectory()) {
            PubMethod.P(PubMethod.ERO, "root_env [%s]=[%s] dir not exists", _rootEnv, root_str_);
            _usage(1);
        }

        if (PubMethod.IsBlank(_dbloginKey)) {
            PubMethod.P(PubMethod.ERO, "dblogin_key not specified");
            _usage(1);
        }

        if (PubMethod.IsBlank(_instNm)) {
            PubMethod.P(PubMethod.ERO, "inst_nm not specified");
            _usage(1);
        }

        if (PubMethod.IsBlank(_workingDir)) {
            _workingDir = root_str_ + "/var/working/" + _module + "/" + _instNm;
        }
        File wrk_dir_obj_ = new File(_workingDir);
        if (!wrk_dir_obj_.isDirectory()) {
            PubMethod.P(PubMethod.ERO, "working dir [%s] not exists", _workingDir);
            _usage(1);
        }

        if (System.setProperty("user.dir", wrk_dir_obj_.getAbsolutePath()) == null) {
            PubMethod.P(PubMethod.ERO, "set user.dir [%s] failed", wrk_dir_obj_.getAbsolutePath());
            _usage(1);
        }
    }

    protected void _usage(int jvm_exit_code) {
        String newline_ = String.format("%n");
        StringBuilder sb_ = new StringBuilder();
        sb_.append("Usage: -e root_env -k dblogin_key -i inst_nm [-w working_dir]");
        sb_.append(newline_);
        sb_.append("eg   : -e BILLING_HOME -k billing -i sample");
        sb_.append(newline_);
        sb_.append("     : -w default ${root_env}/var/working/" + _module + "/{inst_nm}");
        sb_.append(newline_);
        System.out.printf("%s", sb_.substring(0));

        if (jvm_exit_code >= 0) {
            System.exit(jvm_exit_code);
        }
    }
}
