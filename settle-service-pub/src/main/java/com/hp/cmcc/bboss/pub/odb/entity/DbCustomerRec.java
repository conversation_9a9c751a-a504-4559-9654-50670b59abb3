package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbCustomerRec extends GsonObj {
	public static final int V_00_EFF_DATE = 0;
	public static final int V_01_EXP_DATE = 1;
	public static final int V_02_ID = 2;
	public static final int V_03_PARENT_ID = 3;
	public static final int V_04_CUSTOMER_CODE = 4;
	public static final int V_05_PROVINCE_CODE = 5;
	public static final int V_06_CUSTOMER_TYPE = 6;
	public static final int V_07_BILLABLE_FLAG = 7;
	public static final int V_08_PAID_TYPE = 8;
	public static final int V_09_FIRST_NAME = 9;

	private Long id;
	private String customer_code;
	private String province_code;
	private String first_name;
	private Integer customer_type;
	private Integer billable_flag;
	private Long parent_id;
	private Integer paid_type;
	private Timestamp effective_date;
	private Timestamp expiry_date;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCustomer_code() {
		return customer_code;
	}

	public void setCustomer_code(String customer_code) {
		this.customer_code = customer_code;
	}

	public String getProvince_code() {
		return province_code;
	}

	public void setProvince_code(String province_code) {
		this.province_code = province_code;
	}

	public String getFirst_name() {
		return first_name;
	}

	public void setFirst_name(String first_name) {
		this.first_name = first_name;
	}

	public Integer getCustomer_type() {
		return customer_type;
	}

	public void setCustomer_type(Integer customer_type) {
		this.customer_type = customer_type;
	}

	public Integer getBillable_flag() {
		return billable_flag;
	}

	public void setBillable_flag(Integer billable_flag) {
		this.billable_flag = billable_flag;
	}

	public Long getParent_id() {
		return parent_id;
	}

	public void setParent_id(Long parent_id) {
		this.parent_id = parent_id;
	}

	public Integer getPaid_type() {
		return paid_type;
	}

	public void setPaid_type(Integer paid_type) {
		this.paid_type = paid_type;
	}

	public Timestamp getEffective_date() {
		return effective_date;
	}

	public void setEffective_date(Timestamp effective_date) {
		this.effective_date = effective_date;
	}

	public Timestamp getExpiry_date() {
		return expiry_date;
	}

	public void setExpiry_date(Timestamp expiry_date) {
		this.expiry_date = expiry_date;
	}
}
