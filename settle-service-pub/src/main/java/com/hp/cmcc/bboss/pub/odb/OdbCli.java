package com.hp.cmcc.bboss.pub.odb;

//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//import java.sql.Statement;
//import java.util.List;
//import java.util.regex.Pattern;
//
//import org.apache.commons.dbutils.DbUtils;
//import org.apache.commons.dbutils.QueryRunner;
//import org.apache.commons.dbutils.handlers.ArrayHandler;
//import org.apache.commons.dbutils.handlers.ArrayListHandler;
//import org.apache.commons.dbutils.handlers.BeanHandler;
//import org.apache.commons.dbutils.handlers.BeanListHandler;
//import org.apache.commons.dbutils.handlers.ColumnListHandler;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import com.hp.cmcc.bboss.pub.util.LoginPasswd;
//import com.hp.cmcc.bboss.pub.util.PubMethod;
//import com.mchange.v2.c3p0.ComboPooledDataSource;

import cn.hutool.db.DbUtil;
import com.alibaba.druid.pool.DruidDataSource;
import com.hp.cmcc.bboss.pub.util.LoginPasswd;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.commons.dbutils.DbUtils;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.ArrayHandler;
import org.apache.commons.dbutils.handlers.ArrayListHandler;
import org.apache.commons.dbutils.handlers.BeanHandler;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.dbutils.handlers.ColumnListHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.regex.Pattern;

public class OdbCli {

	private static Logger L = LoggerFactory.getLogger(OdbCli.class);
	private String _loginKey;
	private String _dbInstance;
	private int _maxPoolSize;
	private String _loginStr;
	private String _loginStrSqlplus;
	private String _dbUser;
	private String _dbPasswd;
	private String _dbSid;
	private String _dbHost;
	private int _dbPort;
	private String _jdbcUrl;
//	private ComboPooledDataSource _cpds = null;
	private DruidDataSource _cpds = null;
	private static final String _JDBC_PFX_MYSQL = "jdbc:mysql:";
	private static final Pattern _ExpExclamation = Pattern.compile("!");
	private static final Pattern _ExpIpv6 = Pattern.compile(":.*:");

	public String getLoginKey() {
		return _loginKey;
	}

	public int getMaxPoolSize() {
		return _maxPoolSize;
	}

	public String getLoginStr() {
		return _loginStr;
	}

	public String getLoginStrSqlpus() {
		return _loginStrSqlplus;
	}

	public String getDbUser() {
		return _dbUser;
	}

	public String getDbPasswd() {
		return _dbPasswd;
	}

	public String getDbSid() {
		return _dbSid;
	}

	public String getDbHost() {
		return _dbHost;
	}

	public int getDbPort() {
		return _dbPort;
	}

	public String getJdbcUrl() {
		return _jdbcUrl;
	}

	public OdbCli() {
		// do nothing explicitly
	}

	public boolean init(String login_key, String db_instance, int max_pool_size) {
		_loginKey = login_key;
		_dbInstance = db_instance;
		_maxPoolSize = max_pool_size;
		String login_str_ = LoginPasswd.DecryptLogin(_loginKey, _dbInstance);
		if (PubMethod.IsBlank(login_str_)) {
			L.warn("obtain $DB_LOGIN_FILE login key [{}] failed", _loginKey);
			return false;
		}
		return init(login_str_, max_pool_size);
	}

	public boolean init(String login_str, int max_pool_size) {
		// login_str fmt: USER/PASSWD@SID:IP:PORT, eg: sttl/sttl123@orcl:*************:1521
		_maxPoolSize = max_pool_size;
		String db_user_rest_[] = login_str.split("/", 2);
		if (db_user_rest_.length != 2) {
			L.warn("login_str [{}] split user by '/' error, {}", login_str, db_user_rest_.length);
			return false;
		}

		String db_passwd_rest_[] = db_user_rest_[1].split("(?<!\\\\)@", 2);
		if (db_passwd_rest_.length != 2) {
			L.warn("login_str [{}] split passwd by '(?<!\\\\)@' error, {}", login_str, db_passwd_rest_.length);
			return false;
		}

		String db_sid_host_port_[] = db_passwd_rest_[1].split("(?<!\\\\):");
		if (db_sid_host_port_.length != 3) {
			L.warn("login_str [{}] split sid:host:port by '(?<!\\\\):' error, {}", login_str, db_sid_host_port_.length);
			return false;
		}
		for (int i = 0; i < db_sid_host_port_.length; ++i) {
			db_sid_host_port_[i] = db_sid_host_port_[i].replaceAll("\\\\:", ":");
		}

		String db_user_ = db_user_rest_[0];
		String db_passwd_ = db_passwd_rest_[0].replaceAll("\\\\@", "@");
		String db_sid_ = db_sid_host_port_[0];
		String db_host_ = db_sid_host_port_[1];
		int db_port_ = (int) PubMethod.A2L(db_sid_host_port_[2]);
		_loginStr = login_str;
		L.debug("user [{}], sid [{}], host [{}], port [{}]", db_user_, db_sid_, db_host_, db_port_);
		return init(db_user_, db_passwd_, db_sid_, db_host_, db_port_, max_pool_size);
	}

	public boolean init(String db_user, String db_passwd, String db_sid, String db_host, int db_port, int max_pool_size) {
		_maxPoolSize = max_pool_size;
//        String jdbc_url_ = null;
//        if (_ExpIpv6.matcher(db_host).find()) {
//            jdbc_url_ = String.format(
//                    "%s(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=[%s])(PORT=%d))(CONNECT_DATA=(SERVICE_NAME=%s)))",
//                    _JDBC_PFX_MYSQL, db_host, db_port, db_sid);

//            jdbc_url_ = String.format(
//                    "%s//%s:%d/%s?serverTimezone=GMT%2b8&useUnicode=true&characterEncoding=utf8&autoReconnect=true&rewriteBatchedStatements=true&allowMultiQueries=true&useSSL=true",
//                    _JDBC_PFX_MYSQL, db_host, db_port, db_sid);
//
//        } else {
//            jdbc_url_ = String.format("%s%s:%d:%s", _JDBC_PFX_MYSQL, db_host, db_port, db_sid);
//        }

		String jdbc_url_ = String.format(
				"%s//%s:%d/%s?serverTimezone=GMT%%2b8&useUnicode=true&characterEncoding=utf8&autoReconnect=true&rewriteBatchedStatements=true&allowMultiQueries=true&useSSL=true&allowLoadLocalInfile=true",
				_JDBC_PFX_MYSQL, db_host, db_port, db_sid);
		L.info("jdbc_url_=[{}]", jdbc_url_);
		L.info("db_user=[{}], db_sid=[{}], db_host=[{}], db_port=[{}]", new Object[] { db_user,  db_sid, db_host, db_port });
		if (_cpds != null) {
			L.warn("_cpds not null, will be re-initialized");
		}
//		_cpds = new ComboPooledDataSource();
		try {
			// add geyongan start 修改配置参数
//			_cpds.setDriverClass("com.mysql.cj.jdbc.Driver");
//			_cpds.setJdbcUrl(jdbc_url_);
//			_cpds.setUser(db_user);
//			_cpds.setPassword(db_passwd);
//			_cpds.setMinPoolSize(5);
//			_cpds.setInitialPoolSize(5);
//			_cpds.setMaxPoolSize(5);
//			_cpds.setAcquireIncrement(5);
//			_cpds.setMaxIdleTime(3600);
//			_cpds.setPreferredTestQuery("SELECT 1 FROM DUAL");
//			_cpds.setIdleConnectionTestPeriod(180);
			// add geyongan end
			_cpds = new DruidDataSource();
			_cpds.setDriverClassName("com.mysql.cj.jdbc.Driver");
			_cpds.setUrl(jdbc_url_);
			_cpds.setUsername(db_user);
			_cpds.setPassword(db_passwd);
			_cpds.setMinIdle(5);
			_cpds.setInitialSize(5);
			_cpds.setMaxActive(5);
			_cpds.setMaxWait(60000);
			_cpds.setTimeBetweenEvictionRunsMillis(60000);
			_cpds.setMinEvictableIdleTimeMillis(3600000);
			_cpds.setValidationQuery("SELECT 1 FROM DUAL");
			_cpds.setTestWhileIdle(true);
		} catch (Exception e) {
			L.warn("init db connection pool exception", e);
			return false;
		}
		Connection connection = null;
		try {
			connection = _cpds.getConnection();
		} catch (Exception e) {
			L.warn("init db connection pool exception2", e);
		}finally {
			DbUtil.close(connection);
		}
		String sql_ = "select 1";
		try {
			queryForTs(sql_);
			L.info("init db connection, db_user=[{}], db_sid=[{}], db_ip=[{}]", new Object[] { db_user, db_sid, db_host });
		} catch (Exception e) {
			L.warn("exec [{}] error", sql_, e);
			return false;
		}
		_loginStrSqlplus = String.format("%s/%s@%s", db_user, db_passwd, db_sid);
		if (_ExpExclamation.matcher(_loginStrSqlplus).find()) {
			_loginStrSqlplus = _ExpExclamation.matcher(_loginStrSqlplus).replaceAll("\\\\!");
		}
		_dbUser = db_user;
		_dbPasswd = db_passwd;
		_dbSid = db_sid;
		_dbHost = db_host;
		_dbPort = db_port;
		_jdbcUrl = jdbc_url_;
		return true;
	}

	public Connection getConnection() {
		Connection conn = null;
		try {
			conn = _cpds.getConnection();
			if (conn.getAutoCommit() == true)
				conn.setAutoCommit(false);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return conn;
	}

	/***
	 * a canonical Oracle connection can be used to access CLOB or BLOB field
	 *
	 * @return a canonical Oracle connection
	 * @throws SQLException
	 */
	public Connection getCanonicalConnection() throws SQLException {
		DriverManager.registerDriver(new com.mysql.cj.jdbc.Driver());
		Connection conn_ = DriverManager.getConnection(_jdbcUrl, _dbUser, _dbPasswd);
		return conn_;
	}

	/***
	 * æ‰¹é‡�æ�’å…¥æ•°æ�®
	 *
	 * @param sql
	 * @param params
	 * @throws SQLException
	 */
	public void insetBatch(Connection conn, String sql, Object[][] params) throws SQLException {
		QueryRunner qr = new QueryRunner(_cpds);
		qr.batch(conn, sql, params);
	}

	public void insetBatchFill(String sql, Object[][] params) {
		Connection conn = startTransaction();
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			PreparedStatement ps = conn.prepareStatement(sql);
			for (Object[] str : params) {
				qr.fillStatement(ps, str);
				ps.addBatch();
			}
			ps.executeBatch();
			commit(conn);
		} catch (SQLException e) {
			rollback(conn);
			L.warn("insertBatch for object[][] exception, [{}]", sql, e);
		} finally {
			close(conn);
		}
	}

	/**
	 * è¿”å›žæŒ‡å®šåˆ—å��è®°å½•
	 *
	 * @param sql
	 * @param params
	 * @return
	 */
	public List<Object> queryForOListObject(String sql, String params) {
		List<Object> obj = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			obj = qr.query(sql, new ColumnListHandler<Object>(params));
		} catch (SQLException e) {
			L.warn("query for obj list exception, [{}]", sql, e);
		}
		return obj;
	}

	/**
	 * æŸ¥è¯¢è¿”å›žå�•ä¸ªå¯¹è±¡
	 *
	 * @param sql
	 * @param params
	 * @param clazz
	 * @return
	 */
	public <T> T queryForObject(String sql, Class<T> clazz, Object... params) {
		T obj = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			BeanHandler<T> rsh = new BeanHandler<T>(clazz);
			obj = (T) qr.query(sql, rsh, params);
		} catch (SQLException e) {
			L.warn("query for one obj exception, [{}]", sql, e);
		}
		return obj;
	}

	/**
	 * è¿”å›žå¤šè¡Œ
	 *
	 * @param sql
	 * @param clazz
	 * @param params
	 * @return
	 */
	public <T> List<T> queryForOList(String sql, Class<T> clazz, Object... params) {
		List<T> obj = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			BeanListHandler<T> rsh = new BeanListHandler<T>(clazz);
			obj = (List<T>) qr.query(sql, rsh, params);
		} catch (SQLException e) {
			L.warn("query for obj list exception, [{}]", sql, e);
		}
		return obj;
	}

	public <T> List<T> queryForOListWithThrow(String sql, Class<T> clazz, Object... params) throws SQLException {
		List<T> obj = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			BeanListHandler<T> rsh = new BeanListHandler<T>(clazz);
			obj = (List<T>) qr.query(sql, rsh, params);
		} catch (SQLException e) {
			L.warn("query for obj list exception, [{}]", sql, e);
			throw e;
		}
		return obj;
	}

	/**
	 * å¤šè¡¨æŸ¥è¯¢è¿”å›žç¬¬ä¸€æ�¡è®°å½•
	 *
	 * @param sql
	 * @param params
	 * @return
	 */
	public Object[] queryForTs(String sql, Object... params) {
		Object[] objs = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			objs = qr.query(sql, new ArrayHandler(), params);
		} catch (SQLException e) {
			L.warn("query for one result exception, [{}]", sql, e);
		}
		return objs;
	}

	/**
	 * å¤šè¡¨æŸ¥è¯¢è¿”å›žListé›†å�ˆ
	 *
	 * @param sql
	 * @param params
	 * @return
	 */
	public List<Object[]> queryForTsList(String sql, Object... params) {
		List<Object[]> objList = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			objList = qr.query(sql, new ArrayListHandler(), params);
		} catch (SQLException e) {
			L.warn("query for result list exception, [{}]", sql, e);
		}
		return objList;
	}

	public List<Object[]> queryForTsListWithThrow(String sql, Object... params) throws SQLException {
		List<Object[]> objList = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			objList = qr.query(sql, new ArrayListHandler(), params);
		} catch (SQLException e) {
			L.warn("query for result list exception, [{}]", sql, e);
			throw e;
		}
		return objList;
	}

	/**
	 * äº‹åŠ¡ç”±æ•°æ�®æº�ç®¡ç�†
	 *
	 * @param sql
	 * @param params
	 * @return
	 */
	public int update(String sql, Object... params) {
		int affectRow = 0;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			affectRow = qr.update(sql, params);
		} catch (SQLException e) {
			L.warn("update exception, [{}]", sql, e);
		}
		return affectRow;
	}

	public int updateWithThrow(String sql, Object... params) throws SQLException {
		int affectRow = 0;
		QueryRunner qr = new QueryRunner(_cpds);
		affectRow = qr.update(sql, params);
		return affectRow;
	}

	/**
	 * æ”¯æŒ�äº‹åŠ¡å¤„ç�†
	 *
	 * @param conn
	 * @param sql
	 * @param params
	 * @return
	 * @throws SQLException
	 */
	public int update(Connection conn, String sql, Object... params) throws SQLException {
		int affectRow = 0;
		QueryRunner qr = new QueryRunner();
		affectRow = qr.update(conn, sql, params);
		return affectRow;
	}

	public <T> List<T> queryForOList(Connection conn, String sql, Class<T> clazz, Object... params) {
		List<T> obj = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			BeanListHandler<T> rsh = new BeanListHandler<T>(clazz);
			obj = (List<T>) qr.query(conn, sql, rsh, params);
		} catch (SQLException e) {
			L.warn("transaction {}, query for obj list exception, [{}]", conn, sql, e);
		}
		return obj;
	}

	public List<Object[]> queryForTsList(Connection conn, String sql, Object... params) {
		List<Object[]> objList = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			objList = qr.query(conn, sql, new ArrayListHandler(), params);
		} catch (SQLException e) {
			L.warn("transaction {}, query for result list exception, [{}]", conn, sql, e);
		}
		return objList;
	}

	public List<Object[]> queryForTsListWithThrow(Connection conn, String sql, Object... params) throws SQLException {
		List<Object[]> objList = null;
		try {
			QueryRunner qr = new QueryRunner(_cpds);
			objList = qr.query(conn, sql, new ArrayListHandler(), params);
		} catch (SQLException e) {
			L.warn("transaction {}, query for result list exception, [{}]", conn, sql, e);
			throw e;
		}
		return objList;
	}

	public Connection startTransaction() {
		Connection conn = null;
		try {
			conn = this.getConnection();
			conn.setAutoCommit(false);
		} catch (SQLException e) {
			L.warn("transaction {}, begin exception", conn.toString(), e);
		}
		return conn;
	}

	public void commit(Connection conn) {
		if (conn != null) {
			try {
				conn.commit();
			} catch (SQLException e) {
				L.warn("transaction {}, commit exception, try auto rollback", conn.toString(), e);
				rollback(conn);
			}
		}
	}

	public void rollback(Connection conn) {
		if (conn != null) {
			try {
				conn.rollback();
				L.info("transaction {}, rollback done", conn.toString());
			} catch (SQLException e) {
				L.warn("transaction {}, rollback exception, :-(", conn.toString(), e);
			}
		}
	}

	public void close(Connection conn) {
		if (conn != null) {
			DbUtils.closeQuietly(conn);
		}
	}

	public void close(ResultSet rs, Statement stmt, Connection conn) {
		if (rs != null) {
			try {
				rs.close();
			} catch (Exception e) {
				L.warn("close ResultSet exception, pls ignore", e);
			}
		}
		if (stmt != null) {
			try {
				stmt.close();
			} catch (Exception e) {
				L.warn("close Statement exception, pls ignore", e);
			}
		}
		if (conn != null) {
			try {
				conn.close();
			} catch (Exception e) {
				L.warn("close Connection exception, pls ignore", e);
			}
		}
	}






//	private static Logger L = LoggerFactory.getLogger(OdbCli.class);
//	private String _loginKey;
//	private String _dbInstance;
//	private int _maxPoolSize;
//	private String _loginStr;
//	private String _loginStrSqlplus;
//	private String _dbUser;
//	private String _dbPasswd;
//	private String _dbSid;
//	private String _dbHost;
//	private int _dbPort;
//	private String _jdbcUrl;
//	private ComboPooledDataSource _cpds = null;
//	private static final String _JDBC_PFX_ORACLE = "jdbc:oracle:thin:@";
//	private static final Pattern _ExpExclamation = Pattern.compile("!");
//	private static final Pattern _ExpIpv6 = Pattern.compile(":.*:");
//
//	public String getLoginKey() {
//		return _loginKey;
//	}
//
//	public int getMaxPoolSize() {
//		return _maxPoolSize;
//	}
//
//	public String getLoginStr() {
//		return _loginStr;
//	}
//
//	public String getLoginStrSqlpus() {
//		return _loginStrSqlplus;
//	}
//
//	public String getDbUser() {
//		return _dbUser;
//	}
//
//	public String getDbPasswd() {
//		return _dbPasswd;
//	}
//
//	public String getDbSid() {
//		return _dbSid;
//	}
//
//	public String getDbHost() {
//		return _dbHost;
//	}
//
//	public int getDbPort() {
//		return _dbPort;
//	}
//
//	public String getJdbcUrl() {
//		return _jdbcUrl;
//	}
//
//	public OdbCli() {
//		// do nothing explicitly
//	}
//
//	public boolean init(String login_key, String db_instance, int max_pool_size) {
//		_loginKey = login_key;
//		_dbInstance = db_instance;
//		_maxPoolSize = max_pool_size;
//		String login_str_ = LoginPasswd.DecryptLogin(_loginKey, _dbInstance);
//		if (PubMethod.IsBlank(login_str_)) {
//			L.warn("obtain $DB_LOGIN_FILE login key [{}] failed", _loginKey);
//			return false;
//		}
//		return init(login_str_, max_pool_size);
//	}
//
//	public boolean init(String login_str, int max_pool_size) {
//		// login_str fmt: USER/PASSWD@SID:IP:PORT, eg: sttl/sttl123@orcl:*************:1521
//		_maxPoolSize = max_pool_size;
//		String db_user_rest_[] = login_str.split("/", 2);
//		if (db_user_rest_.length != 2) {
//			L.warn("login_str [{}] split user by '/' error, {}", login_str, db_user_rest_.length);
//			return false;
//		}
//
//		String db_passwd_rest_[] = db_user_rest_[1].split("(?<!\\\\)@", 2);
//		if (db_passwd_rest_.length != 2) {
//			L.warn("login_str [{}] split passwd by '(?<!\\\\)@' error, {}", login_str, db_passwd_rest_.length);
//			return false;
//		}
//
//		String db_sid_host_port_[] = db_passwd_rest_[1].split("(?<!\\\\):");
//		if (db_sid_host_port_.length != 3) {
//			L.warn("login_str [{}] split sid:host:port by '(?<!\\\\):' error, {}", login_str, db_sid_host_port_.length);
//			return false;
//		}
//		for (int i = 0; i < db_sid_host_port_.length; ++i) {
//			db_sid_host_port_[i] = db_sid_host_port_[i].replaceAll("\\\\:", ":");
//		}
//
//		String db_user_ = db_user_rest_[0];
//		String db_passwd_ = db_passwd_rest_[0].replaceAll("\\\\@", "@");
//		String db_sid_ = db_sid_host_port_[0];
//		String db_host_ = db_sid_host_port_[1];
//		int db_port_ = (int) PubMethod.A2L(db_sid_host_port_[2]);
//		_loginStr = login_str;
//		L.debug("user [{}], sid [{}], host [{}], port [{}]", db_user_, db_sid_, db_host_, db_port_);
//		return init(db_user_, db_passwd_, db_sid_, db_host_, db_port_, max_pool_size);
//	}
//
//	public boolean init(String db_user, String db_passwd, String db_sid, String db_host, int db_port, int max_pool_size) {
//		_maxPoolSize = max_pool_size;
//		String jdbc_url_ = null;
//		if (_ExpIpv6.matcher(db_host).find()) {
//			jdbc_url_ = String.format(
//					"%s(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=[%s])(PORT=%d))(CONNECT_DATA=(SERVICE_NAME=%s)))",
//					_JDBC_PFX_ORACLE, db_host, db_port, db_sid);
//		} else {
//			jdbc_url_ = String.format("%s%s:%d:%s", _JDBC_PFX_ORACLE, db_host, db_port, db_sid);
//		}
//
//		if (_cpds != null) {
//			L.warn("_cpds not null, will be re-initialized");
//		}
//		_cpds = new ComboPooledDataSource();
//		try {
//			_cpds.setDriverClass("oracle.jdbc.driver.OracleDriver");
//			_cpds.setJdbcUrl(jdbc_url_);
//			_cpds.setUser(db_user);
//			_cpds.setPassword(db_passwd);
//
//			_cpds.setMinPoolSize(1);
//			_cpds.setInitialPoolSize(1);
//			_cpds.setMaxPoolSize(_maxPoolSize);
//			_cpds.setAcquireIncrement(1);
//			_cpds.setMaxIdleTime(3600);
//			_cpds.setPreferredTestQuery("SELECT 1 FROM DUAL");
//			_cpds.setIdleConnectionTestPeriod(180);
//		} catch (Exception e) {
//			L.warn("init db connection pool exception", e);
//			return false;
//		}
//
//		String sql_ = "select count(*) from dual";
//		try {
//			queryForTs(sql_);
//			L.info("init db connection, db_user=[{}], db_sid=[{}], db_ip=[{}]", new Object[] { db_user, db_sid, db_host });
//		} catch (Exception e) {
//			L.warn("exec [{}] error", sql_, e);
//			return false;
//		}
//		_loginStrSqlplus = String.format("%s/%s@%s", db_user, db_passwd, db_sid);
//		if (_ExpExclamation.matcher(_loginStrSqlplus).find()) {
//			_loginStrSqlplus = _ExpExclamation.matcher(_loginStrSqlplus).replaceAll("\\\\!");
//		}
//		_dbUser = db_user;
//		_dbPasswd = db_passwd;
//		_dbSid = db_sid;
//		_dbHost = db_host;
//		_dbPort = db_port;
//		_jdbcUrl = jdbc_url_;
//		return true;
//	}
//
//	public Connection getConnection() {
//		Connection conn = null;
//		try {
//			conn = _cpds.getConnection();
//			if (conn.getAutoCommit() == true)
//				conn.setAutoCommit(false);
//		} catch (SQLException e) {
//			e.printStackTrace();
//		}
//		return conn;
//	}
//
//	/***
//	 * a canonical Oracle connection can be used to access CLOB or BLOB field
//	 *
//	 * @return a canonical Oracle connection
//	 * @throws SQLException
//	 */
//	public Connection getCanonicalConnection() throws SQLException {
//		DriverManager.registerDriver(new oracle.jdbc.driver.OracleDriver());
//		Connection conn_ = DriverManager.getConnection(_jdbcUrl, _dbUser, _dbPasswd);
//		return conn_;
//	}
//
//	/***
//	 * æ‰¹é‡�æ�’å…¥æ•°æ�®
//	 *
//	 * @param sql
//	 * @param params
//	 * @throws SQLException
//	 */
//	public void insetBatch(Connection conn, String sql, Object[][] params) throws SQLException {
//		QueryRunner qr = new QueryRunner(_cpds);
//		qr.batch(conn, sql, params);
//	}
//
//	public void insetBatchFill(String sql, Object[][] params) {
//		Connection conn = startTransaction();
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			PreparedStatement ps = conn.prepareStatement(sql);
//			for (Object[] str : params) {
//				qr.fillStatement(ps, str);
//				ps.addBatch();
//			}
//			ps.executeBatch();
//			commit(conn);
//		} catch (SQLException e) {
//			rollback(conn);
//			L.warn("insertBatch for object[][] exception, [{}]", sql, e);
//		} finally {
//			close(conn);
//		}
//	}
//
//	/**
//	 * è¿”å›žæŒ‡å®šåˆ—å��è®°å½•
//	 *
//	 * @param sql
//	 * @param params
//	 * @return
//	 */
//	public List<Object> queryForOListObject(String sql, String params) {
//		List<Object> obj = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			obj = qr.query(sql, new ColumnListHandler<Object>(params));
//		} catch (SQLException e) {
//			L.warn("query for obj list exception, [{}]", sql, e);
//		}
//		return obj;
//	}
//
//	/**
//	 * æŸ¥è¯¢è¿”å›žå�•ä¸ªå¯¹è±¡
//	 *
//	 * @param sql
//	 * @param param
//	 * @param clazz
//	 * @return
//	 */
//	public <T> T queryForObject(String sql, Class<T> clazz, Object... params) {
//		T obj = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			BeanHandler<T> rsh = new BeanHandler<T>(clazz);
//			obj = (T) qr.query(sql, rsh, params);
//		} catch (SQLException e) {
//			L.warn("query for one obj exception, [{}]", sql, e);
//		}
//		return obj;
//	}
//
//	/**
//	 * è¿”å›žå¤šè¡Œ
//	 *
//	 * @param sql
//	 * @param clazz
//	 * @param param
//	 * @return
//	 */
//	public <T> List<T> queryForOList(String sql, Class<T> clazz, Object... params) {
//		List<T> obj = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			BeanListHandler<T> rsh = new BeanListHandler<T>(clazz);
//			obj = (List<T>) qr.query(sql, rsh, params);
//		} catch (SQLException e) {
//			L.warn("query for obj list exception, [{}]", sql, e);
//		}
//		return obj;
//	}
//
//	public <T> List<T> queryForOListWithThrow(String sql, Class<T> clazz, Object... params) throws SQLException {
//		List<T> obj = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			BeanListHandler<T> rsh = new BeanListHandler<T>(clazz);
//			obj = (List<T>) qr.query(sql, rsh, params);
//		} catch (SQLException e) {
//			L.warn("query for obj list exception, [{}]", sql, e);
//			throw e;
//		}
//		return obj;
//	}
//
//	/**
//	 * å¤šè¡¨æŸ¥è¯¢è¿”å›žç¬¬ä¸€æ�¡è®°å½•
//	 *
//	 * @param sql
//	 * @param params
//	 * @return
//	 */
//	public Object[] queryForTs(String sql, Object... params) {
//		Object[] objs = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			objs = qr.query(sql, new ArrayHandler(), params);
//		} catch (SQLException e) {
//			L.warn("query for one result exception, [{}]", sql, e);
//		}
//		return objs;
//	}
//
//	/**
//	 * å¤šè¡¨æŸ¥è¯¢è¿”å›žListé›†å�ˆ
//	 *
//	 * @param sql
//	 * @param params
//	 * @return
//	 */
//	public List<Object[]> queryForTsList(String sql, Object... params) {
//		List<Object[]> objList = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			objList = qr.query(sql, new ArrayListHandler(), params);
//		} catch (SQLException e) {
//			L.warn("query for result list exception, [{}]", sql, e);
//		}
//		return objList;
//	}
//
//	public List<Object[]> queryForTsListWithThrow(String sql, Object... params) throws SQLException {
//		List<Object[]> objList = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			objList = qr.query(sql, new ArrayListHandler(), params);
//		} catch (SQLException e) {
//			L.warn("query for result list exception, [{}]", sql, e);
//			throw e;
//		}
//		return objList;
//	}
//
//	/**
//	 * äº‹åŠ¡ç”±æ•°æ�®æº�ç®¡ç�†
//	 *
//	 * @param sql
//	 * @param params
//	 * @return
//	 */
//	public int update(String sql, Object... params) {
//		int affectRow = 0;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			affectRow = qr.update(sql, params);
//		} catch (SQLException e) {
//			L.warn("update exception, [{}]", sql, e);
//		}
//		return affectRow;
//	}
//
//	public int updateWithThrow(String sql, Object... params) throws SQLException {
//		int affectRow = 0;
//		QueryRunner qr = new QueryRunner(_cpds);
//		affectRow = qr.update(sql, params);
//		return affectRow;
//	}
//
//	/**
//	 * æ”¯æŒ�äº‹åŠ¡å¤„ç�†
//	 *
//	 * @param conn
//	 * @param sql
//	 * @param params
//	 * @return
//	 * @throws SQLException
//	 */
//	public int update(Connection conn, String sql, Object... params) throws SQLException {
//		int affectRow = 0;
//		QueryRunner qr = new QueryRunner();
//		affectRow = qr.update(conn, sql, params);
//		return affectRow;
//	}
//
//	public <T> List<T> queryForOList(Connection conn, String sql, Class<T> clazz, Object... params) {
//		List<T> obj = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			BeanListHandler<T> rsh = new BeanListHandler<T>(clazz);
//			obj = (List<T>) qr.query(conn, sql, rsh, params);
//		} catch (SQLException e) {
//			L.warn("transaction {}, query for obj list exception, [{}]", conn, sql, e);
//		}
//		return obj;
//	}
//
//	public List<Object[]> queryForTsList(Connection conn, String sql, Object... params) {
//		List<Object[]> objList = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			objList = qr.query(conn, sql, new ArrayListHandler(), params);
//		} catch (SQLException e) {
//			L.warn("transaction {}, query for result list exception, [{}]", conn, sql, e);
//		}
//		return objList;
//	}
//
//	public List<Object[]> queryForTsListWithThrow(Connection conn, String sql, Object... params) throws SQLException {
//		List<Object[]> objList = null;
//		try {
//			QueryRunner qr = new QueryRunner(_cpds);
//			objList = qr.query(conn, sql, new ArrayListHandler(), params);
//		} catch (SQLException e) {
//			L.warn("transaction {}, query for result list exception, [{}]", conn, sql, e);
//			throw e;
//		}
//		return objList;
//	}
//
//	public Connection startTransaction() {
//		Connection conn = null;
//		try {
//			conn = this.getConnection();
//			conn.setAutoCommit(false);
//		} catch (SQLException e) {
//			L.warn("transaction {}, begin exception", conn.toString(), e);
//		}
//		return conn;
//	}
//
//	public void commit(Connection conn) {
//		if (conn != null) {
//			try {
//				conn.commit();
//			} catch (SQLException e) {
//				L.warn("transaction {}, commit exception, try auto rollback", conn.toString(), e);
//				rollback(conn);
//			}
//		}
//	}
//
//	public void rollback(Connection conn) {
//		if (conn != null) {
//			try {
//				conn.rollback();
//				L.info("transaction {}, rollback done", conn.toString());
//			} catch (SQLException e) {
//				L.warn("transaction {}, rollback exception, :-(", conn.toString(), e);
//			}
//		}
//	}
//
//	public void close(Connection conn) {
//		if (conn != null) {
//			DbUtils.closeQuietly(conn);
//		}
//	}
//
//	public void close(ResultSet rs, Statement stmt, Connection conn) {
//		if (rs != null) {
//			try {
//				rs.close();
//			} catch (Exception e) {
//				L.warn("close ResultSet exception, pls ignore", e);
//			}
//		}
//		if (stmt != null) {
//			try {
//				stmt.close();
//			} catch (Exception e) {
//				L.warn("close Statement exception, pls ignore", e);
//			}
//		}
//		if (conn != null) {
//			try {
//				conn.close();
//			} catch (Exception e) {
//				L.warn("close Connection exception, pls ignore", e);
//			}
//		}
//	}
}
