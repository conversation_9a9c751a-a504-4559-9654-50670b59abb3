package com.hp.cmcc.bboss.pub.odb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.handler.EntityListHandler;
import cn.hutool.db.sql.SqlExecutor;
import com.hp.cmcc.bboss.pub.odb.entity.DbBizTypeDefRec;
import com.hp.cmcc.bboss.pub.odb.entity.DbRawAlmRec;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.pub.util.StrTruncator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public class OdbUtils {
	private static Logger L = LoggerFactory.getLogger(OdbUtils.class);
	private static final String _Q_SEQ_CTL = "SELECT PKG_SEQ_CTL.NEXT_SEQ(?, ?) FROM DUAL";
	private static final String _I_RAW_ALM = "INSERT INTO RAW_ALM (MMDD, MODULE, INST_NM, HOST_NM, PID, ALM_TS, ALM_TM, "
			+ "ALM_LEVEL, ALM_CODE, ALM_MSG, ALM_JSON, ALM_KPI) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String _Q_SUR_ORB_IORS = "SELECT VALUE FROM SUR_ORB_IORS WHERE NAME = ?";
	private static final String _Q_BIZ_TYPE_DEF = "SELECT BIZ_TYPE, TYPE_ID, ENABLED, EFF_DATE, EXP_DATE FROM "
			+ "BIZ_TYPE_DEF WHERE ENABLED = 1";


	private static final String _Q_SEQ_CTL_QUERY = "SELECT seq_id FROM seq_ctl WHERE seq_dt = ? AND seq_nm = ?";
	private static final String _Q_SEQ_CTL_INSERT = "INSERT INTO seq_ctl (seq_dt, seq_nm, seq_id, seq_tm) VALUES (?, ?, 1, now())";
	private static final String _Q_SEQ_CTL_UPDATE = "UPDATE seq_ctl SET seq_id = seq_id + 1, seq_tm = now() WHERE seq_dt = ? AND seq_nm = ?";

	public int nextSeq(int ymd, String seq_nm) {
		if (ymd < 19700101 || ymd > 99991231) {
			String alm_ = PubMethod.FmtArgs("ymd %d out of range, should between [19700101,99991231]", ymd);
			L.warn(alm_);
			throw new RuntimeException(alm_);
		}
		OdbCli cli_ = OdbAgt.GetBizInstance();

		Connection connection = cli_.startTransaction();

		List<Entity> query;
		int seq_ = 1;
		try {
			query = SqlExecutor.query(connection, _Q_SEQ_CTL_QUERY, new EntityListHandler(), ymd, seq_nm);

			int count;
			if(CollectionUtil.isEmpty(query)){

				count = SqlExecutor.execute(connection, _Q_SEQ_CTL_INSERT, ymd, seq_nm);

			} else {

				Entity entity = query.get(0);
				String seq_id = entity.getStr("seq_id");
				seq_ = Integer.valueOf(seq_id) + 1;
				count = SqlExecutor.execute(connection, _Q_SEQ_CTL_UPDATE, ymd, seq_nm);
			}
			cli_.commit(connection);
		} catch (SQLException e) {
			cli_.rollback(connection);
			L.error("get next seq error");
			throw new RuntimeException(e);
		} finally {
			cli_.close(connection);
		}

		L.debug("PKG_SEQ_CTL.NEXT_SEQ({},{}) returns {}", new Object[] { ymd, seq_nm, seq_ });
		return seq_;
	}

	public int addRawAlm(Connection conn, DbRawAlmRec rec) {
		if (rec.getAlm_ts() == null)
			rec.setAlm_ts(System.currentTimeMillis());
		rec.setAlm_tm(new Timestamp(rec.getAlm_ts()));
		rec.setMmdd(Integer.parseInt(PubMethod.Long2Str(rec.getAlm_ts(), PubMethod.TimeStrFmt.Fmt14).substring(4, 8)));
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		rec.setModule(PubMethod.IsBlank(cmdline_._module) ? "-" : cmdline_._module);
		rec.setInst_nm(PubMethod.IsBlank(cmdline_._instNm) ? "-" : cmdline_._instNm);
		rec.setHost_nm(PubMethod._JvmHost);
		rec.setPid(Integer.parseInt(PubMethod._JvmPid));
		if (PubMethod.IsBlank(rec.getAlm_level()))
			rec.setAlm_level("-");
		if (rec.getAlm_code() == null)
			rec.setAlm_code(0);
		if (rec.getAlm_msg() == null) {
			rec.setAlm_msg("-");
		} else {
			StrTruncator truncator_ = StrTruncator.GetStrTruncator(Charset.defaultCharset().name());
			rec.setAlm_msg(truncator_.truncate(rec.getAlm_msg(), 4000));
		}
		if (rec.getAlm_json() != null) {
			StrTruncator truncator_ = StrTruncator.GetStrTruncator(Charset.defaultCharset().name());
			rec.setAlm_json(truncator_.truncate(rec.getAlm_json(), 4000));
		}
		if (rec.getAlm_kpi() != null) {
			StrTruncator truncator_ = StrTruncator.GetStrTruncator(Charset.defaultCharset().name());
			rec.setAlm_kpi(truncator_.truncate(rec.getAlm_kpi(), 2000));
		}
		int rows_affected_ = 0;
		OdbCli cli_ = OdbAgt.GetBizInstance();
		if (conn == null) {
			rows_affected_ = cli_.update(_I_RAW_ALM, rec.asInsertObjArray());
			L.info("add {}, rows_affected_={}", rec.toGsonStr(), rows_affected_);
		} else {
			try {
				rows_affected_ = cli_.update(conn, _I_RAW_ALM, rec.asInsertObjArray());
				L.info("transaction {}, add {}, rows_affected_={}", conn.toString(), rec.toGsonStr(), rows_affected_);
			} catch (Exception e) {
				L.warn("transaction{}, add error, {}", conn.toString(), rec.toGsonStr());
			}
		}
		return rows_affected_;
	}

	public String getIorStr(String ior_name) {
		OdbCli cli_ = OdbAgt.GetRatInstance();
		return getIorStr(cli_, ior_name);
	}

	public String getIorStr(OdbCli cli, String ior_name) {
		Object[] rec_ = cli.queryForTs(_Q_SUR_ORB_IORS, ior_name);
		if (rec_ == null) {
			L.warn("query SUR_ORB_IORS with {} returns null", ior_name);
			return null;
		} else if (rec_.length == 0) {
			L.warn("query SUR_ORB_IORS with {} returns empty", ior_name);
			return null;
		}
		String value_ = (String) rec_[0];
		if (PubMethod.IsBlank(value_))
			L.warn("query SUR_ORB_IORS with {} returns blank", ior_name);
		else
			L.debug("query SUR_ORB_IORS with {} returns {}", ior_name, value_);
		return value_;
	}

	public boolean getBizTypeDef(Map<String, DbBizTypeDefRec> biz_type_map, Map<Integer, DbBizTypeDefRec> type_id_map) {
		if (biz_type_map != null) {
			biz_type_map.clear();
		}
		if (type_id_map != null) {
			type_id_map.clear();
		}
		OdbCli cli_ = OdbAgt.GetBizInstance();
		List<DbBizTypeDefRec> db_result_ = cli_.queryForOList(_Q_BIZ_TYPE_DEF, DbBizTypeDefRec.class); //业务类型定义表加载
		if (db_result_ == null) {
			L.warn("query BIZ_TYPE_DEF returns null");
			return false;
		} else if (db_result_.isEmpty()) {
			L.warn("query BIZ_TYPE_DEF empty result, pls chk");
			return false;
		} else {
			L.debug("query BIZ_TYPE_DEF {} records fetched", db_result_.size());
		}
		for (DbBizTypeDefRec rec_ : db_result_) {
			if (biz_type_map != null) {
				biz_type_map.put(rec_.getBiz_type(), rec_);
			}
			if (type_id_map != null) {
				type_id_map.put(rec_.getType_id(), rec_);
			}
		}
		return true;
	}
}
