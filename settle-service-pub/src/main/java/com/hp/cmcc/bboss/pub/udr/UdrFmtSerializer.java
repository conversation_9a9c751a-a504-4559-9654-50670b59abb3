package com.hp.cmcc.bboss.pub.udr;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;

import java.util.ArrayList;
import java.util.HashMap;

public class UdrFmtSerializer extends Serializer<UdrFmt> {
    @Override
    public void write(Kryo kryo, Output output, UdrFmt object) {
        kryo.writeObject(output, object._eFields);
        kryo.writeObject(output, object._uFields);
        kryo.writeObject(output, object._gsonStr);
        kryo.writeObject(output, object._auxMap);
        kryo.writeObject(output, object._uList);
    }

    @Override
    public UdrFmt read(Kryo kryo, Input input, Class<UdrFmt> type) {
        UdrFmt fmt = new UdrFmt();
        fmt._eFields = kryo.readObject(input, String[].class);
        fmt._uFields = kryo.readObject(input, String[].class);
        fmt._gsonStr = kryo.readObject(input, String.class);
        fmt._auxMap = kryo.readObject(input, HashMap.class);
        fmt._uList = kryo.readObject(input, ArrayList.class);
        return fmt;
    }
}