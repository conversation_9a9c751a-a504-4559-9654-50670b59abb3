package com.hp.cmcc.bboss.pub.udr;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hp.cmcc.bboss.pub.util.GsonObj;
import com.hp.cmcc.bboss.pub.util.PubMethod;

public class UdrAccumulationInfo extends GsonObj {
	private static Logger L = LoggerFactory.getLogger(UdrAccumulationInfo.class);

	public class AccumulationItem {
		public String _productOfferCode;
		public int _isUsed;
		public Long _usedVolume;
		public Long _freeVolume;
	}

	public List<AccumulationItem> _itemList;

	public UdrAccumulationInfo() {
		_itemList = new ArrayList<AccumulationItem>();
	}

	public static UdrAccumulationInfo ParseAccumulationInfo(String accumulation_info) {
		if (PubMethod.IsBlank(accumulation_info))
			return null;
		Matcher m = UdrChargeInfo._AngleBrackets.matcher(accumulation_info);
		List<String> brackets_ = new ArrayList<String>();
		while (m.find()) {
			String s = m.group(1);
			brackets_.add(s.substring(1, s.length() - 1));
		}
		if (brackets_.isEmpty()) {
			L.warn("accumulation_info [{}] no angle-bracket pair found", accumulation_info);
			return null;
		}
		UdrAccumulationInfo info_ = new UdrAccumulationInfo();
		for (int i = 0; i < brackets_.size(); ++i) {
			String s = brackets_.get(i);
			String[] pair_ = s.split(":");
			if (pair_.length < 4) {
				L.warn("accumulation_info [{}] No. {} item [{}] split by ':' error", accumulation_info, i, s);
				return null;
			}
			AccumulationItem item_ = info_.new AccumulationItem();
			item_._productOfferCode = pair_[0];
			item_._isUsed = Integer.parseInt(pair_[1]);
			item_._usedVolume = Long.parseLong(pair_[2]);
			item_._freeVolume = Long.parseLong(pair_[3]);
			info_._itemList.add(item_);
		}
		return info_;
	}
}
