package com.hp.cmcc.bboss.pub.udr;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;

import java.util.ArrayList;

public class UdrFmtMsgSerializer extends Serializer<UdrFmtMsg> {
    @Override
    public void write(Kryo kryo, Output output, UdrFmtMsg object) {
        kryo.writeObject(output, object._rndmId);
        kryo.writeObject(output, object._udrFmtList);
    }

    @Override
    public UdrFmtMsg read(Kryo kryo, Input input, Class<UdrFmtMsg> type) {
        UdrFmtMsg msg = new UdrFmtMsg();
        msg._rndmId = kryo.readObject(input, String.class);
        msg._udrFmtList = kryo.readObject(input, ArrayList.class);
        return msg;
    }
}
