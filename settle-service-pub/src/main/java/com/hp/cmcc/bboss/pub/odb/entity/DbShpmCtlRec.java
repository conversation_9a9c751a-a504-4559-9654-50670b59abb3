package com.hp.cmcc.bboss.pub.odb.entity;

import java.sql.Timestamp;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbShpmCtlRec extends GsonObj {
	private Integer ver_mm;
	private String inst_nm;
	private String shpm_ver;
	private String agt_inst;
	private Integer ctl_flag;
	private Timestamp tm_rqst;
	private Timestamp tm_rsps;
	private Timestamp tm_done;
	private Long ts_rqst;
	private Long ts_rsps;
	private Long ts_done;
	private String err_msg;

	public Object[] asInsertObjArray() {
		Object[] o = new Object[7];
		o[0] = ver_mm;
		o[1] = inst_nm;
		o[2] = shpm_ver;
		o[3] = agt_inst;
		o[4] = ctl_flag;
		o[5] = tm_rqst;
		o[6] = ts_rqst;
		return o;
	}

	public Integer getVer_mm() {
		return ver_mm;
	}

	public void setVer_mm(Integer ver_mm) {
		this.ver_mm = ver_mm;
	}

	public String getInst_nm() {
		return inst_nm;
	}

	public void setInst_nm(String inst_nm) {
		this.inst_nm = inst_nm;
	}

	public String getShpm_ver() {
		return shpm_ver;
	}

	public void setShpm_ver(String shpm_ver) {
		this.shpm_ver = shpm_ver;
	}

	public String getAgt_inst() {
		return agt_inst;
	}

	public void setAgt_inst(String agt_inst) {
		this.agt_inst = agt_inst;
	}

	public Integer getCtl_flag() {
		return ctl_flag;
	}

	public void setCtl_flag(Integer ctl_flag) {
		this.ctl_flag = ctl_flag;
	}

	public Timestamp getTm_rqst() {
		return tm_rqst;
	}

	public void setTm_rqst(Timestamp tm_rqst) {
		this.tm_rqst = tm_rqst;
	}

	public Timestamp getTm_rsps() {
		return tm_rsps;
	}

	public void setTm_rsps(Timestamp tm_rsps) {
		this.tm_rsps = tm_rsps;
	}

	public Timestamp getTm_done() {
		return tm_done;
	}

	public void setTm_done(Timestamp tm_done) {
		this.tm_done = tm_done;
	}

	public Long getTs_rqst() {
		return ts_rqst;
	}

	public void setTs_rqst(Long ts_rqst) {
		this.ts_rqst = ts_rqst;
	}

	public Long getTs_rsps() {
		return ts_rsps;
	}

	public void setTs_rsps(Long ts_rsps) {
		this.ts_rsps = ts_rsps;
	}

	public Long getTs_done() {
		return ts_done;
	}

	public void setTs_done(Long ts_done) {
		this.ts_done = ts_done;
	}

	public String getErr_msg() {
		return err_msg;
	}

	public void setErr_msg(String err_msg) {
		this.err_msg = err_msg;
	}
}
