package com.hp.cmcc.bboss.pub.edb;

import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.*;
import java.util.*;
import java.util.Map.Entry;

public class EdbDupchkPool {
	private static Logger L = LoggerFactory.getLogger(EdbDupchkPool.class);
	private static final String _P_SYNC_OFF = "PRAGMA SYNCHRONOUS = OFF";
	private static final String _P_JOURNAL_MEM = "PRAGMA JOURNAL_MODE = MEMORY";
	private static final String _C_TBL_DUPCHK = "CREATE TABLE IF NOT EXISTS DUPCHK_%02d "
			+ "(DUP_KEY TEXT PRIMARY KEY, DB_TM TEXT NOT NULL) WITHOUT ROWID";
	private static final String _I_DUPCHK = "INSERT INTO DUPCHK_%02d (DUP_KEY, DB_TM) VALUES (?, ?)";
	private static final String _Q_DUPCHK = "SELECT DUP_KEY, DB_TM FROM DUPCHK_%02d WHERE DUP_KEY = ?";
	private static int _CommitBatch = 1000;
	private String _baseDir;
	private String _prefix;
	private int _expireDays;
	private int _dayHashNum;
	private int _tblHashNum;
	private Map<String, Edb> _edbMap; // key is yyyymmdd:<day_hash_slot>
	private Map<String, EdbDupchkStatRec> _statMap; // key is yyyymmdd:<day_hash_slot>
	private Map<Integer, String> _sqlCacheMap; // key is '<tbl_hash_slot>' for insert or '1000 + <tbl_hash_slot>' for select

	public EdbDupchkPool(String base_dir, String prefix, int expire_days, int day_hash_num, int tbl_hash_num) {
		_baseDir = base_dir;
		_prefix = prefix;
		_expireDays = expire_days;
		_dayHashNum = day_hash_num;
		_tblHashNum = tbl_hash_num;
		_edbMap = new HashMap<String, Edb>();
		_statMap = new TreeMap<String, EdbDupchkStatRec>();
		_sqlCacheMap = new HashMap<Integer, String>();
		File base_dir_ = new File(_baseDir);
		if (!base_dir_.isDirectory()) {
			String alm_ = PubMethod.FmtArgs("_baseDir [{}] not exists", base_dir_.getAbsolutePath());
			L.warn("{}", alm_);
			throw new RuntimeException(alm_);
		}
	}

	public boolean chkDup(String yyyymmdd, String dup_key) {
		if (PubMethod.IsEmpty(dup_key)) {
			L.warn("dup_key is empty, return true (no dup chk)");
			return true;
		}
		if (!PubMethod._ExpTMFMT8.matcher(yyyymmdd).find()) {
			L.warn("yyyymmdd [{}] not match [{}], return true (no dup chk)", yyyymmdd, PubMethod._ExpTMFMT8.pattern());
			return true;
		}
		int raw_hash_ = dup_key.hashCode();
		int day_slot_ = raw_hash_ % _dayHashNum;
		int tbl_slot_ = raw_hash_ % _tblHashNum;
		day_slot_ = (day_slot_ + _dayHashNum) % _dayHashNum; // ensure positive value
		tbl_slot_ = (tbl_slot_ + _tblHashNum) % _tblHashNum; // ensure positive value
		long ts_now_ = System.currentTimeMillis();
		try {
			Edb edb_ = _getEdb(yyyymmdd, dup_key, ts_now_, day_slot_);
			if (edb_ == null) {
				return true;
			} else {
				return _chkDup(yyyymmdd, dup_key, ts_now_, edb_, day_slot_, tbl_slot_);
			}
		} catch (Exception e) {
			L.warn("chkDup({},{}) exception, sleep 500 millis and return true (regard as not dup)", yyyymmdd, dup_key, e);
			PubMethod.Sleep(500);
		}
		return true;
	}

	public void forceCommit() {
		long ts_now_ = System.currentTimeMillis();
		//计算出过期的日期（2天过期），然后删除过期的数据，同时删除过期的数据库
		long ts_min_ = ts_now_ - _expireDays * 86400L * 1000L;
		String ymd_min_ = PubMethod.Long2Str(ts_min_, PubMethod.TimeStrFmt.Fmt8);
		List<String> expire_keys_ = new ArrayList<String>();
		for (Entry<String, Edb> ent_ : _edbMap.entrySet()) {
			String yyyymmdd_ = ent_.getKey().substring(0, 8);
			EdbDupchkStatRec stat_rec_ = _statMap.get(ent_.getKey());
			if (stat_rec_ == null) {
				L.info("[{}] not exists in _statMap", ent_.getKey());
				continue;
			}
			if (stat_rec_._cmtCnt == 0) {
				// L.trace("[{}] _cmtCnt is 0, no need commit", ent_.getKey());
			} else {
				try {
					ent_.getValue().commit();
					L.debug("[{}] force commit done, {}", ent_.getKey(), stat_rec_.toGsonStr());
				} catch (SQLException e) {
					L.warn("[{}], {}, commit exception", ent_.getKey(), stat_rec_.toGsonStr(), e);
				} finally {
					stat_rec_._cmtCnt = 0;
				}
			}
			if (ymd_min_.compareTo(yyyymmdd_) > 0) {
				expire_keys_.add(ent_.getKey());
				ent_.getValue().destroy();
				L.info("[{}] expired and destroyed, {}", ent_.getKey(), stat_rec_.toGsonStr());
			}
		}
		for (String garbage_ : expire_keys_) {
			_statMap.remove(garbage_);
			_edbMap.remove(garbage_);
		}
	}

	private boolean _chkDup(String yyyymmdd, String dup_key, long ts_now, Edb edb, int day_slot, int tbl_slot) throws SQLException {
		String sql_ = _getISql(tbl_slot);
		Connection dbc_ = edb.getConnection();
		PreparedStatement pstmt_ = null;
		String day_key_ = String.format("%s:%02d", yyyymmdd, day_slot);
		EdbDupchkStatRec stat_rec_ = _statMap.get(day_key_);
		try {
			if (stat_rec_ == null) {
				stat_rec_ = new EdbDupchkStatRec();
				_statMap.put(day_key_, stat_rec_);
				// L.trace("day_key_ [{}], dup_key [{}], begin", day_key_, dup_key);
				edb.begin();
			} else if (stat_rec_._cmtCnt == 0) {
				// L.trace("day_key_ [{}], dup_key [{}], begin", day_key_, dup_key);
				edb.begin();
			}
			++stat_rec_._cmtCnt;
			++stat_rec_._totCnt;

			pstmt_ = dbc_.prepareStatement(sql_);
			pstmt_.setString(1, dup_key);
			pstmt_.setString(2, PubMethod.Long2Str(ts_now, PubMethod.TimeStrFmt.Fmt18).substring(6));
			pstmt_.execute();
			++stat_rec_._nmlCnt;
		} catch (SQLException e) {
			if (e.getErrorCode() == 19) { // '#define SQLITE_CONSTRAINT 19' in sqlite3.h
				++stat_rec_._dupCnt;
				_showDupWith(yyyymmdd, dup_key, edb, day_slot, tbl_slot);
				return false;
			} else {
				++stat_rec_._errCnt;
				throw e;
			}
		} finally {
			if (pstmt_ != null) {
				try {
					pstmt_.close();
				} catch (Exception e) {
					L.warn("pstmt_.close exception, just ignore, [{},{}]", yyyymmdd, dup_key, e);
				}
			}

			if (stat_rec_ != null && stat_rec_._cmtCnt >= _CommitBatch) {
				edb.commit();
				L.trace("[{}], commit done, {}", day_key_, stat_rec_.toGsonStr());
				stat_rec_._cmtCnt = 0;
			}
		}
		return true;
	}

	private void _showDupWith(String yyyymmdd, String dup_key, Edb edb, int day_slot, int tbl_slot) {
		String sql_ = _getQSql(tbl_slot);
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			pstmt_ = edb.getConnection().prepareStatement(sql_);
			pstmt_.setString(1, dup_key);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				String db_tm_ = rs_.getString(2);
				L.debug("[{},{}] dup with [{}] in slot {}:{}", yyyymmdd, dup_key, db_tm_, day_slot, tbl_slot);
				break;
			}
		} catch (SQLException e) {
			L.warn("dup_key [{}], tbl_slot {} query exception", dup_key, tbl_slot, e);
		} finally {
			Edb.Close(rs_, pstmt_, null);
		}
	}

	private Edb _getEdb(String yyyymmdd, String dup_key, long ts_now, int day_slot) throws Exception {
		long ts_min_ = ts_now - _expireDays * 86400L * 1000L;
		String ymd_min_ = PubMethod.Long2Str(ts_min_, PubMethod.TimeStrFmt.Fmt8);
		if (ymd_min_.compareTo(yyyymmdd) > 0) {
			L.warn("yyyymmdd [{}] lt [{}], out of dupchk range, ts_min_ is {}", yyyymmdd, ymd_min_,
					PubMethod.Long2Str(ts_min_, PubMethod.TimeStrFmt.Fmt19));
			return null;
		}
		String day_key_ = String.format("%s:%02d", yyyymmdd, day_slot);
		Edb edb_ = _edbMap.get(day_key_);
		if (edb_ == null) {
			String edb_path_ = PubMethod.FmtArgs("%s/%s_%s_%02d.db3", _baseDir, _prefix, yyyymmdd, day_slot);
			edb_ = new Edb(edb_path_, false, -8000);
			Connection dbc_ = edb_.getConnection();
			Statement stmt_ = dbc_.createStatement();
			stmt_.executeUpdate(_P_SYNC_OFF);
			stmt_.executeUpdate(_P_JOURNAL_MEM);
			for (int i = 0; i < _tblHashNum; ++i) {
				String sql_ = PubMethod.FmtArgs(_C_TBL_DUPCHK, i);
				stmt_.executeUpdate(sql_);
			}
			L.debug("{} initialized", edb_path_);
			_edbMap.put(day_key_, edb_);
		}
		return edb_;
	}

	private String _getISql(int tbl_slot) {
		String sql_ = _sqlCacheMap.get(tbl_slot);
		if (sql_ == null) {
			sql_ = PubMethod.FmtArgs(_I_DUPCHK, tbl_slot);
			_sqlCacheMap.put(tbl_slot, sql_);
		}
		return sql_;
	}

	private String _getQSql(int tbl_slot) {
		String sql_ = _sqlCacheMap.get(1000 + tbl_slot);
		if (sql_ == null) {
			sql_ = PubMethod.FmtArgs(_Q_DUPCHK, tbl_slot);
			_sqlCacheMap.put(1000 + tbl_slot, sql_);
		}
		return sql_;
	}
}
