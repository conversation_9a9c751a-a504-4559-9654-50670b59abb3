package com.hp.cmcc.bboss.pub.odb.entity;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class DbStlRepartRateRec extends GsonObj {
	private Long rate_id;
	private Integer tariff_type;
	private Integer match_mode;

	public Long getRate_id() {
		return rate_id;
	}

	public void setRate_id(Long rate_id) {
		this.rate_id = rate_id;
	}

	public Integer getTariff_type() {
		return tariff_type;
	}

	public void setTariff_type(Integer tariff_type) {
		this.tariff_type = tariff_type;
	}

	public Integer getMatch_mode() {
		return match_mode;
	}

	public void setMatch_mode(Integer match_mode) {
		this.match_mode = match_mode;
	}
}
