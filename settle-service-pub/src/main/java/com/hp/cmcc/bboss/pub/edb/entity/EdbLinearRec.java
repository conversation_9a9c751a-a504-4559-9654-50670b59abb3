package com.hp.cmcc.bboss.pub.edb.entity;

import com.hp.cmcc.bboss.pub.util.GsonObj;

public class EdbLinearRec extends GsonObj {
	public static final int OBJ_TYPE_1_DUR = 1; // duration, for voice
	public static final int OBJ_TYPE_2_OCC = 2; // occurrence, for SMS
	public static final int OBJ_TYPE_3_QUA = 3; // quantity, for GPRS
	public static final int OBJ_TYPE_4_FEE = 4; // fee (in CNY Li), for settle-fee proportion

	public Integer _rateId;
	public String _bizType;
	public String _offerCode;
	public String _productCode;
	public String _ecCode;
	public String _auxKey;
	public Integer _rateObj;
	public Integer _rateUnit;
	public Integer _rate;
	public String _effDate;
	public String _expDate;
	public String _memo;
}
