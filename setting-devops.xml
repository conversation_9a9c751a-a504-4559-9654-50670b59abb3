<?xml version="1.0" encoding="UTF-8"?>

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <localRepository>/github/workspace/repository/</localRepository>

    <pluginGroups></pluginGroups>

    <proxies></proxies>

    <servers>
        <server>
            <username>admin</username>
            <password>admin123</password>
            <id>releases</id>
        </server>
    </servers>

    <profiles>
        <profile>
            <repositories>
                <repository>
                    <id>releases</id>
                    <name>maven</name>
                    <url>http://10.248.68.131:32014/nexus/content/repositories/releases/</url>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>releases</id>
                    <name>maven</name>
                    <url>http://10.248.68.131:32014/nexus/content/repositories/releases/</url>
                </pluginRepository>
            </pluginRepositories>
            <id>artifactory</id>
        </profile>
    </profiles>

    <activeProfiles>
        <activeProfile>artifactory</activeProfile>
    </activeProfiles>
</settings>
