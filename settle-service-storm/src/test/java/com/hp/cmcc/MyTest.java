package com.hp.cmcc;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.LoginPasswd;
import com.hp.cmcc.bboss.storm.service.sidd.RaterSIDD;
import org.junit.Test;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Transaction;
import redis.clients.jedis.util.JedisClusterCRC16;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 * @create 2023/12/1
 * @since 1.0.0
 */
public class MyTest {


    @Test
    public void test1() {
        String CSV_NONE_ESC_PATTERN = "(?<!\\\\),";
        String[] split = "a,b,c".split(CSV_NONE_ESC_PATTERN);
        System.out.println(split.length);
    }

    @Test
    public void test2() {
        String CSV_NONE_ESC_PATTERN = "(?<!\\\\),";
        String text = "Hello,World\\,AI,Programming";
        String[] parts = text.split(CSV_NONE_ESC_PATTERN);
        for (String part : parts) {
            System.out.println(part);
        }
    }

    @Test
    public void test3() {
        String str = "V000,19702,210,38,,,,Read_Add_Comm_Fee,,,,0,,,,0,,,,RATE-PLAN-500001685,,,,210015464210210000,210,5002001,,210,20230914114341,20230914114341,0,1,0,2598135894249,    11867438229,20230930,NEWRCS,0100100000,1,NEWRCS%%230914114558607612792044,20230914114341,0,50020,32100099781,62100006453,3,,,62100006453,,OCC_E,Read_Add_Comm_Fee,,1,,01_01,20#|#230914114558607612792044        #|#621000    06453                     #|#210015464210210000            #|#03#|#95 #|#20230914114558#|#20230914114341#|#0#|#I0SV57nTpcfUqDmuNMc_01_01                                   #|#                                                                #|#                                                            ";
        String CSV_NONE_ESC_PATTERN = "(?<!\\\\),";
        String[] split = str.split(CSV_NONE_ESC_PATTERN);
        System.out.println(split.length);

        Pattern patternTm14 = Pattern.compile("^[0-9]{14}$");
        String s = split[40];
        if (patternTm14.matcher(s).matches()) {
            System.out.println(s);
        } else {
            System.out.println("no");
        }
    }

    @Test
    public void test4() {
        String str = "V000,84,000,,,,00001843,,,,1,,,,,,,,,,,,,000AE0002019040110491154,000,99904,13331088331,,20230901235959,20230901235959,0,0,0,2598081233942,11867435631,20230930,GPRSDOM,0100100100,1,GPRSDOM%%20230901133310883311710000054,2023090170000,0,010190003,4290370,9005220749,1,,,9005220749,,DUR_T,,,,,,201001710000054                    1333108833100001843";
        String CSV_NONE_ESC_PATTERN = "(?<!\\\\),";
        String[] split = str.split(CSV_NONE_ESC_PATTERN);
        System.out.println(split.length);
        Pattern patternTm14 = Pattern.compile("^[0-9]{14}$");
        String s = split[40];
        System.out.println(s);
        if (patternTm14.matcher(s).matches()) {
            System.out.println(s);
        } else {
            System.out.println("no");
        }
        System.out.println(split[UdrFmt.U_35_ACCT_DAY_36]);
    }

    @Test
    public void test5() {
        String str = "V000,2,,,,,,,,,,,,,,,,,,,,,,,,9200397,,200,20231001092000,20231001092000,0,5120,0,1369076549273,1541003858,20231001,ECDN,0100101000,1,ECDN%%2023100109200100007072,20231001092000,0,9200397,,10014191685,1,1,1,10014191685,,QUANT_D,GPRS_Fee,,5120,20231001,,20#|#2023100109200100007072#|#20231001092000#|#01#|#     10014191685#|#200#|#               5#|#               0#|#                #|#1#|#1#|#2#|#000";
        String CSV_NONE_ESC_PATTERN = "(?<!\\\\),";
        String[] split = str.split(CSV_NONE_ESC_PATTERN);
        System.out.println(split.length);
        System.out.println(UdrFmt.U_35_ACCT_DAY_36);
        System.out.println(split[UdrFmt.U_35_ACCT_DAY_36]);
    }

    @Test
    public void test6() {
        String s = LoginPasswd.PasswdDecryptAES("8vo0lx+nc0+66t2/Cr/5Dsec949rR/BrHFnb9GB4iYfOlN2jE3708Lu+MaKkOIio");
        System.out.println(s);
        String s1 = LoginPasswd.PasswdDecryptAES("KHBTL8e5zWMC9RDKyFy3q1XzkL53+UroVbcbdW6VBrGly0eR80Ca4sLgbZGAzgd8");
        System.out.println(s1);
        String s2 = LoginPasswd.PasswdDecryptAES("j5VJJrSYsQ3N+adYR7FQuIyO7fdSY+wY627JADnb5plBXh54Yh0yhyYUHndpuyTQ");
        System.out.println(s2);
        String s3 = LoginPasswd.PasswdDecryptAES("3bQWeXBfjSmIIKkiH8E3XGmofOXkBuAPY+t4MfDYajNkLO/UpaGay71Z05QCaX2Z");
        System.out.println(s3);

        String s4 = LoginPasswd.PasswdDecryptAES("pc5rHIxwzZg42vfn+WFD9maFXeO5sfqKhfxc2uJmS/aWI4yYBTSb74I1AxTscR6E");
        System.out.println(s4);
    }

    @Test
    public void test7() {
        String str = "V000,157,,,,,,,,,,,,9200397,,100,20231008231000,20231008231000,0,43008,42,1932076755,1535567256,20231008,ECDN,0100001000,0,ECDN%%2023100823100100002078,20231008231000,0,9200397,,10017161634,3,2,1,10017161634,,,QUANT_D,GPRS_Fee,,,,20231008,,20\\,2023100823100100002078\\,20231008231000\\,01\\,     10017161634\\,100\\,              42\\,               0\\,                \\,1\\,2\\,2\\,311";
        String CSV_NONE_ESC_PATTERN = "(?<!\\\\),";
        String[] split = str.split(CSV_NONE_ESC_PATTERN);
        System.out.println(split.length);
        System.out.println(UdrFmt.U_31_VOLUME_UP_32);
        System.out.println(split[UdrFmt.U_31_VOLUME_UP_32]);
    }

    @Test
    public void test8() {
        BigDecimal rate_value_ = new BigDecimal("null");
    }

    @Test
    public void test9() {
        String str = "V000,1,100,0,,,,Area_Query_Fee,2154,,,100A23100095799129,100,2021999400052091,,000,20231023114528,20231023114528,0,0,0,1431844048,1536428059,20231108,ZFWS,0100100000,1,ZFWS%%10231145000015589,20231023114528,0,50024,31000198279,61000005507,3,,,61000005507,0,0,OCC_E,Area_Query_Fee,,0,RATE-PLAN-500001052,,000,20\\,10231145000015589\\,1\\,100A23100095799129            \\,61000005507         \\,1\\,13951935739                     \\,000\\,20231023114524\\,000000000000000000\\,B\\,250\\,20231023114528\\,  \n";
        String[] fields_ = str.split(UdrFmt.CSV_NONE_ESC_PATTERN, -1);
        System.out.println(fields_.length);

    }

    @Test
    public void test10() {
        String str = "V000,157,,,,,,,,,,,,9200397,,100,20231008231000,20231008231000,0,43008,42,1932076755,1535567256,20231008,ECDN,0100001000,0,ECDN%%2023100823100100002078,20231008231000,0,9200397,,10017161634,3,2,1,10017161634,,,QUANT_D,GPRS_Fee,,,,20231008,,20\\,2023100823100100002078\\,20231008231000\\,01\\,     10017161634\\,100\\,              42\\,               0\\,                \\,1\\,2\\,2\\,311\n";
        String[] split = split(str);
        System.out.println(split.length);

    }

    public String[] split(String str) {
        String[] fields_ = str.split(UdrFmt.CSV_NONE_ESC_PATTERN, -1);
        return fields_;
    }


    @Test
    public void test11() {
        String s = LoginPasswd.PasswdEncryptAES("stlusers/EiQFlH%xu&@stlusers:10.248.203.52:2206");
        System.out.println(s);

        String s1 = LoginPasswd.PasswdDecryptAES(s);
        System.out.println(s1);
    }

    @Test
    public void test12() {
        String stlusers = "stlusers/EiQFlH%xu&@stlusers:10.248.203.52:2206";
        String stlusersEncry = LoginPasswd.PasswdEncryptAES(stlusers);
        System.out.println(stlusersEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stlusersEncry));
        getSettleRedis(stlusersEncry, "biz");
        System.out.println("---");
        String stludr = "stludr/EiQFlH%xu&@stludr:10.248.203.52:2206";
        String stludrEncry = LoginPasswd.PasswdEncryptAES(stludr);
        System.out.println(stludrEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stludrEncry));
        getSettleRedis(stludrEncry, "udr");
        System.out.println("---");
        getBillingRedis(stludrEncry, "udr");
        System.out.println("---");

        String sttlbiz = "sttlbiz/EiQFlH%xu&@sttlbiz:10.248.203.52:2206";
        String sttlbizEncry = LoginPasswd.PasswdEncryptAES(sttlbiz);
        System.out.println(sttlbizEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(sttlbizEncry));
        getBillingRedis(sttlbizEncry, "biz");
        System.out.println("---");
    }

    @Test
    public void test13() {
        String stlusers = "stlusers/FMXNht_8UgB8z_hkEIDZ@stlusers:10.103.161.243:3306";
        String stlusersEncry = LoginPasswd.PasswdEncryptAES(stlusers);
        System.out.println(stlusersEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stlusersEncry));
        getSettleRedis(stlusersEncry, "biz");
        System.out.println("---");
        String stludr = "stludr/FMXNht_8UgB8z_hkEIDZ@stludr:10.103.161.243:3306";
        String stludrEncry = LoginPasswd.PasswdEncryptAES(stludr);
        System.out.println(stludrEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stludrEncry));
        getSettleRedis(stludrEncry, "udr");
        System.out.println("---");
        getBillingRedis(stludrEncry, "udr");
        System.out.println("---");

        String sttlbiz = "sttlbiz/FMXNht_8UgB8z_hkEIDZ@sttlbiz:10.103.161.243:3306";
        String sttlbizEncry = LoginPasswd.PasswdEncryptAES(sttlbiz);
        System.out.println(sttlbizEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(sttlbizEncry));
        getBillingRedis(sttlbizEncry, "biz");
        System.out.println("---");
    }

    @Test
    public void test13_test() {
        String stlusers = "stlusers/EiQFlH%xu&@stlusers:10.248.203.58:4406";
        String stlusersEncry = LoginPasswd.PasswdEncryptAES(stlusers);
        System.out.println(stlusersEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stlusersEncry));
        getSettleRedis(stlusersEncry, "biz");
        System.out.println("---");
        String stludr = "stludr/EiQFlH%xu&@stludr:10.248.203.58:4406";
        String stludrEncry = LoginPasswd.PasswdEncryptAES(stludr);
        System.out.println(stludrEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stludrEncry));
        getSettleRedis(stludrEncry, "udr");
        System.out.println("---");
        getBillingRedis(stludrEncry, "udr");
        System.out.println("---");

        String sttlbiz = "sttlbiz/EiQFlH%xu&@sttlbiz:10.248.203.58:4406";
        String sttlbizEncry = LoginPasswd.PasswdEncryptAES(sttlbiz);
        System.out.println(sttlbizEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(sttlbizEncry));
        getBillingRedis(sttlbizEncry, "biz");
        System.out.println("---");
    }

    @Test
    public void test13_kf() {
        String stlusers = "stlusers/EiQFlH%xu&@stlusers:172.19.30.151:3311";
        String stlusersEncry = LoginPasswd.PasswdEncryptAES(stlusers);
        System.out.println(stlusersEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stlusersEncry));
        getSettleRedis(stlusersEncry, "biz");
        System.out.println("---");
        String stludr = "stludr/EiQFlH%xu&@stludr:172.19.30.151:3311";
        String stludrEncry = LoginPasswd.PasswdEncryptAES(stludr);
        System.out.println(stludrEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stludrEncry));
        getSettleRedis(stludrEncry, "udr");
        System.out.println("---");
        getBillingRedis(stludrEncry, "udr");
        System.out.println("---");

        String sttlbiz = "sttlbiz/EiQFlH%xu&@sttlbiz:172.19.30.151:3311";
        String sttlbizEncry = LoginPasswd.PasswdEncryptAES(sttlbiz);
        System.out.println(sttlbizEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(sttlbizEncry));
        getBillingRedis(sttlbizEncry, "biz");
        System.out.println("---");
    }

    @Test
    public void test13_err() {
        String stlusers = "stlusers/FMXNht_8UgB8z_hkEIDZ1@stlusers:10.103.161.243:3306";
        String stlusersEncry = LoginPasswd.PasswdEncryptAES(stlusers);
        System.out.println(stlusersEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stlusersEncry));
        getSettleRedis(stlusersEncry, "biz");
        System.out.println("---");
        String stludr = "stludr/FMXNht_8UgB8z_hkEIDZ1@stludr:10.103.161.243:3306";
        String stludrEncry = LoginPasswd.PasswdEncryptAES(stludr);
        System.out.println(stludrEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stludrEncry));
        getSettleRedis(stludrEncry, "udr");
        System.out.println("---");
        getBillingRedis(stludrEncry, "udr");
        System.out.println("---");

        String sttlbiz = "sttlbiz/FMXNht_8UgB8z_hkEIDZ1@sttlbiz:10.103.161.243:3306";
        String sttlbizEncry = LoginPasswd.PasswdEncryptAES(sttlbiz);
        System.out.println(sttlbizEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(sttlbizEncry));
        getBillingRedis(sttlbizEncry, "biz");
        System.out.println("---");
    }
    private void getSettleRedis(String str, String str1) {
        String cli = "SET \"login:settle:" + str1 + ":crypt\" \"" + str + "\"";
        System.out.println(cli);
    }

    private void getBillingRedis(String str, String str1) {
        String cli = "SET \"login:billing:" + str1 + ":crypt\" \"" + str + "\"";
        System.out.println(cli);
    }

    @Test
    public void zhunprod() {
        String stlusers = "stlusers/ru7q7^A-Y70)@stlusers:10.103.33.87:3306";
        String stlusersEncry = LoginPasswd.PasswdEncryptAES(stlusers);
        System.out.println(stlusersEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stlusersEncry));
        getSettleRedis(stlusersEncry, "biz");
        System.out.println("---");
        String stludr = "stludr/ru7q7^A-Y70)@stludr:10.103.33.87:3306";
        String stludrEncry = LoginPasswd.PasswdEncryptAES(stludr);
        System.out.println(stludrEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(stludrEncry));
        getSettleRedis(stludrEncry, "udr");
        System.out.println("---");
        getBillingRedis(stludrEncry, "udr");
        System.out.println("---");

        String sttlbiz = "sttlbiz/ru7q7^A-Y70)@sttlbiz:10.103.33.87:3306";
        String sttlbizEncry = LoginPasswd.PasswdEncryptAES(sttlbiz);
        System.out.println(sttlbizEncry);
        System.out.println(LoginPasswd.PasswdDecryptAES(sttlbizEncry));
        getBillingRedis(sttlbizEncry, "biz");
        System.out.println("---");
    }
    @Test
    public void test14() {
        String s = LoginPasswd.PasswdDecryptAES("gBX+IAWlCCpnwRnN8KR+pND/0aEeAFUpLklMX6XsI9ydh3LGr7m7kb6ppKKLKQ0d");
        System.out.println(s);

        String s2 = LoginPasswd.PasswdDecryptAES("Kod52J5d9FI6d0+BowGnVlFljpiXWS8HZ8sEbEi2LdY=");
        System.out.println(s2);
    }

    @Test
    public void test15() {
        String s = LoginPasswd.PasswdDecryptAES("UNEgI1pt3Z5QzHsjhofoQBb7eTR1NkojsAr5obo3ADPLgHCtYK0kqZvHJvOcs6vMkBVx7o/84aUx5ISkRIM9BQ==");
        System.out.println(s);

        String s1 = LoginPasswd.PasswdDecryptAES("as4rH8gWanwVTYd5bBkH2z/3EtXe1KlP9xTd/cuzknsGffxrJhrPgf/F2Zc3t6rCyS7gWSgSfWpkUfoZPszAZw==");
        System.out.println(s1);

        String s2 = LoginPasswd.PasswdDecryptAES("CvMTSt4rfhJ+GPYozn5wBPyI3RvlaGEsAd4uzioNxKo5lFlI9nfp6S7IC29SRsYvYItcLippvLniGntxKvRTcQ==");
        System.out.println(s2);
    }

    @Test
    public void test16() {
        long start = 20230708173224L;
        Long expire = 20230829021430L;

        long expire_ = expire - 150 * 86400L * 1000L;

        System.out.println("start:" + start);
        System.out.println("expire:" + expire);
        System.out.println("expire_:" + expire_);

        System.out.println(start < expire_);
    }

    @Test
    public void test17() {
        int i = -1;
        int n = 1;
        float m = 1.1f;

        BigDecimal bigDecimal = new BigDecimal(i);
        BigDecimal bigDecimal1 = new BigDecimal(n);
        BigDecimal bigDecimal2 = new BigDecimal(m);

        System.out.println(bigDecimal.compareTo(bigDecimal2));
        System.out.println(bigDecimal1.compareTo(bigDecimal2));

        String plainString = bigDecimal2.setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
        System.out.println(plainString);
    }

    @Test
    public void test18() {
        BigDecimal rateValue = new BigDecimal(1.5);
        int i = RaterSIDD.POS_ONE.compareTo(rateValue);
        System.out.println(i);

    }


    @Test
    public void test19() {
        String urd = "V000,19,BILL,1,1433477319,,,50055,2023999400010056,31002301481,61023618801,,,,,3,,4097,205,205,0,6,202312,,1738446879433198264,202312,,,,,,1742039546947829849,,,1,20231201000000,,,,1\\,1433477319          \\,100A23100096848703\\,3\\,50055\\,2023999400010056\\,31002301481\\,61023618801\\,\\,\\,4097\\,205\\,205\\,0\\,6\\,202312\\,202312\\,20231201000000\\,1\\,\\,";
        String[] split = urd.split(UdrFmt.CSV_NONE_ESC_PATTERN, -1);
        System.out.println(split.length);
        int attachment_idx_ = 0;
        int attachment_len_ = 0;
        for (int i = UdrFmt.S_FIELD_MIN_40 - 1; i < split.length; i++) {
            if (split[i] == null)
                continue;
            if (split[i].length() > attachment_len_) {
                attachment_len_ = split[i].length();
                attachment_idx_ = i;
            }
        }
        System.out.println(attachment_idx_);
        System.out.println(attachment_len_);
    }


    @Test
    public void test20() {
        MdbCli2 cli2 = new MdbCli2("bizlog", "***************:7000","NULL" , 1000, 3);
        cli2.init();
        JedisCluster jedisCluster = cli2.getJedisCluster();
        jedisCluster.set("storm", "storm");
        cli2.getJedis("storm");
    }

    @Test
    public void test21() {
        MdbCli2 cli2 = new MdbCli2("bizlog", "***************:7000","NULL" , 1000, 3);
        cli2.init();
        String key = "RF:111:222:333";
        Jedis jedis = cli2.getJedis("RF:111:222:333");

        jedis.hset(key, "INIT", LocalDateTimeUtil.now().toString());
        jedis.hset(key, "RESIDUE", String.valueOf(1000));
        jedis.watch(key);

        Transaction tx = jedis.multi();
        tx.hincrBy(key, "RESIDUE", -1000);
        List<Object> exec = tx.exec();
        if (exec != null) {
            System.out.println(exec);
        }else {
            System.out.println("exec null");
            jedis.unwatch();
            String residue = jedis.hget(key, "RESIDUE");
            System.out.println(residue);
        }

        jedis.close();

    }

    @Test
    public void test22() {
        MdbCli2 cli2 = new MdbCli2("bizlog", "***************:7000","NULL" , 1000, 3);
        cli2.init();
        String key = "TF:111:222:333";
        Jedis jedis = cli2.getJedis(key);
        Transaction multi = jedis.multi();
        multi.hset(key, "INIT", LocalDateTimeUtil.now().toString());
        multi.hset(key, "RESIDUE", String.valueOf(1000));

        multi.set("storm1", "storm1");
        multi.set("storm2", "storm2");
        multi.set("storm3", "storm3");
        multi.exec();
        jedis.close();

    }

    @Test
    public void test23() {
        MdbCli2 cli2 = new MdbCli2("bizlog", "***************:7000","NULL" , 1000, 3);
        cli2.init();
        JedisCluster jedisCluster = cli2.getJedisCluster();
        String key = "TF:111:222:333";

        jedisCluster.hset(key, "INIT", LocalDateTimeUtil.now().toString());
        jedisCluster.hset(key, "RESIDUE", String.valueOf(1000));

        jedisCluster.set("storm1", "storm1");
        jedisCluster.set("storm2", "storm2");
        jedisCluster.set("storm3", "storm3");

    }

    @Test
    public void test24() {
        Set<HostAndPort> redisCluster = new HashSet<>();
        redisCluster.add(new HostAndPort("***************", 7000));
        JedisCluster jedisCluster = new JedisCluster(redisCluster, 6000, 6000, 5, new JedisPoolConfig());
        jedisCluster.set("login:billing:udr:crypt", "bbb");


        String key = "{pipeline}:6666:";
        Jedis jedis = jedisCluster.getConnectionFromSlot((JedisClusterCRC16.getSlot(key)));

        Pipeline pipelined = jedis.pipelined();

        DecimalFormat format = new DecimalFormat("#.00");
        for (int i = 0; i < 100; i++) {
            pipelined.set(key + format.format(i), format.format(i));
        }

        pipelined.syncAndReturnAll();

    }

    @Test
    public void test25() {
        List<String[]> all_sidd_ = new ArrayList<>();
        for (String[] strings : all_sidd_) {
            System.out.println(strings);
        }
    }

    @Test
    public void test26() {
        String jsonStr = JSONUtil.toJsonStr(null);
        System.out.println(jsonStr);
    }

}