package com.hp.cmcc.bboss.storm.uidd;


import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbLinearRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbTierRec;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisSentinelPool;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class RaterUIDDNew extends FunctorIDD {
    private static final String _Q_LINEAR = "SELECT RATE_ID, BIZ_TYPE, OFFER_CODE, PRODUCT_CODE, EC_CODE, AUX_KEY, "
            + "RATE_OBJ, RATE_UNIT, RATE, EFF_DATE, EXP_DATE, MEMO FROM LINEAR WHERE BIZ_TYPE = ? "
            + "AND (OFFER_CODE = ? OR OFFER_CODE = '-1') AND (PRODUCT_CODE = ? OR PRODUCT_CODE = '-1') "
            + "AND (EC_CODE = ? OR EC_CODE = '-1') AND (AUX_KEY = ? OR AUX_KEY = '-1') "
            + "AND ? BETWEEN EFF_DATE AND EXP_DATE ORDER BY OFFER_CODE DESC, PRODUCT_CODE DESC, "
            + "EC_CODE DESC, AUX_KEY DESC, EFF_DATE DESC";
    private static final String _Q_TIER = "SELECT RATE_ID, BIZ_TYPE, EC_CODE, AUX_KEY, RATE_OBJ, RATE_UNIT, RATE, "
            + "TIER_OBJ, TIER_UNIT, TIER_IDX, TIER_MIN, TIER_MAX, EFF_DATE, EXP_DATE, IS_MONTHLY, MEMO FROM TIER "
            + "WHERE BIZ_TYPE = ? AND EC_CODE = ? AND AUX_KEY = ? AND ? BETWEEN EFF_DATE AND EXP_DATE "
            + "ORDER BY EFF_DATE DESC, TIER_IDX DESC";

    public RaterUIDDNew() {
        super();
        l= LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void execUdr(String log_tag, UdrFmt udr) throws Exception {
        _logTag = log_tag;
        if (!PubMethod.IsBlank(udr._eFields[UdrFmt.E_27_X02_BIZRCY_CHARGE])) {
//            l.info("{} X02_BIZRCY_CHARGE [{}], X03_BIZRCY_ACUMLT [{}]", _logTag, udr._eFields[UdrFmt.E_27_X02_BIZRCY_CHARGE],
//                    udr._eFields[UdrFmt.E_28_X03_BIZRCY_ACUMLT]);
            udr._uFields[UdrFmt.U_05_FEE3_06] = udr._eFields[UdrFmt.E_27_X02_BIZRCY_CHARGE];
            // udr.setProcFlag(UdrFmt.PROC_FLAG_RATING);
        } else {
            _ratingUIDD(udr);
        }
    }

    private void _ratingUIDD(UdrFmt udr) throws Exception {
        String key_ = null;
        List<EdbLinearRec> linear_rates_ = new ArrayList<EdbLinearRec>();
        key_ = _seekLinear(udr, linear_rates_);
        if (!linear_rates_.isEmpty()) {
            _ratingLinear(udr, linear_rates_, key_);
            return;
        }

        List<EdbTierRec> tier_rates_ = new ArrayList<EdbTierRec>();
        key_ = _seekTier(udr, tier_rates_);
        if (!tier_rates_.isEmpty()) {
            _ratingTier(udr, tier_rates_, key_);
            return;
        }

        udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V070_RATING_NO_MATCH;
        udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%s,%s,%s", udr._uFields[UdrFmt.U_36_BIZ_TYPE_37],
                udr._uFields[UdrFmt.U_42_OFFER_CODE_43], udr._uFields[UdrFmt.U_25_PRODUCT_CODE_26],
                udr._uFields[UdrFmt.U_23_EC_CODE_24]);
        udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
                "no rate match BIZ_TYPE_25:OFFER_CODE_31:PRODUCT_CODE_14:EC_CODE_12 %s", udr._eFields[UdrFmt.E_16_E02_ERR_VAL]);
        //l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
    }

    private String _seekTier(UdrFmt udr, List<EdbTierRec> result) throws Exception {
        long stime = System.currentTimeMillis();
        String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
        Edb edb_ = null;
        PreparedStatement pstmt_ = null;
        ResultSet rs_ = null;
        String key_ = null;
        result.clear();
        try {
            EdbTierRec tier_ = null;
            edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
            Connection conn_ = edb_.getConnection();
            pstmt_ = conn_.prepareStatement(_Q_TIER);
            for (int i = 0; i < 2; ++i) {
                pstmt_.setString(1, udr._uFields[UdrFmt.U_36_BIZ_TYPE_37]);
                pstmt_.setString(2, udr._uFields[UdrFmt.U_23_EC_CODE_24]);
                pstmt_.setString(3, "-1");
                pstmt_.setString(4, udr._uFields[UdrFmt.U_35_ACCT_DAY_36] + "000000");
                switch (i) {
                    case 0:
                        key_ = PubMethod.FmtArgs("%s:%s", udr._uFields[UdrFmt.U_36_BIZ_TYPE_37], udr._uFields[UdrFmt.U_23_EC_CODE_24]);
                        break;
                    default:
                        pstmt_.setString(2, "-1");
                        key_ = PubMethod.FmtArgs("%s:-1", udr._uFields[UdrFmt.U_36_BIZ_TYPE_37]);
                        break;
                }
                rs_ = pstmt_.executeQuery();
                while (rs_.next()) {
//                    l.info("EdbTierRec:{}",GsonUtil.toJsonString(rs_));
                    tier_ = _extractTier(rs_);
//                    l.info("EdbTierRec tier_:{}",GsonUtil.toJsonString(tier_));
                    result.add(tier_);
                }
                if (!result.isEmpty()) {
                    break;
                } else {
                    key_ = null;
                }
            }
            Edb.Close(rs_, pstmt_, null);
            rs_ = null;
            pstmt_ = null;
            if (key_ != null) {
                key_ += ":" + result.get(0)._effDate;
                if (1 == result.get(0)._isMonthly.intValue()) {
                    key_ += ":" + udr._uFields[UdrFmt.U_35_ACCT_DAY_36].substring(0, 6);
                }
            }
            return key_;
        } finally {
            Edb.Close(rs_, pstmt_, null);
            if (edb_ != null) {
                TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
            }
        }
    }

    private EdbTierRec _extractTier(ResultSet rs) throws SQLException {
        EdbTierRec rec_ = new EdbTierRec();
        int idx_ = 0;
        rec_._rateId = rs.getInt(++idx_);
        rec_._bizType = rs.getString(++idx_);
        rec_._ecCode = rs.getString(++idx_);
        rec_._auxKey = rs.getString(++idx_);
        rec_._rateObj = rs.getInt(++idx_);
        rec_._rateUnit = rs.getInt(++idx_);
        rec_._rate = rs.getInt(++idx_);
        rec_._tierObj = rs.getInt(++idx_);
        rec_._tierUnit = rs.getInt(++idx_);
        rec_._tierIdx = rs.getInt(++idx_);
        rec_._tierMin = rs.getLong(++idx_);
        rec_._tierMax = rs.getLong(++idx_);
        if (rec_._tierMax < 0) {
            rec_._tierMax = Long.MAX_VALUE;
        }
        rec_._effDate = rs.getString(++idx_);
        rec_._expDate = rs.getString(++idx_);
        rec_._isMonthly = rs.getInt(++idx_);
        rec_._memo = rs.getString(++idx_);
        return rec_;
    }

    private void _ratingTier(UdrFmt udr, List<EdbTierRec> tier_rates, String acumlt_key) {
        long tier_units_ = _getTierTierUnits(udr, tier_rates.get(0));
        long rate_units_ = _getTierRateUnits(udr, tier_rates.get(0));

        Long acumlt_val_ = _acumlt(udr, acumlt_key, tier_units_);
        if (acumlt_val_ == null) {
            udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V084_EXP_RATING;
            udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = "no acumlt val";
            udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("get acumlt val by key %s returns null", acumlt_key);
            l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
            return;
        } else {
            udr._eFields[UdrFmt.E_14_A15_ACUMLT] = acumlt_val_.toString();
            udr._eFields[UdrFmt.E_28_X03_BIZRCY_ACUMLT] = acumlt_val_.toString();
        }

        long fee_ = 0;
        boolean found_ = false;
        for (EdbTierRec t : tier_rates) {
            if (acumlt_val_ >= t._tierMin && acumlt_val_ <= t._tierMax) {
                found_ = true;
                fee_ = rate_units_ * t._rate;
                l.debug("{} tier_idx {}, acumlt key {}, val {} between {} and {}, rate_id {}, rate_units*rate=fee, {}*{}={}",
                        new Object[] { _logTag, t._tierIdx, acumlt_key, acumlt_val_, t._tierMin, t._tierMax, t._rateId, rate_units_,
                                t._rate, fee_ });
                udr._eFields[UdrFmt.E_31_X06_RATE_ID] = Integer.toString(t._rateId);
                break;
            }
        }
        if (!found_) {
            udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V070_RATING_NO_MATCH;
            udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%d", acumlt_key, acumlt_val_);
            udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("acumlt key:val %s no range match",
                    udr._eFields[UdrFmt.E_16_E02_ERR_VAL]);
            l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
        } else {
            udr._uFields[UdrFmt.U_05_FEE3_06] = Long.toString(fee_);
        }
    }

    private long _getTierTierUnits(UdrFmt udr, EdbTierRec tier_rec) {
        long tier_units_ = 0;
        if (EdbLinearRec.OBJ_TYPE_3_QUA == tier_rec._tierObj.intValue()) {
            long volume_ = 0;
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_31_VOLUME_UP_32])) {
                volume_ += Long.parseLong(udr._uFields[UdrFmt.U_31_VOLUME_UP_32]);
            }
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33])) {
                volume_ += Long.parseLong(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33]);
            }
            tier_units_ = (volume_ + tier_rec._tierUnit - 1) / tier_rec._tierUnit;
        } else if (EdbLinearRec.OBJ_TYPE_2_OCC == tier_rec._tierObj.intValue()) {
            tier_units_ = 1;
        } else { // EdbLinearRec.OBJ_TYPE_1_DUR == tier_._tierObj.intValue()
            long dur_ = 0;
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_30_DURATION_31])) {
                dur_ += Long.parseLong(udr._uFields[UdrFmt.U_30_DURATION_31]);
            }
            tier_units_ = (dur_ + tier_rec._tierUnit - 1) / tier_rec._tierUnit;
        }
        return tier_units_;
    }

    private long _getTierRateUnits(UdrFmt udr, EdbTierRec tier_rec) {
        long rate_units_ = 0;
        if (EdbLinearRec.OBJ_TYPE_3_QUA == tier_rec._rateObj.intValue()) {
            long volume_ = 0;
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_31_VOLUME_UP_32])) {
                volume_ += Long.parseLong(udr._uFields[UdrFmt.U_31_VOLUME_UP_32]);
            }
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33])) {
                volume_ += Long.parseLong(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33]);
            }
            rate_units_ = (volume_ + tier_rec._rateUnit - 1) / tier_rec._rateUnit;
        } else if (EdbLinearRec.OBJ_TYPE_2_OCC == tier_rec._rateObj.intValue()) {
            rate_units_ = 1;
        } else { // EdbLinearRec.OBJ_TYPE_1_DUR == tier_._rateObj.intValue()
            long dur_ = 0;
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_30_DURATION_31])) {
                dur_ += Long.parseLong(udr._uFields[UdrFmt.U_30_DURATION_31]);
            }
            rate_units_ = (dur_ + tier_rec._rateUnit - 1) / tier_rec._rateUnit;
        }
        return rate_units_;
    }

    private Long _acumlt(UdrFmt udr, String acumlt_key, long tier_units) {
        Long acumlt_val_;
        if (udr.hasProcFlag(UdrFmt.PROC_FLAG_RATING) && !PubMethod.IsEmpty(udr._eFields[UdrFmt.E_14_A15_ACUMLT])) {
            acumlt_val_ = Long.parseLong(udr._eFields[UdrFmt.E_14_A15_ACUMLT]);
            if (acumlt_val_ > 0) {
                l.trace("{} has {} flag, reuse ACUMLT value {}", _logTag, UdrFmt.PROC_FLAG_RATING, acumlt_val_);
                return acumlt_val_;
            }
        }
        if (TopologyCfg.GetInstance().redisClusterMode()) {
            MdbCli2 mdbCli2 = MdbAgt2.GetShparmInstance();
            JedisCluster jedisCluster = mdbCli2.getJedisCluster();
            try {
                acumlt_val_ = jedisCluster.incrBy("ACUMLT:" + acumlt_key, tier_units);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return acumlt_val_;
        }else {
            MdbCli cli_ = MdbAgt.GetShparmInstance();
            JedisSentinelPool pool_ = null;
            Jedis jedis_ = null;
            try {
                pool_ = cli_.getPool();
                jedis_ = pool_.getResource();
                acumlt_val_ = jedis_.incrBy("ACUMLT:" + acumlt_key, tier_units);
            } catch (Exception e) {
                cli_.returnBrokenResource(null, pool_, jedis_);
                jedis_ = null;
                throw new RuntimeException(e);
            } finally {
                cli_.returnResource(pool_, jedis_);
            }
            return acumlt_val_;
        }
    }

    private String _seekLinear(UdrFmt udr, List<EdbLinearRec> result) throws Exception {
        long stime = System.currentTimeMillis();
        String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
        Edb edb_ = null;
        PreparedStatement pstmt_ = null;
        ResultSet rs_ = null;
        String key_ = null;
        result.clear();
        try {
            EdbLinearRec linear_ = null;
            edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
            Connection conn_ = edb_.getConnection();
            pstmt_ = conn_.prepareStatement(_Q_LINEAR);
            pstmt_.setString(1, udr._uFields[UdrFmt.U_36_BIZ_TYPE_37]);
            pstmt_.setString(2, udr._uFields[UdrFmt.U_42_OFFER_CODE_43]);
            pstmt_.setString(3, udr._uFields[UdrFmt.U_25_PRODUCT_CODE_26]);
            pstmt_.setString(4, udr._uFields[UdrFmt.U_23_EC_CODE_24]);
            pstmt_.setString(5, udr._uFields[UdrFmt.U_07_CHARGE_CODE1_08]);
            pstmt_.setString(6, udr._uFields[UdrFmt.U_35_ACCT_DAY_36] + "000000");
            rs_ = pstmt_.executeQuery();
            while (rs_.next()) {
                linear_ = _extractLinear(rs_);
                result.add(linear_);
                if (result.size() == 1) {
                    key_ = PubMethod.FmtArgs("%s:%s:%s:%s:%s", linear_._bizType, linear_._offerCode, linear_._productCode,
                            linear_._ecCode, linear_._auxKey);
                }
            }
            if (!result.isEmpty()) {
                // l.trace("{} [{}] {} linear rates matched, first {}", _logTag, key_, result.size(),
                // result.get(0).toGsonStr());
            } else {
                key_ = null;
            }
            Edb.Close(rs_, pstmt_, null);
            rs_ = null;
            pstmt_ = null;
            return key_;
        } finally {
            long etime = System.currentTimeMillis();
            l.debug("_seekLinear执行时长：{}毫秒.", (etime - stime));
            Edb.Close(rs_, pstmt_, null);
            if (edb_ != null) {
                TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
            }
        }
    }

    private EdbLinearRec _extractLinear(ResultSet rs) throws SQLException {
        EdbLinearRec rec_ = new EdbLinearRec();
        int idx_ = 0;
        rec_._rateId = rs.getInt(++idx_);
        rec_._bizType = rs.getString(++idx_);
        rec_._offerCode = rs.getString(++idx_);
        rec_._productCode = rs.getString(++idx_);
        rec_._ecCode = rs.getString(++idx_);
        rec_._auxKey = rs.getString(++idx_);
        rec_._rateObj = rs.getInt(++idx_);
        rec_._rateUnit = rs.getInt(++idx_);
        rec_._rate = rs.getInt(++idx_);
        rec_._effDate = rs.getString(++idx_);
        rec_._expDate = rs.getString(++idx_);
        rec_._memo = rs.getString(++idx_);
        return rec_;
    }

    private void _ratingLinear(UdrFmt udr, List<EdbLinearRec> linear_rates, String linear_key) {
        EdbLinearRec linear_rec_ = linear_rates.get(0);
        long rate_units_ = _getLinearRateUnits(udr, linear_rec_);
        long fee_ = rate_units_ * linear_rec_._rate;
//        l.info("{} linear key {}, rate_id {}, rate_units*rate=fee, {}*{}={}", _logTag, linear_key, linear_rec_._rateId,
//                rate_units_, linear_rec_._rate, fee_);
        udr._uFields[UdrFmt.U_05_FEE3_06] = Long.toString(fee_);
    }

    private long _getLinearRateUnits(UdrFmt udr, EdbLinearRec linear_rec) {
        long rate_units_ = 0;
        if (EdbLinearRec.OBJ_TYPE_4_FEE == linear_rec._rateObj.intValue()) {
            long fee_ = 0;
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_03_FEE1_04])) {
                fee_ = Long.parseLong(udr._uFields[UdrFmt.U_03_FEE1_04]);
            }
            rate_units_ = (fee_ + linear_rec._rateUnit - 1) / linear_rec._rateUnit;
        } else if (EdbLinearRec.OBJ_TYPE_3_QUA == linear_rec._rateObj.intValue()) {
            long volume_ = 0;
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_31_VOLUME_UP_32])) {
                volume_ += Long.parseLong(udr._uFields[UdrFmt.U_31_VOLUME_UP_32]);
            }
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33])) {
                volume_ += Long.parseLong(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33]);
            }
            rate_units_ = (volume_ + linear_rec._rateUnit - 1) / linear_rec._rateUnit;
        } else if (EdbLinearRec.OBJ_TYPE_2_OCC == linear_rec._rateObj.intValue()) {
            rate_units_ = 1;
        } else { // EdbLinearRec.OBJ_TYPE_1_DUR == tier_._rateObj.intValue()
            long dur_ = 0;
            if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.U_30_DURATION_31])) {
                dur_ = Long.parseLong(udr._uFields[UdrFmt.U_30_DURATION_31]);
            }
            rate_units_ = (dur_ + linear_rec._rateUnit - 1) / linear_rec._rateUnit;
        }
        return rate_units_;
    }

}
