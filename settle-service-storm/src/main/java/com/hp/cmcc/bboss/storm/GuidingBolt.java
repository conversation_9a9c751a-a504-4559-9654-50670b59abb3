package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.storm.sidd.GuiderSIDD;
import com.hp.cmcc.bboss.storm.sidd.GuiderSIDDNew;
import com.hp.cmcc.bboss.storm.sidd.GuiderSIDDRef;
import org.apache.storm.task.OutputCollector;
import org.apache.storm.task.TopologyContext;
import org.apache.storm.topology.OutputFieldsDeclarer;
import org.apache.storm.topology.base.BaseRichBolt;
import org.apache.storm.tuple.Fields;
import org.apache.storm.tuple.Tuple;
import org.apache.storm.tuple.Values;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/** 
 * @ClassName: GuidingBolt 
 * @Description: 详单规整
 * @company HPE  
 * <AUTHOR>   mod
 * @date 2018年1月27日 下午4:22:36 
 *  
 */ 
public class GuidingBolt extends BaseRichBolt {
	private static final long serialVersionUID = -6066273280239429958L;

	private Logger l = null;
	private OutputCollector _collector;
	private String _rndmId;
	private String _logTag;
	private FunctorIDD _guider;

	@Override
	public void prepare(@SuppressWarnings("rawtypes") Map stormConf, TopologyContext context, OutputCollector collector) {
		l = LoggerFactory.getLogger(this.getClass());
		TopologyCtx.Init(context.getThisWorkerPort());
		_collector = collector;
		_rndmId = "";
		if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
			//话单不用guiding
			_guider = null;
		} else if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
			//TODO 测试代码，排查问题，后期删除
			if("Ref".equals(TopologyCfg.GetInstance().validationImpl)){
				_guider = new GuiderSIDDRef();
			}else if("New".equals(TopologyCfg.GetInstance().validationImpl)){
				_guider = new GuiderSIDDNew();
			}else {
				_guider = new GuiderSIDD();
			}
		} else {
			throw new RuntimeException("ukn SUB_SYSTEM_TYPE " + OdbSystemParam.GetInstance()._subSystemType);
		}
	}

	@Override
	public void execute(Tuple input) {
		UdrFmtMsg msg_ = null;
		try {
			msg_ = (UdrFmtMsg) input.getValue(0);
			_rndmId = msg_._rndmId;
			for (UdrFmt udr_ : msg_._udrFmtList) {
				_execUdr(udr_);
			}
		} catch (Exception e) {
			l.error("GuidingBolt exception", e);
		} finally {
			try{
				_collector.emit(new Values(msg_));
				_collector.ack(input);
			}catch (Exception e){
				l.error("GuidingBolt error", e);
			}

		}
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields(TopologyCfg.OUTPUT_FIELD_DFT));
	}

	private void _execUdr(UdrFmt udr) {
		try {
			if (udr.isErr())
				return;
			_logTag = _rndmId + ", " + udr.getBizlogUid();
			if (_guider != null) {
				_guider.execUdr(_logTag, udr);
			}
			udr.setProcFlag(UdrFmt.PROC_FLAG_GUIDING);
		} catch (Exception e) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V083_EXP_GUIDING;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = "storm guiding exception";
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = e.getMessage();
			l.error("Guiding异常,话单：{}. logTag: {} err_code: {}, err_val: {}",
					udr.toGsonStr(), _logTag, udr._eFields[UdrFmt.E_02_A03_ERR_CODE], udr._eFields[UdrFmt.E_16_E02_ERR_VAL], e);
		} finally {
			if (TopologyCfg.GetInstance()._bizlogFlags.contains(UdrFmt.PROC_FLAG_GUIDING)) {
				TopologyCtx.BizlogTimestamp(true, udr, MdbConst.BIZLOG_HK_UDR_GUIDING, null);
			}
		}
	}
}
