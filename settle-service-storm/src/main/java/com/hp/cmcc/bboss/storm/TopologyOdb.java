package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.entity.DbDupchkUdrRec;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;

public class TopologyOdb {
	private static Logger L = LoggerFactory.getLogger(TopologyOdb.class);
	private static final String _I_DUPCHK_UDR = "INSERT INTO DUPCHK_UDR (MMDD, HASH_KEY, EVENT_TIME, AUX_KEY, FILE_ID, LINE_NUM) "
			+ "VALUES(?, ?, ?, ?, ?, ?)";
	private static final String _Q_DUPCHK_UDR = "SELECT MMDD, HASH_KEY, EVENT_TIME, AUX_KEY, FILE_ID, LINE_NUM FROM DUPCHK_UDR "
			+ "WHERE MMDD=? AND HASH_KEY=? AND EVENT_TIME=? AND AUX_KEY=?";

	public boolean addSttlDupchk(Connection conn, DbDupchkUdrRec rec) throws SQLException {
		OdbCli cli_ = OdbAgt.GetUdrInstance();
		try {
			if (conn == null) {
				cli_.updateWithThrow(_I_DUPCHK_UDR, rec.asInsertObjArray());
			} else {
				cli_.update(conn, _I_DUPCHK_UDR, rec.asInsertObjArray());
			}
		} catch (SQLException e) {
			L.warn("insert into DUPCHK_UDR errCode:{}",e.getErrorCode());
			if (e.getErrorCode() == 1||e.getErrorCode()==8532||e.getErrorCode()==1062) {
				return false;
			} else if (StringUtils.contains(e.getMessage(),"Duplicate entry")){
				return false;
			}else {
				throw e;

			}
		}
		return true;
	}

	public DbDupchkUdrRec getSttlDupchk(int mmdd, String hash_key, Timestamp event_time, String aux_key) {
		OdbCli cli_ = OdbAgt.GetUdrInstance();
		DbDupchkUdrRec rec_ = cli_.queryForObject(_Q_DUPCHK_UDR, DbDupchkUdrRec.class, mmdd, hash_key, event_time, aux_key);
		if (rec_ == null)
			L.debug("DUPCHK_UDR rec [{},{},{},{}] not found",
					new Object[] { mmdd, hash_key, PubMethod.Timestamp2Str(event_time, PubMethod.TimeStrFmt.Fmt23), aux_key });
		return rec_;
	}
}
