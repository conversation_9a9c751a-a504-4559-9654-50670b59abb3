package com.hp.cmcc.bboss.storm.config;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BizCfg {
	private static Logger L = LoggerFactory.getLogger(BizCfg.class);

	public static boolean Init() {
		/*if (!OdbAgt.Init(AppCmdline.GetInstance()._dbloginKey, 2, 0, 10)) {
			throw new RuntimeException("init db connection failed");
		}*/

		boolean rc_ = OdbSystemParam.GetInstance().refresh();
		if (!rc_) {
			L.warn("call OdbSttlSystemParam init error");
		}

		if (!MdbAgt.Init(OdbSystemParam.GetInstance(), 19, 23, 3)) {
			throw new RuntimeException("init memory db failed");
		}
		return rc_;
	}

}
