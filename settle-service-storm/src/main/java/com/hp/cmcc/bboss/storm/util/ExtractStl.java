package com.hp.cmcc.bboss.storm.util;

import com.hp.cmcc.bboss.pub.edb.entity.*;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * ExtractStl 工具类
 * @author: zhanglei
 * @version: 1.0
 * @date: 2022/1/12 17:28
 */
public class ExtractStl {
    public static EdbStlOfferRec extractStlOffer(ResultSet rs)throws SQLException {
        EdbStlOfferRec rec_ = new EdbStlOfferRec();
        int idx_ = 0;
        rec_._id = rs.getLong(++idx_);
        rec_._dataSource = rs.getInt(++idx_);
        rec_._offerCode = rs.getString(++idx_);
        rec_._productCode = rs.getString(++idx_);
        rec_._orderMode = rs.getString(++idx_);
        rec_._ruleId = rs.getLong(++idx_);
        rec_._routeCode = rs.getString(++idx_);
        rec_._destSource = rs.getString(++idx_);
        rec_._effDate = rs.getString(++idx_);
        rec_._expDate = rs.getString(++idx_);
        return rec_;
    }

    public static EdbStlRuleRec extractStlRule(ResultSet rs) throws SQLException {
        EdbStlRuleRec rec_ = new EdbStlRuleRec();
        int idx_ = 0;
        rec_._ruleId = rs.getLong(++idx_);
        rec_._ruleName = rs.getString(++idx_);
        rec_._objectId = rs.getLong(++idx_);
        rec_._balance = rs.getInt(++idx_);
        rec_._effDate = rs.getString(++idx_);
        rec_._expDate = rs.getString(++idx_);
        return rec_;
    }

    public static EdbStlRuleItemRec extractStlRuleItem(ResultSet rs) throws SQLException {
        EdbStlRuleItemRec rec_ = new EdbStlRuleItemRec();
        int idx_ = 0;
        rec_._id = rs.getLong(++idx_);
        rec_._ruleId = rs.getLong(++idx_);
        rec_._chargeItem = rs.getString(++idx_);
        rec_._itemName = rs.getString(++idx_);
        rec_._effDate = rs.getString(++idx_);
        rec_._expDate = rs.getString(++idx_);
        return rec_;
    }

    public static EdbStlObjectRec extractStlObject(ResultSet rs) throws SQLException {
        EdbStlObjectRec rec_ = new EdbStlObjectRec();
        int idx_ = 0;
        rec_._objectId = rs.getLong(++idx_);
        rec_._objectName = rs.getString(++idx_);
        rec_._objectType = rs.getInt(++idx_);
        rec_._ownerName = rs.getString(++idx_);
        rec_._fieldName = rs.getString(++idx_);
        rec_._searchKey = rs.getString(++idx_);
        rec_._objectValue = rs.getString(++idx_);
        return rec_;
    }

    public static EdbStlRateRec extractStlRate(ResultSet rs) throws SQLException {
        EdbStlRateRec rec_ = new EdbStlRateRec();
        int idx_ = 0;
        rec_._id = rs.getLong(++idx_);
        rec_._rateId = rs.getLong(++idx_);
        rec_._ruleId = rs.getLong(++idx_);
        rec_._inObjectId = rs.getLong(++idx_);
        rec_._rateCode = rs.getString(++idx_);
        rec_._rateType = rs.getInt(++idx_);
        rec_._calcPriority = rs.getInt(++idx_);
        rec_._roundMethod = rs.getInt(++idx_);
        rec_._effDate = rs.getString(++idx_);
        rec_._expDate = rs.getString(++idx_);
        return rec_;
    }

    public static EdbStlPayProvRec extractStlPayProv(ResultSet rs) throws SQLException {
        EdbStlPayProvRec rec_ = new EdbStlPayProvRec();
        int idx_ = 0;
        rec_._svcInstId = rs.getLong(++idx_);
        rec_._chargeItem = rs.getString(++idx_);
        rec_._objectValue = rs.getString(++idx_);
        rec_._effDate = rs.getString(++idx_);
        rec_._expDate = rs.getString(++idx_);
        return rec_;
    }


    public static EdbStlTariffRateRec extractStlTariffRate(ResultSet rs) throws SQLException {
        EdbStlTariffRateRec rec_ = new EdbStlTariffRateRec();
        int idx_ = 0;
        rec_._rateId = rs.getLong(++idx_);
        rec_._tariffType = rs.getInt(++idx_);
        rec_._matchMode = rs.getInt(++idx_);
        return rec_;
    }


    public static EdbStlRepartRateRec extractStlRepartRate(ResultSet rs) throws SQLException {
        EdbStlRepartRateRec rec_ = new EdbStlRepartRateRec();
        int idx_ = 0;
        rec_._rateId = rs.getLong(++idx_);
        rec_._tariffType = rs.getInt(++idx_);
        rec_._matchMode = rs.getInt(++idx_);
        return rec_;
    }

    public static EdbStlEspRateRec extractStlEspRate(ResultSet rs) throws SQLException {
        EdbStlEspRateRec rec_ = new EdbStlEspRateRec();
        int idx_ = 0;
        rec_._rateId = rs.getLong(++idx_);
        rec_._matchMode = rs.getInt(++idx_);
        return rec_;
    }
}
