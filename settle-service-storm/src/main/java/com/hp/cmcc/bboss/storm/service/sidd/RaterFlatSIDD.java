package com.hp.cmcc.bboss.storm.service.sidd;

import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlFlatRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class RaterFlatSIDD extends FunctorIDD {
	private static final String _Q_STL_FLAT_RATE = "SELECT RATE_ID, RATE_VALUE FROM FLAT_RATE WHERE RATE_ID = ?";
	private EdbStlRateRec _rateRec;
	private String[] _inSidd;

	public RaterFlatSIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
	}

	public void prepare(EdbStlRateRec rate_rec, String[] in_sidd) {
		_rateRec = rate_rec;
		_inSidd = in_sidd;
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		EdbStlFlatRateRec flat_rate_ = _seekFlatRate(udr, _rateRec);
		if (flat_rate_ == null || udr.isErr())
			return;

		BigDecimal rate_value_ = new BigDecimal(flat_rate_._rateValue);
		BigDecimal fee_ = new BigDecimal(_inSidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
		fee_ = fee_.multiply(rate_value_);
		_inSidd[UdrFmt.S_29_SETTLE_NOTAX_30] = fee_.setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
		_inSidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
		l.debug("{} FLAT{} id:val {}:{} notax:tax amount {}:{} settle {}:{}", _logTag, udr.isFeedback() ? "(FBK)" : "",
				flat_rate_._rateId, flat_rate_._rateValue, _inSidd[UdrFmt.S_19_AMOUNT_NOTAX_20], _inSidd[UdrFmt.S_20_AMOUNT_TAX_21],
				_inSidd[UdrFmt.S_29_SETTLE_NOTAX_30], _inSidd[UdrFmt.S_30_SETTLE_TAX_31]);
	}

	private EdbStlFlatRateRec _seekFlatRate(UdrFmt udr, EdbStlRateRec rate_rec) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			EdbStlFlatRateRec flat_rate_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_FLAT_RATE);
			pstmt_.setLong(1, rate_rec._rateId);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				flat_rate_ = _extractStlFlatRate(rs_);
				break;
			}
			if (flat_rate_ != null) {
				BigDecimal rate_value_ = new BigDecimal(flat_rate_._rateValue);
				if (RaterSIDD.POS_ONE.compareTo(rate_value_) < 0 || RaterSIDD.NEG_ONE.compareTo(rate_value_) > 0) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S612_FLAT_RATE_VAL_OOR;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%d:%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
							flat_rate_._rateId, flat_rate_._rateValue);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
							"RULE %s FLAT_RATE %d RATE_VALUE %s out of range [-1,1]", udr._uFields[UdrFmt.S_32_RULE_ID_33],
							flat_rate_._rateId, flat_rate_._rateValue);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					flat_rate_ = null;
				} else {
					l.trace("{} RULE {} FLAT_RATE located, {}", _logTag, rate_rec._ruleId, flat_rate_.toGsonStr());
				}
			} else {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S611_NO_FLAT_RATE_CFG;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%d", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						rate_rec._rateId);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %s FLAT_RATE %d no cfg",
						udr._uFields[UdrFmt.S_32_RULE_ID_33], rate_rec._rateId);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return flat_rate_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlFlatRateRec _extractStlFlatRate(ResultSet rs) throws SQLException {
		EdbStlFlatRateRec rec_ = new EdbStlFlatRateRec();
		int idx_ = 0;
		rec_._rateId = rs.getLong(++idx_);
		rec_._rateValue = rs.getString(++idx_);
		return rec_;
	}
}
