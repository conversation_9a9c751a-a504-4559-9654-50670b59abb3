package com.hp.cmcc.bboss.storm.sidd;

import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlObjectRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlOfferRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRuleItemRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRuleRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

public class GuiderSIDDNew extends FunctorIDD {
	private static final String _Q_STL_OFFER = "SELECT ID, DATA_SOURCE, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, RULE_ID, "
			+ "ROUTE_CODE, DEST_SOURCE, EFF_DATE, EXP_DATE FROM OFFER WHERE DATA_SOURCE = ? AND OFFER_CODE = ? "
			+ "AND PRODUCT_CODE = ? AND ORDER_MODE = ? ORDER BY EFF_DATE DESC";
	private static final String _Q_STL_RULE = "SELECT RULE_ID, RULE_NAME, OBJECT_ID, BALANCE, EFF_DATE, EXP_DATE "
			+ "FROM RULE WHERE RULE_ID = ?";
	private static final String _Q_STL_RULE_ITEM = "SELECT ID, RULE_ID, CHARGE_ITEM, ITEM_NAME, EFF_DATE, EXP_DATE "
			+ "FROM RULE_ITEM WHERE RULE_ID = ? AND CHARGE_ITEM = ? ORDER BY EFF_DATE DESC";
	private static final String _Q_STL_OBJECT = "SELECT OBJECT_ID, OBJECT_NAME, OBJECT_TYPE, OWNER_NAME, FIELD_NAME, "
			+ "SEARCH_KEY, OBJECT_VALUE FROM OBJECT WHERE OBJECT_ID = ?";
	private static final String _Q_STL_RATE = "SELECT ID, RATE_ID, RULE_ID, IN_OBJECT_ID, RATE_CODE, RATE_TYPE, "
			+ "CALC_PRIORITY, ROUND_METHOD, EFF_DATE, EXP_DATE FROM RATE WHERE RULE_ID = ? "
			+ "ORDER BY CALC_PRIORITY ASC, EFF_DATE DESC, ID ASC";

	private Map<Long, EdbStlOfferRec> _offerMap; // K:_ruleId
	private Map<Long, EdbStlRuleRec> _ruleMap; // K:_ruleId
	private Map<Long, List<EdbStlRateRec>> _rateMap; // K:_ruleId
	private Map<Long, EdbStlObjectRec> _oObjMap; // K:rule_id
	private Map<Long, EdbStlObjectRec> _iObjMap; // K:_objectId;

	public static String OfferElem(UdrFmt udr) {
		StringBuilder sb_ = new StringBuilder();
		sb_.append(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04] == null ? "" : udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]);
		sb_.append(':');
		sb_.append(udr._uFields[UdrFmt.S_07_OFFER_CODE_08] == null ? "" : udr._uFields[UdrFmt.S_07_OFFER_CODE_08]);
		sb_.append(':');
		sb_.append(udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09] == null ? "" : udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09]);
		sb_.append(':');
		sb_.append(udr._uFields[UdrFmt.S_15_ORDER_MODE_16] == null ? "" : udr._uFields[UdrFmt.S_15_ORDER_MODE_16]);
//		return sb_.substring(0);
		return sb_.toString();
	}

	public GuiderSIDDNew() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
		_offerMap = new HashMap<>();
		_ruleMap = new HashMap<>();
		_rateMap = new HashMap<>();
		_oObjMap = new HashMap<>();
		_iObjMap = new HashMap<>();
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		_clearMaps();
		if (udr.isErr())
			return;
		_guidingSIDD(udr);
	}

	private void _guidingSIDD(UdrFmt udr) throws Exception {
		_guidOffer(udr);
		if (udr.isErr())
			return;

		_guidRule(udr);
		if (udr.isErr())
			return;

		for (EdbStlRuleRec stl_rule_ : _ruleMap.values()) {
			_seekStlObject(udr, stl_rule_);
			if (udr.isErr())
				return;
		}

		for (Long rule_id_ : _ruleMap.keySet()) {
			_guidRate(udr, rule_id_);
			if (udr.isErr())
				return;
		}

		for (List<EdbStlRateRec> stl_rate_list_ : _rateMap.values()) {
			_seekStlInObjects(udr, stl_rate_list_);
			if (udr.isErr())
				return;
		}

		_filterFeedbackOffer(udr);
		if (udr.isErr())
			return;

		_fillAuxMap(udr);
		_traceGuidingInfo(udr);
	}

	private void _traceGuidingInfo(UdrFmt udr) {
		List<String> offer_json_list_ = new ArrayList<>();
		for (EdbStlOfferRec offer_ : _offerMap.values())
			offer_json_list_.add(offer_.toGsonStr());

		List<String> rule_json_list_ = new ArrayList<>();
		for (EdbStlRuleRec rule_ : _ruleMap.values())
			rule_json_list_.add(rule_.toGsonStr());

		List<String> o_obj_json_list_ = new ArrayList<>();
		for (EdbStlObjectRec o_obj_ : _oObjMap.values())
			o_obj_json_list_.add(o_obj_.toGsonStr());

		List<String> rate_json_list_ = new ArrayList<>();
		for (List<EdbStlRateRec> rate_list_ : _rateMap.values())
			for (EdbStlRateRec rate_ : rate_list_)
				rate_json_list_.add(rate_.toGsonStr());

		List<String> i_obj_json_list_ = new ArrayList<>();
		for (EdbStlObjectRec i_obj_ : _iObjMap.values())
			i_obj_json_list_.add(i_obj_.toGsonStr());

		l.trace("{} OFFER({}) {}, RULE({}) {}, RATE({}) {}, O_OBJ({}) {}, I_OBJ({}) {}", _logTag, offer_json_list_.size(),
				PubMethod.Collection2Str(offer_json_list_, ","), rule_json_list_.size(),
				PubMethod.Collection2Str(rule_json_list_, ","), rate_json_list_.size(),
				PubMethod.Collection2Str(rate_json_list_, ","), o_obj_json_list_.size(),
				PubMethod.Collection2Str(o_obj_json_list_, ","), i_obj_json_list_.size(),
				PubMethod.Collection2Str(i_obj_json_list_, ","));
	}

	private void _fillAuxMap(UdrFmt udr) {
		if (udr._auxMap == null)
			udr._auxMap = new HashMap<>();

		List<EdbStlOfferRec> offer_list_ = new ArrayList<>(_offerMap.values());
		udr._auxMap.put(UdrFmt.AUX_MAP_KEY_OFFER, offer_list_);

		for (EdbStlRuleRec rule_ : _ruleMap.values())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RULE, rule_._ruleId), rule_);

		for (Entry<Long, EdbStlObjectRec> ent_ : _oObjMap.entrySet())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_O_OBJECT, ent_.getKey()), ent_.getValue());

		for (Entry<Long, List<EdbStlRateRec>> ent_ : _rateMap.entrySet())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RATE, ent_.getKey()), ent_.getValue());

		for (EdbStlObjectRec i_obj_ : _iObjMap.values())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_I_OBJECT, i_obj_._objectId), i_obj_);
	}

	private void _filterFeedbackOffer(UdrFmt udr) {
		for (EdbStlOfferRec offer_ : _offerMap.values()) {
			if (udr.isFeedback() && (EdbStlOfferRec.ROUTE_CODE_3_EC_RECEIVEABLE == offer_._routeCode
					|| EdbStlOfferRec.ROUTE_CODE_4_EC_RECEIVED == offer_._routeCode)) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFER.ID %d", OfferElem(udr), offer_._id);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S503_INVALID_ROUTE_CODE_FOR_FEEDBACK_UDR;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("OFFER ID %d invalid ROUTE_CODE %d for feedback UDR",
						offer_._id, offer_._routeCode);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
		}
	}

	private void _guidRate(UdrFmt udr, long rule_id) throws Exception {
		List<EdbStlRateRec> stl_rate_list_ = _seekStlRate(udr, rule_id);
		if (udr.isErr())
			return;

		Set<Integer> rate_type_set_ = new HashSet<Integer>();
		for (EdbStlRateRec stl_rate_ : stl_rate_list_)
			rate_type_set_.add(stl_rate_._rateType);
		if (rate_type_set_.size() > 1) {
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID %d", OfferElem(udr), rule_id);
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S504_MULTI_RATE_TYPE_FOR_ONE_RULE;
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d has multi RATE_TYPEs (%s)", rule_id,
					PubMethod.Collection2Str(rate_type_set_, ","));
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}
		_rateMap.put(rule_id, stl_rate_list_);
	}

	private void _guidRule(UdrFmt udr) throws Exception {
		List<EdbStlOfferRec> offer_list_ = new ArrayList<>(_offerMap.values());
		List<EdbStlRuleRec> rule_list_ = _seekStlRule(udr, offer_list_);
		if (udr.isErr())
			return;

		_filterOfferRules(udr, rule_list_);
		if (udr.isErr())
			return;

		_matchStlRuleItem(udr);
	}

	private void _guidOffer(UdrFmt udr) throws Exception {
		List<EdbStlOfferRec> offer_list_ = _seekStlOffer(udr);
		if (offer_list_ == null || udr.isErr())
			return;
		List<Long> offer_id_list_ = new ArrayList<>();
		List<Long> rule_id_list_ = new ArrayList<>();
		for (EdbStlOfferRec offer_ : offer_list_) {
			offer_id_list_.add(offer_._id);
			rule_id_list_.add(offer_._ruleId);
		}

		for (EdbStlOfferRec offer_ : offer_list_) {
			if (_offerMap.containsKey(offer_._ruleId)) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = OfferElem(udr);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S502_MULTI_OFFER_RULE_ID_DUP;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("matched %d OFFERs(%s) has dup RULE_ID(%s)",
						offer_list_.size(), PubMethod.Collection2Str(offer_id_list_, ","),
						PubMethod.Collection2Str(rule_id_list_, ","));
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			_offerMap.put(offer_._ruleId, offer_);
		}
	}

	private List<EdbStlOfferRec> _seekStlOffer(UdrFmt udr) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		List<EdbStlOfferRec> offer_list_ = null;
		try {
			String tm14_ = udr._uFields[UdrFmt.S_35_START_TIME_36];
			int raw_offer_cnt_ = 0;
			boolean has_rs_ = false;
			EdbStlOfferRec stl_offer_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_OFFER);
			pstmt_.setInt(1, Integer.parseInt(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]));
			pstmt_.setString(2, udr._uFields[UdrFmt.S_07_OFFER_CODE_08]);
			pstmt_.setString(3, udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09]);
			pstmt_.setString(4, udr._uFields[UdrFmt.S_15_ORDER_MODE_16]);
			if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09])) {
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_offer_ = _extractStlOffer(rs_);
					++raw_offer_cnt_;
					has_rs_ = true;
					if (!stl_offer_.isEffective(tm14_)) {
						l.trace("{} START_TIME_36 {} ineffective, skip offer {}", _logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
								stl_offer_.toGsonStr());
						continue;
					}
					if (offer_list_ == null)
						offer_list_ = new ArrayList<EdbStlOfferRec>();
					offer_list_.add(stl_offer_);
				}
			}

			if (offer_list_ == null || offer_list_.isEmpty()) {
				pstmt_.setString(3, "-1");
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_offer_ = _extractStlOffer(rs_);
					++raw_offer_cnt_;
					has_rs_ = true;
					if (!stl_offer_.isEffective(tm14_)) {
						l.trace("{} START_TIME_36 {} ineffective, skip offer {}", _logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
								stl_offer_.toGsonStr());
						continue;
					}
					if (offer_list_ == null)
						offer_list_ = new ArrayList<EdbStlOfferRec>();
					offer_list_.add(stl_offer_);
				}
			}

			if (offer_list_ != null && !offer_list_.isEmpty()) {
				// l.trace("{} {} OFFERs located", _logTag, offer_list_.size());
			} else {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = OfferElem(udr);
				if (has_rs_) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S501_OFFER_CFG_INEFFECTIVE;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("%d OFFERs but none effective", raw_offer_cnt_);
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S500_NO_OFFER_CFG;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "no OFFER matches";
				}
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return offer_list_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlOfferRec _extractStlOffer(ResultSet rs) throws SQLException {
		EdbStlOfferRec rec_ = new EdbStlOfferRec();
		int idx_ = 0;
		rec_._id = rs.getLong(++idx_);
		rec_._dataSource = rs.getInt(++idx_);
		rec_._offerCode = rs.getString(++idx_);
		rec_._productCode = rs.getString(++idx_);
		rec_._orderMode = rs.getString(++idx_);
		rec_._ruleId = rs.getLong(++idx_);
		rec_._routeCode = rs.getString(++idx_);
		rec_._destSource = rs.getString(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}

	private void _filterOfferRules(UdrFmt udr, List<EdbStlRuleRec> rule_list) {
		List<Long> offer_id_list_ = new ArrayList<>();
		for (EdbStlOfferRec offer_ : _offerMap.values())
			offer_id_list_.add(offer_._id);

		List<Long> rule_id_list_ = new ArrayList<>();
		for (EdbStlRuleRec rule_ : rule_list)
			rule_id_list_.add(rule_._ruleId);

		if (offer_id_list_.size() != rule_id_list_.size()) { // assert
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S511_RULE_CFG_INEFFECTIVE;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFERs(%s) RULEs(%s)", OfferElem(udr),
					PubMethod.Collection2Str(offer_id_list_, ","), PubMethod.Collection2Str(rule_id_list_, ","));
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("INTERNAL ERROR, count of OFFER:RULE %d:%d ne",
					offer_id_list_.size(), rule_id_list_.size());
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}

		for (EdbStlRuleRec rule_ : rule_list) {
			if (!rule_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
				EdbStlOfferRec offer_ = _offerMap.get(rule_._ruleId);
				l.trace("{} START_TIME_36 {} ineffective, skip RULE {}, skip OFFER {}", _logTag,
						udr._uFields[UdrFmt.S_35_START_TIME_36], rule_.toGsonStr(), offer_.toGsonStr());
				_offerMap.remove(rule_._ruleId);
			} else {
				_ruleMap.put(rule_._ruleId, rule_);
			}
		}

		if (_ruleMap.isEmpty()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S511_RULE_CFG_INEFFECTIVE;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFERs(%s) RULEs(%s)", OfferElem(udr),
					PubMethod.Collection2Str(offer_id_list_, ","), PubMethod.Collection2Str(rule_id_list_, ","));
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("%d RULEs but none effective", rule_id_list_.size());
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
		}
	}

	private List<EdbStlRuleRec> _seekStlRule(UdrFmt udr, List<EdbStlOfferRec> offer_list) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		List<EdbStlRuleRec> rule_list_ = null;
		try {
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_RULE);
			for (EdbStlOfferRec offer_ : offer_list) {
				EdbStlRuleRec stl_rule_ = null;
				pstmt_.setLong(1, offer_._ruleId);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_rule_ = _extractStlRule(rs_);
					if (rule_list_ == null)
						rule_list_ = new ArrayList<EdbStlRuleRec>();
					rule_list_.add(stl_rule_);
				}
				if (stl_rule_ == null) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S510_NO_RULE_CFG;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d)", OfferElem(udr), offer_._ruleId);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("OFFER %d RULE_ID %d not exists", offer_._id,
							offer_._ruleId);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					return null;
				}
			}
			return rule_list_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlRuleRec _extractStlRule(ResultSet rs) throws SQLException {
		EdbStlRuleRec rec_ = new EdbStlRuleRec();
		int idx_ = 0;
		rec_._ruleId = rs.getLong(++idx_);
		rec_._ruleName = rs.getString(++idx_);
		rec_._objectId = rs.getLong(++idx_);
		rec_._balance = rs.getInt(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}

	private void _matchStlRuleItem(UdrFmt udr) throws Exception {
		int cnt_nonexits_ = 0;
		int cnt_ineffective_ = 0;
		AtomicBoolean has_rs_ = new AtomicBoolean(false);
		List<Long> rule_id_list_ = new ArrayList<>(_ruleMap.keySet());

		for (Long rule_id_ : rule_id_list_) {
			EdbStlRuleRec rule_ = _ruleMap.get(rule_id_);
			EdbStlOfferRec offer_ = _offerMap.get(rule_id_);
			has_rs_.set(false);
			EdbStlRuleItemRec stl_rule_item_ = _matchOneStlRuleItem(udr, rule_, has_rs_);
			if (stl_rule_item_ == null) {
				if (has_rs_.get())
					++cnt_ineffective_;
				else
					++cnt_nonexits_;
				_ruleMap.remove(rule_id_);
				_offerMap.remove(rule_id_);
				l.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE {}, skip OFFER {}", _logTag,
						udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], rule_.toGsonStr(), offer_.toGsonStr());
			}
		}

		if (_ruleMap.isEmpty()) {
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_IDs(%s) CHARGE_ITEM(%s)", OfferElem(udr),
					PubMethod.Collection2Str(rule_id_list_, ","), udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			if (cnt_nonexits_ == 0) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S513_RULE_ITEM_INEFFECTIVE;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE_IDs(%s) RULE_ITEM none effective",
						PubMethod.Collection2Str(rule_id_list_, ","));
			} else {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S512_NO_RULE_ITEM_CFG;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE_IDs(%s) RULE_ITEM nonexist:ineffective %d:%d",
						PubMethod.Collection2Str(rule_id_list_, ","), cnt_nonexits_, cnt_ineffective_);
			}
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
		}
	}

	private EdbStlRuleItemRec _matchOneStlRuleItem(UdrFmt udr, EdbStlRuleRec stl_rule, AtomicBoolean has_rs) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			EdbStlRuleItemRec stl_rule_item_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_RULE_ITEM);
			pstmt_.setLong(1, stl_rule._ruleId);
			pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				stl_rule_item_ = _extractStlRuleItem(rs_);
				has_rs.set(true);
				if (!stl_rule_item_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
					l.trace("{} START_TIME_36 {} ineffective, skip rule_item {}", _logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
							stl_rule_item_.toGsonStr());
					stl_rule_item_ = null;
					continue;
				}
				break;
			}
			if (stl_rule_item_ == null) {
				pstmt_.setString(2, "-1");
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_rule_item_ = _extractStlRuleItem(rs_);
					has_rs.set(true);
					if (!stl_rule_item_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
						l.trace("{} START_TIME_36 {} ineffective, skip rule_item {}", _logTag,
								udr._uFields[UdrFmt.S_35_START_TIME_36], stl_rule_item_.toGsonStr());
						stl_rule_item_ = null;
						continue;
					}
					break;
				}
			}
			if (stl_rule_item_ == null) {
				// l.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE {}", _logTag,
				// udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], stl_rule.toGsonStr());
			}
			return stl_rule_item_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlRuleItemRec _extractStlRuleItem(ResultSet rs) throws SQLException {
		EdbStlRuleItemRec rec_ = new EdbStlRuleItemRec();
		int idx_ = 0;
		rec_._id = rs.getLong(++idx_);
		rec_._ruleId = rs.getLong(++idx_);
		rec_._chargeItem = rs.getString(++idx_);
		rec_._itemName = rs.getString(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}

	private void _seekStlObject(UdrFmt udr, EdbStlRuleRec rule_rec) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			EdbStlObjectRec stl_object_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_OBJECT);
			pstmt_.setLong(1, rule_rec._objectId);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				stl_object_ = _extractStlObject(rs_);
				break;
			}
			if (stl_object_ != null) {
				// l.trace("{} OBJECT for RULE {} located, {}", _logTag, rule_rec._ruleId, stl_object_.toGsonStr());
				_oObjMap.put(rule_rec._ruleId, stl_object_);
			} else {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S520_NO_OBJECT_CFG;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d) out OBJ_ID(%d)", OfferElem(udr),
						rule_rec._ruleId, rule_rec._objectId);
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%s:%s:%s:%d:%d",
						udr._uFields[UdrFmt.S_03_DATA_SOURCE_04], udr._uFields[UdrFmt.S_07_OFFER_CODE_08],
						udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09], udr._uFields[UdrFmt.S_15_ORDER_MODE_16], rule_rec._ruleId,
						rule_rec._objectId);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("(out) OBJECT_ID %d for RULE %d no cfg",
						rule_rec._objectId, rule_rec._ruleId);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private List<EdbStlRateRec> _seekStlRate(UdrFmt udr, long rule_id) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			boolean has_rs_ = false;
			List<EdbStlRateRec> stl_rate_list_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_RATE);
			pstmt_.setLong(1, rule_id);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				EdbStlRateRec stl_rate_ = _extractStlRate(rs_);
				has_rs_ = true;
				if (!stl_rate_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
					l.trace("{} START_TIME_36 {} ineffective, skip rate {}", _logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
							stl_rate_.toGsonStr());
					stl_rate_ = null;
					continue;
				}
				if (stl_rate_list_ == null) {
					stl_rate_list_ = new ArrayList<EdbStlRateRec>();
				}
				stl_rate_list_.add(stl_rate_);
			}
			if (stl_rate_list_ != null) {
				// l.trace("{} RATE for RULE {} located, total {} elem", _logTag, rule_id, stl_rate_list_.size());
			} else {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d)", OfferElem(udr), rule_id);
				if (has_rs_) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S531_RATE_CFG_INEFFECTIVE;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RATE for RULE %d none effective", rule_id);
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S530_NO_RATE_CFG;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RATE for RULE %d no cfg", rule_id);
				}
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return stl_rate_list_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlRateRec _extractStlRate(ResultSet rs) throws SQLException {
		EdbStlRateRec rec_ = new EdbStlRateRec();
		int idx_ = 0;
		rec_._id = rs.getLong(++idx_);
		rec_._rateId = rs.getLong(++idx_);
		rec_._ruleId = rs.getLong(++idx_);
		rec_._inObjectId = rs.getLong(++idx_);
		rec_._rateCode = rs.getString(++idx_);
		rec_._rateType = rs.getInt(++idx_);
		rec_._calcPriority = rs.getInt(++idx_);
		rec_._roundMethod = rs.getInt(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}

	private void _seekStlInObjects(UdrFmt udr, List<EdbStlRateRec> rate_list) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_OBJECT);
			for (EdbStlRateRec stl_rate_ : rate_list) {
				if (_iObjMap.containsKey(stl_rate_._inObjectId))
					continue;
				EdbStlObjectRec stl_object_ = null;
				pstmt_.setLong(1, stl_rate_._inObjectId);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_object_ = _extractStlObject(rs_);
					break;
				}
				if (stl_object_ != null) {
					// l.trace("{} IN_OBJECT {} for RATE {} located, {}", _logTag, stl_rate_._inObjectId, stl_rate_._rateId,
					// stl_object_.toGsonStr());
					_iObjMap.put(stl_object_._objectId, stl_object_);
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S532_RATE_NO_COR_IN_OBJECT_CFG;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RATE_ID(%d) in OBJ_ID(%d)", OfferElem(udr),
							stl_rate_._rateId, stl_rate_._inObjectId);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("IN_OBJECT %d for RATE %d no cfg",
							stl_rate_._inObjectId, stl_rate_._rateId);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					return;
				}
			}
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlObjectRec _extractStlObject(ResultSet rs) throws SQLException {
		EdbStlObjectRec rec_ = new EdbStlObjectRec();
		int idx_ = 0;
		rec_._objectId = rs.getLong(++idx_);
		rec_._objectName = rs.getString(++idx_);
		rec_._objectType = rs.getInt(++idx_);
		rec_._ownerName = rs.getString(++idx_);
		rec_._fieldName = rs.getString(++idx_);
		rec_._searchKey = rs.getString(++idx_);
		rec_._objectValue = rs.getString(++idx_);
		return rec_;
	}

	private void _clearMaps() {
		_offerMap.clear();
		_ruleMap.clear();
		_rateMap.clear();
		_oObjMap.clear();
		_iObjMap.clear();
	}
}
