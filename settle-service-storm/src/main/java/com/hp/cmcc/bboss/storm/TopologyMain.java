package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsgSerializer;
import com.hp.cmcc.bboss.pub.udr.UdrFmtSerializer;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.storm.Config;
import org.apache.storm.StormSubmitter;
import org.apache.storm.topology.TopologyBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TopologyMain {
	private static Logger L = LoggerFactory.getLogger(TopologyMain.class);
	private static final String _TopologyName = "bboss";
	public static String remoteurl = "";

//启动参数在storm/conf/storm.yaml中
	//worker.childopts: "-Ddbloginkey=billing -Dremotehost=sett-service-biz.bboss-1  -Dremoteport=8080 -Dworker.port=6627 -Xmx3072m"
	public static void main(String[] args) throws Exception {
        L.info("启动: topology:{}", _TopologyName);
		String db_login_key_ = System.getProperty("dbloginkey");
		if (PubMethod.IsBlank(db_login_key_)) {
			L.error("'-Ddbloginkey=***' property not set, JVM exit");
			System.exit(1);
		}
		if (!OdbAgt.Init(db_login_key_, 2, 0, 0)) {
			L.warn("conn db failed, JVM exit");
			System.exit(1);
		}

		TopologyCfg topology_cfg_ = TopologyCfg.GetInstance();
		if (!topology_cfg_.init()) {
			L.error("init topology cfg error");
			throw new RuntimeException("init topology cfg error");
		}

		TopologyBuilder builder_ = new TopologyBuilder();

		builder_.setSpout("spout", new KafkaSpout(), topology_cfg_._workerNum);
		if (topology_cfg_.validationGroup == 1) {
			builder_.setBolt("validation", new ValidationBolt(), topology_cfg_._paralValidation).shuffleGrouping("spout");
		}else {
			builder_.setBolt("validation", new ValidationBolt(), topology_cfg_._paralValidation).localOrShuffleGrouping("spout");
		}
		if (topology_cfg_.chkdupGroup == 1) {
			builder_.setBolt("chkdup", new ChkdupBolt(), topology_cfg_._paralChkdup).shuffleGrouping("validation"); // 详单剔重
		}else {
			builder_.setBolt("chkdup", new ChkdupBolt(), topology_cfg_._paralChkdup).localOrShuffleGrouping("validation"); // 详单剔重
		}
		if (topology_cfg_.guidingGroup == 1) {
			builder_.setBolt("guiding", new GuidingBolt(), topology_cfg_._paralGuiding).shuffleGrouping("chkdup"); // 详单规整
		}else {
			builder_.setBolt("guiding", new GuidingBolt(), topology_cfg_._paralGuiding).localOrShuffleGrouping("chkdup"); // 详单规整
		}
		if (topology_cfg_.ratingGroup == 1) {
			builder_.setBolt("rating", new RatingBolt(), topology_cfg_._paralRating).shuffleGrouping("guiding"); // 详单批价
		}else {
			builder_.setBolt("rating", new RatingBolt(), topology_cfg_._paralRating).localOrShuffleGrouping("guiding"); // 详单批价
		}
		if (topology_cfg_.erhndlGroup == 1) {
			builder_.setBolt("erhndl", new ErhndlBolt(), topology_cfg_._paralErhndl).shuffleGrouping("rating"); // 错单过滤
		}else {
			builder_.setBolt("erhndl", new ErhndlBolt(), topology_cfg_._paralErhndl).localOrShuffleGrouping("rating"); // 错单过滤
		}
		L.info("topology:{} validationGroup:{} chkdupGroup:{} guidingGroup:{} ratingGroup:{} erhndlGroup:{}",
				_TopologyName, topology_cfg_.validationGroup,topology_cfg_.chkdupGroup,topology_cfg_.guidingGroup,topology_cfg_.ratingGroup,topology_cfg_.erhndlGroup);
		Config storm_conf_ = new Config();
		storm_conf_.setDebug(false);
		storm_conf_.setNumWorkers(topology_cfg_._workerNum);
		/** 此参数比较重要，可适当调大一点 */
		/** 通常情况下 spout 的发射速度会快于下游的 bolt 的消费速度，当下游的 bolt 还有 TOPOLOGY_MAX_SPOUT_PENDING 个 tuple 没有消费完时，spout 会停下来等待，该配置作用于 spout 的每个 task。  */
		storm_conf_.put(Config.TOPOLOGY_MAX_SPOUT_PENDING, 20000);
		storm_conf_.put(Config.TOPOLOGY_ACKER_EXECUTORS, topology_cfg_._workerNum);
		storm_conf_.put(Config.TOPOLOGY_MESSAGE_TIMEOUT_SECS, topology_cfg_._failTimeout);
//		storm_conf_.put(Config.TOPOLOGY_WORKER_CHILDOPTS, "-Xmx4096m -Xms4096m");
//		storm_conf_.put(Config.TOPOLOGY_COMPONENT_RESOURCES_ONHEAP_MEMORY_MB, 512.0);
//		storm_conf_.put(Config.TOPOLOGY_TRANSFER_BUFFER_SIZE,            32);
//		storm_conf_.put(Config.TOPOLOGY_EXECUTOR_RECEIVE_BUFFER_SIZE, 16384);
		// 注册自定义类到Kryo
		storm_conf_.registerSerialization(UdrFmtMsg.class, UdrFmtMsgSerializer.class);
		storm_conf_.registerSerialization(UdrFmt.class, UdrFmtSerializer.class);

		L.info("try submit topology {}", _TopologyName);
		// TODO: 2023/3/2 本地模式使用该API geyongan
/*		LocalCluster localCluster = new LocalCluster();
		localCluster.submitTopology(_TopologyName, storm_conf_, builder_.createTopology());*/
		// TODO: 2023/3/2 集群模式使用该API geyongan
		StormSubmitter.submitTopology(_TopologyName, storm_conf_, builder_.createTopology());
		L.info("topology {} submit done", _TopologyName);
	}
}
