package com.hp.cmcc.bboss.storm.config;

import java.util.regex.Pattern;

public final class ConstantInfo {
    private ConstantInfo(){}

    public static Pattern patternYm6 = Pattern.compile("^[0-9]{6}$");
    public static Pattern patternTm14 = Pattern.compile("^[0-9]{14}$");
    public static Pattern patternSignedDigits = Pattern.compile("^[\\+\\-]?[0-9]+$");
    public static Pattern patternDigits = Pattern.compile("^[0-9]+$");
    public static Pattern patternDt8 = Pattern.compile("^[0-9]{8}$");
}
