package com.hp.cmcc.bboss.storm.sidd;

import com.google.gson.reflect.TypeToken;
import com.hp.cmcc.bboss.pub.dto.BaseRspsMsg;
import com.hp.cmcc.bboss.pub.dto.RequestParam;
import com.hp.cmcc.bboss.pub.dto.ResponseMsg;
import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlEspParameterRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlObjectRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlOfferRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlPayProvRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRepartRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRuleRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlTariffRateRec;
import com.hp.cmcc.bboss.pub.udr.UdrDef;
import com.hp.cmcc.bboss.pub.udr.UdrErrInfo;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.GsonUtil;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import com.hp.cmcc.bboss.storm.service.sidd.RaterEspSIDD;
import com.hp.cmcc.bboss.storm.service.sidd.RaterFlatSIDD;
import com.hp.cmcc.bboss.storm.service.sidd.RaterRepartSIDD;
import com.hp.cmcc.bboss.storm.service.sidd.RaterTariffSIDD;
import com.hp.cmcc.bboss.storm.util.ExtractStl;
import com.hp.cmcc.bboss.storm.util.OkHttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

public class RaterSIDD extends FunctorIDD {
	private static final String _Q_STL_PAY_PROV = "SELECT SVC_INST_ID, CHARGE_ITEM, OBJECT_VALUE, EFF_DATE, EXP_DATE "
			+ "FROM PAY_PROV WHERE SVC_INST_ID = ? AND CHARGE_ITEM = ? ORDER BY EFF_DATE DESC";
	private static final String _Q_STL_TARIFF_RATES = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM TARIFF_RATE "
			+ "WHERE RATE_ID IN (%s)";
	private static final String _Q_STL_REPART_RATES = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM REPART_RATE "
			+ "WHERE RATE_ID IN (%s)";
	//private static final String _Q_STL_ESP_RATES = "SELECT RATE_ID, MATCH_MODE FROM ESP_RATE WHERE RATE_ID IN (%s) ";
	public static final int _DIVIDE_SCALE = 13;
	static final BigDecimal _POS_ONE = new BigDecimal(1);
	static final BigDecimal _NEG_ONE = new BigDecimal(-1);
	static final BigDecimal _ONE_HUNDRED = new BigDecimal(100);

	private static String remoteUrl = "";

	private Map<String, Object> _guidingMap;
	private Map<Long, List<String[]>> _inSiddRuleMap; // K:rule_id V:idd_by_rate
	private Map<String, List<String[]>> _inSiddSeqMap; // K:'rule_id:RECORD_ID_29' V:idd_by_parameter
	private Map<Integer, List<String[]>> _paramSiddMap; // K:RECORD_ID_29

	public RaterSIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
		String remoteHost = System.getProperty("remotehost");
		String remotePort = System.getProperty("remoteport");
		remoteUrl = "http://" + remoteHost + ":" + remotePort;
		_inSiddRuleMap = new HashMap<>();
		_inSiddSeqMap = new HashMap<>();
		_paramSiddMap = new HashMap<>();
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		l.debug("--------------------------start RaterSIDD execUdr -------------------------------------");
		_logTag = log_tag;
		_prepare(udr);
		@SuppressWarnings("unchecked")
//		List<EdbStlOfferRec> offer_list_ = (List<EdbStlOfferRec>) _guidingMap.get(UdrFmt.AUX_MAP_KEY_OFFER); //获取STL_OFFER_T表相关信息
		Object offer_list_Object = _guidingMap.get(UdrFmt.AUX_MAP_KEY_OFFER);
		String jsonString = GsonUtil.toJsonString(offer_list_Object);
		List<EdbStlOfferRec> offer_list_ = GsonUtil.fromJsonString(jsonString,
				new TypeToken<List<EdbStlOfferRec>>(){}.getType());

		for (EdbStlOfferRec offer_ : offer_list_) {
			_ratingSIDD(udr, offer_);
			if (udr.isErr())
				return;
		}
		_mergeInSidd(udr);
	}

	private void _ratingSIDD(UdrFmt udr, EdbStlOfferRec offer) throws Exception {
		udr._eFields[UdrFmt.E_23_E09_SPARE1] = "";
		/** 1  _initInSIDDList(udr, offer) **/
		List<EdbStlRateRec> stl_rate_list_ = _initInSIDDList(udr, offer); //
		if (udr.isErr())
			return;
		/** 2 **/
		_fillInObjectValues(udr, offer); // _inSiddRuleMap中的in_object确定
		if (udr.isErr())
			return;
		/** 3 **/
		_filterRateList(udr, stl_rate_list_); //
		if (udr.isErr())
			return;

		UdrErrInfo err_info_ = new UdrErrInfo();
		//
		List<String[]> in_sidd_list_ = _inSiddRuleMap.get(offer._ruleId);
		int i = 0;
		for (EdbStlRateRec rate_rec_ : stl_rate_list_) {
			/** 4 **/
			// _ratingInSIDD(udr, rate_rec_, in_sidd_list_.get(i++));
			_ratingInSIDD(udr, rate_rec_, offer._ruleId, i);
			i++;
			if (udr.isErr()) {
				if (_errCanBeSkipped(udr, rate_rec_, err_info_)) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = "";
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = "";
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "";
				} else {
					return;
				}
			}
		}

		for (Entry<Integer, List<String[]>> ent_ : _paramSiddMap.entrySet()) {
			String seq_key_ = PubMethod.FmtArgs("%d:%d", offer._ruleId, ent_.getKey());
			_inSiddSeqMap.put(seq_key_, ent_.getValue());
		}
		_paramSiddMap.clear();

		int rate_type_ = stl_rate_list_.get(0)._rateType;
		int in_sidd_map_val_cnt_ = 0;
		String key_prefix_ = PubMethod.FmtArgs("%d:", offer._ruleId);
		for (Entry<String, List<String[]>> ent_ : _inSiddSeqMap.entrySet()) {
			if (ent_.getKey().startsWith(key_prefix_))
				in_sidd_map_val_cnt_ += ent_.getValue().size();
		}
		if (!PubMethod.IsEmpty(err_info_._errCode) && in_sidd_map_val_cnt_ == 0
				&& (EdbStlRateRec.RATE_TYPE_2_TARIFF == rate_type_ || EdbStlRateRec.RATE_TYPE_3_REPART == rate_type_)) {
			err_info_.toUdr(udr);
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}
		/** 5 **/
		_rewriteRecordId(offer._ruleId);
		/** 6 **/
		_rebalance(udr, offer._ruleId);
	}

	/**
	 * @param udr
	 * @param rule_id
	 */
	private void _rebalance(UdrFmt udr, long rule_id) {
		String url = remoteUrl + "/api/v1/settle/storm/Rater/sidd/rebalance";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setRule_id(rule_id);
		requestParam.setGuidingMap(_guidingMap);
		requestParam.setInSiddRuleMap(_inSiddRuleMap);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = rebalance(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
			udr._uList = data.getUdr()._uList;
		}

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
			udr._uList = data.getUdr()._uList;
			l.warn("{} {}, {} reject, RULE {},  {}", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE], rule_id, udr.toGsonStr());
		}
	}

	public BaseRspsMsg<ResponseMsg> rebalance(RequestParam requestParam) {
//		l.info("--------------- rebalance RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));
		long rule_id = requestParam.getRule_id();
		String logTag = requestParam.getLogTag();
		UdrFmt udr = requestParam.getUdr();
		Map<String, Object> guidingMap = requestParam.getGuidingMap();
		Map<Long, List<String[]>> inSiddRuleMap = requestParam.getInSiddRuleMap();

		ResponseMsg responseMsg = new ResponseMsg();

		String rule_key_ = PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RULE, rule_id);
//        EdbStlRuleRec rule_rec_ = (EdbStlRuleRec) guidingMap.get(rule_key_);
		Object rule_rec_object = guidingMap.get(rule_key_);
		String jsonString = GsonUtil.toJsonString(rule_rec_object);
		EdbStlRuleRec rule_rec_ = GsonUtil.fromJsonString(jsonString,
				new TypeToken<EdbStlRuleRec>(){}.getType());

		responseMsg.setUdr(udr);

		if (!(rule_rec_._balance.intValue() == 1)) {
			return BaseRspsMsg.ok(responseMsg);
		}
		List<String[]> in_sidd_list_ = inSiddRuleMap.get(rule_id);
		if (in_sidd_list_.size() == 1||in_sidd_list_.isEmpty()) { // only one result no need re-balance
			return BaseRspsMsg.ok(responseMsg);
		}
		/*
		 * 新增第二种固定值结算
		 * 平衡性校验
		 */
		if (Integer.valueOf(udr._eFields[UdrFmt.E_33_X08_RATE_TYPE]) == EdbStlRateRec.RATE_TYPE_3_REPART
				&& Integer.valueOf(udr._eFields[UdrFmt.E_34_X09_TARIFF_TYPE]) == EdbStlTariffRateRec.TARIFF_TYPE_3_FIXEDVALUE ) {
			return BaseRspsMsg.ok(responseMsg);
		}

		long tot_notax_ = Long.parseLong(udr._uFields[UdrFmt.S_19_AMOUNT_NOTAX_20]);//结算金额
		//根据业务需求，政企ESP局数据结算 按标准价结算时，结算金额存储在 S_37_RES2_38 字段中
		if ( Integer.parseInt(udr._eFields[UdrFmt.E_33_X08_RATE_TYPE]) == EdbStlRateRec.RATE_TYEP_5_ESP
				&& Integer.valueOf(udr._eFields[UdrFmt.E_34_X09_TARIFF_TYPE]) == EdbStlEspParameterRec.SETT_TYPE_1_STANDARD_PRICE ) {
			tot_notax_ = Long.parseLong(udr._uFields[UdrFmt.S_37_RES2_38]);//结算金额
		}

		long sum_notax_ = 0;
		long lst_notax_ = 0;
		for (String[] in_sidd_ : in_sidd_list_) {
			lst_notax_ = Long.parseLong(in_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30]);
			sum_notax_ += lst_notax_;
		}

		long sub_notax_ = sum_notax_ - lst_notax_;
		long adj_notax_ = lst_notax_;
		if (tot_notax_ != sum_notax_) {
			StringBuilder sb_ = new StringBuilder();
            adj_notax_ = tot_notax_ - sub_notax_;
            sb_.append(PubMethod.FmtArgs("notax cnt:tot:sum:lst:sub:adj %d:%d:%d:%d:%d:%d", in_sidd_list_.size(), tot_notax_,
                    sum_notax_, lst_notax_, sub_notax_, adj_notax_));
//            l.info("{} RULE_ID {}, {}", logTag, rule_id, sb_.substring(0));
			if ((tot_notax_ >= 0 && adj_notax_ >= 0) || (tot_notax_ < 0 && adj_notax_ <= 0)) {
				String[] in_sidd_ = in_sidd_list_.get(in_sidd_list_.size() - 1);
				in_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString(adj_notax_);
			} else {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S606_CAN_NOT_REBALANCE;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = sb_.substring(0);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d, %d results, re-balance adj inconsistent sign, pls chk cfg", rule_id, in_sidd_list_.size());
				_mergeInSidd(udr, inSiddRuleMap); // for logging clearly
				l.warn("{} {}, {} reject, RULE , {}, {},{}", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE], rule_id, sb_.substring(0), udr.toGsonStr());
			}
		}

		responseMsg.setUdr(udr);
		return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
				responseMsg);
	}

	private void _mergeInSidd(UdrFmt udr, Map<Long, List<String[]>> inSiddRuleMap) {
		List<String[]> all_sidd_ = new ArrayList<>();
		for (List<String[]> val_ : inSiddRuleMap.values()) {
			all_sidd_.addAll(val_);
		}
		int idx_ = 0;
		for (String[] sub_sidd_ : all_sidd_) {
			sub_sidd_[UdrFmt.S_28_RECORD_ID_29] = PubMethod.FmtArgs("%d", ++idx_);
		}

		for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i) {
			udr._uFields[i] = all_sidd_.get(0)[i];
		}
		if (all_sidd_.size() > 1) {
			udr._uList = new ArrayList<String[]>();
			for (int k = 1; k < all_sidd_.size(); ++k) {
				udr._uList.add(all_sidd_.get(k));
			}
		}
	}

	/**
	 * 重写Record_Id
	 * @param rule_id
	 */
	private void _rewriteRecordId(long rule_id) {
		String url = remoteUrl + "/api/v1/settle/storm/Rater/sidd/rewriteRecordId";

		RequestParam requestParam = new RequestParam();
		requestParam.setRule_id(rule_id);
		requestParam.setInSiddRuleMap(_inSiddRuleMap);
		requestParam.setInSiddSeqMap(_inSiddSeqMap);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = rewriteRecordId(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		_inSiddRuleMap = data.getInSiddRuleMap();
	}

	public BaseRspsMsg<ResponseMsg> rewriteRecordId(RequestParam requestParam) {
//		l.info("--------------- rewriteRecordId RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));
		long rule_id = requestParam.getRule_id();
		Map<String, List<String[]>> inSiddSeqMap = requestParam.getInSiddSeqMap();
		Map<Long, List<String[]>> inSiddRuleMap = requestParam.getInSiddRuleMap();

		List<String> key_list_ = new ArrayList<>();
		String key_prefix_ = PubMethod.FmtArgs("%d:", rule_id);
		for (Map.Entry<String, List<String[]>> ent_ : inSiddSeqMap.entrySet()) {
			if (ent_.getKey().startsWith(key_prefix_)) {
				key_list_.add(ent_.getKey());
			}
		}

		if (!key_list_.isEmpty()) {
			Collections.sort(key_list_); // TODO: buggy in case of 1 RULE has 10 or more RATEs
			int i = 0;
			List<String[]> in_sidd_list_ = new ArrayList<>();
			for (String key_ : key_list_) {
				List<String[]> sub_idd_list_ = inSiddSeqMap.get(key_);
				for (String[] sub_idd_ : sub_idd_list_) {
					sub_idd_[UdrFmt.S_28_RECORD_ID_29] = Integer.toString(++i);
					in_sidd_list_.add(sub_idd_);
				}
			}
			inSiddRuleMap.remove(rule_id);
			inSiddRuleMap.put(rule_id, in_sidd_list_);
		}

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setInSiddRuleMap(inSiddRuleMap);
		return BaseRspsMsg.ok(responseMsg);
	}

	private boolean _errCanBeSkipped(UdrFmt udr, EdbStlRateRec rate_rec, UdrErrInfo err_info) {
		String err_code_ = udr._eFields[UdrFmt.E_02_A03_ERR_CODE];
		if (EdbStlRateRec.RATE_TYPE_2_TARIFF == rate_rec._rateType) {
			if (UdrFmt.S624_NO_TARIFF_PARAMETER_CFG.equals(err_code_)
					|| UdrFmt.S625_TARIFF_PARAMETER_INEFFECTIVE.equals(err_code_)) {
				err_info.fromUdr(udr);
				return true;
			}
		} else if (EdbStlRateRec.RATE_TYPE_3_REPART == rate_rec._rateType) {
			if (UdrFmt.S634_NO_REPART_PARTITION_CFG.equals(err_code_) || UdrFmt.S635_NO_REPART_PARAMETER_CFG.equals(err_code_)
					|| UdrFmt.S636_REPART_PARAMETER_INEFFECTIVE.equals(err_code_)) {
				err_info.fromUdr(udr);
				return true;
			}
		}
		return false;
	}

	private void _filterRateList(UdrFmt udr, List<EdbStlRateRec> stl_rate_list) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/Rater/sidd/filterRateList";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setStl_rate_list(stl_rate_list);


		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = filterRateList(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
		}

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}
	}

	public BaseRspsMsg<ResponseMsg> filterRateList(RequestParam requestParam) throws Exception{
//		l.info("--------------- filterRateList RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		List<EdbStlRateRec> stl_rate_list = requestParam.getStl_rate_list();

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(), requestParam.getShpmCacheSize());*/

		ResponseMsg responseMsg = new ResponseMsg();

		if (stl_rate_list.size() <= 1) {
			responseMsg.setUdr(udr);
			return BaseRspsMsg.ok(responseMsg);
		}

		int rate_type_ = stl_rate_list.get(0)._rateType;
		if (EdbStlRateRec.RATE_TYPE_2_TARIFF != rate_type_ && EdbStlRateRec.RATE_TYPE_3_REPART != rate_type_){
			responseMsg.setUdr(udr);
			return BaseRspsMsg.ok(responseMsg);
		}

		FilterRate filterRate = new FilterRate();

		Set<Integer> tariff_type_set_ = new HashSet<>();
		List<Long> rate_id_list_ = new ArrayList<>();
		for (EdbStlRateRec rec_ : stl_rate_list)
			rate_id_list_.add(rec_._rateId);
		if (EdbStlRateRec.RATE_TYPE_2_TARIFF == rate_type_) {
			List<EdbStlTariffRateRec> tariff_rate_list_ = filterRate.seekTariffRates(udr, rate_id_list_);
			for (EdbStlTariffRateRec rec_ : tariff_rate_list_)
				tariff_type_set_.add(rec_._tariffType);
			if (tariff_type_set_.size() > 1) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S613_TARIFF_RATE_MULTI_TARIFF_TYPES;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%s,%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						PubMethod.Collection2Str(rate_id_list_, ":"), PubMethod.Collection2Str(tariff_type_set_, ":"));
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d %d TARIFF_RATEs (%s) more than one TARIFF_TYPEs (%s)", stl_rate_list.get(0)._ruleId,
						stl_rate_list.size(), PubMethod.Collection2Str(rate_id_list_, ":"),
						PubMethod.Collection2Str(tariff_type_set_, ":"));
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
		} else if (EdbStlRateRec.RATE_TYPE_3_REPART == rate_type_) {
			List<EdbStlRepartRateRec> repart_rate_list_ = filterRate.seekRepartRates(udr, rate_id_list_);
			for (EdbStlRepartRateRec rec_ : repart_rate_list_)
				tariff_type_set_.add(rec_._tariffType);
			if (tariff_type_set_.size() > 1) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S614_REPART_RATE_MULTI_TARIFF_TYPES;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%s,%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						PubMethod.Collection2Str(rate_id_list_, ":"), PubMethod.Collection2Str(tariff_type_set_, ":"));
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d %d REPART_RATEs (%s) more than one TARIFF_TYPEs (%s)", stl_rate_list.get(0)._ruleId,
						stl_rate_list.size(), PubMethod.Collection2Str(rate_id_list_, ":"),
						PubMethod.Collection2Str(tariff_type_set_, ":"));
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
		}

		responseMsg.setUdr(udr);
		return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
				responseMsg);
	}

	/**
	 *
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/14 15:18
	 */
	public static class FilterRate{

		private static final String _Q_STL_TARIFF_RATES = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM TARIFF_RATE "
				+ "WHERE RATE_ID IN (%s)";

		private static final String _Q_STL_REPART_RATES = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM REPART_RATE "
				+ "WHERE RATE_ID IN (%s)";

		public List<EdbStlTariffRateRec> seekTariffRates(UdrFmt udr, List<Long> rate_id_list) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			List<EdbStlTariffRateRec> result_ = new ArrayList<>();
			try {
				EdbStlTariffRateRec tariff_rate_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				String sql_ = PubMethod.FmtArgs(_Q_STL_TARIFF_RATES, PubMethod.Collection2Str(rate_id_list, ","));
				pstmt_ = conn_.prepareStatement(sql_);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					tariff_rate_ = ExtractStl.extractStlTariffRate(rs_);
					result_.add(tariff_rate_);
					break;
				}
				return result_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}


		public List<EdbStlRepartRateRec> seekRepartRates(UdrFmt udr, List<Long> rate_id_list) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			List<EdbStlRepartRateRec> result_ = new ArrayList<>();
			try {
				EdbStlRepartRateRec repart_rate_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				String sql_ = PubMethod.FmtArgs(_Q_STL_REPART_RATES, PubMethod.Collection2Str(rate_id_list, ","));
				pstmt_ = conn_.prepareStatement(sql_);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					repart_rate_ = ExtractStl.extractStlRepartRate(rs_);
					result_.add(repart_rate_);
					break;
				}
				return result_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	private void _mergeInSidd(UdrFmt udr) {
		List<String[]> all_sidd_ = new ArrayList<>();
		for (List<String[]> val_ : _inSiddRuleMap.values()) {
			all_sidd_.addAll(val_);
		}
//		if (CollectionUtil.isEmpty(all_sidd_)) {
//			return;
//		}
		int idx_ = 0;
		for (String[] sub_sidd_ : all_sidd_) {
			sub_sidd_[UdrFmt.S_28_RECORD_ID_29] = PubMethod.FmtArgs("%d", ++idx_);
		}

		for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i) {
			udr._uFields[i] = all_sidd_.get(0)[i];
		}
		if (all_sidd_.size() > 1) {
			udr._uList = new ArrayList<String[]>();
			for (int k = 1; k < all_sidd_.size(); ++k) {
				udr._uList.add(all_sidd_.get(k));
			}
		}
	}

	private void _ratingInSIDD(UdrFmt udr, EdbStlRateRec rate_rec, Long ruleId, int i) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/Rater/sidd/ratingInSIDD";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setRate_rec(rate_rec);
		requestParam.setIn_sidd(_inSiddRuleMap.get(ruleId).get(i));
		requestParam.setParamSiddMap(_paramSiddMap);


		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = ratingInSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		udr._eFields = data.getUdr()._eFields;
		udr._uFields = data.getUdr()._uFields;
		List<String[]> siddRuleList = _inSiddRuleMap.get(ruleId);
		siddRuleList.set(i, data.getIn_sidd());
		_inSiddRuleMap.put(ruleId, siddRuleList);
		_paramSiddMap = data.getParamSiddMap();
	}

	public BaseRspsMsg<ResponseMsg> ratingInSIDD(RequestParam requestParam) throws Exception{
//		l.info("--------------- ratingInSIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		EdbStlRateRec rate_rec = requestParam.getRate_rec();
		String[] in_sidd = requestParam.getIn_sidd();
		Map<Integer, List<String[]>> paramSiddMap = requestParam.getParamSiddMap();

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		ResponseMsg responseMsg = new ResponseMsg();


		udr._eFields[UdrFmt.E_33_X08_RATE_TYPE] = String.valueOf(rate_rec._rateType);
		switch (rate_rec._rateType) {
			case EdbStlRateRec.RATE_TYPE_1_FLAT:
				RaterFlatSIDD rater_flat_ = new RaterFlatSIDD();
				rater_flat_.prepare(rate_rec, in_sidd);
				rater_flat_.execUdr(logTag, udr);
				responseMsg.setIn_sidd(in_sidd);
				break;
			case EdbStlRateRec.RATE_TYPE_2_TARIFF:
				RaterTariffSIDD rater_tariff_ = new RaterTariffSIDD();
				rater_tariff_.prepare(rate_rec, in_sidd, paramSiddMap);
				rater_tariff_.execUdr(logTag, udr);
				break;
			case EdbStlRateRec.RATE_TYPE_3_REPART:
				RaterRepartSIDD rater_repart_ = new RaterRepartSIDD();
				rater_repart_.prepare(rate_rec, in_sidd, paramSiddMap);
				rater_repart_.execUdr(logTag, udr);
				break;
			case EdbStlRateRec.RATE_TYPE_4_SCRIPT:
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S610_RATE_TYPE_UKN;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						rate_rec._rateId, rate_rec._rateType);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %s RATE_ID %d RATE_TYPE %d unsupported yet",
						udr._uFields[UdrFmt.S_32_RULE_ID_33], rate_rec._rateId, rate_rec._rateType);
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				break;
			case EdbStlRateRec.RATE_TYEP_5_ESP:
				RaterEspSIDD rater_esp_ = new RaterEspSIDD();
				rater_esp_.prepare(rate_rec, in_sidd, paramSiddMap);
				rater_esp_.execUdr(logTag, udr);
				break;
			default:
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S610_RATE_TYPE_UKN;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						rate_rec._rateId, rate_rec._rateType);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %s RATE_ID %d RATE_TYPE %d ukn",
						udr._uFields[UdrFmt.S_32_RULE_ID_33], rate_rec._rateId, rate_rec._rateType);
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				break;
		}

		responseMsg.setUdr(udr);
		responseMsg.setParamSiddMap(paramSiddMap);
//		l.info("--------------- ratingInSIDD RequestParam responseMsg = {}", GsonUtil.toJsonString(responseMsg));
		return BaseRspsMsg.ok(responseMsg);
	}

	private void _ratingInSIDD(UdrFmt udr, EdbStlRateRec rate_rec, String[] in_sidd) {
		String url = remoteUrl + "/api/v1/settle/storm/Rater/sidd/ratingInSIDD";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setRate_rec(rate_rec);
		requestParam.setIn_sidd(in_sidd);
		requestParam.setParamSiddMap(_paramSiddMap);


		BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);

		ResponseMsg data = baseRspsMsg.getData();

		udr._eFields = data.getUdr()._eFields;
		udr._uFields = data.getUdr()._uFields;
		_paramSiddMap = data.getParamSiddMap();
	}

	private void _fillInObjectValues(UdrFmt udr, EdbStlOfferRec offer) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/Rater/sidd/fillInObjectValues";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setOffer(offer);
		requestParam.setInSiddRuleMap(_inSiddRuleMap);
		requestParam.setGuidingMap(_guidingMap);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = fillInObjectValues(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		_inSiddRuleMap = data.getInSiddRuleMap();
	}

	public BaseRspsMsg<ResponseMsg> fillInObjectValues(RequestParam requestParam) throws Exception{
//		l.info("--------------- fillInObjectValues RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		EdbStlOfferRec offer = requestParam.getOffer();
		String logTag = requestParam.getLogTag();
		Map<Long, List<String[]>> inSiddRuleMap = requestParam.getInSiddRuleMap();
		Map<String, Object> guidingMap = requestParam.getGuidingMap();
		ObjectValue objectValue = new ObjectValue();
		objectValue.setLogTag(logTag);

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setInSiddRuleMap(inSiddRuleMap);

		List<String[]> in_sidd_list_ = inSiddRuleMap.get(offer._ruleId);
		for (String[] in_sidd_ : in_sidd_list_) {
			String in_obj_key_ = String.format(UdrFmt.AUX_MAP_KEY_I_OBJECT, Long.parseLong(in_sidd_[UdrFmt.S_27_IN_OBJECT_28]));

//            EdbStlObjectRec in_object_rec_ = (EdbStlObjectRec) guidingMap.get(in_obj_key_);

			Object in_object_rec_object = guidingMap.get(in_obj_key_);
			String jsonString = GsonUtil.toJsonString(in_object_rec_object);
			EdbStlObjectRec in_object_rec_ = GsonUtil.fromJsonString(jsonString,
					new TypeToken<EdbStlObjectRec>(){}.getType());

			String in_object_val_ = objectValue.getObjectValue(udr, in_object_rec_);
			if (udr.isErr())
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			in_sidd_[UdrFmt.S_27_IN_OBJECT_28] = in_object_val_;
		}

		responseMsg.setUdr(udr);
		responseMsg.setInSiddRuleMap(inSiddRuleMap);
		return BaseRspsMsg.ok(responseMsg);
	}

	/**
	 * 初始化 STL_RATE_T 表信息
	 * 返回  _inSiddRuleMap 中对应offer.ruleId 的内容
	 * @param udr
	 * @param offer
	 * @return
	 * @throws Exception
	 */
	private List<EdbStlRateRec> _initInSIDDList(UdrFmt udr, EdbStlOfferRec offer) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/Rater/sidd/initInSIDDList";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setOffer(offer);
		requestParam.setGuidingMap(_guidingMap);
		requestParam.setInSiddRuleMap(_inSiddRuleMap);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = initInSIDDList(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		udr._uFields = data.getUdr()._uFields;
		_inSiddRuleMap = data.getInSiddRuleMap();
		List<EdbStlRateRec> stl_rate_list = data.getStl_rate_list();

		return stl_rate_list;
	}

	public BaseRspsMsg<ResponseMsg> initInSIDDList(RequestParam requestParam) throws Exception{
//		l.info("--------------- initInSIDDList RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		EdbStlOfferRec offer = requestParam.getOffer();
		Map<String, Object> guidingMap = requestParam.getGuidingMap();
		Map<Long, List<String[]>> inSiddRuleMap = requestParam.getInSiddRuleMap();

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		responseMsg.setInSiddRuleMap(inSiddRuleMap);

		udr._uFields[UdrFmt.S_32_RULE_ID_33] = offer._ruleId.toString();
		udr._uFields[UdrFmt.S_33_DEST_SOURCE_34] = offer._destSource;
		//l.info("^^^^^^^^^^^^^^offer._destSource:" + offer._destSource + "^^^^^^^^^^^^^^^^^^^^");
		udr._uFields[UdrFmt.S_42_ROUTE_CODE_43] = offer._routeCode;
		String o_obj_key_ = PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_O_OBJECT, offer._ruleId);
//        EdbStlObjectRec o_object_rec_ = (EdbStlObjectRec) guidingMap.get(o_obj_key_); //获取STL_OBJECT_T表相关信息

		Object o_object_rec_object = guidingMap.get(o_obj_key_);
		String jsonString = GsonUtil.toJsonString(o_object_rec_object);
		EdbStlObjectRec o_object_rec_ = GsonUtil.fromJsonString(jsonString,
				new TypeToken<EdbStlObjectRec>() {}.getType());

		ObjectValue objectValue = new ObjectValue();
		objectValue.setLogTag(logTag);
		String o_object_val_ = objectValue.getObjectValue(udr, o_object_rec_);

		if (udr.isErr()) {
			responseMsg.setStl_rate_list(Collections.<EdbStlRateRec>emptyList());
			return BaseRspsMsg.ok(responseMsg);
		}
		List<String[]> in_sidd_list_ = new ArrayList<String[]>();
		int rec_id_ = 0;
		String rate_key_ = PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RATE, offer._ruleId);
		@SuppressWarnings("unchecked")
//        List<EdbStlRateRec> stl_rate_list_ = (List<EdbStlRateRec>) guidingMap.get(rate_key_); //获取 STL_RATE_T表相关信息

		Object stl_rate_list_object = guidingMap.get(rate_key_);
		String stl_rate_list_object_json = GsonUtil.toJsonString(stl_rate_list_object);
		List<EdbStlRateRec> stl_rate_list_ = GsonUtil.fromJsonString(stl_rate_list_object_json,
				new TypeToken<List<EdbStlRateRec>>() {}.getType());

		for (EdbStlRateRec stl_rate_ : stl_rate_list_) {
			String[] in_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
			for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i) {
				in_sidd_[i] = udr._uFields[i];
			}
			in_sidd_[UdrFmt.S_28_RECORD_ID_29] = Integer.toString(++rec_id_);
			if (!udr.isFeedback() || PubMethod.IsEmpty(in_sidd_[UdrFmt.S_26_OUT_OBJECT_27])) {
				in_sidd_[UdrFmt.S_26_OUT_OBJECT_27] = o_object_val_;
			}
			in_sidd_[UdrFmt.S_27_IN_OBJECT_28] = Long.toString(stl_rate_._inObjectId);
			in_sidd_list_.add(in_sidd_);
		}
		inSiddRuleMap.put(offer._ruleId, in_sidd_list_); //

		responseMsg.setStl_rate_list(stl_rate_list_);
		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		responseMsg.setInSiddRuleMap(inSiddRuleMap);

		return BaseRspsMsg.ok(responseMsg);
	}

	/**
	 *
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/13 17:58
	 */
	public static class ObjectValue{

		private static final String _Q_STL_PAY_PROV = "SELECT SVC_INST_ID, CHARGE_ITEM, OBJECT_VALUE, EFF_DATE, EXP_DATE "
				+ "FROM PAY_PROV WHERE SVC_INST_ID = ? AND CHARGE_ITEM = ? ORDER BY EFF_DATE DESC";

		private String logTag;
		private Logger log;

		public ObjectValue() {
			log = LoggerFactory.getLogger(this.getClass());
		}
		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		/**
		 * 返回ObjectValue内容
		 * @param udr
		 * @param object_rec
		 * @return
		 * @throws Exception
		 */
		public String getObjectValue(UdrFmt udr, EdbStlObjectRec object_rec) throws Exception {
			String result_ = "";
			switch (object_rec._objectType) {
				case EdbStlObjectRec.OBJECT_TYPE_1_FIX:
					if (object_rec._objectValue != null)
						result_ = object_rec._objectValue;
					break;
				case EdbStlObjectRec.OBJECT_TYPE_2_DEF:
					if (EdbStlObjectRec.OBJECT_OWNER_MEM.equals(object_rec._ownerName)
							|| EdbStlObjectRec.OBJECT_OWNER_ORDER.equals(object_rec._ownerName)) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S601_OBJECT_OWNER_UKN;
						udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d,%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
								object_rec._objectId, object_rec._objectType, object_rec._ownerName);
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
								"RULE %s OBJECT_ID %d OBJECT_TYPE %d OWNER_NAME %s unsupported yet", udr._uFields[UdrFmt.S_32_RULE_ID_33],
								object_rec._objectId, object_rec._objectType, object_rec._ownerName);
						log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
								udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					} else if (EdbStlObjectRec.OBJECT_OWNER_RULE.equals(object_rec._ownerName)
							|| EdbStlObjectRec.OBJECT_OWNER_REPART.equals(object_rec._ownerName)
							|| EdbStlObjectRec.OBJECT_OWNER_ESP.equals(object_rec._ownerName)) {
						result_ = PubMethod.FmtArgs("<%d>", object_rec._objectId); // should be overwritten later
					} else if (EdbStlObjectRec.OBJECT_OWNER_IDD.equals(object_rec._ownerName)) {
						Integer idx_ = UdrDef.S_NM2IDX_MAP.get(object_rec._fieldName == null ? "" : object_rec._fieldName);
						if (idx_ == null) {
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S602_OBJECT_FIELD_UKN;
							udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d,%s,%s",
									udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId, object_rec._objectType,
									object_rec._ownerName, object_rec._fieldName);
							udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
									"RULE %s OBJECT_ID %d OBJECT_TYPE %d OWNER_NAME %s FIELD_NAME %s invalid",
									udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId, object_rec._objectType,
									object_rec._ownerName, object_rec._fieldName);
							log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
									udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
						} else {
							if (PubMethod.IsEmpty(udr._uFields[idx_])) {
								udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S607_OBJECT_IDD_FIELD_EMPTY;
								udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d,%s,%s",
										udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId, object_rec._objectType,
										object_rec._ownerName, object_rec._fieldName);
								udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
										"RULE %s OBJECT_ID %d OBJECT_TYPE %d OWNER_NAME %s FIELD_NAME %s IDX(%d) field is empty",
										udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId, object_rec._objectType,
										object_rec._ownerName, object_rec._fieldName, idx_);
								log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
										udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
							} else {
								result_ = udr._uFields[idx_];
							}
						}
					} else if (EdbStlObjectRec.OBJECT_OWNER_PAY.equals(object_rec._ownerName)) {
						EdbStlPayProvRec pay_prov_ = _seekPayProv(udr, object_rec);
						if (pay_prov_ != null) {
							result_ = pay_prov_._objectValue;
						}
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S601_OBJECT_OWNER_UKN;
						udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d,%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
								object_rec._objectId, object_rec._objectType, object_rec._ownerName);
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
								"RULE %s OBJECT_ID %d OBJECT_TYPE %d OWNER_NAME %s ukn", udr._uFields[UdrFmt.S_32_RULE_ID_33],
								object_rec._objectId, object_rec._objectType, object_rec._ownerName);
						log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
								udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					}
					break;
				case EdbStlObjectRec.OBJECT_TYPE_3_GRP:
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S600_OBJECT_TYPE_UKN;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d", udr._uFields[UdrFmt.S_32_RULE_ID_33],
							object_rec._objectId, object_rec._objectType);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %s OBJECT_ID %d OBJECT_TYPE %d unsupported yet",
							udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId, object_rec._objectType);
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					break;
				default:
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S600_OBJECT_TYPE_UKN;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d", udr._uFields[UdrFmt.S_32_RULE_ID_33],
							object_rec._objectId, object_rec._objectType);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %s OBJECT_ID %d OBJECT_TYPE %d ukn",
							udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId, object_rec._objectType);
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					break;
			}
			return result_;
		}

		/***
		 * DAO
		 * @author: zhanglei
		 * @date: 2022/1/13 17:52
		 * @param udr
		 * @param object_rec
		 * @return: com.hp.cmcc.bboss.pub.edb.entity.EdbStlPayProvRec
		 * @exception:
		 * @update:
		 * @updatePerson:
		 */
		private EdbStlPayProvRec _seekPayProv(UdrFmt udr, EdbStlObjectRec object_rec) throws Exception {
			// String rule_key_ = PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RULE, Long.parseLong(udr._uFields[UdrFmt.S_32_RULE_ID_33]));
			// EdbStlRuleRec rule_rec_ = (EdbStlRuleRec) _guidingMap.get(rule_key_);
			if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11])) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S603_OBJECT_PAY_PROV_PRODUCT_ORDER_ID_EMPTY;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s,%d,%d,%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						object_rec._objectId, object_rec._objectType, object_rec._ownerName);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %s OBJECT_ID %d OBJECT_TYPE %d OWNER_NAME %s but PRODUCT_ORDER_ID_11 is empty can not seek PAY_PROV",
						udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId, object_rec._objectType, object_rec._ownerName);
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return null;
			}

			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				EdbStlPayProvRec pay_prov_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_PAY_PROV);
				pstmt_.setString(1, udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11]);
				pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				rs_ = pstmt_.executeQuery();
				boolean exists_ = false;
				while (rs_.next()) {
					pay_prov_ = ExtractStl.extractStlPayProv(rs_);
					exists_ = true;
					if (!pay_prov_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
						log.trace("{} START_TIME_36 {} ineffective, skip PAY_PROV {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
								pay_prov_.toGsonStr());
						pay_prov_ = null;
						continue;
					} else {
						break;
					}
				}

				if (pay_prov_ == null && !"-1".equals(udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18])) {
					pstmt_.setString(2, "-1");
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						pay_prov_ = ExtractStl.extractStlPayProv(rs_);
						exists_ = true;
						if (!pay_prov_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
							log.trace("{} START_TIME_36 {} ineffective, skip PAY_PROV {}", logTag,
									udr._uFields[UdrFmt.S_35_START_TIME_36], pay_prov_.toGsonStr());
							pay_prov_ = null;
							continue;
						} else {
							break;
						}
					}
				}

				if (pay_prov_ != null) {
					log.trace("{} RULE {} OBJECT {} PAY_PROV located, {}", logTag, udr._uFields[UdrFmt.S_32_RULE_ID_33],
							object_rec._objectId, pay_prov_.toGsonStr());
				} else {
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%d:%s:%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
							object_rec._objectId, udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
							udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
					if (exists_) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S605_OBJECT_PAY_PROV_INEFFECTIVE;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
								"RULE %s OBJECT_ID %d PRODUCT_ORDER_ID_11 %s CHARGE_ITEM_18 %s PAY_PROV cfg none effective",
								udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId,
								udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11], udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S604_OBJECT_PAY_PROV_NO_CFG;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
								"RULE %s OBJECT_ID %d PRODUCT_ORDER_ID_11 %s CHARGE_ITEM_18 %s PAY_PROV no cfg",
								udr._uFields[UdrFmt.S_32_RULE_ID_33], object_rec._objectId,
								udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11], udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
					}
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				}
				return pay_prov_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	/**
	 * 从UdrFmt中将aux内容提取到_guidingMap变量中，置空原位置信息，并查验amount
	 * @param udr
	 */
	private void _prepare(UdrFmt udr) {
		_guidingMap = udr._auxMap;
		udr._auxMap = null;
		_inSiddRuleMap.clear();
		_inSiddSeqMap.clear();
		_paramSiddMap.clear();
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_19_AMOUNT_NOTAX_20]))
			udr._uFields[UdrFmt.S_19_AMOUNT_NOTAX_20] = "0";
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_20_AMOUNT_TAX_21]))
			udr._uFields[UdrFmt.S_20_AMOUNT_TAX_21] = "0";
	}

}
