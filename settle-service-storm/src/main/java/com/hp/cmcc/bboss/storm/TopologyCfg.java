package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbAppParam;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.regex.Pattern;

public class TopologyCfg {
    private static Logger L = LoggerFactory.getLogger(TopologyCfg.class);
    private static TopologyCfg _Instance;

    public static final String OUTPUT_FIELD_DFT = "DFT";
    public static final String STREAM_ID_NML = "NML";
    public static final String STREAM_ID_ERO = "ERO";
    public static final Pattern _ExpIddFieldName = Pattern.compile("^\\$\\{[A-Z0-9_]+\\}$");

    public String _kafkaTopicRaw;
    public String _kafkaTopicNml;
    public String _kafkaTopicErr;
    public String _shpmBaseDir;
    public String _shpmStormBaseDir;
    public int _shpmCacheSize = -16000;
    public String _bizlogFlags;
    public int _paralValidation = 1;
    public int _paralChkdup = 1;
    public int _paralGuiding = 1;
    public int _paralRating = 1;
    public int _paralErhndl = 1;
    public int _workerNum = 1;
    public int _failTimeout = 60;
    public int _udrExpireDays = 100;
    public int _udrAheadMinutes = 120;
    public int _udrDurationMax = 259200;
    public int _spoutDupchkExpire = 3600;
    private boolean _initOk;

    public int validationGroup = 1;
    public int chkdupGroup = 1;
    public int guidingGroup = 1;
    public int ratingGroup = 1;
    public int erhndlGroup = 1;

    public String validationImpl = "Def"; //Def 默认 ValidatorSIDD， New ValidatorSIDDNew，Ref ValidatorSIDDRef
    public String guidingImpl = "Def";
    public String ratingImpl = "Def";

    public String redisMode = "sentinel";

    static {
        _Instance = new TopologyCfg();
    }

    public static TopologyCfg GetInstance() {
        return _Instance;
    }

    public boolean isInitOk() {
        return _initOk;
    }

    public boolean init() {
        OdbAppParam app_param_ = new OdbAppParam();
        String module_ = OdbAgt.GetBizInstance().getLoginKey();
        String inst_nm_ = "topology";
        boolean rc_ = app_param_.refresh(module_, inst_nm_);
        if (!rc_) {
            L.warn("init app_param for inst_nm [{}] error", inst_nm_);
            return false;
        }

        rc_ = app_param_.subValByEnv();
        if (!rc_) {
            L.warn("app_param for inst_nm [{}] subValByEnv() error", inst_nm_);
            return false;
        }

        String sval_ = app_param_.chkValStr("COMMON", "KAFKA_TOPIC_RAW", null);
        if (sval_ == null) {
            rc_ = false;
        } else {
            _kafkaTopicRaw = sval_;
            //测试使用
            //_kafkaTopicRaw = "udr_raw_new";
        }

        sval_ = app_param_.chkValStr("COMMON", "KAFKA_TOPIC_NML", null);
        if (sval_ == null) {
            rc_ = false;
        } else {
            _kafkaTopicNml = sval_;
        }

        sval_ = app_param_.chkValStr("COMMON", "KAFKA_TOPIC_ERR", null);
        if (sval_ == null) {
            rc_ = false;
        } else {
            _kafkaTopicErr = sval_;
        }

        sval_ = app_param_.chkValStr("COMMON", "BIZLOG_FLAGS", "^[A-Z]+$");
        if (sval_ == null) {
            rc_ = false;
        } else {
            _bizlogFlags = sval_;
        }

        Long lval_ = app_param_.chkValNum("COMMON", "PARAL_VALIDATION", 1L, 999999L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _paralValidation = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "PARAL_CHKDUP", 1L, 999999L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _paralChkdup = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "PARAL_GUIDING", 1L, 999999L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _paralGuiding = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "PARAL_RATING", 1L, 999999L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _paralRating = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "PARAL_ERHNDL", 1L, 999999L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _paralErhndl = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "FAIL_TIMEOUT", 15L, 8640000L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _failTimeout = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "WORKER_NUM", 1L, 999L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _workerNum = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "UDR_EXPIRE_DAYS", 1L, 3660L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _udrExpireDays = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "UDR_AHEAD_MINUTES", 1L, 4320L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _udrAheadMinutes = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "UDR_DURATION_MAX", 0L, 2147483647L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _udrDurationMax = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "SPOUT_DUPCHK_EXPIRE", 60L, 86400L);
        if (lval_ == null) {
            rc_ = false;
        } else {
            _spoutDupchkExpire = lval_.intValue();
        }

/*        File fval_ = app_param_.chkValFile("COMMON", "SHPM_BASE_DIR", true, true, true);
        if (fval_ == null) {
            rc_ = false;
        } else {
            _shpmBaseDir = fval_.getAbsolutePath();
            //修改物理机地址，原来在容器
//			_shpmBaseDir = "/settle-sett/var/shpm";
        }*/
        File fval_ = app_param_.chkValFile("COMMON", "STORM_SHPM_BASE_DIR", true, true, true);
        if (fval_ == null) {
            rc_ = false;
        } else {
            _shpmStormBaseDir = fval_.getAbsolutePath();
        }
        lval_ = app_param_.chkValNum("COMMON", "SHPM_CACHE_SIZE", null, null);
        if (lval_ == null) {
            rc_ = false;
        } else if (lval_ > -2000 && lval_ < 2000) {
            L.warn("SHPM_CACHE_SIZE {} can not between (-2000,2000), pls chk", lval_);
            rc_ = false;
        } else {
            _shpmCacheSize = lval_.intValue();
        }

        lval_ = app_param_.chkValNum("COMMON", "VALIDATION_GROUP", null,null);
        if (lval_ != null) {
            validationGroup = lval_.intValue();;
        }
        lval_ = app_param_.chkValNum("COMMON", "CHKDUP_GROUP", null,null);
        if (lval_ != null) {
            chkdupGroup = lval_.intValue();;
        }
        lval_ = app_param_.chkValNum("COMMON", "GUIDING_GROUP", null,null);
        if (lval_ != null) {
            guidingGroup = lval_.intValue();;
        }
        lval_ = app_param_.chkValNum("COMMON", "RATING_GROUP", null,null);
        if (lval_ != null) {
            ratingGroup = lval_.intValue();;
        }
        lval_ = app_param_.chkValNum("COMMON", "ERHNDL_GROUP", null,null);
        if (lval_ != null) {
            erhndlGroup = lval_.intValue();;
        }
        if (rc_) {
            if (_paralValidation < _workerNum || _paralValidation % _workerNum != 0) {
                L.warn("PARAL_VALIDATION {} is not divisible by WORKER_NUM {}, pls chk", _paralValidation, _workerNum);
                rc_ = false;
            }
            if (_paralChkdup < _workerNum || _paralChkdup % _workerNum != 0) {
                L.warn("PARAL_CHKDUP {} is not divisible by WORKER_NUM {}, pls chk", _paralChkdup, _workerNum);
                rc_ = false;
            }
            if (_paralGuiding < _workerNum || _paralGuiding % _workerNum != 0) {
                L.warn("PARAL_GUIDING {} is not divisible by WORKER_NUM {}, pls chk", _paralGuiding, _workerNum);
                rc_ = false;
            }
            if (_paralRating < _workerNum || _paralRating % _workerNum != 0) {
                L.warn("PARAL_RATING {} is not divisible by WORKER_NUM {}, pls chk", _paralRating, _workerNum);
                rc_ = false;
            }
            if (_paralErhndl < _workerNum || _paralErhndl % _workerNum != 0) {
                L.warn("PARAL_ERHNDL {} is not divisible by WORKER_NUM {}, pls chk", _paralErhndl, _workerNum);
                rc_ = false;
            }
        }

        if (rc_) {
            _initOk = true;
        }

        sval_ = app_param_.chkValStr("COMMON", "VALIDATION_IMPL", null);
        if (sval_ != null) {
            validationImpl = sval_;
        }
        sval_ = app_param_.chkValStr("COMMON", "GUIDING_IMPL", null);
        if (sval_ != null) {
            guidingImpl = sval_;
        }
        sval_ = app_param_.chkValStr("COMMON", "RATING_IMPL", null);
        if (sval_ != null) {
            ratingImpl = sval_;
        }
        redisMode = OdbSystemParam.GetInstance().redisMode;
        return rc_;
    }

    private TopologyCfg() {
        _initOk = false;
    }

    public boolean redisClusterMode() {
        return redisMode.equals("cluster");
    }
}
