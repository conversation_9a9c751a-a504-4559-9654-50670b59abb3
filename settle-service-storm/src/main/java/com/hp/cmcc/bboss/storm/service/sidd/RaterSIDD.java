package com.hp.cmcc.bboss.storm.service.sidd;

import java.math.BigDecimal;
import java.util.regex.Pattern;

public final class RaterSIDD {
   public static final BigDecimal POS_ONE = new BigDecimal(1);
    static final BigDecimal NEG_ONE = new BigDecimal(-1);
    static final BigDecimal ONE_HUNDRED = new BigDecimal(100);
    static final Pattern ExpIddFieldName = Pattern.compile("^\\$\\{[A-Z0-9_]+\\}$");
}
