package com.hp.cmcc.bboss.storm.service.sidd;

import cn.hutool.json.JSONUtil;
import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRepartParameterRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRepartPartitionRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRepartRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlTariffRateRec;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbCli;
import com.hp.cmcc.bboss.pub.odb.entity.DbStlRepartParameterRec;
import com.hp.cmcc.bboss.pub.udr.UdrDef;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import com.hp.cmcc.bboss.storm.util.ExtractStl;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RaterRepartSIDD extends FunctorIDD {
	private static final String _Q_STL_REPART_RATE = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM REPART_RATE "
			+ "WHERE RATE_ID = ?";
	private static final String _Q_STL_REPART_PARTITION = "SELECT RATE_ID, ACCT_MONTH, RULE_ID FROM REPART_PARTITION "
			+ "WHERE RATE_ID = ? AND ACCT_MONTH = ? AND RULE_ID = ?";
	private static final String _Q_STL_REPART_YYYYMM_PROD_OFFER = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, OBJECT_VALUE, TARIFF_TYPE, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE FROM REPART_%s WHERE PROD_INST_ID = ? "
			+ "AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? "
			+ "ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
	private static final String _Q_STL_REPART_YYYYMM_PROD_SVC = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, OBJECT_VALUE, TARIFF_TYPE, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE FROM REPART_%s WHERE SVC_INST_ID = ? "
			+ "AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? "
			+ "AND SVC_INST_ID IS NOT NULL ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
	private static final String _Q_STL_ODB_REPART_PROD_OFFER = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, OBJECT_VALUE, TARIFF_TYPE, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE, ACCT_MONTH FROM STL_REPART_PARAMETER_T "
			+ "WHERE ACCT_MONTH = ? AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? AND PROD_INST_ID = ? "
			+ "AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
	private static final String _Q_STL_ODB_REPART_PROD_SVC = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, CALC_PRIORITY, OBJECT_VALUE, TARIFF_TYPE, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE, ACCT_MONTH FROM STL_REPART_PARAMETER_T "
			+ "WHERE ACCT_MONTH = ? AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? AND SVC_INST_ID IS NOT NULL "
			+ "AND SVC_INST_ID = ? AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
	private EdbStlRateRec _rateRec;
	private String[] _inSidd;
	private Map<Integer, List<String[]>> _inSiddMap;

	public RaterRepartSIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
	}

	public void prepare(EdbStlRateRec rate_rec, String[] in_sidd, Map<Integer, List<String[]>> in_sidd_map) {
		_rateRec = rate_rec;
		_inSidd = in_sidd;
		_inSiddMap = in_sidd_map;
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		EdbStlRepartRateRec repart_rate_ = _seekRepartRate(udr, _rateRec);
		if (repart_rate_ == null || udr.isErr())
			return;

		List<EdbStlRepartParameterRec> parameter_list_ = null;
		EdbStlRepartPartitionRec repart_partition_ = _seekRepartPartition(udr, _rateRec);
		if (udr.isErr())
			return;

		if (repart_partition_ == null) {
			parameter_list_ = _seekRepartParameterOdb(udr, repart_rate_);
		} else {
			parameter_list_ = _seekRepartParameter(udr, repart_rate_);
		}

		l.debug("商品编码:{},商品订购:{},费项:{},匹配到的repart规则信息:{}",
				udr._uFields[UdrFmt.S_07_OFFER_CODE_08],
				udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10],
				udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18],
				JSONUtil.toJsonStr(parameter_list_));
		if (parameter_list_ == null || parameter_list_.isEmpty() || udr.isErr())
			return;

		String[] split_idd_ = null;
		for (EdbStlRepartParameterRec parameter_rec_ : parameter_list_) {
			if (split_idd_ != null) {
				split_idd_ = _ratingInRepartParameterSIDD(udr, parameter_rec_, split_idd_);
			} else {
				split_idd_ = _ratingInRepartParameterSIDD(udr, parameter_rec_, _inSidd);
			}
			if (udr.isErr())
				break;
		}
		l.debug("商品编码:{},商品订购:{},费项:{},repart批价结果:{}",
				udr._uFields[UdrFmt.S_07_OFFER_CODE_08],
				udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10],
				udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18],
				JSONUtil.toJsonStr(_inSiddMap));
	}

	/**
	 * 
	 * @param udr
	 * @param parameter_rec
	 * @param in_sidd
	 * @return
	 */
	private String[] _ratingInRepartParameterSIDD(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String[] in_sidd) {
		String[] parameter_sidd_ = _initParameterSIDD(udr, parameter_rec, in_sidd);
		l.debug("补充后账单结果:{} ,内容:{}", udr.isErr(), JSONUtil.toJsonStr(parameter_sidd_));
		if (udr.isErr())
			return null;

		long amount_total_ = 0;
		String[] split_idd_ = null;
		udr._eFields[UdrFmt.E_34_X09_TARIFF_TYPE] = String.valueOf(parameter_rec._tariffType);
		
		if (EdbStlTariffRateRec.TARIFF_TYPE_1_PROPORTION == parameter_rec._tariffType) {
			_ratingInRepartProportion(udr, parameter_rec, parameter_sidd_);
		} else if (EdbStlTariffRateRec.TARIFF_TYPE_2_FIXEDVALUE == parameter_rec._tariffType) {
			if (PubMethod.IsEmpty(udr._eFields[UdrFmt.E_23_E09_SPARE1])) {
				amount_total_ = Long.parseLong(in_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
				udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_);
			} else {
				amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
			}
			if (amount_total_ > 0) {
				if ("$".equals(parameter_rec._rateValue)) {
					_ratingInRepartFixedDollar(udr, parameter_rec, parameter_sidd_);
				} else {
					split_idd_ = _ratingInRepartFixedValue(udr, parameter_rec, parameter_sidd_);
				}
			}
		} else if (EdbStlTariffRateRec.TARIFF_TYPE_3_FIXEDVALUE == parameter_rec._tariffType) {
			if (PubMethod.IsEmpty(udr._eFields[UdrFmt.E_23_E09_SPARE1])) {
				amount_total_ = Long.parseLong(in_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
				udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_);
			} else {
				amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
			}
			
			if ("$".equals(parameter_rec._rateValue)) {
				if ( amount_total_ > 0 ) {
					_ratingInRepartFixedDollar(udr, parameter_rec, parameter_sidd_);
				}
			} else {
				split_idd_ = _ratingInRepartOtherFixedValue(udr, parameter_rec, parameter_sidd_);
			}
			
//			if ( "14080601".equals(udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10]) && "201712".equals(udr._uFields[UdrFmt.S_22_ORG_MONTH_23]) ) {
//				l.info("*^*^PARAMETER_ID={}|S_26_OUT_OBJECT_27={}|S_27_IN_OBJECT_28={}|S_22_ORG_MONTH_23={}|S_18_CHARGE_19={}|S_29_SETTLE_NOTAX_30={} ***", 
//						parameter_rec._id,
//						parameter_sidd_[UdrFmt.S_26_OUT_OBJECT_27], 
//						parameter_sidd_[UdrFmt.S_27_IN_OBJECT_28], 
//						parameter_sidd_[UdrFmt.S_22_ORG_MONTH_23], 
//						parameter_sidd_[UdrFmt.S_18_CHARGE_19], 
//						parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] );
//			}
		} else {
			String alm_ = PubMethod.FmtArgs("absurd!!! REPART_PARAMETER %d ukn TARIFF_TYPE %d", parameter_rec._id,
					parameter_rec._tariffType);
			l.warn("{} {}", _logTag, alm_);
			throw new RuntimeException(alm_);
		}

		if (udr.isErr())
			return null;
		if (split_idd_ != null && split_idd_ == parameter_sidd_) {
			l.trace("{} repart_parameter id:type:val:tr:tot {}:{}:{}:{}:{} no RESIDUE, skip", _logTag, parameter_rec._id,
					parameter_rec._tariffType, parameter_rec._rateValue, parameter_sidd_[UdrFmt.S_21_TAX_RATE_22], amount_total_);
		} else {
			l.debug("{} repart_parameter id:type:val:tr:tot {}:{}:{}:{}:{} notax:tax amount {}:{} settle {}:{}", _logTag,
					parameter_rec._id, parameter_rec._tariffType, parameter_rec._rateValue,
					parameter_sidd_[UdrFmt.S_21_TAX_RATE_22], amount_total_, parameter_sidd_[UdrFmt.S_19_AMOUNT_NOTAX_20],
					parameter_sidd_[UdrFmt.S_20_AMOUNT_TAX_21], parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30],
					parameter_sidd_[UdrFmt.S_30_SETTLE_TAX_31]);
			List<String[]> val_ = _inSiddMap.get(Integer.parseInt(in_sidd[UdrFmt.S_28_RECORD_ID_29]));
			if (EdbStlTariffRateRec.TARIFF_TYPE_2_FIXEDVALUE == parameter_rec._tariffType && amount_total_ <= 0
					&& !val_.isEmpty()) {
				l.trace("{} repart_parameter id {}, amount_total_ is {}, val_.size() is {}, skip add", _logTag, parameter_rec._id,
						amount_total_, val_.size());
//			} else if ( EdbStlTariffRateRec.TARIFF_TYPE_3_FIXEDVALUE == parameter_rec._tariffType && Long.parseLong(parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30]) <= 0 ) {
//				l.trace("{} repart_parameter id {}, amount_total_ is {}, SETTLE_NOTAX is {}, val_.size() is {}, skip add", 
//						_logTag, 
//						parameter_rec._id,
//						amount_total_, 
//						parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30],
//						val_.size());
			} else {
				val_.add(parameter_sidd_);
			}
		}
		return split_idd_;
	}

	/**
	 * 按照结算规则，根据结算单，初始化一条结算数据数组
	 * @param udr
	 * @param parameter_rec
	 * @param in_sidd
	 * @return
	 */
	private String[] _initParameterSIDD(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String[] in_sidd) {
		String[] parameter_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
		for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
			parameter_sidd_[i] = in_sidd[i];

		String tmp_ = _parseParameterValue(udr, parameter_rec, parameter_rec._objectValue, parameter_sidd_);
		if (tmp_ == null || udr.isErr())
			return null;
		parameter_sidd_[UdrFmt.S_27_IN_OBJECT_28] = tmp_;

		tmp_ = _parseParameterValue(udr, parameter_rec, parameter_rec._destSource, parameter_sidd_);
		if (tmp_ == null || udr.isErr())
			return null;
		if (!"-1".equals(tmp_))
			parameter_sidd_[UdrFmt.S_33_DEST_SOURCE_34] = tmp_;

		int key_ = Integer.parseInt(in_sidd[UdrFmt.S_28_RECORD_ID_29]);
		List<String[]> val_ = _inSiddMap.get(key_);
		if (val_ == null) {
			val_ = new ArrayList<String[]>();
			_inSiddMap.put(key_, val_);
		}
		parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
		parameter_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
		parameter_sidd_[UdrFmt.S_38_RES3_39] = parameter_rec._routeFlag;
		return parameter_sidd_;
	}

	/**
	 * 第一种固定值结算
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 * @return
	 */
	private String[] _ratingInRepartFixedValue(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String[] parameter_sidd) {
		String[] split_sidd_ = null;
		long amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
		if (amount_total_ <= 0) {
			l.debug("{} {} amount_total_ {} le 0, no more process", _logTag, parameter_rec._id, amount_total_);
			return null;
		}
		MdbCli cli_ = null;
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		Transaction tx_ = null;
		MdbCli2 mdbCli2 = null;
		try {
			String hkey_ = PubMethod.FmtArgs("RF:%s:%s:%d:%d:%s:%d", // RF : 'Repart Fixed'
					udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26].substring(0), udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._chargeItem, parameter_rec._id);
			if (TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2 = MdbAgt2.GetShparmInstance();
				jedis_ = mdbCli2.getJedis(hkey_);
			}else {
				cli_ = MdbAgt.GetShparmInstance();
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
			}
//			String hkey_ = PubMethod.FmtArgs("RF:%s:%s:%d:%d:%s:%d", // RF : 'Repart Fixed'
//					udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6), udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
//					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._chargeItem, parameter_rec._id);
			String init_val_ = PubMethod.FmtArgs("%s|%s|%s|%s|%s", PubMethod._JvmHost,
					PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23),
					udr._eFields[UdrFmt.E_04_A05_FILE_ID], udr._eFields[UdrFmt.E_05_A06_LINE_NUM], parameter_rec._rateValue);
			long cnt_ = jedis_.hsetnx(hkey_, "INIT", init_val_);
			if (cnt_ > 0)
				l.trace("{} hsetnx {} INIT {}, cnt_ = {}", _logTag, hkey_, init_val_, cnt_);
			cnt_ = jedis_.hsetnx(hkey_, "RESIDUE", parameter_rec._rateValue);
			if (cnt_ > 0)
				l.trace("{} hsetnx {} RESIDUE {}, cnt_ = {}", _logTag, hkey_, parameter_rec._rateValue, cnt_);

			while (true) {
				jedis_.watch(hkey_);
				String residue_ = jedis_.hget(hkey_, "RESIDUE");
				long residual_ = Long.parseLong(residue_);
				if (residual_ <= 0) {
					l.debug("{} {} residue_ {} le 0, try match next cfg", _logTag, parameter_rec._id, residual_);
					split_sidd_ = parameter_sidd;
					break;
				} else {
					long deduct_ = Math.min(residual_, amount_total_);
					tx_ = jedis_.multi();
					tx_.hset(hkey_,
							PubMethod.FmtArgs("%s|%s|%s|%s", udr._eFields[UdrFmt.E_04_A05_FILE_ID],
									udr._eFields[UdrFmt.E_05_A06_LINE_NUM], PubMethod._JvmHost,
									PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23)),
							PubMethod.FmtArgs("%d|%d|%d|%d", residual_, amount_total_, deduct_, residual_ - deduct_));
					tx_.hincrBy(hkey_, "RESIDUE", -deduct_);
					List<Object> tx_echo_list_ = tx_.exec();
					if (tx_echo_list_ == null) {
						jedis_.unwatch();
						l.info("{} {} residual_:amount_total_:deduct_ {}:{}:{}, exec canceled", _logTag, parameter_rec._id,
								residual_, amount_total_, deduct_);
						PubMethod.Sleep(1);
						continue;
					}
					l.trace("{} {} tx_echo_list_ {}", _logTag, parameter_rec._id, PubMethod.Collection2Str(tx_echo_list_, ","));
					udr._eFields[UdrFmt.E_14_A15_ACUMLT] = Long.toString(deduct_);

					parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString(deduct_);
					parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_ - deduct_);

					split_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
					for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
						split_sidd_[i] = parameter_sidd[i];
					split_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
					split_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					break;
				}
			}
		} catch (Exception e) {
			if (jedis_ != null)
				jedis_.unwatch();
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnBrokenResource(tx_);
			} else if (cli_ != null) {
				cli_.returnBrokenResource(tx_, pool_, jedis_);
			}
			throw e;
		} finally {
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnResource(jedis_);
			} else if (cli_ != null) {
				cli_.returnResource(pool_, jedis_);
			}
		}
		return split_sidd_;
	}
	
	/**
	 * 第二种固定值结算
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 * @return
	 */
	private String[] _ratingInRepartOtherFixedValue(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String[] parameter_sidd) {
		String[] split_sidd_ = null;
		long amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
		
		MdbCli cli_ = null;
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		Transaction tx_ = null;
		MdbCli2 mdbCli2 = null;
		try {
			String hkey_ = PubMethod.FmtArgs("RFO:%s:%s:%d:%d:%s:%d", // RFO : 'Repart Fixed'
					udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26].substring(0),
					udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
					parameter_rec._ruleId,
					parameter_rec._rateId,
					parameter_rec._chargeItem,
					parameter_rec._id);
			if (TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2 = MdbAgt2.GetShparmInstance();
				jedis_ = mdbCli2.getJedis(hkey_);
			}else {
				cli_ = MdbAgt.GetShparmInstance();
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
			}
//			String hkey_ = PubMethod.FmtArgs("RFO:%s:%s:%d:%d:%s:%d", // RFO : 'Repart Fixed'
//					udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6),
//					udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
//					parameter_rec._ruleId,
//					parameter_rec._rateId,
//					parameter_rec._chargeItem,
//					parameter_rec._id);
			String init_val_ = PubMethod.FmtArgs("%s|%s|%s|%s|%s",
					PubMethod._JvmHost,
					PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23),
					udr._eFields[UdrFmt.E_04_A05_FILE_ID],
					udr._eFields[UdrFmt.E_05_A06_LINE_NUM],
					parameter_rec._rateValue);
			
			jedis_.hsetnx(hkey_, "INIT_VALUE_DESC", "JvmHost|New DateTime|File_Id|Line_Num|RateValue");
			jedis_.hsetnx(hkey_, "KEY_DESC", "File_Id|Line_Num|JvmHost|New DateTime");
			jedis_.hsetnx(hkey_, "VALUE_DESC", "Old RESIDUE_Value|AmountTotal|RateValue|New RESIDUE_Value");
			
			long cnt_ = jedis_.hsetnx(hkey_, "INIT", init_val_);
			if (cnt_ > 0)
				l.trace("{} hsetnx {} INIT {}, cnt_ = {}", _logTag, hkey_, init_val_, cnt_);
			cnt_ = jedis_.hsetnx(hkey_, "RESIDUE", parameter_rec._rateValue);
			if (cnt_ > 0) 
				l.trace("{} hsetnx {} RESIDUE {}, cnt_ = {}", _logTag, hkey_, parameter_rec._rateValue, cnt_);

			while (true) {
				jedis_.watch(hkey_);
				String residue_ = jedis_.hget(hkey_, "RESIDUE");
				long residual_ = Long.parseLong(residue_);
				if (residual_ == 0) {
					l.debug("{} {} residue_ {} le 0, try match next cfg", _logTag, parameter_rec._id, residual_);
					split_sidd_ = parameter_sidd;
					break;
				} else if ( residual_ < 0 ) {
					if ( amount_total_ >= 0) {
						tx_ = jedis_.multi();
						long res_num_ = 0;
						if ( -residual_ >= amount_total_ ) {
							res_num_ = amount_total_ ;
						} else {
							res_num_ = -residual_;
						}
						tx_.hset(hkey_,
								PubMethod.FmtArgs("%s|%s|%s|%s", 	udr._eFields[UdrFmt.E_04_A05_FILE_ID],udr._eFields[UdrFmt.E_05_A06_LINE_NUM], PubMethod._JvmHost,
																	PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23)),
								PubMethod.FmtArgs("%d|%d|%s|%d", 	residual_, amount_total_, parameter_rec._rateValue, residual_ + res_num_ ));
						
						tx_.hincrBy(hkey_, "RESIDUE", res_num_ );
						
						List<Object> tx_echo_list_ = tx_.exec();
						if (tx_echo_list_ == null) {
							jedis_.unwatch();
							l.info("{} {} residual_:amount_total_:deduct_ {}:{}:{}, exec canceled", _logTag, parameter_rec._id,
									residual_, amount_total_, residual_);
							PubMethod.Sleep(1);
							continue;
						}
						l.trace("{} {} tx_echo_list_ {}", _logTag, parameter_rec._id, PubMethod.Collection2Str(tx_echo_list_, ","));
						udr._eFields[UdrFmt.E_14_A15_ACUMLT] = Long.toString(residual_); //有批价累积量的情况,记录累积值快照
						l.debug("{} {} Try to return the residue_ number to zero, Return to progress {} , try match next cfg", _logTag, parameter_rec._id, residual_ + res_num_  );
						
						split_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
						for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
							split_sidd_[i] = parameter_sidd[i];
						split_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
						split_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
						
					} else {
						l.debug("{} {} residue_={} and amount_total_={} Are less than zero, No operation, try match next cfg", _logTag, parameter_rec._id, residual_, amount_total_);
						split_sidd_ = parameter_sidd;
					}
					udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString( residual_ + amount_total_  );
					break;
				} else {
					tx_ = jedis_.multi();
					long res_num_ = 0;
					if ( amount_total_ >= residual_ ) {
						res_num_ = -residual_ ;
					} else if ( residual_ > amount_total_ &&  amount_total_ >= 0 ) {
						res_num_ = (-residual_ * 2) + amount_total_;
					} else {
						res_num_ = -residual_ * 2 ;
					}
					tx_.hset(hkey_,
							PubMethod.FmtArgs("%s|%s|%s|%s", 	udr._eFields[UdrFmt.E_04_A05_FILE_ID],udr._eFields[UdrFmt.E_05_A06_LINE_NUM], PubMethod._JvmHost,
																PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23)),
							PubMethod.FmtArgs("%d|%d|%s|%d", 	residual_, amount_total_, parameter_rec._rateValue, residual_ + res_num_ ));

					tx_.hincrBy(hkey_, "RESIDUE", res_num_ );
					
					List<Object> tx_echo_list_ = tx_.exec();
					if (tx_echo_list_ == null) {
						jedis_.unwatch();
						l.info("{} {} residual_:amount_total_:deduct_ {}:{}:{}, exec canceled", _logTag, parameter_rec._id,
								residual_, amount_total_, residual_);
						PubMethod.Sleep(1);
						continue;
					}
					l.trace("{} {} tx_echo_list_ {}", _logTag, parameter_rec._id, PubMethod.Collection2Str(tx_echo_list_, ","));
					udr._eFields[UdrFmt.E_14_A15_ACUMLT] = Long.toString(residual_);

					parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString(residual_);
					parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					
					udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_ - residual_);
					
					split_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
					for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
						split_sidd_[i] = parameter_sidd[i];
					split_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
					split_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					
					break;
				}
			}
		} catch (Exception e) {
			if (jedis_ != null)
				jedis_.unwatch();
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnBrokenResource(tx_);
			} else if (cli_ != null) {
				cli_.returnBrokenResource(tx_, pool_, jedis_);
			}
			throw e;
		} finally {
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnResource(jedis_);
			} else if (cli_ != null) {
				cli_.returnResource(pool_, jedis_);
			}
		}
		return split_sidd_;
	}

//	private void _ratingInRepartFixedDollar(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String[] parameter_sidd) {
//		long fee_notax_ = Long.parseLong(parameter_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
//		int key_ = Integer.parseInt(parameter_sidd[UdrFmt.S_28_RECORD_ID_29]);
//		List<String[]> val_ = _inSiddMap.get(key_);
//		if (val_ != null) {
//			for (String[] prev_ : val_) {
//				fee_notax_ = fee_notax_ - Long.parseLong(prev_[UdrFmt.S_29_SETTLE_NOTAX_30]);
//			}
//		}
//		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString( (fee_notax_>=0)?fee_notax_:0 );
//		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
//		udr._eFields[UdrFmt.E_23_E09_SPARE1] = "0";
//	}
	
	/**
	 * 固定值结算规则的最后一条 $ 的值得获取方式
	 * 也就是 话单的 AMOUNT_NOTAX - 已经结算的金额（rateValue）
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 */
	private void _ratingInRepartFixedDollar(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String[] parameter_sidd) {
		long fee_notax_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString( (fee_notax_ >= 0) ? fee_notax_ : 0 );
		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
		udr._eFields[UdrFmt.E_23_E09_SPARE1] = "0";
	}

	/**
	 * 按比例结算
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 */
	private void _ratingInRepartProportion(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String[] parameter_sidd) {
		BigDecimal fee_notax_ = new BigDecimal(parameter_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
		if ("$".equals(parameter_rec._rateValue)) {
			int key_ = Integer.parseInt(parameter_sidd[UdrFmt.S_28_RECORD_ID_29]);
			List<String[]> val_ = _inSiddMap.get(key_);
			if (val_ != null) {
				for (String[] prev_ : val_) {
					fee_notax_ = fee_notax_.subtract(new BigDecimal(prev_[UdrFmt.S_29_SETTLE_NOTAX_30]));
				}
			}
		} else {
			BigDecimal rate_value_ = new BigDecimal(parameter_rec._rateValue);
			if (RaterSIDD.POS_ONE.compareTo(rate_value_) < 0 || RaterSIDD.NEG_ONE.compareTo(rate_value_) > 0) {
				String acnt_month_ = udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S637_REPART_PARAMETER_VALUE_INVALID;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%s:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, acnt_month_, parameter_rec._id, parameter_rec._rateValue);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d REPART %s ID %d RATE_VALUE %s out of range [-1,1]", parameter_rec._ruleId,
						parameter_rec._rateId, acnt_month_, parameter_rec._id, parameter_rec._rateValue);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			fee_notax_ = fee_notax_.multiply(rate_value_);
		}
		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = fee_notax_.setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
	}

	private String _parseParameterValue(UdrFmt udr, EdbStlRepartParameterRec parameter_rec, String parameter_value, String[] sidd) {
		String acnt_month_ = udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6);
		if (parameter_value == null) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S637_REPART_PARAMETER_VALUE_INVALID;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%s:%d", parameter_rec._ruleId, parameter_rec._rateId,
					acnt_month_, parameter_rec._id);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
					"RULE %d RATE_ID %d REPART %s ID %d PARAMETER_VALUE is null", parameter_rec._ruleId, parameter_rec._rateId,
					acnt_month_, parameter_rec._id);
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return null;
		}
		Integer idx_ = null;
		if (RaterSIDD.ExpIddFieldName.matcher(parameter_value).find()) {
			String field_nm_ = parameter_value.substring(2, parameter_value.length() - 1);
			idx_ = UdrDef.S_NM2IDX_MAP.get(field_nm_);
			if (idx_ == null) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S637_REPART_PARAMETER_VALUE_INVALID;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%s:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, acnt_month_, parameter_rec._id, parameter_value);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d REPART %s ID %d invalid IDD field %s", parameter_rec._ruleId, parameter_rec._rateId,
						acnt_month_, parameter_rec._id, parameter_value);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return null;
			}
			if (PubMethod.IsEmpty(sidd[idx_])) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S638_REPART_PARAMETER_IDD_FIELD_EMPTY;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%s:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, acnt_month_, parameter_rec._id, parameter_value);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d REPART %s ID %d IDD %s IDX(%d) field empty", parameter_rec._ruleId,
						parameter_rec._rateId, acnt_month_, parameter_rec._id, parameter_value, idx_);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return null;
			}
			// l.trace("{} cfg_value {} ==> {}", _logTag, parameter_value, sidd[idx_]);
		}
		return idx_ == null ? parameter_value : sidd[idx_] == null ? "" : sidd[idx_];
	}

	private List<EdbStlRepartParameterRec> _seekRepartParameter(UdrFmt udr, EdbStlRepartRateRec repart_rate) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		List<EdbStlRepartParameterRec> parameter_list_ = null;
		try {
			EdbStlRepartParameterRec repart_parameter_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = _prepareRepartParameterStmt(conn_, udr, repart_rate);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				repart_parameter_ = _extractStlRepartParameter(rs_);
				if (parameter_list_ == null)
					parameter_list_ = new ArrayList<EdbStlRepartParameterRec>();
				parameter_list_.add(repart_parameter_);
			}
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
		String acnt_month_ = udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6);
		if (repart_rate._matchMode == 1) {
			l.debug("查询SQLite库 {}.REPART_{} 表,matchMode=1."+
							"查询条件: RULE_ID = {} AND RATE_ID = {} AND TARIFF_TYPE = {} AND PROD_INST_ID = {} AND (CHARGE_ITEM = {} OR CHARGE_ITEM = '-1')",
					shpm_ver_,acnt_month_,udr._uFields[UdrFmt.S_32_RULE_ID_33], repart_rate._rateId,repart_rate._tariffType,
					udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10],udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
		}else {
			l.debug("查询SQLite库 {}.REPART_{} 表,matchMode=2."+
							"查询条件: RULE_ID = {} AND RATE_ID = {} AND TARIFF_TYPE = {} AND SVC_INST_ID = {} AND (CHARGE_ITEM = {} OR CHARGE_ITEM = '-1')",
					shpm_ver_,acnt_month_,udr._uFields[UdrFmt.S_32_RULE_ID_33], repart_rate._rateId,repart_rate._tariffType,
					udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
		}

		_matchRepartParameter(udr, repart_rate, parameter_list_);
		return parameter_list_;
	}

	private EdbStlRepartPartitionRec _seekRepartPartition(UdrFmt udr, EdbStlRateRec rate_rec) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			String acnt_month_ = udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6);
			EdbStlRepartPartitionRec repart_partition_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_REPART_PARTITION);
			pstmt_.setLong(1, rate_rec._rateId);
			pstmt_.setString(2, acnt_month_);
			pstmt_.setLong(3, rate_rec._ruleId);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				repart_partition_ = _extractStlRepartPartition(rs_);
				break;
			}
			if (repart_partition_ == null) {
				long ts_ = System.currentTimeMillis();
				ts_ -= 86400L * 1000L * DbStlRepartParameterRec._MAX_DAYS_CACHED_IN_SQLITE;
				String min_month_ = PubMethod.Long2Str(ts_, PubMethod.TimeStrFmt.Fmt8).substring(0, 6);
				if (min_month_.compareTo(acnt_month_) < 0) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S634_NO_REPART_PARTITION_CFG;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%s", rate_rec._ruleId, rate_rec._rateId,
							acnt_month_);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d RATE_ID %d REPART %s no cfg",
							rate_rec._ruleId, rate_rec._rateId, acnt_month_);
					l.info("{} {}, {}", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				} else {
					l.trace("{} acnt month {} lt {}, try seek Oracle directly", _logTag, acnt_month_, min_month_);
				}
			} else {
				// l.trace("{} RULE {} REPART_PARTITION located, {}", _logTag, rate_rec._ruleId, repart_partition_.toGsonStr());
			}
			return repart_partition_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlRepartRateRec _seekRepartRate(UdrFmt udr, EdbStlRateRec rate_rec) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			EdbStlRepartRateRec repart_rate_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_REPART_RATE);
			pstmt_.setLong(1, rate_rec._rateId);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				repart_rate_ = ExtractStl.extractStlRepartRate(rs_);
				break;
			}
			if (repart_rate_ != null) {
				if (repart_rate_._tariffType < 1 || repart_rate_._tariffType > 3) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S632_REPART_RATE_TYPE_INVALID;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d", rate_rec._ruleId, rate_rec._rateId,
							repart_rate_._tariffType);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
							"RULE %d REPART_RATE %d TARIFF_TYPE %d out of range [1,2]", rate_rec._ruleId, rate_rec._rateId,
							repart_rate_._tariffType);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					return null;
				}
				if (repart_rate_._matchMode < 1 || repart_rate_._matchMode > 2) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S633_REPART_RATE_MATCH_MODE_INVALID;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d", rate_rec._ruleId, rate_rec._rateId,
							repart_rate_._matchMode);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
							"RULE %d REPART_RATE %d MATCH_MODE %d out of range [1,2]", rate_rec._ruleId, rate_rec._rateId,
							repart_rate_._matchMode);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					return null;
				}
				// l.trace("{} RULE {} REPART_RATE located, {}", _logTag, rate_rec._ruleId, repart_rate_.toGsonStr());
			} else {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S631_NO_REPART_RATE_CFG;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d", rate_rec._ruleId, rate_rec._rateId);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d REPART_RATE %d no cfg", rate_rec._ruleId,
						rate_rec._rateId);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return repart_rate_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private List<EdbStlRepartParameterRec> _seekRepartParameterOdb(UdrFmt udr, EdbStlRepartRateRec repart_rate) throws Exception {
		OdbCli cli_ = OdbAgt.GetBizInstance();
		String acnt_month_ = udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6);
		List<DbStlRepartParameterRec> odb_result_ = null;
		if (EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER == repart_rate._matchMode) {
			odb_result_ = cli_.queryForOListWithThrow(_Q_STL_ODB_REPART_PROD_OFFER, DbStlRepartParameterRec.class, acnt_month_,
					Long.parseLong(udr._uFields[UdrFmt.S_32_RULE_ID_33]), repart_rate._rateId, repart_rate._tariffType,
					Long.parseLong(udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10]), udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			
		} else if (EdbStlTariffRateRec.MATCH_MODE_2_PROD_SVC == repart_rate._matchMode) {
			if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11])) {
				l.info("{} PRODUCT_ORDER_ID_11 is empty, can not seek REPART_PARAMETER, {}", _logTag, repart_rate.toGsonStr());
			} else {
				odb_result_ = cli_.queryForOListWithThrow(_Q_STL_ODB_REPART_PROD_SVC, DbStlRepartParameterRec.class, acnt_month_,
						Long.parseLong(udr._uFields[UdrFmt.S_32_RULE_ID_33]), repart_rate._rateId, repart_rate._tariffType,
						Long.parseLong(udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11]), udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			}
		} else {
			l.warn("{} absurd!!! repart_rate {} invalid _matchMode", _logTag, repart_rate.toGsonStr());
			return null;
		}
		if (repart_rate._matchMode == 1) {
			l.debug("查询数据库stl_repart_paramter表,matchMode=1." +
							"查询条件:ACCT_MONTH = {} AND RULE_ID = {} AND RATE_ID = {} AND TARIFF_TYPE = {} AND PROD_INST_ID = {} AND (CHARGE_ITEM = {} OR CHARGE_ITEM = '-1')",
					acnt_month_,udr._uFields[UdrFmt.S_32_RULE_ID_33], repart_rate._rateId,repart_rate._tariffType,
					udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10],udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
		}else {
			l.debug("查询数据库stl_repart_paramter表,matchMode=2." +
							"查询条件:ACCT_MONTH = {} AND RULE_ID = {} AND RATE_ID = {} AND TARIFF_TYPE = {} AND SVC_INST_ID = {} AND (CHARGE_ITEM = {} OR CHARGE_ITEM = '-1')",
					acnt_month_,udr._uFields[UdrFmt.S_32_RULE_ID_33], repart_rate._rateId,repart_rate._tariffType,
					udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
		}

		List<EdbStlRepartParameterRec> parameter_list_ = null;
		EdbStlRepartParameterRec repart_parameter_ = null;
		if (odb_result_ != null) {
			for (DbStlRepartParameterRec odb_rec_ : odb_result_) {
				repart_parameter_ = _extractStlRepartParameter(odb_rec_);
				if (parameter_list_ == null)
					parameter_list_ = new ArrayList<EdbStlRepartParameterRec>();
				parameter_list_.add(repart_parameter_);
			}
		}
		_matchRepartParameter(udr, repart_rate, parameter_list_);
		return parameter_list_;
	}

	private void _matchRepartParameter(UdrFmt udr, EdbStlRepartRateRec repart_rate, List<EdbStlRepartParameterRec> parameter_list) {
		String acnt_month_ = udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6);
		if (parameter_list == null || parameter_list.isEmpty()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S635_NO_REPART_PARAMETER_CFG;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%s:%d:%d:%d:%s:%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
					acnt_month_, repart_rate._rateId, repart_rate._tariffType, repart_rate._matchMode,
					repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER
							? udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10] : udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
					udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
					"RULE %s REPART %s RATE_ID %d TYPE %d MODE %d %s %s CHARGE_ITEM_18 %s no REPART_PARAMETER cfg",
					udr._uFields[UdrFmt.S_32_RULE_ID_33], acnt_month_, repart_rate._rateId, repart_rate._tariffType,
					repart_rate._matchMode,
					repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER ? "OFFER_ORDER_ID_10"
							: "PRODUCT_ORDER_ID_11",
					repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER
							? udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10] : udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
					udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			l.info("{} {}, {}", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}

		EdbStlRepartParameterRec repart_parameter_ = null;
		boolean has_charge_item_matched_ = false;
		for (int i = parameter_list.size() - 1; i >= 0; i--) {
			repart_parameter_ = parameter_list.get(i);
			if (!repart_parameter_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
				l.trace("{} START_TIME_36 {} ineffective, skip repart_parameter {}", _logTag,
						udr._uFields[UdrFmt.S_35_START_TIME_36], repart_parameter_.toGsonStr());
				parameter_list.remove(i);
			} else if (repart_parameter_._chargeItem.equals(udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18])) {
				has_charge_item_matched_ = true;
			}
		}

		if (has_charge_item_matched_) {
			for (int i = parameter_list.size() - 1; i >= 0; i--) {
				repart_parameter_ = parameter_list.get(i);
				if ("-1".equals(repart_parameter_._chargeItem)) {
					l.trace("{} REPART_PARAMETER removed because matched charge_item {} exists, {}", _logTag,
							udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], repart_parameter_.toGsonStr());
					parameter_list.remove(i);
				}
			}
		}

		if (parameter_list.isEmpty()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S636_REPART_PARAMETER_INEFFECTIVE;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%s:%d:%d:%d:%s:%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
					acnt_month_, repart_rate._rateId, repart_rate._tariffType, repart_rate._matchMode,
					repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER
							? udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10] : udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
					udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
					"RULE %s REPART %s RATE_ID %d TYPE %d MODE %d %s %s CHARGE_ITEM_18 %s none effective REPART_PARAMETER",
					udr._uFields[UdrFmt.S_32_RULE_ID_33], acnt_month_, repart_rate._rateId, repart_rate._tariffType,
					repart_rate._matchMode,
					repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER ? "OFFER_ORDER_ID_10"
							: "PRODUCT_ORDER_ID_11",
					repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER
							? udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10] : udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
					udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
			l.info("{} {}, {}", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}

		List<String> param_json_list_ = new ArrayList<>();
		for (EdbStlRepartParameterRec param_rec_ : parameter_list) {
			param_json_list_.add(param_rec_.toGsonStr());
		}
		l.debug("查询后过滤结果：{}", PubMethod.Collection2Str(param_json_list_, ","));

		l.trace("{} REPART_RATE {}, PARAMETER({}) {}", _logTag, repart_rate.toGsonStr(), parameter_list.size(),
				PubMethod.Collection2Str(param_json_list_, ","));
	}

	private PreparedStatement _prepareRepartParameterStmt(Connection conn, UdrFmt udr, EdbStlRepartRateRec repart_rate)
			throws SQLException {
		String acnt_month_ = udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6);
		PreparedStatement pstmt_ = null;
		if (repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER) {
			pstmt_ = conn.prepareStatement(String.format(_Q_STL_REPART_YYYYMM_PROD_OFFER, acnt_month_));
			pstmt_.setString(1, udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10]);
		} else if (repart_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_2_PROD_SVC) {
			pstmt_ = conn.prepareStatement(String.format(_Q_STL_REPART_YYYYMM_PROD_SVC, acnt_month_));
			pstmt_.setString(1, udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11]);
		} else {
			l.warn("{} absurd!!! repart_rate {} invalid _matchMode", _logTag, repart_rate.toGsonStr());
			return null;
		}
		pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
		pstmt_.setLong(3, Long.parseLong(udr._uFields[UdrFmt.S_32_RULE_ID_33]));
		pstmt_.setLong(4, repart_rate._rateId);
		pstmt_.setInt(5, repart_rate._tariffType);
		return pstmt_;
	}

	private EdbStlRepartParameterRec _extractStlRepartParameter(DbStlRepartParameterRec odb_rec) {
		EdbStlRepartParameterRec rec_ = new EdbStlRepartParameterRec();
		rec_._id = odb_rec.getId();
		rec_._offerCode = odb_rec.getOffer_code();
		rec_._productCode = odb_rec.getProduct_code();
		rec_._prodInstId = odb_rec.getProd_inst_id();
		rec_._svcInstId = odb_rec.getSvc_inst_id();
		rec_._orderMode = odb_rec.getOrder_mode();
		rec_._ruleId = odb_rec.getRule_id();
		rec_._rateId = odb_rec.getRate_id();
		rec_._chargeItem = odb_rec.getCharge_item();
		rec_._calcPriority = odb_rec.getCalc_priority();
		rec_._objectValue = odb_rec.getObject_value();
		rec_._tariffType = odb_rec.getTariff_type();
		rec_._rateValue = odb_rec.getRate_value();
		rec_._destSource = odb_rec.getDest_source();
		rec_._effDate = PubMethod.Timestamp2Str(odb_rec.getEff_date(), PubMethod.TimeStrFmt.Fmt14);
		rec_._expDate = PubMethod.Timestamp2Str(odb_rec.getExp_date(), PubMethod.TimeStrFmt.Fmt14);
		return rec_;
	}

	private EdbStlRepartParameterRec _extractStlRepartParameter(ResultSet rs) throws SQLException {
		EdbStlRepartParameterRec rec_ = new EdbStlRepartParameterRec();
		int idx_ = 0;
		rec_._id = rs.getLong(++idx_);
		rec_._offerCode = rs.getString(++idx_);
		rec_._productCode = rs.getString(++idx_);
		rec_._prodInstId = rs.getLong(++idx_);
		rec_._svcInstId = rs.getLong(++idx_);
		rec_._orderMode = rs.getString(++idx_);
		rec_._ruleId = rs.getLong(++idx_);
		rec_._rateId = rs.getLong(++idx_);
		rec_._chargeItem = rs.getString(++idx_);
		rec_._calcPriority = rs.getInt(++idx_);
		rec_._objectValue = rs.getString(++idx_);
		rec_._tariffType = rs.getInt(++idx_);
		rec_._rateValue = rs.getString(++idx_);
		rec_._destSource = rs.getString(++idx_);
		rec_._routeFlag = rs.getString(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}

	private EdbStlRepartPartitionRec _extractStlRepartPartition(ResultSet rs) throws SQLException {
		EdbStlRepartPartitionRec rec_ = new EdbStlRepartPartitionRec();
		int idx_ = 0;
		rec_._rateId = rs.getLong(++idx_);
		rec_._acctMonth = rs.getString(++idx_);
		rec_._ruleId = rs.getLong(++idx_);
		return rec_;
	}
}
