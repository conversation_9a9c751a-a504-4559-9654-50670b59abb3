package com.hp.cmcc.bboss.storm.sidd;

import cn.hutool.json.JSONUtil;
import com.hp.cmcc.bboss.pub.dto.BaseRspsMsg;
import com.hp.cmcc.bboss.pub.dto.RequestParam;
import com.hp.cmcc.bboss.pub.dto.ResponseMsg;
import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.*;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import com.hp.cmcc.bboss.storm.util.ExtractStl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

public class GuiderSIDDRef extends FunctorIDD {

	private Map<Long, EdbStlOfferRec> _offerMap; // K:_ruleId
	private Map<Long, EdbStlRuleRec> _ruleMap; // K:_ruleId
	private Map<Long, List<EdbStlRateRec>> _rateMap; // K:_ruleId
	private Map<Long, EdbStlObjectRec> _oObjMap; // K:rule_id
	private Map<Long, EdbStlObjectRec> _iObjMap; // K:_objectId;

	public GuiderSIDDRef() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
		_offerMap = new HashMap<>();
		_ruleMap = new HashMap<>();
		_rateMap = new HashMap<>();
		_oObjMap = new HashMap<>();
		_iObjMap = new HashMap<>();
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		l.debug("--------------------------start GuiderSIDDRef execUdr -------------------------------------");
		_logTag = log_tag;
		_clearMaps();
		if (udr.isErr())
			return;
		_guidingSIDD(udr);
	}

	private void _guidingSIDD(UdrFmt udr) throws Exception {
		/**1 offer表**/
		_guidOffer(udr);
		if (udr.isErr()){
			return;
		}

		/**2 规则表**/
		_guidRule(udr);
		if (udr.isErr()){
			return;
		}

//		if (this.seekStlObjectlist(udr)) return;
		SeekStlObject seekStlObject = new SeekStlObject();
		for (EdbStlRuleRec stl_rule_ : _ruleMap.values()) {
			seekStlObject.seekStlObject(udr, stl_rule_);
			if (udr.isErr()) {
				return;
			}
		}
//		for (EdbStlRuleRec stl_rule_ : _ruleMap.values()) {
//			/**3 结出 结算对象**/
//			_seekStlObject(udr, stl_rule_);
//			if (udr.isErr())
//				return;
//		}

//		if (this.guidRate(udr)) return;
		GuidRate guidRate = new GuidRate();
		for (Long rule_id_ : _ruleMap.keySet()) {
			/**4 结算方式**/
			guidRate.guidRate(udr, rule_id_);
			if (udr.isErr()){
				return;
			}
		}
//		for (Long rule_id_ : _ruleMap.keySet()) {
//			/**4 结算方式**/
//			_guidRate(udr, rule_id_);
//			if (udr.isErr())
//				return;
//		}

//		if (seekStlInObjectlist(udr)) return;
		SeekStlInObjects seekStlInObjects = new SeekStlInObjects();
		for (List<EdbStlRateRec> stl_rate_list_ : _rateMap.values()) {
			/**5 结入对象**/
			seekStlInObjects.seekStlInObjects(udr, stl_rate_list_);
			if (udr.isErr()) {
				return;
			}
		}
//		for (List<EdbStlRateRec> stl_rate_list_ : _rateMap.values()) {
//			/**5 结入对象**/
//			_seekStlInObjects(udr, stl_rate_list_);
//			if (udr.isErr())
//				return;
//		}

		/**6 **/
		_filterFeedbackOffer(udr);
		if (udr.isErr())
			return;

		/**7 **/
		_fillAuxMap(udr);
		/**8 **/
		_traceGuidingInfo(udr);
	}

/*	private boolean seekStlInObjectlist(UdrFmt udr) throws Exception {
		SeekStlInObjects seekStlInObjects = new SeekStlInObjects();
		seekStlInObjects.setLogTag(_logTag);
		seekStlInObjects.setiObjMap(_oObjMap);

		for (List<EdbStlRateRec> stl_rate_list_ : _rateMap.values()) {
			*//**5 结入对象**//*
			seekStlInObjects.seekStlInObjects(udr, stl_rate_list_);
			if (udr.isErr())
				return true;
		}
		return false;
	}*/
	/**
	 *
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/13 16:49
	 */
	public class SeekStlInObjects{

		private static final String _Q_STL_OBJECT = "SELECT OBJECT_ID, OBJECT_NAME, OBJECT_TYPE, OWNER_NAME, FIELD_NAME, "
				+ "SEARCH_KEY, OBJECT_VALUE FROM OBJECT WHERE OBJECT_ID = ?";

		private Logger log;

		public SeekStlInObjects() {
			log = LoggerFactory.getLogger(this.getClass());
		}

		public void seekStlInObjects(UdrFmt udr, List<EdbStlRateRec> rate_list) throws Exception{
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_OBJECT);
				for (EdbStlRateRec stl_rate_ : rate_list) {
					if (_iObjMap.containsKey(stl_rate_._inObjectId))
						continue;
					EdbStlObjectRec stl_object_ = null;
					pstmt_.setLong(1, stl_rate_._inObjectId);
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_object_ = ExtractStl.extractStlObject(rs_);
						break;
					}
					if (stl_object_ != null) {
						// l.trace("{} IN_OBJECT {} for RATE {} located, {}", _logTag, stl_rate_._inObjectId, stl_rate_._rateId,
						// stl_object_.toGsonStr());
						_iObjMap.put(stl_object_._objectId, stl_object_);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S532_RATE_NO_COR_IN_OBJECT_CFG;
						udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RATE_ID(%d) in OBJ_ID(%d)", OfferElem.offerElem(udr),
								stl_rate_._rateId, stl_rate_._inObjectId);
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("IN_OBJECT %d for RATE %d no cfg",
								stl_rate_._inObjectId, stl_rate_._rateId);
						log.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
								udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
						return;
					}
				}
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	/***
	* 
	* @author: zhanglei
	* @date: 2022/1/20 11:11
	* @return: boolean
	* @exception:  
	* @update: 
	* @updatePerson: 
	*/
//	private boolean guidRate(UdrFmt udr) throws Exception {
//		GuidRate guidRate = new GuidRate();
//		guidRate.setLogTag(_logTag);
//
//		for (Long rule_id_ : _ruleMap.keySet()) {
//			/**4 结算方式**/
//			guidRate.guidRate(udr, rule_id_);
//			if (udr.isErr()){
//				return true;
//			}
//		}
//		return false;
//	}

	public class GuidRate{

		private static final String _Q_STL_RATE = "SELECT ID, RATE_ID, RULE_ID, IN_OBJECT_ID, RATE_CODE, RATE_TYPE, "
				+ "CALC_PRIORITY, ROUND_METHOD, EFF_DATE, EXP_DATE FROM RATE WHERE RULE_ID = ? "
				+ "ORDER BY CALC_PRIORITY ASC, EFF_DATE DESC, ID ASC";

		private Logger log;

		public GuidRate() {
			log = LoggerFactory.getLogger(this.getClass());
		}

		public void guidRate(UdrFmt udr, long rule_id) throws Exception {
			List<EdbStlRateRec> stl_rate_list_ = seekStlRate(udr, rule_id);
			if (udr.isErr())
				return;

			Set<Integer> rate_type_set_ = new HashSet<Integer>();
			for (EdbStlRateRec stl_rate_ : stl_rate_list_)
				rate_type_set_.add(stl_rate_._rateType);
			if (rate_type_set_.size() > 1) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID %d", OfferElem.offerElem(udr), rule_id);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S504_MULTI_RATE_TYPE_FOR_ONE_RULE;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d has multi RATE_TYPEs (%s)", rule_id,
						PubMethod.Collection2Str(rate_type_set_, ","));
				log.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			_rateMap.put(rule_id, stl_rate_list_);
		}


		private List<EdbStlRateRec> seekStlRate(UdrFmt udr, long rule_id) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				boolean has_rs_ = false;
				List<EdbStlRateRec> stl_rate_list_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_RATE);
				pstmt_.setLong(1, rule_id);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					EdbStlRateRec stl_rate_ = ExtractStl.extractStlRate(rs_);
					has_rs_ = true;
					if (!stl_rate_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
						log.trace("{} START_TIME_36 {} ineffective, skip rate {}", _logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
								stl_rate_.toGsonStr());
						stl_rate_ = null;
						continue;
					}
					if (stl_rate_list_ == null) {
						stl_rate_list_ = new ArrayList<EdbStlRateRec>();
					}
					stl_rate_list_.add(stl_rate_);
				}
				if (stl_rate_list_ != null) {
					// l.trace("{} RATE for RULE {} located, total {} elem", _logTag, rule_id, stl_rate_list_.size());
				} else {
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d)", OfferElem.offerElem(udr), rule_id);
					if (has_rs_) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S531_RATE_CFG_INEFFECTIVE;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RATE for RULE %d none effective", rule_id);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S530_NO_RATE_CFG;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RATE for RULE %d no cfg", rule_id);
					}
					log.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				}
				return stl_rate_list_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}


	/***
	* 
	* @author: zhanglei
	* @date: 2022/1/19 17:12
	* @param udr
	* @return: boolean
	* @exception:  
	* @update: 
	* @updatePerson: 
	*/
/*	private boolean seekStlObjectlist(UdrFmt udr) throws Exception {
		SeekStlObject seekStlObject = new SeekStlObject();
		seekStlObject.setLogTag(_logTag);

		for (EdbStlRuleRec stl_rule_ : _ruleMap.values()) {
			seekStlObject.seekStlObject(udr, stl_rule_);
			if (udr.isErr())
				return true;
		}
		return false;
	}*/

	/**
	 *
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/13 11:27
	 */
	public class SeekStlObject{

		private static final String _Q_STL_OBJECT = "SELECT OBJECT_ID, OBJECT_NAME, OBJECT_TYPE, OWNER_NAME, FIELD_NAME, "
				+ "SEARCH_KEY, OBJECT_VALUE FROM OBJECT WHERE OBJECT_ID = ?";

		private Logger log;

		public SeekStlObject() {
			log = LoggerFactory.getLogger(this.getClass());
		}

		public void seekStlObject(UdrFmt udr, EdbStlRuleRec rule_rec) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				EdbStlObjectRec stl_object_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_OBJECT);
				pstmt_.setLong(1, rule_rec._objectId);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_object_ = ExtractStl.extractStlObject(rs_);
					break;
				}
				if (stl_object_ != null) {
					// l.trace("{} OBJECT for RULE {} located, {}", _logTag, rule_rec._ruleId, stl_object_.toGsonStr());
					_oObjMap.put(rule_rec._ruleId, stl_object_);
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S520_NO_OBJECT_CFG;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d) out OBJ_ID(%d)", OfferElem.offerElem(udr),
							rule_rec._ruleId, rule_rec._objectId);
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%s:%s:%s:%d:%d",
							udr._uFields[UdrFmt.S_03_DATA_SOURCE_04], udr._uFields[UdrFmt.S_07_OFFER_CODE_08],
							udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09], udr._uFields[UdrFmt.S_15_ORDER_MODE_16], rule_rec._ruleId,
							rule_rec._objectId);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("(out) OBJECT_ID %d for RULE %d no cfg",
							rule_rec._objectId, rule_rec._ruleId);
					log.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				}
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	private void _traceGuidingInfo(UdrFmt udr) {
		List<String> offer_json_list_ = new ArrayList<>();
		for (EdbStlOfferRec offer_ : _offerMap.values())
			offer_json_list_.add(offer_.toGsonStr());

		List<String> rule_json_list_ = new ArrayList<>();
		for (EdbStlRuleRec rule_ : _ruleMap.values())
			rule_json_list_.add(rule_.toGsonStr());

		List<String> o_obj_json_list_ = new ArrayList<>();
		for (EdbStlObjectRec o_obj_ : _oObjMap.values())
			o_obj_json_list_.add(o_obj_.toGsonStr());

		List<String> rate_json_list_ = new ArrayList<>();
		for (List<EdbStlRateRec> rate_list_ : _rateMap.values())
			for (EdbStlRateRec rate_ : rate_list_)
				rate_json_list_.add(rate_.toGsonStr());

		List<String> i_obj_json_list_ = new ArrayList<>();
		for (EdbStlObjectRec i_obj_ : _iObjMap.values())
			i_obj_json_list_.add(i_obj_.toGsonStr());

		l.trace("{} OFFER({}) {}, RULE({}) {}, RATE({}) {}, O_OBJ({}) {}, I_OBJ({}) {}", _logTag, offer_json_list_.size(),
				PubMethod.Collection2Str(offer_json_list_, ","), rule_json_list_.size(),
				PubMethod.Collection2Str(rule_json_list_, ","), rate_json_list_.size(),
				PubMethod.Collection2Str(rate_json_list_, ","), o_obj_json_list_.size(),
				PubMethod.Collection2Str(o_obj_json_list_, ","), i_obj_json_list_.size(),
				PubMethod.Collection2Str(i_obj_json_list_, ","));
	}

	private void _fillAuxMap(UdrFmt udr) throws Exception {
		if (udr._auxMap == null)
			udr._auxMap = new HashMap<>();

		List<EdbStlOfferRec> offer_list_ = new ArrayList<>(_offerMap.values());
		udr._auxMap.put(UdrFmt.AUX_MAP_KEY_OFFER, offer_list_);

		for (EdbStlRuleRec rule_ : _ruleMap.values())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RULE, rule_._ruleId), rule_);

		for (Map.Entry<Long, EdbStlObjectRec> ent_ : _oObjMap.entrySet())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_O_OBJECT, ent_.getKey()), ent_.getValue());

		for (Map.Entry<Long, List<EdbStlRateRec>> ent_ : _rateMap.entrySet())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RATE, ent_.getKey()), ent_.getValue());

		for (EdbStlObjectRec i_obj_ : _iObjMap.values())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_I_OBJECT, i_obj_._objectId), i_obj_);

	}

	private void _filterFeedbackOffer(UdrFmt udr) throws Exception {
		for (EdbStlOfferRec offer_ : _offerMap.values()) {
			if (udr.isFeedback() && (EdbStlOfferRec.ROUTE_CODE_3_EC_RECEIVEABLE == offer_._routeCode
					|| EdbStlOfferRec.ROUTE_CODE_4_EC_RECEIVED == offer_._routeCode)) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFER.ID %d", OfferElem.offerElem(udr), offer_._id);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S503_INVALID_ROUTE_CODE_FOR_FEEDBACK_UDR;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("OFFER ID %d invalid ROUTE_CODE %d for feedback UDR",
						offer_._id, offer_._routeCode);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
		}
	}

	private void _guidRule(UdrFmt udr) throws Exception {
		GuidRule guidRule = new GuidRule();
		guidRule.setLogTag(_logTag);

		List<EdbStlOfferRec> offer_list_ = new ArrayList<>(_offerMap.values());
		List<EdbStlRuleRec> rule_list_ = guidRule.seekStlRule(udr, offer_list_);

		if (udr.isErr()) {
			return;
		}
		guidRule.filterOfferRules(udr, rule_list_);
		if (udr.isErr()){
			return;
		}

		guidRule.matchStlRuleItem(udr);

	}

	/**
	 * guidRule 业务类
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/12 17:52
	 */
	public class GuidRule{

		private static final String _Q_STL_RULE_ITEM = "SELECT ID, RULE_ID, CHARGE_ITEM, ITEM_NAME, EFF_DATE, EXP_DATE "
				+ "FROM RULE_ITEM WHERE RULE_ID = ? AND CHARGE_ITEM = ? ORDER BY EFF_DATE DESC";

		private static final String _Q_STL_RULE = "SELECT RULE_ID, RULE_NAME, OBJECT_ID, BALANCE, EFF_DATE, EXP_DATE "
				+ "FROM RULE WHERE RULE_ID = ?";

		private String logTag;

		private Logger log;

		public GuidRule() {
			log = LoggerFactory.getLogger(this.getClass());
		}

		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		public List<EdbStlRuleRec> seekStlRule(UdrFmt udr, List<EdbStlOfferRec> offer_list) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			List<EdbStlRuleRec> rule_list_ = null;
			try {
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_RULE);
				for (EdbStlOfferRec offer_ : offer_list) {
					EdbStlRuleRec stl_rule_ = null;
					pstmt_.setLong(1, offer_._ruleId);
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_rule_ = ExtractStl.extractStlRule(rs_);
						if (rule_list_ == null)
							rule_list_ = new ArrayList<EdbStlRuleRec>();
						rule_list_.add(stl_rule_);
					}
					if (stl_rule_ == null) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S510_NO_RULE_CFG;
						udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d)", OfferElem.offerElem(udr), offer_._ruleId);
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("OFFER %d RULE_ID %d not exists", offer_._id,
								offer_._ruleId);
						log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
								udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
						return null;
					}
				}
				return rule_list_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}

		public void filterOfferRules(UdrFmt udr, List<EdbStlRuleRec> rule_list) {
			List<Long> offer_id_list_ = new ArrayList<>();
			for (EdbStlOfferRec offer_ : _offerMap.values()) {
				offer_id_list_.add(offer_._id);
			}

			List<Long> rule_id_list_ = new ArrayList<>();
			for (EdbStlRuleRec rule_ : rule_list)
				rule_id_list_.add(rule_._ruleId);

			if (offer_id_list_.size() != rule_id_list_.size()) { // assert
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S511_RULE_CFG_INEFFECTIVE;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFERs(%s) RULEs(%s)", OfferElem.offerElem(udr),
						PubMethod.Collection2Str(offer_id_list_, ","), PubMethod.Collection2Str(rule_id_list_, ","));
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("INTERNAL ERROR, count of OFFER:RULE %d:%d ne",
						offer_id_list_.size(), rule_id_list_.size());
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}

			for (EdbStlRuleRec rule_ : rule_list) {
				if (!rule_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
					EdbStlOfferRec offer_ = _offerMap.get(rule_._ruleId);
					log.trace("{} START_TIME_36 {} ineffective, skip RULE {}, skip OFFER {}", logTag,
							udr._uFields[UdrFmt.S_35_START_TIME_36], rule_.toGsonStr(), offer_.toGsonStr());
					_offerMap.remove(rule_._ruleId);
				} else {
					_ruleMap.put(rule_._ruleId, rule_);
				}
			}

			if (_ruleMap.isEmpty()) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S511_RULE_CFG_INEFFECTIVE;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFERs(%s) RULEs(%s)", OfferElem.offerElem(udr),
						PubMethod.Collection2Str(offer_id_list_, ","), PubMethod.Collection2Str(rule_id_list_, ","));
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("%d RULEs but none effective", rule_id_list_.size());
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
		}

		public void matchStlRuleItem(UdrFmt udr) throws Exception {
			int cnt_nonexits_ = 0;
			int cnt_ineffective_ = 0;
			AtomicBoolean has_rs_ = new AtomicBoolean(false);
			List<Long> rule_id_list_ = new ArrayList<>(_ruleMap.keySet());

			for (Long rule_id_ : rule_id_list_) {
				EdbStlRuleRec rule_ = _ruleMap.get(rule_id_);
				EdbStlOfferRec offer_ = _offerMap.get(rule_id_);
				has_rs_.set(false);
				EdbStlRuleItemRec stl_rule_item_ = this.matchOneStlRuleItem(udr, rule_, has_rs_);
				if (stl_rule_item_ == null) {
					if (has_rs_.get()) {
						++cnt_ineffective_;
					}
					else {
						++cnt_nonexits_;
					}
					_ruleMap.remove(rule_id_);
					_offerMap.remove(rule_id_);
					if (offer_ != null && udr._uFields != null && udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18] != null) {
						log.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE {}, skip OFFER {}", logTag,
								udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], JSONUtil.toJsonStr(rule_), JSONUtil.toJsonStr(offer_));
					} else if (udr._uFields != null && udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18] != null) {
						log.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE null or skip OFFER null", logTag,
								udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
					}else {
						log.debug("{} udr._uFields is null , skip RULE null or skip OFFER null", logTag);
					}

				}
			}

			if (_ruleMap.isEmpty()) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_IDs(%s) CHARGE_ITEM(%s)", OfferElem.offerElem(udr),
						PubMethod.Collection2Str(rule_id_list_, ","), udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				if (cnt_nonexits_ == 0) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S513_RULE_ITEM_INEFFECTIVE;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE_IDs(%s) RULE_ITEM none effective",
							PubMethod.Collection2Str(rule_id_list_, ","));
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S512_NO_RULE_ITEM_CFG;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE_IDs(%s) RULE_ITEM nonexist:ineffective %d:%d",
							PubMethod.Collection2Str(rule_id_list_, ","), cnt_nonexits_, cnt_ineffective_);
				}
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
		}

		private EdbStlRuleItemRec matchOneStlRuleItem(UdrFmt udr, EdbStlRuleRec stl_rule, AtomicBoolean has_rs) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				EdbStlRuleItemRec stl_rule_item_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				/**
				 l.info("-----------------GuiderSIDD._matchOneStlRuleItem._Q_STL_RULE_ITEM = {}",
				 _Q_STL_RULE_ITEM);
				 l.info("-----------------GuiderSIDD._matchOneStlRuleItem.stl_rule._ruleId = {}",
				 stl_rule._ruleId);
				 l.info("-----------------GuiderSIDD._matchOneStlRuleItem.udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18] = {}",
				 udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				 **/
				pstmt_ = conn_.prepareStatement(_Q_STL_RULE_ITEM);
				pstmt_.setLong(1, stl_rule._ruleId);
				pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_rule_item_ = ExtractStl.extractStlRuleItem(rs_);
					has_rs.set(true);
					if (!stl_rule_item_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
						log.trace("{} START_TIME_36 {} ineffective, skip rule_item {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
								stl_rule_item_.toGsonStr());
						stl_rule_item_ = null;
						continue;
					}
					break;
				}
				if (stl_rule_item_ == null) {
					pstmt_.setString(2, "-1");
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_rule_item_ = ExtractStl.extractStlRuleItem(rs_);
						has_rs.set(true);
						if (!stl_rule_item_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
							log.trace("{} START_TIME_36 {} ineffective, skip rule_item {}", logTag,
									udr._uFields[UdrFmt.S_35_START_TIME_36], stl_rule_item_.toGsonStr());
							stl_rule_item_ = null;
							continue;
						}
						break;
					}
				}
				if (stl_rule_item_ == null) {
					// l.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE {}", _logTag,
					// udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], stl_rule.toGsonStr());
				}
				return stl_rule_item_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	private void _guidOffer(UdrFmt udr) throws Exception {
		GuidOffer guidOffer = new GuidOffer();
//		Map<Long, EdbStlOfferRec> offerMap = guidOffer.getOfferMap();
		guidOffer.setLogTag(_logTag);
		List<EdbStlOfferRec> offer_list_ = guidOffer.seekStlOffer(udr);

		if (offer_list_ == null || udr.isErr()) {
			return;
		}
		List<Long> offer_id_list_ = new ArrayList<>();
		List<Long> rule_id_list_ = new ArrayList<>();
		for (EdbStlOfferRec offer_ : offer_list_) {
			offer_id_list_.add(offer_._id);
			rule_id_list_.add(offer_._ruleId);
		}

		for (EdbStlOfferRec offer_ : offer_list_) {
			if (_offerMap.containsKey(offer_._ruleId)) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = OfferElem.offerElem(udr);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S502_MULTI_OFFER_RULE_ID_DUP;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("matched %d OFFERs(%s) has dup RULE_ID(%s)",
						offer_list_.size(), PubMethod.Collection2Str(offer_id_list_, ","),
						PubMethod.Collection2Str(rule_id_list_, ","));
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			_offerMap.put(offer_._ruleId, offer_);
		}
	}

	public static class GuidOffer{

		private static final String _Q_STL_OFFER = "SELECT ID, DATA_SOURCE, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, RULE_ID, "
				+ "ROUTE_CODE, DEST_SOURCE, EFF_DATE, EXP_DATE FROM OFFER WHERE DATA_SOURCE = ? AND OFFER_CODE = ? "
				+ "AND PRODUCT_CODE = ? AND ORDER_MODE = ? ORDER BY EFF_DATE DESC";

		private Map<Long, EdbStlOfferRec> offerMap = new HashMap<>();

		private String logTag;
		private Logger log;
		public GuidOffer() {
			log = LoggerFactory.getLogger(this.getClass());
		}
		public Map<Long, EdbStlOfferRec> getOfferMap() {
			return offerMap;
		}

		public void setOfferMap(Map<Long, EdbStlOfferRec> offerMap) {
			this.offerMap = offerMap;
		}

		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		/***
		 *
		 * @author: zhanglei
		 * @date: 2022/1/12 17:29
		 * @param udr
		 * @return: java.util.List<com.hp.cmcc.bboss.pub.edb.entity.EdbStlOfferRec>
		 * @exception:
		 * @update:
		 * @updatePerson:
		 */
		private List<EdbStlOfferRec> seekStlOffer(UdrFmt udr) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			List<EdbStlOfferRec> offer_list_ = null;
			try {
				String tm14_ = udr._uFields[UdrFmt.S_35_START_TIME_36];
				int raw_offer_cnt_ = 0;
				boolean has_rs_ = false;
				EdbStlOfferRec stl_offer_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_OFFER);
				pstmt_.setInt(1, Integer.parseInt(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]));
				pstmt_.setString(2, udr._uFields[UdrFmt.S_07_OFFER_CODE_08]);
				pstmt_.setString(3, udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09]);
				pstmt_.setString(4, udr._uFields[UdrFmt.S_15_ORDER_MODE_16]);
				if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09])) {
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_offer_ = ExtractStl.extractStlOffer(rs_);
						++raw_offer_cnt_;
						has_rs_ = true;
						if (!stl_offer_.isEffective(tm14_)) {
							log.trace("{} START_TIME_36 {} ineffective, skip offer {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
									stl_offer_.toGsonStr());
							continue;
						}
						if (offer_list_ == null)
							offer_list_ = new ArrayList<EdbStlOfferRec>();
						offer_list_.add(stl_offer_);
					}
				}

				if (offer_list_ == null || offer_list_.isEmpty()) {
					pstmt_.setString(3, "-1");
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_offer_ = ExtractStl.extractStlOffer(rs_);
						++raw_offer_cnt_;
						has_rs_ = true;
						if (!stl_offer_.isEffective(tm14_)) {
							log.trace("{} START_TIME_36 {} ineffective, skip offer {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
									stl_offer_.toGsonStr());
							continue;
						}
						if (offer_list_ == null)
							offer_list_ = new ArrayList<EdbStlOfferRec>();
						offer_list_.add(stl_offer_);
					}
				}

				if (offer_list_ != null && !offer_list_.isEmpty()) {
					// l.trace("{} {} OFFERs located", _logTag, offer_list_.size());
				} else {
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = OfferElem.offerElem(udr);
					if (has_rs_) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S501_OFFER_CFG_INEFFECTIVE;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("%d OFFERs but none effective", raw_offer_cnt_);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S500_NO_OFFER_CFG;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "no OFFER matches";
					}
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				}
				return offer_list_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	public static class OfferElem{
		public static String offerElem(UdrFmt udr) {
			StringBuilder sb_ = new StringBuilder();
			sb_.append(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04] == null ? "" : udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]);
			sb_.append(':');
			sb_.append(udr._uFields[UdrFmt.S_07_OFFER_CODE_08] == null ? "" : udr._uFields[UdrFmt.S_07_OFFER_CODE_08]);
			sb_.append(':');
			sb_.append(udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09] == null ? "" : udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09]);
			sb_.append(':');
			sb_.append(udr._uFields[UdrFmt.S_15_ORDER_MODE_16] == null ? "" : udr._uFields[UdrFmt.S_15_ORDER_MODE_16]);
			return sb_.toString();
		}
	}


	private void _clearMaps() {
		_offerMap.clear();
		_ruleMap.clear();
		_rateMap.clear();
		_oObjMap.clear();
		_iObjMap.clear();
	}
}
