package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.storm.sidd.*;
import com.hp.cmcc.bboss.storm.uidd.ValidatorUIDD;
import org.apache.storm.task.OutputCollector;
import org.apache.storm.task.TopologyContext;
import org.apache.storm.topology.OutputFieldsDeclarer;
import org.apache.storm.topology.base.BaseRichBolt;
import org.apache.storm.tuple.Fields;
import org.apache.storm.tuple.Tuple;
import org.apache.storm.tuple.Values;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.regex.Pattern;


public class ValidationBolt extends BaseRichBolt {

    private static final long serialVersionUID = -7384015484561315374L;
    public static Pattern _PatternTm14 = Pattern.compile("^[0-9]{14}$");
    public static Pattern _PatternDt8 = Pattern.compile("^[0-9]{8}$");
    public static Pattern _PatternYm6 = Pattern.compile("^[0-9]{6}$");
    public static Pattern _PatternDigits = Pattern.compile("^[0-9]+$");
    public static Pattern _PatternSignedDigits = Pattern.compile("^[\\+\\-]?[0-9]+$");
    private Logger l = null;
    private OutputCollector _collector;
    private String _rndmId;
    private String _logTag;
    private FunctorIDD _validator;

    /* (non-Javadoc)
     * Map stormConf 为bolt提供配置
     * TopologyContext context 在topology中提供完整的bolt位置信息,包括它的任务id,输入和输出信息等
     * OutputCollector collector 保证处理过的tuple能被发送出去
     * @see backtype.storm.task.IBolt#prepare(java.util.Map, backtype.storm.task.TopologyContext, backtype.storm.task.OutputCollector)
     */
    @Override
    public void prepare(@SuppressWarnings("rawtypes") Map stormConf, TopologyContext context, OutputCollector collector) {
        l = LoggerFactory.getLogger(this.getClass());
        TopologyCtx.Init(context.getThisWorkerPort());
        _collector = collector;
        _rndmId = "";
        l.info("---------------  OdbSystemParam.GetInstance()._subSystemType  = {}------------------------",
                OdbSystemParam.GetInstance()._subSystemType);
        if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
            _validator = new ValidatorUIDD();
        } else if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
            //TODO 测试代码，排查问题，后期删除
            if("Ref".equals(TopologyCfg.GetInstance().validationImpl)){
                _validator = new ValidatorSIDDRef();
            }else if("New".equals(TopologyCfg.GetInstance().validationImpl)){
                _validator = new ValidatorSIDDNew();
            }else {
                _validator = new ValidatorSIDD();
            }
        } else {
            throw new RuntimeException("ukn SUB_SYSTEM_TYPE " + OdbSystemParam.GetInstance()._subSystemType);
        }
    }

    /* (non-Javadoc)
     * execute(Tuple tuple)//这里的tuple是将被处理的输入tuple
     * execute方法每次处理一个元组。
     * 	通过tuple的getValue方法访问元组的数据。
     * 	输入元组不是必须立即处理的，可以过一会再处理。
     * 	可以处理多个元组，处理后产生单个tuple作为输出tuple。
     * 	处理过的tuple可以使用OutputCollector类发送出去。
     * @see backtype.storm.task.IBolt#execute(backtype.storm.tuple.Tuple)
     */
    @Override
    public void execute(Tuple input) {
        UdrFmtMsg msg_ = null;
        try {
            msg_ = (UdrFmtMsg) input.getValue(0);
            _rndmId = msg_._rndmId;
            for (UdrFmt udr_ : msg_._udrFmtList) {
                _execUdr(udr_);
            }
        } catch (Exception e) {
            //l.info("exception, {}, {}", _rndmId, msg_ == null ? "null" : msg_.toGsonStr(), e);
            l.info("ValidationBolt exception", e);
        } finally {
            try{
                _collector.emit(new Values(msg_));
                _collector.ack(input);
            }catch (Exception e){
                l.error("ValidationBolt error", e);
            }

        }
    }

    /* (non-Javadoc)
     * declareOutputFields(OutputFieldsDeclarer declarer)//这里的declarer用来声明输出流的ids，输出字段等信息
     * 	这个方法用来指定tuple的输出shema。ValidationBolt入参
     * @see backtype.storm.topology.IComponent#declareOutputFields(backtype.storm.topology.OutputFieldsDeclarer)
     */
    @Override
    public void declareOutputFields(OutputFieldsDeclarer declarer) {
        declarer.declare(new Fields(TopologyCfg.OUTPUT_FIELD_DFT));
    }

    private void _execUdr(UdrFmt udr) {
        try {
            _logTag = _rndmId + ", " + udr.getBizlogUid();
            _validator.execUdr(_logTag, udr);
        } catch (Exception e) {
            udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V081_EXP_VALIDATION;
            udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = "storm validation exception";
            udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = e.getMessage();
            l.error("validation异常,话单：{}. logTag: {} err_code: {}, err_val: {}",
                    udr.toGsonStr(), _logTag, udr._eFields[UdrFmt.E_02_A03_ERR_CODE], udr._eFields[UdrFmt.E_16_E02_ERR_VAL], e);
        } finally {
            if (!UdrFmt.V081_EXP_VALIDATION.equals(udr._eFields[UdrFmt.E_02_A03_ERR_CODE])) {
                udr.setProcFlag(UdrFmt.PROC_FLAG_VALIDATION);
            }
            if (TopologyCfg.GetInstance()._bizlogFlags.contains(UdrFmt.PROC_FLAG_VALIDATION)) {
                TopologyCtx.BizlogTimestamp(true, udr, MdbConst.BIZLOG_HK_UDR_VALIDATION, null);
            }
        }
    }
}
