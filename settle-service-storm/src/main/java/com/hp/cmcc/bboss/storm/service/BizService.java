/*
package com.hp.cmcc.bboss.storm.service;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.util.AppCmdline;

import com.hp.cmcc.bboss.storm.config.BizCfg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.stereotype.Service;

//@Service
public class BizService {

	private static Logger L = LoggerFactory.getLogger(BizService.class);

	private static void _Init() {
		if (!BizCfg.Init()) {
			L.error("init cfg error, JVM exit");
			System.exit(1);
		}
		MdbAgt.Init(OdbSystemParam.GetInstance(), 19, 28, 3);
		L.debug("*** *** start BizService *** ***");
	}


	public void run(ApplicationArguments args) {
		L.info("------------------Start BizService-----------------------");
		AppCmdline cmdline_ = AppCmdline.GetInstance();
		cmdline_.init("biz", args.getSourceArgs());
		try {
			_Init();
			L.info("BizService running");
		} catch (Exception e) {
			L.error("encounter exception, JVM exit", e);
			System.exit(1);
		}
	}
}
*/
