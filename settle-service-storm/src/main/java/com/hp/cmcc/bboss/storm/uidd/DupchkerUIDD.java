package com.hp.cmcc.bboss.storm.uidd;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.odb.entity.DbDupchkUdrRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.TopologyOdb;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisSentinelPool;

import java.sql.SQLException;

public class DupchkerUIDD extends FunctorIDD {
	private String _dupWith;

	public DupchkerUIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
	}

	public String getDupWith() {
		return _dupWith;
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		_dupWith = null;
		_chkDup(udr);
	}

	private void _chkDup(UdrFmt udr) {
		OdbSystemParam sys_param_ = OdbSystemParam.GetInstance();
		long now_ = System.currentTimeMillis();
		long ts_direct_ = now_ - ((long) sys_param_._redisDupchkHoursDirect * 3600 * 1000);
		long ts_hybrid_ = now_ - ((long) sys_param_._redisDupchkHoursHybrid * 3600 * 1000);
		long ts_udr_ = PubMethod.Str2Long(udr._uFields[UdrFmt.U_40_DUP_TIME_41], PubMethod.TimeStrFmt.Fmt14);
		if (ts_udr_ > ts_direct_) {
			_chkDupMdb(udr, "direct");
		} else if (ts_udr_ > ts_hybrid_) {
			_chkDupMdb(udr, "hybrid");
			_chkDupOdb(udr, "hybrid");
		} else {
			_chkDupOdb(udr, "oracle");
		}
	}

	private void _chkDupMdb(UdrFmt udr, String chk_type) {
		if (TopologyCfg.GetInstance().redisClusterMode()) {
			MdbCli2 mdbCli2 = MdbAgt2.GetDupchkInstance();
			try {
				JedisCluster jedisCluster = mdbCli2.getJedisCluster();
				String val_ = udr._eFields[UdrFmt.E_04_A05_FILE_ID] + ":" + udr._eFields[UdrFmt.E_05_A06_LINE_NUM];
				long cnt_ = jedisCluster.hsetnx(udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40], val_);
				if (cnt_ <= 0) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V009_DUP_UDR;
					udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_39_ACCUMULATION_KEY_40);
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40];
					val_ = "?:?";
					try {
						val_ = jedisCluster.hget(udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40]);
					} catch (Exception ee) {
						String alm_ = PubMethod.FmtArgs("%s, hget [%s,%s] exception, ignore", chk_type,
								udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40]);
						l.warn("{} {}", _logTag, alm_, ee);

					}
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("dup with %s, (%s:redis)", val_, chk_type);
					_dupWith = val_;
				} else {
					l.trace("{} {} passed", _logTag, chk_type);
				}
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("%s, [%s,%s] jedis exception, re-throw", chk_type,
						udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40]);
				l.warn("{} {}", _logTag, alm_, e);
				throw new RuntimeException(e);
			}
		}else {
			MdbCli cli_ = MdbAgt.GetDupchkInstance();
			JedisSentinelPool pool_ = null;
			Jedis jedis_ = null;
			try {
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
				String val_ = udr._eFields[UdrFmt.E_04_A05_FILE_ID] + ":" + udr._eFields[UdrFmt.E_05_A06_LINE_NUM];
				long cnt_ = jedis_.hsetnx(udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40], val_);
				if (cnt_ <= 0) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V009_DUP_UDR;
					udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_39_ACCUMULATION_KEY_40);
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40];
					val_ = "?:?";
					try {
						val_ = jedis_.hget(udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40]);
					} catch (Exception ee) {
						String alm_ = PubMethod.FmtArgs("%s, hget [%s,%s] exception, ignore", chk_type,
								udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40]);
						l.warn("{} {}", _logTag, alm_, ee);
						cli_.returnBrokenResource(null, pool_, jedis_);
						jedis_ = null;
					}
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("dup with %s, (%s:redis)", val_, chk_type);
//				l.info("{} [{},{}] {}, {} reject", _logTag, udr._uFields[UdrFmt.U_40_DUP_TIME_41],
//						udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40], udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
//						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					_dupWith = val_;
				} else {
					l.trace("{} {} passed", _logTag, chk_type);
				}
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("%s, [%s,%s] jedis exception, re-throw", chk_type,
						udr._uFields[UdrFmt.U_40_DUP_TIME_41], udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40]);
				l.warn("{} {}", _logTag, alm_, e);
				cli_.returnBrokenResource(null, pool_, jedis_);
				jedis_ = null;
				throw new RuntimeException(e);
			} finally {
				cli_.returnResource(pool_, jedis_);
			}
		}

	}

	private void _chkDupOdb(UdrFmt udr, String chk_type) {
		DbDupchkUdrRec rec_ = new DbDupchkUdrRec();
		rec_.setMmdd(Integer.parseInt(udr._uFields[UdrFmt.U_40_DUP_TIME_41].substring(4, 8)));
		rec_.setHash_key(udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40]);
		rec_.setEvent_time(PubMethod.Str2Timestamp(udr._uFields[UdrFmt.U_40_DUP_TIME_41], PubMethod.TimeStrFmt.Fmt14));
		rec_.setAux_key("-");
		rec_.setFile_id(Long.parseLong(udr._eFields[UdrFmt.E_04_A05_FILE_ID]));
		rec_.setLine_num(Integer.parseInt(udr._eFields[UdrFmt.E_05_A06_LINE_NUM]));
		TopologyOdb odb_ = new TopologyOdb();
		try {
			if (!odb_.addSttlDupchk(null, rec_)) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V009_DUP_UDR;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_39_ACCUMULATION_KEY_40);
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40];
				DbDupchkUdrRec db_rec_ = odb_.getSttlDupchk(rec_.getMmdd(), rec_.getHash_key(), rec_.getEvent_time(),
						rec_.getAux_key());
				if (db_rec_ == null) {
					db_rec_ = new DbDupchkUdrRec();
					db_rec_.setFile_id(-1L);
					db_rec_.setLine_num(-1);
				}
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("dup with %d:%d, (%s:oracle)", db_rec_.getFile_id(),
						db_rec_.getLine_num(), chk_type);
				_dupWith = PubMethod.FmtArgs("%d:%d", db_rec_.getFile_id(), db_rec_.getLine_num());
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
	}
}
