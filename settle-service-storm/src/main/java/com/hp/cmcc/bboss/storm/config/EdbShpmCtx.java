package com.hp.cmcc.bboss.storm.config;

import com.hp.cmcc.bboss.pub.edb.EdbShpmFactory;
import com.hp.cmcc.bboss.pub.edb.EdbShpmPool;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;

@Slf4j
public class EdbShpmCtx {

    public static ThreadLocal<EdbShpmPool> _EdbShpmPoolThreadLocal = new ThreadLocal<>();

    public static void _InitEdbShpmPool(String shpmBaseDir, Integer shpmCacheSize) {
        GenericKeyedObjectPoolConfig cfg_ = new GenericKeyedObjectPoolConfig();
        cfg_.setLifo(true);
//        int cnt_per_key_ = ((topology_cfg._paralValidation + topology_cfg._paralGuiding) / topology_cfg._workerNum) + 2;

        /**
         * 设置线程池中的总最大数
         */
        cfg_.setMaxTotal(5 * 400);
        /**
         * 对象池每个key最大实例化对象数
         */
        cfg_.setMaxTotalPerKey(400);
        cfg_.setMaxWaitMillis(5 * 1000);
        cfg_.setTimeBetweenEvictionRunsMillis(15 * 1000);

        EdbShpmFactory factory_ = new EdbShpmFactory(shpmBaseDir, shpmCacheSize);
        EdbShpmPool shpm_pool_ = new EdbShpmPool(factory_, cfg_);
        log.info("getLifo {}, getMaxTotal {}, getMaxTotalPerKey {}, getMaxBorrowWaitTimeMillis {}, getMaxWaitMillis {}",
                new Object[] { shpm_pool_.getLifo(), shpm_pool_.getMaxTotal(), shpm_pool_.getMaxTotalPerKey(),
                        shpm_pool_.getMaxBorrowWaitTimeMillis(), shpm_pool_.getMaxWaitMillis() });

        _EdbShpmPoolThreadLocal.set(shpm_pool_);
    }
}
