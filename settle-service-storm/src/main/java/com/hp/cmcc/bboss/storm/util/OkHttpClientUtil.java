package com.hp.cmcc.bboss.storm.util;

import com.google.gson.reflect.TypeToken;
import com.hp.cmcc.bboss.pub.dto.BaseRspsMsg;
import com.hp.cmcc.bboss.pub.dto.RequestParam;
import com.hp.cmcc.bboss.pub.dto.ResponseMsg;
import com.hp.cmcc.bboss.pub.util.GsonUtil;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 *
 * @author: zhanglei
 * @version: 1.0
 * @date: 2022/1/19 17:30
 */
public final class OkHttpClientUtil {
    private static Logger l = LoggerFactory.getLogger(OkHttpClientUtil.class);
    // 创建OkHttpClient对象, 并设置超时时间
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30L, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(0,1,TimeUnit.NANOSECONDS))
            .build();


    private OkHttpClientUtil(){}

    /***
    * 
    * @author: zhanglei
    * @date: 2022/1/19 17:57
    * @param url
    * @param requestParam
    * @return: com.hp.cmcc.bboss.pub.dto.BaseRspsMsg<com.hp.cmcc.bboss.pub.dto.ResponseMsg>
    * @exception:  
    * @update: 
    * @updatePerson: 
    */
    public static BaseRspsMsg<ResponseMsg> callPost(String url, RequestParam requestParam) {
        BaseRspsMsg<ResponseMsg> baseRspsMsg = BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE, "默认值-失败");
        MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
        try {
            final Request request = new Request.Builder()
                    .url(url)
                    .header("Connection", "close")
                    .post(RequestBody.create(mediaType, GsonUtil.toJsonString(requestParam)))
                    .build();

            Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                String bodyStr = response.body().string();
                l.warn("callPost url =  {} response.body() = {}", url, bodyStr);

                baseRspsMsg = GsonUtil.fromJsonString(bodyStr,
                        new TypeToken<BaseRspsMsg<ResponseMsg>>() {
                        }.getType());
            } else {
                l.error("请求异常,错误码为:{}", response.code());
            }
        } catch (Exception e) {
            l.error("callPost error ------", e);
        }

        return baseRspsMsg;
    }
}
