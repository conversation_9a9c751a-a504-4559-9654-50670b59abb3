package com.hp.cmcc.bboss.storm.uidd;

import cn.hutool.json.JSONUtil;
import com.hp.cmcc.bboss.pub.dto.BaseRspsMsg;
import com.hp.cmcc.bboss.pub.dto.RequestParam;
import com.hp.cmcc.bboss.pub.dto.ResponseMsg;
import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbSvbzcdRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class GuiderUIDD extends FunctorIDD {

	private static String remoteUrl = "";

	public GuiderUIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
		String remoteHost = System.getProperty("remotehost");
		String remotePort = System.getProperty("remoteport");
		remoteUrl = "http://" + remoteHost + ":" + remotePort;
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		if (udr.isErr())
			return;
		_assertUIDD(udr);
		_guidingUIDD(udr);
		_paddingUIDD(udr);
	}

	private void _assertUIDD(UdrFmt udr) throws Exception {

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = assertUIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}
		return;
	}


	public BaseRspsMsg<ResponseMsg> assertUIDD(RequestParam requestParam) throws Exception {

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(true);

		if (System.currentTimeMillis() > 10000) // skip _assertUIDD
			return BaseRspsMsg.ok(responseMsg);
		if (PubMethod.IsBlank(udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45])
				&& PubMethod.IsBlank(udr._uFields[UdrFmt.U_26_MEMBER_CODE_27])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F330_PRODUCT_ORDER_ID_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_44_PRODUCT_ORDER_ID_45);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "both PRODUCT_ORDER_ID_33 and SUBSCRIBER_NUMBER_15 are empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);

			responseMsg.setResult(false);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		return BaseRspsMsg.ok(responseMsg);
	}


	private void _guidingUIDD(UdrFmt udr) throws Exception {

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);


		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = guiding(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}
		return;
	}

	public BaseRspsMsg<ResponseMsg> guiding(RequestParam requestParam) throws Exception {

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(true);

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		SeekSvbzcd seekSvbzcd = new SeekSvbzcd();
		seekSvbzcd.setLogTag(logTag);

		if (System.currentTimeMillis() > 10000) // skip _guiding
			return BaseRspsMsg.ok(responseMsg);
		if (udr.isErr()) {
			responseMsg.setResult(false);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		EdbSvbzcdRec svbzcd_ = seekSvbzcd.seekSvbzcd(udr);
		if (svbzcd_ == null) {
			responseMsg.setResult(false);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		udr._uFields[UdrFmt.U_23_EC_CODE_24] = svbzcd_._ecCode;
		udr._uFields[UdrFmt.U_42_OFFER_CODE_43] = svbzcd_._productCode;
		udr._uFields[UdrFmt.U_25_PRODUCT_CODE_26] = svbzcd_._serviceCode;
		udr._uFields[UdrFmt.U_43_OFFER_ORDER_ID_44] = svbzcd_._prodOrderId;
		udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45] = svbzcd_._orderId;
		udr._uFields[UdrFmt.U_24_EC_PROV_CODE_25] = svbzcd_._provCode;
		udr._uFields[UdrFmt.U_02_HOME_PARTY_03] = svbzcd_._provCode;

		return BaseRspsMsg.ok(responseMsg);
	}

	public static class SeekSvbzcd{

		private static final String _Q_SVBZCD_BY_ORDER_ID = "SELECT ID, EC_CODE, SERV_CODE, BIZ_CODE, PROD_ORDER_ID, ORDER_ID, "
				+ "BIZ_CODE_APPLY, PROV_CODE, PRODUCT_CODE, SERVICE_CODE, EFF_TM, EXP_TM, EC_GROUP_ID, SERVICE_TYPE, SEND_PROV, "
				+ "PROD_ORDER_MODE, SUB_GROUP_FLAG, CARRY_TYPE, SIGN_ENTITY, PARENT_ORDER_ID, LOCON_FLAG, ORDER_LEVEL "
				+ "FROM SVBZCD WHERE ORDER_ID = ? ORDER BY EFF_TM DESC, ID DESC";
		private static final String _Q_SVBZCD_BY_SERV_CODE = "SELECT ID, EC_CODE, SERV_CODE, BIZ_CODE, PROD_ORDER_ID, ORDER_ID, "
				+ "BIZ_CODE_APPLY, PROV_CODE, PRODUCT_CODE, SERVICE_CODE, EFF_TM, EXP_TM, EC_GROUP_ID, SERVICE_TYPE, SEND_PROV, "
				+ "PROD_ORDER_MODE, SUB_GROUP_FLAG, CARRY_TYPE, SIGN_ENTITY, PARENT_ORDER_ID, LOCON_FLAG, ORDER_LEVEL "
				+ "FROM SVBZCD WHERE SERV_CODE = ? ORDER BY EFF_TM DESC, ID DESC";

		private String logTag;
		private Logger log;
		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		public SeekSvbzcd() {
			log = LoggerFactory.getLogger(this.getClass());
		}

		private EdbSvbzcdRec seekSvbzcd(UdrFmt udr) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				EdbSvbzcdRec svbzcd_ = null;
				int order_id_hit_ = 0;
				int serv_code_hit_ = 0;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				if (!PubMethod.IsBlank(udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45])) {
					pstmt_ = conn_.prepareStatement(_Q_SVBZCD_BY_ORDER_ID);
					pstmt_.setString(1, udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45]);
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						EdbSvbzcdRec rec_ = _extractSvbzcd(rs_);
						++order_id_hit_;
						if (rec_.isEffective(udr._uFields[UdrFmt.U_28_START_TIME_29])) {
							svbzcd_ = rec_;
							break;
						}
					}
				}
				log.info("依据产品订购编码【44_PRODUCT_ORDER_ID_45】查询 SERV_BIZ_CODE记录：{}", svbzcd_ != null ? JSONUtil.toJsonStr(svbzcd_) : "null");
				Edb.Close(rs_, pstmt_, null);
				rs_ = null;
				pstmt_ = null;
				if (svbzcd_ != null) {
					log.debug("{} PRODUCT_ORDER_ID_33 {}, EC_CODE_12 {}, OFFER_CODE_31 {}, PRODUCT_CODE_14 {}, OFFER_ORDER_ID {}",
							new Object[] { logTag, udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45], svbzcd_._ecCode,
									svbzcd_._productCode, svbzcd_._serviceCode, svbzcd_._prodOrderId });
					return svbzcd_;
				}

				if (!PubMethod.IsBlank(udr._uFields[UdrFmt.U_26_MEMBER_CODE_27])) {
					pstmt_ = conn_.prepareStatement(_Q_SVBZCD_BY_SERV_CODE);
					pstmt_.setString(1, udr._uFields[UdrFmt.U_26_MEMBER_CODE_27]);
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						EdbSvbzcdRec rec_ = _extractSvbzcd(rs_);
						++serv_code_hit_;
						if (rec_.isEffective(udr._uFields[UdrFmt.U_28_START_TIME_29])) {
							svbzcd_ = rec_;
							break;
						}
					}
				}
				log.info("依据成员号码【26_MEMBER_CODE_27】查询 SERV_BIZ_CODE记录：{}", svbzcd_ != null ? JSONUtil.toJsonStr(svbzcd_) : "null");
				if (svbzcd_ == null) {
					if (order_id_hit_ == 0 && serv_code_hit_ == 0) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F331_PRODUCT_ORDER_ID_INVALID;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
								"PRODUCT_ORDER_ID_33 %s and SUBSCRIBER_NUMBER_15 %s has no cfg",
								udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45], udr._uFields[UdrFmt.U_26_MEMBER_CODE_27]);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F332_PRODUCT_ORDER_ID_INEFFECTIVE;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
								"PRODUCT_ORDER_ID_33 %s and SUBSCRIBER_NUMBER_15 %s cfg ineffective",
								udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45], udr._uFields[UdrFmt.U_26_MEMBER_CODE_27]);
					}
					udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_44_PRODUCT_ORDER_ID_45);
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%s", udr._uFields[UdrFmt.U_44_PRODUCT_ORDER_ID_45],
							udr._uFields[UdrFmt.U_26_MEMBER_CODE_27]);
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				} else {
					log.debug("{} SUBSCRIBER_NUMBER_15 {}, EC_CODE_12 {}, OFFER_CODE_31 {}, PRODUCT_CODE_14 {}, OFFER_ORDER_ID {}",
							new Object[] { logTag, udr._uFields[UdrFmt.U_26_MEMBER_CODE_27], svbzcd_._ecCode, svbzcd_._productCode,
									svbzcd_._serviceCode, svbzcd_._prodOrderId });
				}

				return svbzcd_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}


		private EdbSvbzcdRec _extractSvbzcd(ResultSet rs) throws SQLException {
			EdbSvbzcdRec rec_ = new EdbSvbzcdRec();
			int idx_ = 0;
			rec_._id = rs.getLong(++idx_);
			rec_._ecCode = rs.getString(++idx_);
			rec_._servCode = rs.getString(++idx_);
			rec_._bizCode = rs.getString(++idx_);
			rec_._prodOrderId = rs.getString(++idx_);
			rec_._orderId = rs.getString(++idx_);
			rec_._bizCodeApply = rs.getInt(++idx_);
			rec_._provCode = rs.getString(++idx_);
			rec_._productCode = rs.getString(++idx_);
			rec_._serviceCode = rs.getString(++idx_);
			rec_._effTm = rs.getString(++idx_);
			rec_._expTm = rs.getString(++idx_);
			rec_._ecGroupId = rs.getString(++idx_);
			rec_._serviceType = rs.getString(++idx_);
			rec_._sendProv = rs.getString(++idx_);
			rec_._prodOrderMode = rs.getString(++idx_);
			rec_._subGroupFlag = rs.getInt(++idx_);
			rec_._carryType = rs.getString(++idx_);
			rec_._signEntity = rs.getString(++idx_);
			rec_._parentOrderId = rs.getString(++idx_);
			rec_._loconFlag = rs.getInt(++idx_);
			rec_._orderLevel = rs.getInt(++idx_);
			return rec_;
		}
	}

	private void _paddingUIDD(UdrFmt udr) {
		if (udr.isErr())
			return;
		// udr._uFields[UdrFmt.U_48_SHPARM_VER_49] = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		// udr._uFields[UdrFmt.U_49_STTL_FILE_ID_50] = udr._eFields[UdrFmt.E_04_A05_FILE_ID];
		// udr._uFields[UdrFmt.U_50_STTL_RAW_FILE_ID_51] = udr._eFields[UdrFmt.E_03_A04_ORG_FILE_ID];
		// udr._uFields[UdrFmt.U_51_STTL_RAW_LINE_NUM_52] = udr._eFields[UdrFmt.E_01_A02_RAW_LINE_NUM];
		// udr._uFields[UdrFmt.U_52_FILE_TYPE_53] = udr._eFields[UdrFmt.E_04_A05_FILE_ID].substring(8, 10);
		// if (udr._eFields[UdrFmt.E_04_A05_FILE_ID].equals(udr._eFields[UdrFmt.E_03_A04_ORG_FILE_ID])) {
		// udr._uFields[UdrFmt.U_53_ERCY_FLAG_54] = "0";
		// } else {
		// udr._uFields[UdrFmt.U_53_ERCY_FLAG_54] = "1";
		// udr._uFields[UdrFmt.U_54_ERCY_TM_55] = udr._eFields[UdrFmt.E_12_A13_ERCY_TIME];
		// }
		// udr._uFields[UdrFmt.U_55_RCV_TM_56] = udr._eFields[UdrFmt.E_08_A09_RCV_TM];
		// udr._uFields[UdrFmt.U_56_FILE_YMD_57] = udr._eFields[UdrFmt.E_09_A10_FILE_YMD];
		// udr._uFields[UdrFmt.U_63_ACNT_YM_64] = udr._uFields[UdrFmt.U_23_ACCT_DAY_24].substring(0, 6);
		// udr._uFields[UdrFmt.U_64_PART_DD_65] = udr._uFields[UdrFmt.U_28_DUP_TIME_29].substring(6, 8);
	}
}
