package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.edb.EdbShpmFactory;
import com.hp.cmcc.bboss.pub.edb.EdbShpmPool;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.odb.OdbAgt;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.pub.udr.UdrKafkaConsumerRunnable2;
import com.hp.cmcc.bboss.pub.util.AppCmdline;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

public class TopologyCtx {
	private static final Logger L = LoggerFactory.getLogger(TopologyCtx.class);
	private static final ReentrantLock _Lock = new ReentrantLock();
	private static boolean _InitFlag = false;
	public static BlockingQueue<UdrFmtMsg> _BlockingQueue = new LinkedBlockingQueue<UdrFmtMsg>();
	public static EdbShpmPool _EdbShpmPool;

	private static ScheduledExecutorService scheduledExecutorService;

	public static synchronized void Init(int workPort) {
		MDC.put(PubMethod.MDC_JVM_PID, PubMethod._JvmPid);
		MDC.put(PubMethod.MDC_INST_NM, System.getProperty("worker.port", "0"));
		if (!_InitFlag) {
			_Lock.lock();
			try {
				if (_InitFlag)
					return;
				String worker_port_ = System.getProperty("worker.port");
				if (PubMethod.IsBlank(worker_port_)) {
					throw new RuntimeException("'-Dworker.port=***' property not set");
				}
				AppCmdline.GetInstance()._module = "worker";
				AppCmdline.GetInstance()._instNm = worker_port_;
				String db_login_key_ = System.getProperty("dbloginkey");
				if (PubMethod.IsBlank(db_login_key_)) {
					throw new RuntimeException("'-Ddbloginkey=***' property not set");
				}
				if (!OdbAgt.Init(db_login_key_, 2, 0, 10)) {
					throw new RuntimeException("init db connection failed");
				}
				if (!OdbSystemParam.GetInstance().refresh()) {
					throw new RuntimeException("init system param failed");
				}
				TopologyCfg topology_cfg_ = TopologyCfg.GetInstance();
				if (!topology_cfg_.init()) {
					throw new RuntimeException("init topology cfg failed");
				}
				int pool_max_shparm_ = ((topology_cfg_._paralValidation + topology_cfg_._paralGuiding + topology_cfg_._paralRating)
						/ topology_cfg_._workerNum);
				int pool_max_bizlog_ = ((topology_cfg_._workerNum + topology_cfg_._paralValidation + topology_cfg_._paralChkdup
						+ topology_cfg_._paralGuiding + topology_cfg_._paralRating + topology_cfg_._paralErhndl)
						/ topology_cfg_._workerNum);
				pool_max_bizlog_ = topology_cfg_._workerNum;
				int pool_max_dupchk_ = ((topology_cfg_._paralChkdup) / topology_cfg_._workerNum) + 1;
				L.info("pool_max shparm:bizlog:dupchk {}:{}:{}", pool_max_shparm_, pool_max_bizlog_, pool_max_dupchk_);
				if (topology_cfg_.redisClusterMode()) {
					L.info("redis 模式：集群");
					if (!MdbAgt2.Init(OdbSystemParam.GetInstance(), pool_max_shparm_, pool_max_bizlog_, pool_max_dupchk_)) {
						throw new RuntimeException("init memory db failed");
					}
				}else {
					L.info("redis 模式：非集群");
					if (!MdbAgt.Init(OdbSystemParam.GetInstance(), pool_max_shparm_, pool_max_bizlog_, pool_max_dupchk_)) {
						throw new RuntimeException("init memory db failed");
					}
				}
				_InitEdbShpmPool(topology_cfg_);
				InitRaterConnectionPool();
				UdrKafkaConsumerRunnable2<UdrFmtMsg> kafkaConsumerRunnable =
						new UdrKafkaConsumerRunnable2<>(
								_BlockingQueue,
								10000,
								100,
								UdrFmtMsg.class,
						topology_cfg_._kafkaTopicRaw);
				Thread kafkaConsumerThread = new Thread(kafkaConsumerRunnable, "CNSM-" + workPort);
				kafkaConsumerThread.start();
				_InitFlag = true;
			} finally {
				_Lock.unlock();
			}
		}
	}

	private static void startTaskThread(Thread kafkaConsumerThread) {
		try {
			scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
			scheduledExecutorService.scheduleWithFixedDelay(kafkaConsumerThread, 0L, 1L, TimeUnit.SECONDS);
			L.info("Speed queue thread start success, ThreadName:{}", kafkaConsumerThread.getName());
		} catch (Exception e) {
			L.error("Speed queue thread start failed", e);
		}
	}

	@PreDestroy
	public void destroy() {
		if (null != scheduledExecutorService) {
			scheduledExecutorService.shutdown();
		}
	}

	private static void _InitEdbShpmPool(TopologyCfg topology_cfg) {
		GenericKeyedObjectPoolConfig cfg_ = new GenericKeyedObjectPoolConfig();
		cfg_.setLifo(true);
		int cnt_per_key_ = ((topology_cfg._paralValidation + topology_cfg._paralGuiding + topology_cfg._paralRating) / topology_cfg._workerNum) + 2;
//		int cnt_per_key_ = topology_cfg._paralRating *4 + 10;
		cfg_.setMaxTotal(5 * cnt_per_key_);
		cfg_.setMaxTotalPerKey(cnt_per_key_);
		cfg_.setMaxWaitMillis(5 * 1000);
		cfg_.setTimeBetweenEvictionRunsMillis(15 * 1000);

		EdbShpmFactory factory_ = new EdbShpmFactory(topology_cfg._shpmStormBaseDir, topology_cfg._shpmCacheSize);
		EdbShpmPool shpm_pool_ = new EdbShpmPool(factory_, cfg_);
		L.debug("getLifo {}, getMaxTotal {}, getMaxTotalPerKey {}, getMaxBorrowWaitTimeMillis {}, getMaxWaitMillis {}",
				new Object[] { shpm_pool_.getLifo(), shpm_pool_.getMaxTotal(), shpm_pool_.getMaxTotalPerKey(),
						shpm_pool_.getMaxBorrowWaitTimeMillis(), shpm_pool_.getMaxWaitMillis() });
		_EdbShpmPool = shpm_pool_;
	}

	public static void InitRaterConnectionPool() {
	}

	public static void BizlogTimestamp(boolean enabled, UdrFmt udr, String ts_hash_key, String aux_info) {
		if (!enabled)
			return;
		if (TopologyCfg.GetInstance().redisClusterMode()) {
			MdbCli2 mdbCli2 = MdbAgt2.GetBizlogInstance();
			JedisCluster jedisCluster = mdbCli2.getJedisCluster();
			String bizlog_uid_ = udr.getBizlogUid();
			try {
				long now_ = System.currentTimeMillis();
				String value_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23) + "|" + PubMethod._JvmHost + "|"
						+ (udr._eFields[UdrFmt.E_02_A03_ERR_CODE] == null ? "" : udr._eFields[UdrFmt.E_02_A03_ERR_CODE]) + "|"
						+ (aux_info == null ? "" : aux_info);
				jedisCluster.hset(bizlog_uid_, ts_hash_key, value_);
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("jedis hset [%s,%s] exception, %s", bizlog_uid_, ts_hash_key, udr.toGsonStr());
				L.warn(alm_, e);
			}
		}else {
			String bizlog_uid_ = udr.getBizlogUid();
			MdbCli cli_ = MdbAgt.GetBizlogInstance();
			JedisSentinelPool pool_ = null;
			Jedis jedis_ = null;
			try {
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
				long now_ = System.currentTimeMillis();
				String value_ = PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23) + "|" + PubMethod._JvmHost + "|"
						+ (udr._eFields[UdrFmt.E_02_A03_ERR_CODE] == null ? "" : udr._eFields[UdrFmt.E_02_A03_ERR_CODE]) + "|"
						+ (aux_info == null ? "" : aux_info);
				jedis_.hset(bizlog_uid_, ts_hash_key, value_);
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("jedis hset [%s,%s] exception, %s", bizlog_uid_, ts_hash_key, udr.toGsonStr());
				L.warn(alm_, e);
				cli_.returnBrokenResource(null, pool_, jedis_);
				jedis_ = null;
			} finally {
				cli_.returnResource(pool_, jedis_);
			}
		}
	}

	// public static void BizlogTimestamp(boolean enabled, String bizlog_uid, String ts_hash_key) {
	// if (!enabled)
	// return;
	// MdbCli cli_ = MdbAgt.GetBizlogInstance();
	// JedisPool pool_ = null;
	// Jedis jedis_ = null;
	// try {
	// pool_ = cli_.getPool();
	// jedis_ = pool_.getResource();
	// long now_ = System.currentTimeMillis();
	// jedis_.hset(bizlog_uid, ts_hash_key, PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23) + "|" + PubMethod._JvmHost);
	// } catch (Exception e) {
	// String alm_ = PubMethod.FmtArgs("jedis hset [%s,%s] exception", bizlog_uid, ts_hash_key);
	// L.warn(alm_, e);
	// cli_.returnBrokenResource(null, pool_, jedis_);
	// jedis_ = null;
	// } finally {
	// cli_.returnResource(pool_, jedis_);
	// }
	// }

	public static void BizlogUdr(UdrFmt udr, String udr_hash_key, String ts_hash_key, String aux_info) {
		if (TopologyCfg.GetInstance().redisClusterMode()) {
			MdbCli2 mdbCli2 = MdbAgt2.GetBizlogInstance();
			String bizlog_uid_ = udr.getBizlogUid();
			Jedis jedis = null;
			Transaction tx_ = null;
			try {
				jedis = mdbCli2.getJedis(bizlog_uid_);
				tx_ = jedis.multi();
				tx_.hset(bizlog_uid_, udr_hash_key, udr.toGsonStr());
				long now_ = System.currentTimeMillis();
				tx_.hset(bizlog_uid_, ts_hash_key,
						PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23) + "|" + PubMethod._JvmHost + "|"
								+ (udr._eFields[UdrFmt.E_02_A03_ERR_CODE] == null ? "" : udr._eFields[UdrFmt.E_02_A03_ERR_CODE]) + "|"
								+ (aux_info == null ? "" : aux_info));
				List<Object> response_ = tx_.exec();
				if (response_.size() != 2) {
					L.warn("jedis hset [%s,%s/%s] expect 2 but returns %d objects",
							new Object[] { bizlog_uid_, udr_hash_key, ts_hash_key, response_.size() });
				}
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("jedis hset [%s,%s/%s] exception, %s", bizlog_uid_, udr_hash_key, ts_hash_key,
						udr.toGsonStr());
				L.warn(alm_, e);
				mdbCli2.returnBrokenResource(tx_);
			} finally {
				mdbCli2.returnResource(jedis);
			}
		}else {
			String bizlog_uid_ = udr.getBizlogUid();
			MdbCli cli_ = MdbAgt.GetBizlogInstance();
			JedisSentinelPool pool_ = null;
			Jedis jedis_ = null;
			Transaction tx_ = null;
			try {
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
				tx_ = jedis_.multi();
				tx_.hset(bizlog_uid_, udr_hash_key, udr.toGsonStr());
				long now_ = System.currentTimeMillis();
				tx_.hset(bizlog_uid_, ts_hash_key,
						PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23) + "|" + PubMethod._JvmHost + "|"
								+ (udr._eFields[UdrFmt.E_02_A03_ERR_CODE] == null ? "" : udr._eFields[UdrFmt.E_02_A03_ERR_CODE]) + "|"
								+ (aux_info == null ? "" : aux_info));
				List<Object> response_ = tx_.exec();
				if (response_.size() != 2) {
					L.warn("jedis hset [%s,%s/%s] expect 2 but returns %d objects",
							new Object[] { bizlog_uid_, udr_hash_key, ts_hash_key, response_.size() });
				}
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("jedis hset [%s,%s/%s] exception, %s", bizlog_uid_, udr_hash_key, ts_hash_key,
						udr.toGsonStr());
				L.warn(alm_, e);
				cli_.returnBrokenResource(tx_, pool_, jedis_);
				jedis_ = null;
			} finally {
				cli_.returnResource(pool_, jedis_);
			}
		}
	}
}
