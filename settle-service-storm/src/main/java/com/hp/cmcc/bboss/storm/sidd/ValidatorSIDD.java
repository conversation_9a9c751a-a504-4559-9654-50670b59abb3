package com.hp.cmcc.bboss.storm.sidd;

import com.hp.cmcc.bboss.pub.dto.BaseRspsMsg;
import com.hp.cmcc.bboss.pub.dto.RequestParam;
import com.hp.cmcc.bboss.pub.dto.ResponseMsg;
import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbBiztRec;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import com.hp.cmcc.bboss.storm.config.ConstantInfo;
import com.hp.cmcc.bboss.storm.config.SourceConfig;
import okhttp3.MediaType;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ValidatorSIDD extends FunctorIDD {

	private static String remoteUrl = "";

	private static final String _Q_BIZT_BY_NM = "SELECT BIZ_TYPE, TYPE_ID, ENABLED, EFF_DATE, EXP_DATE FROM BIZT "
			+ "WHERE BIZ_TYPE = ?";

	public ValidatorSIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
		String remoteHost = System.getProperty("remotehost");
		String remotePort = System.getProperty("remoteport");
		remoteUrl = "http://" + remoteHost + ":" + remotePort;
	}

	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		l.debug("--------------------------start ValidatorSIDD execUdr -------------------------------------");
		_logTag = log_tag;
		String[] fields_ = udr._eFields[UdrFmt.E_24_R01_RAW_UDR].split(UdrFmt.CSV_NONE_ESC_PATTERN, -1);
		if (!_splitSIDD(udr, fields_))
			return;
		if (!_validateErrorCodeSIDD(udr))
			return;
		if (!_validateMonthSIDD(udr))
			return;
		if (!_validateStartTimeSIDD(udr))
			return;
		if (!_validateBizTypeSIDD(udr))
			return;
		if (!_validateOtherSIDD(udr))
			return;
		_miscPadding(udr);
	}

	private boolean _splitSIDD(UdrFmt udr, String[] fields) {

		Boolean result = true;
		String url = remoteUrl + "/api/v1/settle/storm/validation/sidd/split";
		MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setFields(fields);

		/*final Request request = new Request.Builder()
				.url(url)
                .post(RequestBody.create(mediaType, GsonUtil.toJsonString(requestParam)))
				.build();
		OkHttpClient client = new OkHttpClient();
		Call call = client.newCall(request);*/

		try {
			/*Response response = call.execute();

			String bodyStr = response.body().string();
			l.warn("_splitSIDD response.body() = {}", bodyStr);

			BaseRspsMsg<ResponseMsg> baseRspsMsg =
					GsonUtil.fromJsonString(bodyStr,
							new TypeToken<BaseRspsMsg<ResponseMsg>>(){}.getType());*/
			BaseRspsMsg<ResponseMsg> baseRspsMsg = splitSIDD(requestParam);
			ResponseMsg data = baseRspsMsg.getData();

			if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
				udr._eFields = data.getUdr()._eFields;
				l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				result = data.getResult();
			}

			if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
				udr._eFields = data.getUdr()._eFields;
				udr._uFields = data.getUdr()._uFields;
				fields = data.getFields();
			}
		} catch (Exception e) {
			l.error("ValidatorSIDD._splitSIDD error {}-------", _logTag,e);
			result = false;
		}

		return result;
	}


	public BaseRspsMsg<ResponseMsg> splitSIDD(RequestParam requestParam) {

//		l.info("--------------- splitSIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String[] fields = requestParam.getFields();
		String logTag = requestParam.getLogTag();
		try {
			if (fields.length < UdrFmt.S_FIELD_MIN_40) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V099_FMT_ERR;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = Integer.toString(fields.length);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("insufficent fields %d, should be ge %d", fields.length,
						UdrFmt.S_FIELD_MIN_40);
				l.warn("{} {}, {} reject", logTag,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);

				ResponseMsg responseMsg = new ResponseMsg();
				responseMsg.setResult(false);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
			udr._uFields = new String[UdrFmt.S_FIELD_CNT_40];
			for (int i = 0; i < UdrFmt.S_FIELD_MIN_40; i++) {
				if (fields[i] != null) {
					udr._uFields[i] = fields[i].trim();
				}
			}
			int attachment_idx_ = 0;
			int attachment_len_ = 0;
			for (int i = UdrFmt.S_FIELD_MIN_40 - 1; i < fields.length; i++) {
				if (fields[i] == null)
					continue;
				if (fields[i].length() > attachment_len_) {
					attachment_len_ = fields[i].length();
					attachment_idx_ = i;
				}
			}
			if (attachment_idx_ > 0) {
				udr._eFields[UdrFmt.E_25_R02_RAW_ATTACHMENT] = fields[attachment_idx_];
				l.trace("{} attachment idx:len {}:{}", logTag,
						attachment_idx_,
						attachment_len_);
			} else {
				l.trace("{} has no attachment", logTag);
			}
			udr._eFields[UdrFmt.E_17_E03_START_TM] = udr._uFields[UdrFmt.S_22_ORG_MONTH_23];
			udr._eFields[UdrFmt.E_18_E04_EC_CODE] = udr._uFields[UdrFmt.S_05_EC_CODE_06];
			udr._eFields[UdrFmt.E_19_E05_PROV] = udr._uFields[UdrFmt.S_06_EC_PROV_CODE_07];
			udr._eFields[UdrFmt.E_20_E06_ACCT_DAY] = udr._uFields[UdrFmt.S_35_START_TIME_36];
			udr._eFields[UdrFmt.E_21_E07_PP_FILE_ID] = udr._uFields[UdrFmt.S_31_FILE_ID_32];


			ResponseMsg responseMsg = new ResponseMsg();
			responseMsg.setUdr(udr);
			responseMsg.setFields(fields);
			responseMsg.setResult(true);
			return BaseRspsMsg.ok(responseMsg);
		} catch (Exception e) {
			l.error("StormValidatorSIDDController.splitSIDD error:{}", e.getMessage(), e);
			ResponseMsg responseMsg = new ResponseMsg();
			responseMsg.setResult(false);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

	}

	private boolean _validateErrorCodeSIDD(UdrFmt udr) {

		Boolean result = true;
		String url = remoteUrl + "/api/v1/settle/storm/validation/sidd/validateErrorCode";
		MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		/*final Request request = new Request.Builder()
				.url(url)
				.post(RequestBody.create(mediaType, GsonUtil.toJsonString(requestParam)))
				.build();
		OkHttpClient client = new OkHttpClient();
		Call call = client.newCall(request);*/

		try {
			/*Response response = call.execute();

			String bodyStr = response.body().string();
			l.warn(" _validateErrorCodeSIDD response.body() = {}", bodyStr);

			BaseRspsMsg<ResponseMsg> baseRspsMsg =
					GsonUtil.fromJsonString(bodyStr,
							new TypeToken<BaseRspsMsg<ResponseMsg>>(){}.getType());*/
			BaseRspsMsg<ResponseMsg> baseRspsMsg = validateErrorCodeSIDD(requestParam);
			ResponseMsg data = baseRspsMsg.getData();

			if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
				udr._eFields = data.getUdr()._eFields;
				l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				result = data.getResult();
			}

			if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
				udr._eFields = data.getUdr()._eFields;
				udr._uFields = data.getUdr()._uFields;
			}
		} catch (Exception e) {
			l.error("ValidatorSIDD._validateErrorCodeSIDD error {}-------", _logTag,e);
			result = false;
		}

		return result;


		/**
		if (UdrFmt.V000_NML_UDR.equals(udr._uFields[UdrFmt.S_00_ERROR_CODE_01]))
			return true;
		if (PubMethod.IsBlank(udr._uFields[UdrFmt.S_00_ERROR_CODE_01]))
			return true;
		udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = udr._uFields[UdrFmt.S_00_ERROR_CODE_01];
		udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_00_ERROR_CODE_01);
		udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_00_ERROR_CODE_01];
		udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ERROR_CODE_01 not %s or empty", UdrFmt.V000_NML_UDR);
		l.info("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
		return false;
		 **/
	}


	public BaseRspsMsg<ResponseMsg> validateErrorCodeSIDD(RequestParam requestParam) {
//		l.info("---------------validateErrorCodeSIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));
		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setFields(null);
		responseMsg.setResult(true);

		if (UdrFmt.V000_NML_UDR.equals(udr._uFields[UdrFmt.S_00_ERROR_CODE_01]))
			return BaseRspsMsg.ok(responseMsg);
		if (PubMethod.IsBlank(udr._uFields[UdrFmt.S_00_ERROR_CODE_01]))
			return BaseRspsMsg.ok(responseMsg);
		udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = udr._uFields[UdrFmt.S_00_ERROR_CODE_01];
		udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_00_ERROR_CODE_01);
		udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_00_ERROR_CODE_01];
		udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ERROR_CODE_01 not %s or empty", UdrFmt.V000_NML_UDR);
//		l.info("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);

		responseMsg.setUdr(udr);
		responseMsg.setResult(false);
		return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
				responseMsg);
	}

	private boolean _validateMonthSIDD(UdrFmt udr) {
		String url = remoteUrl + "/api/v1/settle/storm/validation/sidd/validateMonthSIDD";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateMonthSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();


		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}


	public BaseRspsMsg<ResponseMsg> validateMonthSIDD(RequestParam requestParam) {
//		l.info("---------------validateMonthSIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_22_ORG_MONTH_23])
				&& PubMethod.IsEmpty(udr._uFields[UdrFmt.S_23_PAID_MONTH_24])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S230_ORG_PAID_MONTH_BOTH_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_22_ORG_MONTH_23);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "both ORG_MONTH_23 and PAID_MONTH_24 are empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S260_SETTLE_MONTH_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_25_SETTLE_MONTH_26);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "SETTLE_MONTH_26 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		String ymd_1st_ = null;
		long ts_ = 0L;
		if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.S_22_ORG_MONTH_23])) {
			if (!ConstantInfo.patternYm6.matcher(udr._uFields[UdrFmt.S_22_ORG_MONTH_23]).find()) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S231_ORG_MONTH_INVALID;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_22_ORG_MONTH_23);
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_22_ORG_MONTH_23];
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ORG_MONTH_23 %s not match %s",
						udr._uFields[UdrFmt.S_22_ORG_MONTH_23], ConstantInfo.patternYm6.pattern());
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
			ymd_1st_ = udr._uFields[UdrFmt.S_22_ORG_MONTH_23] + "01";
			ts_ = PubMethod.Str2Long(ymd_1st_, PubMethod.TimeStrFmt.Fmt8);
			String ymd_cmp_ = PubMethod.Long2Str(ts_, PubMethod.TimeStrFmt.Fmt8);
			if (!ymd_1st_.equals(ymd_cmp_)) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S231_ORG_MONTH_INVALID;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_22_ORG_MONTH_23);
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_22_ORG_MONTH_23];
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ORG_MONTH_23 %s ne %s", ymd_1st_, ymd_cmp_);
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
		}

		if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.S_23_PAID_MONTH_24])) {
			if (!ConstantInfo.patternYm6.matcher(udr._uFields[UdrFmt.S_23_PAID_MONTH_24]).find()) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S241_PAID_MONTH_INVALID;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_23_PAID_MONTH_24);
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_23_PAID_MONTH_24];
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("PAID_MONTH_24 %s not match %s",
						udr._uFields[UdrFmt.S_23_PAID_MONTH_24], ConstantInfo.patternYm6.pattern());
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
			ymd_1st_ = udr._uFields[UdrFmt.S_23_PAID_MONTH_24] + "01";
			ts_ = PubMethod.Str2Long(ymd_1st_, PubMethod.TimeStrFmt.Fmt8);
			String ymd_cmp_ = PubMethod.Long2Str(ts_, PubMethod.TimeStrFmt.Fmt8);
			if (!ymd_1st_.equals(ymd_cmp_)) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S241_PAID_MONTH_INVALID;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_23_PAID_MONTH_24);
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_23_PAID_MONTH_24];
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("PAID_MONTH_24 %s ne %s", ymd_1st_, ymd_cmp_);
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
		}

		if (!ConstantInfo.patternYm6.matcher(udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S261_SETTLE_MONTH_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_25_SETTLE_MONTH_26);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("SETTLE_MONTH_26 %s not match %s",
					udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26], ConstantInfo.patternYm6.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		ymd_1st_ = udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26] + "01";
		ts_ = PubMethod.Str2Long(ymd_1st_, PubMethod.TimeStrFmt.Fmt8);
		String ymd_cmp_ = PubMethod.Long2Str(ts_, PubMethod.TimeStrFmt.Fmt8);
		if (!ymd_1st_.equals(ymd_cmp_)) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S261_SETTLE_MONTH_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_25_SETTLE_MONTH_26);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("SETTLE_MONTH_26 %s ne %s", ymd_1st_, ymd_cmp_);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		udr._eFields[UdrFmt.E_30_X05_ACNT_YM] = udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26];

		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}

	private boolean _validateStartTimeSIDD(UdrFmt udr) {
		String url = remoteUrl + "/api/v1/settle/storm/validation/sidd/validateStartTimeSIDD";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateStartTimeSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}

	public BaseRspsMsg<ResponseMsg> validateStartTimeSIDD(RequestParam requestParam) {
//		l.info("---------------validateStartTimeSIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S360_START_TIME_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_35_START_TIME_36);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "START_TIME_36 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (!ConstantInfo.patternTm14.matcher(udr._uFields[UdrFmt.S_35_START_TIME_36]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S361_START_TIME_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_35_START_TIME_36);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_35_START_TIME_36];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("START_TIME_36 %s not match %s",
					udr._uFields[UdrFmt.S_35_START_TIME_36], ConstantInfo.patternTm14.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		long ts_ = 0L;
		ts_ = PubMethod.Str2Long(udr._uFields[UdrFmt.S_35_START_TIME_36], PubMethod.TimeStrFmt.Fmt14);
		String tm_cmp_ = PubMethod.Long2Str(ts_, PubMethod.TimeStrFmt.Fmt14);
		if (!udr._uFields[UdrFmt.S_35_START_TIME_36].equals(tm_cmp_)) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S361_START_TIME_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_35_START_TIME_36);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_35_START_TIME_36];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("START_TIME_36 %s ne %s",
					udr._uFields[UdrFmt.S_35_START_TIME_36], tm_cmp_);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}

	private boolean _validateBizTypeSIDD(UdrFmt udr) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/validation/sidd/validateBizTypeSIDD";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateBizTypeSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}


	public BaseRspsMsg<ResponseMsg> validateBizTypeSIDD(RequestParam requestParam) throws Exception {
//		l.info("---------------validateBizTypeSIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));
		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_02_BIZ_TYPE_03])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S030_BIZ_TYPE_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_02_BIZ_TYPE_03);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "BIZ_TYPE_03 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			boolean has_rs_ = false;
			EdbBiztRec bizt_ = null;
//			l.info("------------------------------_Q_BIZT_BY_NM = {},   udr._uFields[UdrFmt.S_02_BIZ_TYPE_03] = {}"
//					,_Q_BIZT_BY_NM,udr._uFields[UdrFmt.S_02_BIZ_TYPE_03]);
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_BIZT_BY_NM);
			pstmt_.setString(1, udr._uFields[UdrFmt.S_02_BIZ_TYPE_03]);
			rs_ = pstmt_.executeQuery();

			while (rs_.next()) {
				bizt_ = this.extractBizt(rs_);
				has_rs_ = true;
				if (!bizt_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
					l.trace("{} START_TIME_36 {} ineffective BIZT {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
							bizt_.toGsonStr());
					bizt_ = null;
					continue;
				}
				break;
			}

			if (bizt_ != null) {
				// l.trace("{} BIZT located, {}", _logTag, bizt_.toGsonStr());
				int type_id_ = Integer.parseInt(udr.getFileType());
				if (type_id_ != bizt_._typeId) {
					udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_02_BIZ_TYPE_03);
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_02_BIZ_TYPE_03];
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S033_BIZ_TYPE_INCONSISTENT;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("BIZ_TYPE_03 %s type_id %d inconsistent with %s",
							udr._eFields[UdrFmt.E_16_E02_ERR_VAL], bizt_._typeId, udr._eFields[UdrFmt.E_04_A05_FILE_ID]);
					return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
							udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							responseMsg);
				} else {
					responseMsg.setResult(true);
					return BaseRspsMsg.ok(responseMsg);
				}
			} else {
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_02_BIZ_TYPE_03);
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_02_BIZ_TYPE_03];
				if (has_rs_) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S032_BIZ_TYPE_INEFFECTIVE;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("BIZ_TYPE_03 %s has cfg but not effective",
							udr._eFields[UdrFmt.E_16_E02_ERR_VAL]);
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S031_NO_BIZ_TYPE_CFG;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("BIZ_TYPE_03 %s no cfg",
							udr._eFields[UdrFmt.E_16_E02_ERR_VAL]);
				}
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbBiztRec extractBizt(ResultSet rs) throws SQLException {
		EdbBiztRec rec_ = new EdbBiztRec();
		int idx_ = 0;
		rec_._bizType = rs.getString(++idx_);
		rec_._typeId = rs.getInt(++idx_);
		rec_._enabled = rs.getInt(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}

	private boolean _validateOtherSIDD(UdrFmt udr) {
		String url = remoteUrl + "/api/v1/settle/storm/validation/sidd/validateOtherSIDD";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateOtherSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}


	public BaseRspsMsg<ResponseMsg> validateOtherSIDD(RequestParam requestParam){
//		l.info("---------------validateOtherSIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));
		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S040_DATA_SOURCE_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_03_DATA_SOURCE_04);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "DATA_SOURCE_04 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		if (!ConstantInfo.patternDigits.matcher(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S041_DATA_SOURCE_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_03_DATA_SOURCE_04);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.S_03_DATA_SOURCE_04];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("DATA_SOURCE_04 %s not match %s",
					udr._uFields[UdrFmt.S_03_DATA_SOURCE_04], ConstantInfo.patternDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_07_OFFER_CODE_08])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S080_OFFER_CODE_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_07_OFFER_CODE_08);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "OFFER_CODE_08 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		//过滤 EC_CODE 为空的情况  modify  lei.li3 --↓
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_05_EC_CODE_06])) {
			if ( PubMethod.IsEmpty(udr._uFields[UdrFmt.S_36_RES1_37]) || !udr._uFields[UdrFmt.S_36_RES1_37].trim().equalsIgnoreCase(SourceConfig.FILE_SOURCE_BA) ){
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S060_EC_CODE_EMPTY;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_05_EC_CODE_06);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "EC_CODE_06 is empty";
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
		}
		//过滤 EC_CODE 为空的情况  modify  lei.li3 --↑

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_15_ORDER_MODE_16])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S160_ORDER_MODE_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_15_ORDER_MODE_16);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "ORDER_MODE_16 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		//过滤 CHARGE_CODE 为空的情况  modify  lei.li3 --↓
		if ( PubMethod.IsEmpty(udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18])) {
			if( PubMethod.IsEmpty(udr._uFields[UdrFmt.S_36_RES1_37]) ||
					(!udr._uFields[UdrFmt.S_36_RES1_37].trim().equalsIgnoreCase(SourceConfig.FILE_SOURCE_BA)
							&& !udr._uFields[UdrFmt.S_36_RES1_37].trim().equalsIgnoreCase(SourceConfig.FILE_SOURCE_CC)) ){
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S180_CHARGE_ITEM_EMPTY;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_17_CHARGE_ITEM_18);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "CHARGE_ITEM_18 is empty";
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
		}
		//过滤 CHARGE_CODE 为空的情况  modify  lei.li3 --↑

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_19_AMOUNT_NOTAX_20])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S200_AMOUNT_NOTAX_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_19_AMOUNT_NOTAX_20);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "AMOUNT_NOTAX_20 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		} else if (!ConstantInfo.patternSignedDigits.matcher(udr._uFields[UdrFmt.S_19_AMOUNT_NOTAX_20]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S201_AMOUNT_NOTAX_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_19_AMOUNT_NOTAX_20);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("AMOUNT_NOTAX_20 %s not match %s",
					udr._uFields[UdrFmt.S_19_AMOUNT_NOTAX_20],ConstantInfo.patternSignedDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_20_AMOUNT_TAX_21])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S210_AMOUNT_TAX_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_20_AMOUNT_TAX_21);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "AMOUNT_TAX_21 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);

		} else if (!ConstantInfo.patternSignedDigits.matcher(udr._uFields[UdrFmt.S_20_AMOUNT_TAX_21]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S211_AMOUNT_TAX_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_20_AMOUNT_TAX_21);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("AMOUNT_TAX_21 %s not match %s",
					udr._uFields[UdrFmt.S_20_AMOUNT_TAX_21], ConstantInfo.patternSignedDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_21_TAX_RATE_22])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S220_TAX_RATE_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_21_TAX_RATE_22);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "TAX_RATE_22 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);

		} else if (!ConstantInfo.patternDigits.matcher(udr._uFields[UdrFmt.S_21_TAX_RATE_22]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S221_TAX_RATE_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_21_TAX_RATE_22);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("TAX_RATE_22 %s not match %s",
					udr._uFields[UdrFmt.S_21_TAX_RATE_22], ConstantInfo.patternDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);

		} else {
			int tax_rate_ = Integer.parseInt(udr._uFields[UdrFmt.S_21_TAX_RATE_22]);
			if (tax_rate_ < 0 || tax_rate_ > 100) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S221_TAX_RATE_INVALID;
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_21_TAX_RATE_22);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("TAX_RATE_22 %s out of range [0,100]",
						udr._uFields[UdrFmt.S_21_TAX_RATE_22]);
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);

			}
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.S_31_FILE_ID_32])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S320_FILE_ID_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_31_FILE_ID_32);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "FILE_ID_32 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		} else if (!ConstantInfo.patternSignedDigits.matcher(udr._uFields[UdrFmt.S_31_FILE_ID_32]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S321_FILE_ID_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.S_31_FILE_ID_32);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("FILE_ID_32 %s not match %s",
					udr._uFields[UdrFmt.S_31_FILE_ID_32], ConstantInfo.patternSignedDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setFields(null);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}


	private void _miscPadding(UdrFmt udr) {
		if (udr.isErr())
			return;
		//l.info("*************lei.li3**************RES1:{} DATA_SOURCE:{} *************************", udr._uFields[UdrFmt.S_36_RES1_37], udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]);
		if(!udr._uFields[UdrFmt.S_03_DATA_SOURCE_04].trim().equalsIgnoreCase(SourceConfig.DATA_SOURCE_BA_) 
				&& !udr._uFields[UdrFmt.S_03_DATA_SOURCE_04].trim().equalsIgnoreCase(SourceConfig.DATA_SOURCE_CC_)){
			if (!udr.isFeedback()) {
				udr._uFields[UdrFmt.S_36_RES1_37] = udr._uFields[UdrFmt.S_21_TAX_RATE_22];
			}
		}
	}
}
