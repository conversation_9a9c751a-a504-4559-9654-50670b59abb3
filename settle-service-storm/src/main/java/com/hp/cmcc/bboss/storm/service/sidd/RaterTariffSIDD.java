package com.hp.cmcc.bboss.storm.service.sidd;

import cn.hutool.json.JSONUtil;
import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlTariffParameterRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlTariffRateRec;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.udr.UdrDef;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import com.hp.cmcc.bboss.storm.util.ExtractStl;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class RaterTariffSIDD extends FunctorIDD {
	private static final String _Q_STL_TARIFF_RATE = "SELECT RATE_ID, TARIFF_TYPE, MATCH_MODE FROM TARIFF_RATE "
			+ "WHERE RATE_ID = ?";
	private static final String _Q_STL_TARIFF_PARAMETER_PROD_OFFER = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, OBJECT_VALUE, TARIFF_TYPE, CALC_PRIORITY, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE FROM TARIFF_PARAMETER WHERE PROD_INST_ID = ? "
			+ "AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? "
			+ "ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
	private static final String _Q_STL_TARIFF_PARAMETER_PROD_SVC = "SELECT ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, "
			+ "SVC_INST_ID, ORDER_MODE, RULE_ID, RATE_ID, CHARGE_ITEM, OBJECT_VALUE, TARIFF_TYPE, CALC_PRIORITY, "
			+ "RATE_VALUE, DEST_SOURCE, ROUTE_FLAG, EFF_DATE, EXP_DATE FROM TARIFF_PARAMETER WHERE SVC_INST_ID = ? "
			+ "AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? "
			+ "AND SVC_INST_ID IS NOT NULL ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
//	private static final String _Q_STL_TARIFF_PARAMETER_PROD_OFFER_CNT = "SELECT COUNT(*) FROM TARIFF_PARAMETER "
//			+ "WHERE PROD_INST_ID = ? AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') "
//			+ "AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? AND (? BETWEEN EFF_DATE AND EXP_DATE)";
//	private static final String _Q_STL_TARIFF_PARAMETER_PROD_SVC_CNT = "SELECT COUNT(*) FROM TARIFF_PARAMETER "
//			+ "WHERE SVC_INST_ID = ? AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') "
//			+ "AND RULE_ID = ? AND RATE_ID = ? AND TARIFF_TYPE = ? AND SVC_INST_ID IS NOT NULL "
//			+ "AND (? BETWEEN EFF_DATE AND EXP_DATE)";
	private EdbStlRateRec _rateRec;
	private String[] _inSidd;
	private Map<Integer, List<String[]>> _inSiddMap;
	//出账0，但是要结算的商品编码 ：（高精度,专线卫士）
	private static List<String> ZERO_BILL_BUT_SETTLE_OFFER = Arrays.asList("50091","50085","60006");

	public RaterTariffSIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
	}

	public void prepare(EdbStlRateRec rate_rec, String[] in_sidd, Map<Integer, List<String[]>> in_sidd_map) {
		_rateRec = rate_rec;
		_inSidd = in_sidd;
		_inSiddMap = in_sidd_map;
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		EdbStlTariffRateRec tariff_rate_ = _seekTariffRate(udr, _rateRec);
		if (tariff_rate_ == null || udr.isErr())
			return;

		List<EdbStlTariffParameterRec> parameter_list_ = _seekTariffParameter(udr, tariff_rate_);
		l.debug("商品编码:{},产品订购:{},费项:{},RULE_ID:{},RATE_ID:{},RATE_TYPE:{}.匹配到的tariff规则信息:{}",
				udr._uFields[UdrFmt.S_07_OFFER_CODE_08],
				udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
				udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18],
				udr._uFields[UdrFmt.S_32_RULE_ID_33],
				tariff_rate_._rateId,tariff_rate_._tariffType,
				JSONUtil.toJsonStr(parameter_list_));
		if (parameter_list_ == null || parameter_list_.isEmpty() || udr.isErr())
			return;

		String[] split_idd_ = null;
		for (EdbStlTariffParameterRec parameter_rec_ : parameter_list_) {
			if (split_idd_ != null) {
				split_idd_ = _ratingInTariffParameterSIDD(udr, parameter_rec_, split_idd_);
			} else {
				split_idd_ = _ratingInTariffParameterSIDD(udr, parameter_rec_, _inSidd);
			}
			if (udr.isErr())
				break;
		}
		l.debug("商品编码:{},产品订购:{},费项:{},tariff批价结果:{}",
				udr._uFields[UdrFmt.S_07_OFFER_CODE_08],
				udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
				udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18],
				JSONUtil.toJsonStr(_inSiddMap));
	}

//	public int cntParameter(String log_tag, UdrFmt udr) throws Exception {
//		_logTag = log_tag;
//		EdbStlTariffRateRec tariff_rate_ = _seekTariffRate(udr, _rateRec);
//		if (tariff_rate_ == null || udr.isErr())
//			return -1;
//		return _cntTariffParameter(udr, tariff_rate_);
//	}
//
//	private int _cntTariffParameter(UdrFmt udr, EdbStlTariffRateRec tariff_rate) throws Exception {
//		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
//		Edb edb_ = null;
//		PreparedStatement pstmt_ = null;
//		ResultSet rs_ = null;
//		int cnt_ = -1;
//		try {
//			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
//			Connection conn_ = edb_.getConnection();
//			if (tariff_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER) {
//				pstmt_ = conn_.prepareStatement(_Q_STL_TARIFF_PARAMETER_PROD_OFFER_CNT);
//				pstmt_.setString(1, udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10]);
//			} else if (tariff_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_2_PROD_SVC) {
//				pstmt_ = conn_.prepareStatement(_Q_STL_TARIFF_PARAMETER_PROD_SVC_CNT);
//				pstmt_.setString(1, udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11]);
//			} else {
//				l.warn("{} absurd!!! tariff_rate {} invalid _matchMode", _logTag, tariff_rate.toGsonStr());
//				return cnt_;
//			}
//			pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
//			pstmt_.setLong(3, Long.parseLong(udr._uFields[UdrFmt.S_32_RULE_ID_33]));
//			pstmt_.setLong(4, tariff_rate._rateId);
//			pstmt_.setInt(5, tariff_rate._tariffType);
//			pstmt_.setString(6, udr._uFields[UdrFmt.S_35_START_TIME_36]);
//			rs_ = pstmt_.executeQuery();
//			while (rs_.next()) {
//				cnt_ = rs_.getInt(1);
//			}
//			l.trace("{} tariff_rate {}, parameter cnt {}", _logTag, tariff_rate.toGsonStr(), cnt_);
//			return cnt_;
//		} finally {
//			Edb.Close(rs_, pstmt_, null);
//			if (edb_ != null) {
//				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
//			}
//		}
//	}

	private String[] _ratingInTariffParameterSIDD(UdrFmt udr, EdbStlTariffParameterRec parameter_rec, String[] in_sidd) {
		String[] parameter_sidd_ = _initParameterSIDD(udr, parameter_rec, in_sidd); //按照结算规则初始化一条结算结果数据
		if (udr.isErr())
			return null;

		long amount_total_ = 0; //存储结算单余额
		String[] split_idd_ = null;
		if (EdbStlTariffRateRec.TARIFF_TYPE_1_PROPORTION == parameter_rec._tariffType) {
			_ratingInTariffProportion(udr, parameter_rec, parameter_sidd_); //比例结算
		} else if (EdbStlTariffRateRec.TARIFF_TYPE_2_FIXEDVALUE == parameter_rec._tariffType) {
			//第一种固定值结算
			if (PubMethod.IsEmpty(udr._eFields[UdrFmt.E_23_E09_SPARE1])) {
				amount_total_ = Long.parseLong(in_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
				udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_);
			} else {
				amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
			}
			if (amount_total_ > 0 || ZERO_BILL_BUT_SETTLE_OFFER.contains(udr._uFields[UdrFmt.S_07_OFFER_CODE_08])) {
				if ("$".equals(parameter_rec._rateValue)) {
					_ratingInTariffFixedDollar(udr, parameter_rec, parameter_sidd_);
				} else {
					split_idd_ = _ratingInTariffFixedValue(udr, parameter_rec, parameter_sidd_);
				}
			}
			l.debug("TARIFF_RATE:{}:{} S_07_OFFER_CODE_08:{},S_19_AMOUNT_NOTAX_20:{},S_29_SETTLE_NOTAX_30:{}",
					udr._eFields[UdrFmt.E_04_A05_FILE_ID],udr._eFields[UdrFmt.E_05_A06_LINE_NUM],udr._uFields[UdrFmt.S_07_OFFER_CODE_08], amount_total_,
					parameter_sidd_ != null ? parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] : null);
		} else {
			String alm_ = PubMethod.FmtArgs("absurd!!! TARIFF_PARAMETER %d ukn TARIFF_TYPE %d", parameter_rec._id,
					parameter_rec._tariffType);
			l.warn("{} {}", _logTag, alm_);
			throw new RuntimeException(alm_);
		}

		if (udr.isErr())
			return null;
		if (split_idd_ != null && split_idd_ == parameter_sidd_) {
			l.trace("{} tariff_parameter id:type:val:tr:tot {}:{}:{}:{}:{} no RESIDUE, skip", _logTag, parameter_rec._id,
					parameter_rec._tariffType, parameter_rec._rateValue, parameter_sidd_[UdrFmt.S_21_TAX_RATE_22], amount_total_);
		} else {
			l.debug("{} tariff_parameter id:type:val:tr:tot {}:{}:{}:{}:{} notax:tax amount {}:{} settle {}:{}", _logTag,
					parameter_rec._id, parameter_rec._tariffType, parameter_rec._rateValue,
					parameter_sidd_[UdrFmt.S_21_TAX_RATE_22], amount_total_, parameter_sidd_[UdrFmt.S_19_AMOUNT_NOTAX_20],
					parameter_sidd_[UdrFmt.S_20_AMOUNT_TAX_21], parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30],
					parameter_sidd_[UdrFmt.S_30_SETTLE_TAX_31]);
			List<String[]> val_ = _inSiddMap.get(Integer.parseInt(in_sidd[UdrFmt.S_28_RECORD_ID_29]));
			if (EdbStlTariffRateRec.TARIFF_TYPE_2_FIXEDVALUE == parameter_rec._tariffType && amount_total_ <= 0 && !val_.isEmpty()) {
				l.trace("{} tariff_parameter id {}, amount_total_ is {}, val_.size() is {}, skip add", 
						_logTag, 
						parameter_rec._id,
						amount_total_, 
						val_.size());
			} else {
				val_.add(parameter_sidd_);
			}
		}
		return split_idd_;
	}

	private String[] _initParameterSIDD(UdrFmt udr, EdbStlTariffParameterRec parameter_rec, String[] in_sidd) {
		String[] parameter_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
		for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
			parameter_sidd_[i] = in_sidd[i];

		String tmp_ = _parseParameterValue(udr, parameter_rec, parameter_rec._objectValue, parameter_sidd_);
		if (tmp_ == null || udr.isErr())
			return null;
		parameter_sidd_[UdrFmt.S_27_IN_OBJECT_28] = tmp_;

		tmp_ = _parseParameterValue(udr, parameter_rec, parameter_rec._destSource, parameter_sidd_);
		if (tmp_ == null || udr.isErr())
			return null;
		if (!"-1".equals(tmp_))
			parameter_sidd_[UdrFmt.S_33_DEST_SOURCE_34] = tmp_;

		int key_ = Integer.parseInt(in_sidd[UdrFmt.S_28_RECORD_ID_29]);
		List<String[]> val_ = _inSiddMap.get(key_);
		if (val_ == null) {
			val_ = new ArrayList<String[]>();
			_inSiddMap.put(key_, val_);
		}
		parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
		parameter_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
		parameter_sidd_[UdrFmt.S_38_RES3_39] = parameter_rec._routeFlag;
		return parameter_sidd_;
	}

	/**
	 * 固定值结算方式1
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 * @return
	 */
	private String[] _ratingInTariffFixedValue(UdrFmt udr, EdbStlTariffParameterRec parameter_rec, String[] parameter_sidd) {
		String[] split_sidd_ = null;
		long amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
		if (amount_total_ <= 0 && !ZERO_BILL_BUT_SETTLE_OFFER.contains(udr._uFields[UdrFmt.S_07_OFFER_CODE_08])) {
			l.debug("{} {} amount_total_ {} le 0, no more process", _logTag, parameter_rec._id, amount_total_);
			return null;
		}
		MdbCli cli_ = null;
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		Transaction tx_ = null;
		MdbCli2 mdbCli2 = null;
		try {
			String hkey_ = PubMethod.FmtArgs("TF:%s:%s:%d:%d:%s:%d", // TF : 'Tariff Fixed'
					udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26].substring(0), udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._chargeItem, parameter_rec._id);
			if (TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2 = MdbAgt2.GetShparmInstance();
				jedis_ = mdbCli2.getJedis(hkey_);
			}else {
				cli_ = MdbAgt.GetShparmInstance();
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
			}
//			String hkey_ = PubMethod.FmtArgs("TF:%s:%s:%d:%d:%s:%d", // TF : 'Tariff Fixed'
//					udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6), udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
//					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._chargeItem, parameter_rec._id);

			String init_val_ = PubMethod.FmtArgs("%s|%s|%s|%s|%s", PubMethod._JvmHost,
					PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23),
					udr._eFields[UdrFmt.E_04_A05_FILE_ID], udr._eFields[UdrFmt.E_05_A06_LINE_NUM], parameter_rec._rateValue);
			long cnt_ = jedis_.hsetnx(hkey_, "INIT", init_val_);
			if (cnt_ > 0)
				l.trace("{} hsetnx {} INIT {}, cnt_ = {}", _logTag, hkey_, init_val_, cnt_);
			cnt_ = jedis_.hsetnx(hkey_, "RESIDUE", parameter_rec._rateValue);
			if (cnt_ > 0)
				l.trace("{} hsetnx {} RESIDUE {}, cnt_ = {}", _logTag, hkey_, parameter_rec._rateValue, cnt_);

			while (true) {
				jedis_.watch(hkey_);
				String residue_ = jedis_.hget(hkey_, "RESIDUE");
				long residual_ = Long.parseLong(residue_);
				if (!udr._uFields[UdrFmt.S_07_OFFER_CODE_08].equals("50091") && 
					!udr._uFields[UdrFmt.S_07_OFFER_CODE_08].equals("50085") && residual_ <= 0) {
					l.debug("{} {} residue_ {} le 0, try match next cfg", _logTag, parameter_rec._id, residual_);
					split_sidd_ = parameter_sidd;
					break;
				} else {
					
					//fixed by Nova, 20201222
					/*long deduct_ = (residual_ > amount_total_) ? amount_total_ : residual_;*/
					long deduct_ = residual_;

					tx_ = jedis_.multi();
					tx_.hset(hkey_,
							PubMethod.FmtArgs("%s|%s|%s|%s", udr._eFields[UdrFmt.E_04_A05_FILE_ID],
									udr._eFields[UdrFmt.E_05_A06_LINE_NUM], PubMethod._JvmHost,
									PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23)),
							PubMethod.FmtArgs("%d|%d|%d|%d", residual_, amount_total_, deduct_, residual_ - deduct_));
					tx_.hincrBy(hkey_, "RESIDUE", -deduct_);
					List<Object> tx_echo_list_ = tx_.exec();
					if (tx_echo_list_ == null) {
						jedis_.unwatch();
						l.info("{} {} residual_:amount_total_:deduct_ {}:{}:{}, exec canceled", _logTag, parameter_rec._id,
								residual_, amount_total_, deduct_);
						PubMethod.Sleep(1);
						continue;
					}
					l.trace("{} {} tx_echo_list_ {}", _logTag, parameter_rec._id, PubMethod.Collection2Str(tx_echo_list_, ","));
					udr._eFields[UdrFmt.E_14_A15_ACUMLT] = Long.toString(deduct_);

					parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString(deduct_);
					parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_ - deduct_);//改变余额

					split_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
					for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
						split_sidd_[i] = parameter_sidd[i];
					split_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
					split_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					break;
				}
			}
		} catch (Exception e) {
			if (jedis_ != null)
				jedis_.unwatch();
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnBrokenResource(tx_);
			} else if (cli_ != null) {
				cli_.returnBrokenResource(tx_, pool_, jedis_);
			}
			throw e;
		} finally {
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnResource(jedis_);
			} else if (cli_ != null) {
				cli_.returnResource(pool_, jedis_);
			}
		}
		return split_sidd_;
	}

	private void _ratingInTariffFixedDollar(UdrFmt udr, EdbStlTariffParameterRec parameter_rec, String[] parameter_sidd) {
		long fee_notax_ = Long.parseLong(parameter_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
		int key_ = Integer.parseInt(parameter_sidd[UdrFmt.S_28_RECORD_ID_29]);
		List<String[]> val_ = _inSiddMap.get(key_);
		if (val_ != null) {
			for (String[] prev_ : val_) {
				fee_notax_ = fee_notax_ - Long.parseLong(prev_[UdrFmt.S_29_SETTLE_NOTAX_30]);
			}
		}
		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString(fee_notax_);
		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
		udr._eFields[UdrFmt.E_23_E09_SPARE1] = "0";
	}

	/**
	 * 比例结算方式
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 */
	private void _ratingInTariffProportion(UdrFmt udr, EdbStlTariffParameterRec parameter_rec, String[] parameter_sidd) {
		BigDecimal fee_notax_ = new BigDecimal(parameter_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
		if ("$".equals(parameter_rec._rateValue)) {
			int key_ = Integer.parseInt(parameter_sidd[UdrFmt.S_28_RECORD_ID_29]);
			List<String[]> val_ = _inSiddMap.get(key_);
			if (val_ != null) {
				for (String[] prev_ : val_) {
					fee_notax_ = fee_notax_.subtract(new BigDecimal(prev_[UdrFmt.S_29_SETTLE_NOTAX_30]));
				}
			}
		} else {
			BigDecimal rate_value_ = new BigDecimal(parameter_rec._rateValue);
			if (RaterSIDD.POS_ONE.compareTo(rate_value_) < 0 || RaterSIDD.NEG_ONE.compareTo(rate_value_) > 0) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S626_TARIFF_PARAMETER_VALUE_INVALID;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_rec._rateValue);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d TARIFF ID %d RATE_VALUE %s out of range [-1,1]", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_rec._rateValue);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			fee_notax_ = fee_notax_.multiply(rate_value_);
		}
		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = fee_notax_.setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
	}

	private String _parseParameterValue(UdrFmt udr, EdbStlTariffParameterRec parameter_rec, String parameter_value, String[] sidd) {
		if (parameter_value == null) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S626_TARIFF_PARAMETER_VALUE_INVALID;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d", parameter_rec._ruleId, parameter_rec._rateId,
					parameter_rec._id);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d RATE_ID %d TARIFF ID %d PARAMETER_VALUE is null",
					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._id);
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return null;
		}
		Integer idx_ = null;
		if (RaterSIDD.ExpIddFieldName.matcher(parameter_value).find()) {
			String field_nm_ = parameter_value.substring(2, parameter_value.length() - 1);
			idx_ = UdrDef.S_NM2IDX_MAP.get(field_nm_);
			if (idx_ == null) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S626_TARIFF_PARAMETER_VALUE_INVALID;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_value);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d RATE_ID %d TARIFF ID %d invalid IDD field %s",
						parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._id, parameter_value);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return null;
			}
			if (PubMethod.IsEmpty(sidd[idx_])) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S627_TARIFF_PARAMETER_IDD_FIELD_EMPTY;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_value);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d TARIFF ID %d IDD %s IDX(%d) field empty", parameter_rec._ruleId, parameter_rec._rateId,
						parameter_rec._id, parameter_value, idx_);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return null;
			}
			// l.trace("{} cfg_value {} ==> {}", _logTag, parameter_value, sidd[idx_]);
		}
		return idx_ == null ? parameter_value : sidd[idx_] == null ? "" : sidd[idx_];
	}

	private List<EdbStlTariffParameterRec> _seekTariffParameter(UdrFmt udr, EdbStlTariffRateRec tariff_rate) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			List<EdbStlTariffParameterRec> parameter_list_ = null;
			EdbStlTariffParameterRec tariff_parameter_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			boolean has_charge_item_matched_ = false;
			pstmt_ = _prepareTariffParameterStmt(conn_, udr, tariff_rate);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				tariff_parameter_ = _extractStlTariffParameter(rs_);
				if (!tariff_parameter_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
					l.trace("{} START_TIME_36 {} ineffective, skip tariff_parameter {}", _logTag,
							udr._uFields[UdrFmt.S_35_START_TIME_36], tariff_parameter_.toGsonStr());
					continue;
				}
				if (tariff_parameter_._chargeItem.equals(udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]))
					has_charge_item_matched_ = true;
				if (parameter_list_ == null)
					parameter_list_ = new ArrayList<EdbStlTariffParameterRec>();
				parameter_list_.add(tariff_parameter_);
			}
			if (parameter_list_ != null) {
				if (has_charge_item_matched_) {
					for (int i = parameter_list_.size() - 1; i >= 0; i--) {
						tariff_parameter_ = parameter_list_.get(i);
						if ("-1".equals(tariff_parameter_._chargeItem)) {
							l.trace("{} TARIFF_PARAMETER removed because matched charge_item {} exists, {}", _logTag,
									udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], tariff_parameter_.toGsonStr());
							parameter_list_.remove(i);
						}
					}
				}
				List<String> param_json_list_ = new ArrayList<>();
				for (EdbStlTariffParameterRec param_rec_ : parameter_list_) {
					param_json_list_.add(param_rec_.toGsonStr());
				}
				l.trace("{} TARIFF_RATE {}, PARAMETER({}) {}", _logTag, tariff_rate.toGsonStr(), parameter_list_.size(),
						PubMethod.Collection2Str(param_json_list_, ","));
			} else {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%d:%d:%d:%s:%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						tariff_rate._rateId, tariff_rate._tariffType, tariff_rate._matchMode,
						tariff_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER
								? udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10] : udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
						udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %s TARIFF RATE_ID %d TYPE %d MODE %d %s %s CHARGE_ITEM_18 %s ", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						tariff_rate._rateId, tariff_rate._tariffType, tariff_rate._matchMode,
						tariff_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER ? "OFFER_ORDER_ID_10"
								: "PRODUCT_ORDER_ID_11",
						tariff_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER
								? udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10] : udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11],
						udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				if (tariff_parameter_ == null) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S624_NO_TARIFF_PARAMETER_CFG;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] += "no TARIFF_PARAMETER cfg";
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S625_TARIFF_PARAMETER_INEFFECTIVE;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] += "none effective TARIFF_PARAMETER cfg";
				}
				l.info("{} {}, {}", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return parameter_list_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private EdbStlTariffRateRec _seekTariffRate(UdrFmt udr, EdbStlRateRec rate_rec) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try {
			EdbStlTariffRateRec tariff_rate_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_TARIFF_RATE);
			pstmt_.setLong(1, rate_rec._rateId);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				tariff_rate_ = ExtractStl.extractStlTariffRate(rs_);
				break;
			}
			if (tariff_rate_ != null) {
				if (tariff_rate_._tariffType < 1 || tariff_rate_._tariffType > 3) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S622_TARIFF_RATE_TYPE_INVALID;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d", rate_rec._ruleId, rate_rec._rateId,
							tariff_rate_._tariffType);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
							"RULE %d TARIFF RATE_ID %d TARIFF_TYPE %d out of range [1,2]", rate_rec._ruleId, rate_rec._rateId,
							tariff_rate_._tariffType);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					tariff_rate_ = null;
				} else if (tariff_rate_._matchMode < 1 || tariff_rate_._matchMode > 2) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S623_TARIFF_RATE_MATCH_MODE_INVALID;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d", rate_rec._ruleId, rate_rec._rateId,
							tariff_rate_._matchMode);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
							"RULE %d TARIFF RATE_ID %d MATCH_MODE %d out of range [1,2]", rate_rec._ruleId, rate_rec._rateId,
							tariff_rate_._matchMode);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					tariff_rate_ = null;
				} else {
					// l.trace("{} RULE {} TARIFF_RATE located, {}", _logTag, rate_rec._ruleId, tariff_rate_.toGsonStr());
				}
			} else {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S621_NO_TARIFF_RATE_CFG;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d", rate_rec._ruleId, rate_rec._rateId);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d TARIFF RATE_ID %d no cfg", rate_rec._ruleId,
						rate_rec._rateId);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return tariff_rate_;
		} finally {
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}

	private PreparedStatement _prepareTariffParameterStmt(Connection conn, UdrFmt udr, EdbStlTariffRateRec tariff_rate)
			throws SQLException {
		PreparedStatement pstmt_ = null;
		if (tariff_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_1_PROD_OFFER) {
			pstmt_ = conn.prepareStatement(_Q_STL_TARIFF_PARAMETER_PROD_OFFER);
			pstmt_.setString(1, udr._uFields[UdrFmt.S_09_OFFER_ORDER_ID_10]);
		} else if (tariff_rate._matchMode == EdbStlTariffRateRec.MATCH_MODE_2_PROD_SVC) {
			pstmt_ = conn.prepareStatement(_Q_STL_TARIFF_PARAMETER_PROD_SVC);
			pstmt_.setString(1, udr._uFields[UdrFmt.S_10_PRODUCT_ORDER_ID_11]);
		} else {
			l.warn("{} absurd!!! tariff_rate {} invalid _matchMode", _logTag, tariff_rate.toGsonStr());
			return null;
		}
		pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
		pstmt_.setLong(3, Long.parseLong(udr._uFields[UdrFmt.S_32_RULE_ID_33]));
		pstmt_.setLong(4, tariff_rate._rateId);
		pstmt_.setInt(5, tariff_rate._tariffType);
		return pstmt_;
	}

	private EdbStlTariffParameterRec _extractStlTariffParameter(ResultSet rs) throws SQLException {
		EdbStlTariffParameterRec rec_ = new EdbStlTariffParameterRec();
		int idx_ = 0;
		rec_._id = rs.getLong(++idx_);
		rec_._offerCode = rs.getString(++idx_);
		rec_._productCode = rs.getString(++idx_);
		rec_._prodInstId = rs.getLong(++idx_);
		rec_._svcInstId = rs.getLong(++idx_);
		rec_._orderMode = rs.getString(++idx_);
		rec_._ruleId = rs.getLong(++idx_);
		rec_._rateId = rs.getLong(++idx_);
		rec_._chargeItem = rs.getString(++idx_);
		rec_._objectValue = rs.getString(++idx_);
		rec_._tariffType = rs.getInt(++idx_);
		rec_._calcPriority = rs.getInt(++idx_);
		rec_._rateValue = rs.getString(++idx_);
		rec_._destSource = rs.getString(++idx_);
		rec_._routeFlag = rs.getString(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}
}
