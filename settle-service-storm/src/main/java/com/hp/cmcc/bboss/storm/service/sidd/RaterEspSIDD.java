package com.hp.cmcc.bboss.storm.service.sidd;

import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlEspParameterRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlEspRateRec;
import com.hp.cmcc.bboss.pub.edb.entity.EdbStlRateRec;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.udr.UdrDef;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import com.hp.cmcc.bboss.storm.util.ExtractStl;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RaterEspSIDD extends FunctorIDD {
	private static final String _Q_STL_ESP_RATE = "SELECT RATE_ID, MATCH_MODE FROM ESP_RATE WHERE RATE_ID = ?";
	
	private static final String _Q_STL_ESP_PARAMETER_PROD_OFFER = "SELECT ID,OFFER_CODE,PRODUCT_CODE,ORDER_MODE,RULE_ID,RATE_ID,CHARGE_ITEM,CALC_PRIORITY,OBJECT_VALUE,SETT_TYPE,RATE_VALUE,"
       + "TAX_RATE,DEST_SOURCE,ROUTE_FLAG,EFF_DATE,EXP_DATE "
       + "FROM ESP_PARAMETER WHERE OFFER_CODE = ? AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? "
       + "AND RATE_ID = ? ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
	
	private static final String _Q_STL_ESP_PARAMETER_PROD_SVC = "SELECT ID,OFFER_CODE,PRODUCT_CODE,ORDER_MODE,RULE_ID,RATE_ID,CHARGE_ITEM,CALC_PRIORITY,OBJECT_VALUE,SETT_TYPE,RATE_VALUE,"
		       + "TAX_RATE,DEST_SOURCE,ROUTE_FLAG,EFF_DATE,EXP_DATE "
		       + "FROM ESP_PARAMETER WHERE PRODUCT_CODE = ? AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? "
		       + "AND RATE_ID = ? ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
	
//	private static final String _Q_STL_ODB_ESP_PARAMETER_PROD_OFFER ="SELECT ID,OFFER_CODE,PRODUCT_CODE,ORDER_MODE,RULE_ID,RATE_ID,CHARGE_ITEM,CALC_PRIORITY,OBJECT_VALUE,SETT_TYPE,RATE_VALUE,"
//		       + "TAX_RATE,DEST_SOURCE,ROUTE_FLAG,EFF_DATE,EXP_DATE "
//		       + "FROM STL_ESP_PARAMETER_T WHERE OFFER_CODE = ? AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? "
//		       + "AND RATE_ID = ? ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";
//	
//	private static final String _Q_STL_ODB_ESP_PARAMETER_PROD_SVC ="SELECT ID,OFFER_CODE,PRODUCT_CODE,ORDER_MODE,RULE_ID,RATE_ID,CHARGE_ITEM,CALC_PRIORITY,OBJECT_VALUE,SETT_TYPE,RATE_VALUE,"
//		       + "TAX_RATE,DEST_SOURCE,ROUTE_FLAG,EFF_DATE,EXP_DATE "
//		       + "FROM STL_ESP_PARAMETER_T WHERE PRODUCT_CODE = ? AND (CHARGE_ITEM = ? OR CHARGE_ITEM = '-1') AND RULE_ID = ? "
//		       + "AND RATE_ID = ? ORDER BY CALC_PRIORITY, EFF_DATE DESC, ID";

	private EdbStlRateRec _rateRec;
	private String[] _inSidd;
	private Map<Integer, List<String[]>> _inSiddMap;
	
	public RaterEspSIDD(){
		super();
		l = LoggerFactory.getLogger(this.getClass());
	}
	public void prepare(EdbStlRateRec rate_rec, String[] in_sidd, Map<Integer, List<String[]>> in_sidd_map) {
		_rateRec = rate_rec;
		_inSidd = in_sidd;
		_inSiddMap = in_sidd_map; //默认引入的是空数组
	}
	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		EdbStlEspRateRec esp_rate_ = _seekEspRate(udr, _rateRec);
		if (esp_rate_ == null || udr.isErr())
			return;
		List<EdbStlEspParameterRec> parameter_list_ = _seekEspParameter(udr, esp_rate_); //获取规则列表
		if (udr.isErr())
			return;
		
		if (parameter_list_ == null || parameter_list_.isEmpty() || udr.isErr())
			return;
		if ( !isRuleReasonable(parameter_list_) ) 
			return;
		
		String[] split_idd_ = null;
		for (EdbStlEspParameterRec parameter_rec_ : parameter_list_) {
			if (split_idd_ != null) {
				split_idd_ = _ratingInEspParameterSIDD(udr, parameter_rec_, split_idd_);
			} else {
				split_idd_ = _ratingInEspParameterSIDD(udr, parameter_rec_, _inSidd);
			}
			if (udr.isErr())
				break;
		}
		
	}
	
	private String[] _ratingInEspParameterSIDD(UdrFmt udr, EdbStlEspParameterRec parameter_rec, String[] in_sidd) {
		String[] parameter_sidd_ = _initParameterSIDD(udr, parameter_rec, in_sidd); // 初始化规则到数据
		if (udr.isErr())
			return null;

		long amount_total_ = 0;
		String[] split_idd_ = null;
		
		udr._eFields[UdrFmt.E_34_X09_TARIFF_TYPE] = String.valueOf(parameter_rec._settType);
		
		if ( EdbStlEspParameterRec.SETT_TYPE_1_STANDARD_PRICE == parameter_rec._settType) {
			//标准价
			_ratingInEspStandardPriceProportion(udr, parameter_rec, parameter_sidd_);
		}else if (EdbStlEspParameterRec.SETT_TYPE_2_PRICE == parameter_rec._settType) {
			//售价
			_ratingInEspPriceProportion(udr, parameter_rec, parameter_sidd_);
		}else if (EdbStlEspParameterRec.SETT_TYPE_3_FIXED_VALUE == parameter_rec._settType) {
			//固定值结算
			if (PubMethod.IsEmpty(udr._eFields[UdrFmt.E_23_E09_SPARE1])) {
				amount_total_ = Long.parseLong(in_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
				udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_);
			} else {
				amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
			}
			if ( amount_total_ > 0) {
				if ( "$".equals(parameter_rec._rateValue) ) {
					_ratingInEspFixedDollar(udr, parameter_rec, parameter_sidd_);
				} else {
					split_idd_ = _ratingInEspFixedValue(udr, parameter_rec, parameter_sidd_);
				}
			}
		}else {
			String alm_ = PubMethod.FmtArgs("absurd!!! ESP_PARAMETER %d ukn SETT_TYPE %d", parameter_rec._id,
					parameter_rec._settType);
			l.warn("{} {}", _logTag, alm_);
			throw new RuntimeException(alm_);
		}
		if (udr.isErr())
			return null;
		if (split_idd_ != null && split_idd_ == parameter_sidd_) {
			l.trace("{} esp_parameter id:type:val:tr:tot {}:{}:{}:{}:{} no RESIDUE, skip", _logTag, parameter_rec._id,
					parameter_rec._settType, parameter_rec._rateValue, parameter_sidd_[UdrFmt.S_21_TAX_RATE_22], amount_total_);
		} else {
			l.debug("{} esp_parameter id:type:val:tr:tot {}:{}:{}:{}:{} notax:tax amount {}:{} settle {}:{}", _logTag,
					parameter_rec._id, parameter_rec._settType, parameter_rec._rateValue,
					parameter_sidd_[UdrFmt.S_21_TAX_RATE_22], amount_total_, parameter_sidd_[UdrFmt.S_19_AMOUNT_NOTAX_20],
					parameter_sidd_[UdrFmt.S_20_AMOUNT_TAX_21], parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30],
					parameter_sidd_[UdrFmt.S_30_SETTLE_TAX_31]);
			List<String[]> val_ = _inSiddMap.get(Integer.parseInt(in_sidd[UdrFmt.S_28_RECORD_ID_29]));
			if (EdbStlEspParameterRec.SETT_TYPE_3_FIXED_VALUE == parameter_rec._settType && amount_total_ <= 0
					&& !val_.isEmpty()) {
				l.trace("{} esp_parameter id {}, amount_total_ is {}, val_.size() is {}, skip add", _logTag, parameter_rec._id,
						amount_total_, val_.size());
			} else {
				val_.add(parameter_sidd_);
			}
		}
		return split_idd_;
	}
	
	/**
	 * @param udr
	 * @param parameter_rec
	 * @param in_sidd
	 * @return
	 */
	private String[] _initParameterSIDD(UdrFmt udr, EdbStlEspParameterRec parameter_rec, String[] in_sidd) {
		String[] parameter_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
		for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
			parameter_sidd_[i] = in_sidd[i];

		String tmp_ = _parseParameterValue(udr, parameter_rec, parameter_rec._objectValue, parameter_sidd_);
		if (tmp_ == null || udr.isErr())
			return null;
		parameter_sidd_[UdrFmt.S_27_IN_OBJECT_28] = tmp_;

		tmp_ = _parseParameterValue(udr, parameter_rec, parameter_rec._destSource, parameter_sidd_);
		if (tmp_ == null || udr.isErr())
			return null;
		if (!"-1".equals(tmp_))
			parameter_sidd_[UdrFmt.S_33_DEST_SOURCE_34] = tmp_;

		int key_ = Integer.parseInt(in_sidd[UdrFmt.S_28_RECORD_ID_29]);
		List<String[]> val_ = _inSiddMap.get(key_);
		if (val_ == null) {
			val_ = new ArrayList<String[]>();
			_inSiddMap.put(key_, val_);
		}
		parameter_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
		parameter_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
		parameter_sidd_[UdrFmt.S_38_RES3_39] = parameter_rec._routeFlag;
		return parameter_sidd_;
	}
	
	private String _parseParameterValue(UdrFmt udr, EdbStlEspParameterRec parameter_rec, String parameter_value, String[] sidd) {
		if (parameter_value == null) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S642_ESP_PARAMETER_VALUE_INVALID;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d", parameter_rec._ruleId, parameter_rec._rateId,
					parameter_rec._id);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d RATE_ID %d ESP ID %d PARAMETER_VALUE is null",
					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._id);
			l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return null;
		}
		Integer idx_ = null;
		if (RaterSIDD.ExpIddFieldName.matcher(parameter_value).find()) {
			String field_nm_ = parameter_value.substring(2, parameter_value.length() - 1);
			idx_ = UdrDef.S_NM2IDX_MAP.get(field_nm_);
			if (idx_ == null) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S642_ESP_PARAMETER_VALUE_INVALID;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_value);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d RATE_ID %d ESP ID %d invalid IDD field %s",
						parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._id, parameter_value);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return null;
			}
			if (PubMethod.IsEmpty(sidd[idx_])) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S643_ESP_PARAMETER_IDD_FIELD_EMPTY;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_value);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d ESP ID %d IDD %s IDX(%d) field empty", parameter_rec._ruleId, parameter_rec._rateId,
						parameter_rec._id, parameter_value, idx_);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return null;
			}
			// l.trace("{} cfg_value {} ==> {}", _logTag, parameter_value, sidd[idx_]);
		}
		return idx_ == null ? parameter_value : sidd[idx_] == null ? "" : sidd[idx_];
	}
	/**
	 * 查询且对ESP_RATE配置表信息进行验证
	 * @param udr
	 * @param rate_rec
	 * @return
	 * @throws Exception
	 */
	private EdbStlEspRateRec _seekEspRate(UdrFmt udr, EdbStlRateRec rate_rec) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER]; //获取参数版本
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try{
			EdbStlEspRateRec esp_rate_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			pstmt_ = conn_.prepareStatement(_Q_STL_ESP_RATE);
			pstmt_.setLong(1, rate_rec._rateId);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				esp_rate_ = ExtractStl.extractStlEspRate(rs_);
				break;
			}
			if (esp_rate_ != null) {
				if(esp_rate_._matchMode < 1 || esp_rate_._matchMode > 4){
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S639_ESP_RATE_MATCH_MODE_INVALID;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d", rate_rec._ruleId, rate_rec._rateId,
							esp_rate_._matchMode);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
							"RULE %d ESP RATE_ID %d MATCH_MODE %d out of range [1,2]", rate_rec._ruleId, rate_rec._rateId,
							esp_rate_._matchMode);
					l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
					esp_rate_ = null;
				}
			}else{
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S640_NO_ESP_PARAMETER_CFG;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d", rate_rec._ruleId, rate_rec._rateId);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d ESP RATE_ID %d no cfg", rate_rec._ruleId,
						rate_rec._rateId);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return esp_rate_;
		}finally{
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}
	
	//获取规则列表
	private List<EdbStlEspParameterRec> _seekEspParameter(UdrFmt udr, EdbStlEspRateRec esp_rate) throws Exception {
		String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER]; //获取参数版本
		Edb edb_ = null;
		PreparedStatement pstmt_ = null;
		ResultSet rs_ = null;
		try{
			List<EdbStlEspParameterRec> parameter_list_ = null;
			EdbStlEspParameterRec esp_parameter_ = null;
			edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
			Connection conn_ = edb_.getConnection();
			boolean has_charge_item_matched_ = false;
			pstmt_ = _prepareEspParameterStmt(conn_, udr, esp_rate);
			rs_ = pstmt_.executeQuery();
			while (rs_.next()) {
				esp_parameter_ = _extractStlEspParameter(rs_);
				if (!esp_parameter_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
					l.trace("{} START_TIME_36 {} ineffective, skip esp_parameter {}", _logTag,
							udr._uFields[UdrFmt.S_35_START_TIME_36], esp_parameter_.toGsonStr());
					continue;
				}
				if (esp_parameter_._chargeItem.equals(udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18])) //判断结算单idd文件数据记录中是否有对应的charge_item规则，如果有对应规则，则只保留相应的规则，否则都匹配
					has_charge_item_matched_ = true;
				if (parameter_list_ == null)
					parameter_list_ = new ArrayList<EdbStlEspParameterRec>();
				parameter_list_.add(esp_parameter_);
			}
			if (parameter_list_ != null) {
				if ( has_charge_item_matched_ ) {
					//过滤规则
					for (int i=0; i < parameter_list_.size(); i++) {
						esp_parameter_ = parameter_list_.get(i);
						if ("-1".equals(esp_parameter_._chargeItem)) {
							l.trace("{} ESP_PARAMETER removed because matched charge_item {} exists, {}", _logTag,
									udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], esp_parameter_.toGsonStr());
							parameter_list_.remove(i);
						}
					}
				}
				List<String> param_json_list_ = new ArrayList<>();
				for (EdbStlEspParameterRec param_rec_ : parameter_list_) {
					param_json_list_.add(param_rec_.toGsonStr());
				}
				l.trace("{} ESP_RATE {}, PARAMETER({}) {}", _logTag, esp_rate.toGsonStr(), parameter_list_.size(),
						PubMethod.Collection2Str(param_json_list_, ","));
			} else {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%d:%d:%s:%s", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						esp_rate._rateId, esp_rate._matchMode,
						esp_rate._matchMode == EdbStlEspRateRec.MATCH_MODE_1_PROD_OFFER
								? udr._uFields[UdrFmt.S_07_OFFER_CODE_08] : udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09],
						udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %s ESP RATE_ID %d MODE %d %s %s CHARGE_ITEM_18 %s ", udr._uFields[UdrFmt.S_32_RULE_ID_33],
						esp_rate._rateId, esp_rate._matchMode,
						esp_rate._matchMode == EdbStlEspRateRec.MATCH_MODE_1_PROD_OFFER ? "OFFER_CODE_08"
								: "PRODUCT_CODE_09",
								esp_rate._matchMode == EdbStlEspRateRec.MATCH_MODE_1_PROD_OFFER
								? udr._uFields[UdrFmt.S_07_OFFER_CODE_08] : udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09],
						udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				if (esp_parameter_ == null) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S640_NO_ESP_PARAMETER_CFG;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] += "no ESP_PARAMETER cfg";
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S641_ESP_PARAMETER_INEFFECTIVE;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] += "none effective ESP_PARAMETER cfg";
				}
				l.info("{} {}, {}", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
			return parameter_list_;
		}finally{
			Edb.Close(rs_, pstmt_, null);
			if (edb_ != null) {
				TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
			}
		}
	}
	
	private PreparedStatement _prepareEspParameterStmt(Connection conn, UdrFmt udr, EdbStlEspRateRec esp_rate) throws SQLException {
		PreparedStatement pstmt_ = null;
		if (esp_rate._matchMode == EdbStlEspRateRec.MATCH_MODE_1_PROD_OFFER) {
			pstmt_ = conn.prepareStatement(_Q_STL_ESP_PARAMETER_PROD_OFFER);
			pstmt_.setString(1, udr._uFields[UdrFmt.S_07_OFFER_CODE_08]);
		} else if (esp_rate._matchMode == EdbStlEspRateRec.MATCH_MODE_2_PROD_SVC) {
			pstmt_ = conn.prepareStatement(_Q_STL_ESP_PARAMETER_PROD_SVC);
			pstmt_.setString(1, udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09]);
		} else {
			l.warn("{} absurd!!! esp_rate {} invalid _matchMode", _logTag, esp_rate.toGsonStr());
			return null;
		}
		pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
		pstmt_.setLong(3, Long.parseLong(udr._uFields[UdrFmt.S_32_RULE_ID_33]));
		pstmt_.setLong(4, esp_rate._rateId);
		return pstmt_;
	}
	
	private String[] _ratingInEspFixedValue(UdrFmt udr, EdbStlEspParameterRec parameter_rec, String[] parameter_sidd){
		String[] split_sidd_ = null;
		long amount_total_ = Long.parseLong(udr._eFields[UdrFmt.E_23_E09_SPARE1]);
		if (amount_total_ <= 0) {
			l.debug("{} {} amount_total_ {} le 0, no more process", _logTag, parameter_rec._id, amount_total_);
			return null;
		}
		MdbCli cli_ = null;
		JedisSentinelPool pool_ = null;
		Jedis jedis_ = null;
		Transaction tx_ = null;
		MdbCli2 mdbCli2 = null;
		try{
			String hkey_ = PubMethod.FmtArgs("EF:%s:%s:%d:%d:%s:%d", // EF : 'Esp Fixed'
					udr._uFields[UdrFmt.S_25_SETTLE_MONTH_26].substring(0), udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._chargeItem, parameter_rec._id);

			if (TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2 = MdbAgt2.GetShparmInstance();
				jedis_ = mdbCli2.getJedis(hkey_);
			}else {
				cli_ = MdbAgt.GetShparmInstance();
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
			}
			//插入redis中的hkey定义
//			String hkey_ = PubMethod.FmtArgs("EF:%s:%s:%d:%d:%s:%d", // EF : 'Esp Fixed'
//					udr._uFields[UdrFmt.S_35_START_TIME_36].substring(0, 6), udr._uFields[UdrFmt.S_03_DATA_SOURCE_04],
//					parameter_rec._ruleId, parameter_rec._rateId, parameter_rec._chargeItem, parameter_rec._id);

			//插入redis中的hkey对应的init的value
			String init_val_ = PubMethod.FmtArgs("%s|%s|%s|%s|%s", PubMethod._JvmHost,
					PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23),
					udr._eFields[UdrFmt.E_04_A05_FILE_ID], udr._eFields[UdrFmt.E_05_A06_LINE_NUM], parameter_rec._rateValue);
			long cnt_ = jedis_.hsetnx(hkey_, "INIT", init_val_);
			if (cnt_ > 0)
				l.trace("{} hsetnx {} INIT {}, cnt_ = {}", _logTag, hkey_, init_val_, cnt_);
			
			cnt_ = jedis_.hsetnx(hkey_, "RESIDUE", parameter_rec._rateValue);
			if (cnt_ > 0)
				l.trace("{} hsetnx {} RESIDUE {}, cnt_ = {}", _logTag, hkey_, parameter_rec._rateValue, cnt_);
			while (true) {
				jedis_.watch(hkey_);
				String residue_ = jedis_.hget(hkey_, "RESIDUE");
				long residual_ = Long.parseLong(residue_);
				if (residual_ <= 0) {
					l.debug("{} {} residue_ {} le 0, try match next cfg", _logTag, parameter_rec._id, residual_);
					split_sidd_ = parameter_sidd;
					break;
				} else {
					long deduct_ = (residual_ > amount_total_) ? amount_total_ : residual_;
					tx_ = jedis_.multi();
					tx_.hset(hkey_,
							PubMethod.FmtArgs("%s|%s|%s|%s", udr._eFields[UdrFmt.E_04_A05_FILE_ID],
									udr._eFields[UdrFmt.E_05_A06_LINE_NUM], PubMethod._JvmHost,
									PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23)),
							PubMethod.FmtArgs("%d|%d|%d|%d", residual_, amount_total_, deduct_, residual_ - deduct_));
					tx_.hincrBy(hkey_, "RESIDUE", -deduct_);
					List<Object> tx_echo_list_ = tx_.exec();
					if (tx_echo_list_ == null) {
						jedis_.unwatch();
						l.info("{} {} residual_:amount_total_:deduct_ {}:{}:{}, exec canceled", _logTag, parameter_rec._id,
								residual_, amount_total_, deduct_);
						PubMethod.Sleep(1);
						continue;
					}
					l.trace("{} {} tx_echo_list_ {}", _logTag, parameter_rec._id, PubMethod.Collection2Str(tx_echo_list_, ","));
					udr._eFields[UdrFmt.E_14_A15_ACUMLT] = Long.toString(deduct_);

					parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString(deduct_);
					parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					udr._eFields[UdrFmt.E_23_E09_SPARE1] = Long.toString(amount_total_ - deduct_);//改变余额

					split_sidd_ = new String[UdrFmt.S_FIELD_CNT_40];
					for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i)
						split_sidd_[i] = parameter_sidd[i];
					split_sidd_[UdrFmt.S_29_SETTLE_NOTAX_30] = "0";
					split_sidd_[UdrFmt.S_30_SETTLE_TAX_31] = "0";
					break;
				}
			}
		} catch (Exception e) {
			if (jedis_ != null)
				jedis_.unwatch();
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnBrokenResource(tx_);
			} else if (cli_ != null) {
				cli_.returnBrokenResource(tx_, pool_, jedis_);
			}
			throw e;
		} finally {
			if (mdbCli2 != null && TopologyCfg.GetInstance().redisClusterMode()) {
				mdbCli2.returnResource(jedis_);
			} else if (cli_ != null) {
				cli_.returnResource(pool_, jedis_);
			}
		}
		return split_sidd_;
	}
	
	private void _ratingInEspFixedDollar(UdrFmt udr, EdbStlEspParameterRec parameter_rec, String[] parameter_sidd){
		long fee_notax_ = Long.parseLong(parameter_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
		int key_ = Integer.parseInt(parameter_sidd[UdrFmt.S_28_RECORD_ID_29]);
		List<String[]> val_ = _inSiddMap.get(key_);
		if (val_ != null) {
			for (String[] prev_ : val_) {
				fee_notax_ = fee_notax_ - Long.parseLong(prev_[UdrFmt.S_29_SETTLE_NOTAX_30]);
			}
		}
		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = Long.toString(fee_notax_);
		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
		udr._eFields[UdrFmt.E_23_E09_SPARE1] = "0";
	}
	
	/**
	 * 标准价按比例结算
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 */
	private void _ratingInEspStandardPriceProportion(UdrFmt udr, EdbStlEspParameterRec parameter_rec, String[] parameter_sidd){
		BigDecimal fee_notax_ = new BigDecimal(parameter_sidd[UdrFmt.S_37_RES2_38]);
		if ("$".equals(parameter_rec._rateValue)) {
			int key_ = Integer.parseInt(parameter_sidd[UdrFmt.S_28_RECORD_ID_29]);
			List<String[]> val_ = _inSiddMap.get(key_);
			if (val_ != null) {
				for (String[] prev_ : val_) {
					fee_notax_ = fee_notax_.subtract(new BigDecimal(prev_[UdrFmt.S_29_SETTLE_NOTAX_30]));
				}
			}
		} else {
			BigDecimal rate_value_ = new BigDecimal(parameter_rec._rateValue);
			if (RaterSIDD.POS_ONE.compareTo(rate_value_) < 0 || RaterSIDD.NEG_ONE.compareTo(rate_value_) > 0) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S642_ESP_PARAMETER_VALUE_INVALID;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_rec._rateValue);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d ESP ID %d RATE_VALUE %s out of range [-1,1]", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_rec._rateValue);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			fee_notax_ = fee_notax_.multiply(rate_value_);
		}
		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = fee_notax_.setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
	}
	
	/**
	 * 售价按比例结算
	 * @param udr
	 * @param parameter_rec
	 * @param parameter_sidd
	 */
	private void _ratingInEspPriceProportion(UdrFmt udr, EdbStlEspParameterRec parameter_rec, String[] parameter_sidd){
		BigDecimal fee_notax_ = new BigDecimal(parameter_sidd[UdrFmt.S_19_AMOUNT_NOTAX_20]);
		if ("$".equals(parameter_rec._rateValue)) {
			int key_ = Integer.parseInt(parameter_sidd[UdrFmt.S_28_RECORD_ID_29]);
			List<String[]> val_ = _inSiddMap.get(key_);
			if (val_ != null) {
				for (String[] prev_ : val_) {
					fee_notax_ = fee_notax_.subtract(new BigDecimal(prev_[UdrFmt.S_29_SETTLE_NOTAX_30]));
				}
			}
		} else {
			BigDecimal rate_value_ = new BigDecimal(parameter_rec._rateValue);
			if (RaterSIDD.POS_ONE.compareTo(rate_value_) < 0 || RaterSIDD.NEG_ONE.compareTo(rate_value_) > 0) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S642_ESP_PARAMETER_VALUE_INVALID;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%d:%d:%d:%s", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_rec._rateValue);
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs(
						"RULE %d RATE_ID %d ESP ID %d RATE_VALUE %s out of range [-1,1]", parameter_rec._ruleId,
						parameter_rec._rateId, parameter_rec._id, parameter_rec._rateValue);
				l.warn("{} {}, {} reject", _logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			fee_notax_ = fee_notax_.multiply(rate_value_);
		}
		parameter_sidd[UdrFmt.S_29_SETTLE_NOTAX_30] = fee_notax_.setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
		parameter_sidd[UdrFmt.S_30_SETTLE_TAX_31] = "0";
	}
	
	/**
	 * 验证规则列表的合法性
	 * @param parameter_list
	 * @return
	 */
	private boolean isRuleReasonable(List<EdbStlEspParameterRec> parameter_list){
		boolean _reasonable = true;
		
		return _reasonable;
	}
	
	private EdbStlEspParameterRec _extractStlEspParameter(ResultSet rs) throws SQLException {
		EdbStlEspParameterRec rec_ = new EdbStlEspParameterRec();
		int idx_ = 0;
		rec_._id = rs.getLong(++idx_);
		rec_._offerCode = rs.getString(++idx_);
		rec_._productCode = rs.getString(++idx_);
		rec_._orderMode = rs.getString(++idx_);
		rec_._ruleId = rs.getLong(++idx_);
		rec_._rateId = rs.getLong(++idx_);
		rec_._chargeItem = rs.getString(++idx_);
		rec_._calcPriority = rs.getInt(++idx_);
		rec_._objectValue = rs.getString(++idx_);
		rec_._settType = rs.getInt(++idx_);
		rec_._rateValue = rs.getString(++idx_);
		rec_._taxRate = rs.getString(++idx_);
		rec_._destSource = rs.getString(++idx_);
		rec_._routeFlag = rs.getString(++idx_);
		rec_._effDate = rs.getString(++idx_);
		rec_._expDate = rs.getString(++idx_);
		return rec_;
	}
}
