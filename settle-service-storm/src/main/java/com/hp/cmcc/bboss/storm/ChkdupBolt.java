package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.storm.uidd.DupchkerUIDD;
import org.apache.storm.task.OutputCollector;
import org.apache.storm.task.TopologyContext;
import org.apache.storm.topology.OutputFieldsDeclarer;
import org.apache.storm.topology.base.BaseRichBolt;
import org.apache.storm.tuple.Fields;
import org.apache.storm.tuple.Tuple;
import org.apache.storm.tuple.Values;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class ChkdupBolt extends BaseRichBolt {
	private static final long serialVersionUID = 4152878429520928559L;
	private Logger l = null;
	private OutputCollector _collector;
	private String _rndmId;
	private String _logTag;
	private FunctorIDD _dupchker;

	@Override
	public void prepare(@SuppressWarnings("rawtypes") Map stormConf, TopologyContext context, OutputCollector collector) {
		l = LoggerFactory.getLogger(this.getClass());
		TopologyCtx.Init(context.getThisWorkerPort());
		_collector = collector;
		_rndmId = "";
		if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
			_dupchker = new DupchkerUIDD();
		}
	}

	public void execute(Tuple input) {
		UdrFmtMsg msg_ = null;
		try {
			msg_ = (UdrFmtMsg) input.getValue(0);
			_rndmId = msg_._rndmId;
			for (UdrFmt udr_ : msg_._udrFmtList) {
				_execUdr(udr_);
			}
		} catch (Exception e) {
			l.info("exception, {}, {}", _rndmId, msg_ == null ? "null" : msg_.toGsonStr(), e);
		} finally {
			try{
				_collector.emit(new Values(msg_));
				_collector.ack(input);
			}catch (Exception e){
				l.error("ChkdupBolt error", e);
			}

		}
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields(TopologyCfg.OUTPUT_FIELD_DFT));
	}

	private void _execUdr(UdrFmt udr) {
		try {
			if (udr.isErr())
				return;
			_logTag = _rndmId + ", " + udr.getBizlogUid();
			if (_dupchker != null) {
				if (udr.hasProcFlag(UdrFmt.PROC_FLAG_CHKDUP)) {
					l.trace("{} has {} flag, no need chkdup again", _logTag, UdrFmt.PROC_FLAG_CHKDUP);
					return;
				}
				_dupchker.execUdr(_logTag, udr);
				udr.setProcFlag(UdrFmt.PROC_FLAG_CHKDUP);
			}
		} catch (Exception e) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V082_EXP_CHKDUP;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = "storm chkdup exception";
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = e.getMessage();
			l.error("Chkdup异常,话单：{}. logTag: {} err_code: {}, err_val: {}",
					udr.toGsonStr(), _logTag, udr._eFields[UdrFmt.E_02_A03_ERR_CODE], udr._eFields[UdrFmt.E_16_E02_ERR_VAL], e);
		} finally {
			if (TopologyCfg.GetInstance()._bizlogFlags.contains(UdrFmt.PROC_FLAG_CHKDUP)) {
				String dup_with_ = _dupchker == null ? null : ((DupchkerUIDD) _dupchker).getDupWith();
				TopologyCtx.BizlogTimestamp(true, udr, MdbConst.BIZLOG_HK_UDR_CHKDUP, dup_with_);
			}
		}
	}
}
