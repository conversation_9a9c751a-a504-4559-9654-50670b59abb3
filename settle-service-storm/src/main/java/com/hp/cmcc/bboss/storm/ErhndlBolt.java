package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.edb.entity.EdbStlOfferRec;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.pub.udr.UdrKafkaProducerFactory;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.storm.task.OutputCollector;
import org.apache.storm.task.TopologyContext;
import org.apache.storm.topology.OutputFieldsDeclarer;
import org.apache.storm.topology.base.BaseRichBolt;
import org.apache.storm.tuple.Fields;
import org.apache.storm.tuple.Tuple;
import org.apache.storm.tuple.Values;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.jedis.Transaction;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ErhndlBolt extends BaseRichBolt {
	private static final long serialVersionUID = -8086427954113577538L;
	private static Pattern _ExpCrlf = Pattern.compile("[\r\n]");
	private Logger l = null;
	private Producer<String, String> _producer;
	private OutputCollector _collector;
	private String _rndmId;
	private Map<String, String> _routeFlagMap; // K:S_28_RECORD_ID_29, V:'S_40_ROUTE_CODE_41:S_38_RES3_39'(route_code:route_flag)
	private String _logTag;

	@Override
	public void prepare(@SuppressWarnings("rawtypes") Map stormConf, TopologyContext context, OutputCollector collector) {
		l = LoggerFactory.getLogger(this.getClass());
		TopologyCtx.Init(context.getThisWorkerPort());
		_producer = UdrKafkaProducerFactory.GetKafkaProducuer(OdbSystemParam.GetInstance()._kafkaCluster);
		_collector = collector;
		_rndmId = "";
	}

	@Override
	public void execute(Tuple input) {
		l.debug("--------------------------start ErhndlBolt execute -----------------------");
		UdrFmtMsg msg_ = null;
		try {
			msg_ = (UdrFmtMsg) input.getValue(0);
			_rndmId = msg_._rndmId;
			for (UdrFmt udr_ : msg_._udrFmtList) {
				_execUdr(udr_);
			}
		} catch (Exception e) {
			l.info("exception, {}, {}", _rndmId, msg_ == null ? "null" : msg_.toGsonStr(), e);
		} finally {
			try{
				_collector.emit(new Values(msg_));
				_collector.ack(input);
			}catch (Exception e){
				l.error("ErhndlBolt error", e);
			}

		}
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields(TopologyCfg.OUTPUT_FIELD_DFT));
	}

	private void _execUdr(UdrFmt udr) {
		//l.info("*************lei.li3**************RES1:{} DATA_SOURCE:{} *************************", udr._uFields[UdrFmt.S_36_RES1_37], udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]);
		//l.info("*************lei.li3**************S_33_DEST_SOURCE_34:{} *************************", udr._uFields[UdrFmt.S_33_DEST_SOURCE_34]);
		try {
			_logTag = _rndmId + ", " + udr.getBizlogUid();
			_initRouteFlagMap(udr);
			String topic_ = _decideTopic(udr);
			TopologyCtx.BizlogUdr(udr, MdbConst.BIZLOG_HK_UDR_GSON, MdbConst.BIZLOG_HK_UDR_ERHNDL, null);
			_produce(topic_, udr);
			_feedback(udr);
		} catch (Exception e) {
			l.error("Erhndl处理异常,[sleep 5 seconds],话单：{}. logTag: {} err_code: {}, err_val: {}",
					udr.toGsonStr(), _logTag, udr._eFields[UdrFmt.E_02_A03_ERR_CODE], udr._eFields[UdrFmt.E_16_E02_ERR_VAL], e);
			PubMethod.Sleep(5000);
		}
	}

	private void _initRouteFlagMap(UdrFmt udr) {
		if (OdbSystemParam.GetInstance()._subSystemType != OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD)
			return;
		if (_routeFlagMap == null) {
			_routeFlagMap = new HashMap<>();
		} else {
			_routeFlagMap.clear();
		}
		if (udr.isErr())
			return;

		if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.S_28_RECORD_ID_29])) {
			String val_ = PubMethod.FmtArgs("%s:%s", udr._uFields[UdrFmt.S_42_ROUTE_CODE_43],
					PubMethod.IsEmpty(udr._uFields[UdrFmt.S_38_RES3_39]) ? "1" : udr._uFields[UdrFmt.S_38_RES3_39]);
			_routeFlagMap.put(udr._uFields[UdrFmt.S_28_RECORD_ID_29], val_);
		}
		if (udr._uList != null && !udr._uList.isEmpty()) {
			for (String[] sidd_ : udr._uList) {
				if (PubMethod.IsEmpty(sidd_[UdrFmt.S_28_RECORD_ID_29]))
					continue;
				String val_ = PubMethod.FmtArgs("%s:%s", sidd_[UdrFmt.S_42_ROUTE_CODE_43],
						PubMethod.IsEmpty(sidd_[UdrFmt.S_38_RES3_39]) ? "1" : sidd_[UdrFmt.S_38_RES3_39]);
				_routeFlagMap.put(sidd_[UdrFmt.S_28_RECORD_ID_29], val_);
			}
		}

		udr._uFields[UdrFmt.S_42_ROUTE_CODE_43] = "";
		udr._uFields[UdrFmt.S_38_RES3_39] = "";
		if (udr._uList != null && !udr._uList.isEmpty()) {
			for (String[] sidd_ : udr._uList) {
				sidd_[UdrFmt.S_42_ROUTE_CODE_43] = "";
				sidd_[UdrFmt.S_38_RES3_39] = "";
			}
		}
	}

	private void _feedback(UdrFmt udr) {
		if (OdbSystemParam.GetInstance()._subSystemType != OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD)
			return;
		if (udr.isErr())
			return;
		List<UdrFmt> batch_ = new ArrayList<UdrFmt>();
		UdrFmt fbk_ = _fbkUdr(udr, udr._uFields);
		if (fbk_ != null)
			batch_.add(fbk_);
		if (udr._uList != null && !udr._uList.isEmpty()) {
			for (String[] sidd_ : udr._uList) {
				fbk_ = _fbkUdr(udr, sidd_);
				if (fbk_ != null)
					batch_.add(fbk_);
			}
		}
		if (batch_.isEmpty()) {
			return;
		}
		_adjFbkBizLog(batch_);
		_produceFbk(batch_);
	}

	private void _produceFbk(List<UdrFmt> batch) {
		UdrFmtMsg msg_ = new UdrFmtMsg();
		msg_._udrFmtList = batch;
		msg_.renewRndmId();
		String topic_ = TopologyCfg.GetInstance()._kafkaTopicRaw;
		String json_ = msg_.toGsonStr();
//		KeyedMessage<String, String> kafka_msg_ = new KeyedMessage<String, String>(topic_, msg_._rndmId, json_);
		ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic_, msg_._rndmId, json_);
		while (true) {
			try {
//				_producer.send(kafka_msg_);
				l.debug("二次结算，发送消息, topic:{}. {}",topic_,json_);
				_producer.send(producerRecord);
				break;
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("feedback exception, batch.size()=%d, sleep 3 seconds then retry", batch.size());
				l.error("{} {}, {}", _logTag, alm_, json_, e);
				PubMethod.Sleep(3000);
			}
		}
	}

	private void _adjFbkBizLog(List<UdrFmt> batch) {
		int type_id_ = Integer.parseInt(batch.get(0)._eFields[UdrFmt.E_04_A05_FILE_ID].substring(8, 11));
		String bizlog_key_cache_cnt = String.format(MdbConst.BIZLOG_KEY_CACHE_CNT, type_id_);
		String rcv_ymdh_ = batch.get(0)._eFields[UdrFmt.E_07_A08_RCV_YMDH];
		if (TopologyCfg.GetInstance().redisClusterMode()) {
			MdbCli2 mdbCli2 = MdbAgt2.GetBizlogInstance();
			JedisCluster jedisCluster = mdbCli2.getJedisCluster();
			Transaction tx_ = null;
			String k = null;
			long now_ = System.currentTimeMillis();
			Jedis jedis = null;
			try {
				// 获取一个具体节点
				jedis = mdbCli2.getJedis(batch.get(0).getBizlogLid());
				tx_ = jedis.multi();
				for (UdrFmt udr_ : batch) {
					k = udr_.getBizlogUid();
					tx_.hset(k, MdbConst.BIZLOG_HK_UDR_INIT, PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23));
					tx_.hset(k, MdbConst.BIZLOG_HK_UDR_RAW, udr_.toGsonStr());
				}
				k = batch.get(0).getBizlogLid();
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_TOT, batch.size());
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_FMT, 0);
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_RAT, 0);
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_DUP, 0);
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_NML, 0);

				jedisCluster.hincrBy(bizlog_key_cache_cnt, rcv_ymdh_, batch.size());
//				tx_.hincrBy(bizlog_key_cache_cnt, rcv_ymdh_, batch.size());
			} catch (Exception e) {
				l.error("{} jedis exception, batch.size()={}", _logTag, batch.size(), e);
				mdbCli2.returnBrokenResource(tx_);
			} finally {
				mdbCli2.returnResource( jedis);
			}
		}else {
			MdbCli cli_ = MdbAgt.GetBizlogInstance();
			JedisSentinelPool pool_ = cli_.getPool();
			Jedis jedis_ = null;
			Transaction tx_ = null;
			String k = null;
			long now_ = System.currentTimeMillis();
			try {
				jedis_ = pool_.getResource();
				tx_ = jedis_.multi();
				for (UdrFmt udr_ : batch) {
					k = udr_.getBizlogUid();
					tx_.hset(k, MdbConst.BIZLOG_HK_UDR_INIT, PubMethod.Long2Str(now_, PubMethod.TimeStrFmt.Fmt23));
					tx_.hset(k, MdbConst.BIZLOG_HK_UDR_RAW, udr_.toGsonStr());
				}
				k = batch.get(0).getBizlogLid();
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_TOT, batch.size());
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_FMT, 0);
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_RAT, 0);
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_DUP, 0);
				tx_.hincrBy(k, MdbConst.BIZLOG_HK_LOG_FBK_NML, 0);
				tx_.hincrBy(bizlog_key_cache_cnt, rcv_ymdh_, batch.size());
				List<Object> response_ = tx_.exec();
			} catch (Exception e) {
				l.error("{} jedis exception, batch.size()={}", _logTag, batch.size(), e);
				cli_.returnBrokenResource(tx_, pool_, jedis_);
				jedis_ = null;
			} finally {
				cli_.returnResource(pool_, jedis_);
			}
		}
	}

	private UdrFmt _fbkUdr(UdrFmt udr, String[] sidd) {
		if (PubMethod.IsEmpty(sidd[UdrFmt.S_28_RECORD_ID_29]))
			return null;
		String val_ = _routeFlagMap.get(sidd[UdrFmt.S_28_RECORD_ID_29]);
		String[] code_and_flag_ = val_.split(":", -1);
		String route_code_ = code_and_flag_[0];
		String route_flag_ = code_and_flag_[1];

		if (!EdbStlOfferRec.ROUTE_CODE_3_EC_RECEIVEABLE.equals(route_code_)
				&& !EdbStlOfferRec.ROUTE_CODE_4_EC_RECEIVED.equals(route_code_))
			return null;

		if ("0".equals(route_flag_)) {
			l.trace("{} {} route_flag_ is {}, no need feedback", _logTag, sidd[UdrFmt.S_28_RECORD_ID_29], route_flag_);
			return null;
		}
		UdrFmt fbk_ = new UdrFmt();
		for (int i = 0; i < UdrFmt.E_FIELD_CNT_33; ++i) {
			fbk_._eFields[i] = udr._eFields[i];
			fbk_._eFields[UdrFmt.E_23_E09_SPARE1] = null;
			fbk_._eFields[UdrFmt.E_32_X07_FEEDBACK_TAG] = sidd[UdrFmt.S_28_RECORD_ID_29];
		}
		String[] raw_ = new String[UdrFmt.S_FIELD_CNT_40];
		for (int i = 0; i < UdrFmt.S_FIELD_CNT_40; ++i) {
			raw_[i] = sidd[i];
		}
		raw_[UdrFmt.S_03_DATA_SOURCE_04] = route_code_;
		raw_[UdrFmt.S_21_TAX_RATE_22] = "6";
		// l.trace("{} fbk settle tot:notax:tax {}:{}:{}, amount notax:tax {}:{}", _logTag, settle_tot_,
		// sidd[UdrFmt.S_29_SETTLE_NOTAX_30], sidd[UdrFmt.S_30_SETTLE_TAX_31], amount_notax_, amount_tax_);
		raw_[UdrFmt.S_19_AMOUNT_NOTAX_20] = sidd[UdrFmt.S_29_SETTLE_NOTAX_30];
		raw_[UdrFmt.S_29_SETTLE_NOTAX_30] = null;
		raw_[UdrFmt.S_20_AMOUNT_TAX_21] = "0";
		raw_[UdrFmt.S_30_SETTLE_TAX_31] = null;
		raw_[UdrFmt.S_26_OUT_OBJECT_27] = raw_[UdrFmt.S_27_IN_OBJECT_28];
		raw_[UdrFmt.S_27_IN_OBJECT_28] = null;
		raw_[UdrFmt.S_28_RECORD_ID_29] = null;
		raw_[UdrFmt.S_32_RULE_ID_33] = null;
		raw_[UdrFmt.S_33_DEST_SOURCE_34] = null;
		raw_[UdrFmt.S_39_ATTACHMENT_40] = udr._eFields[UdrFmt.E_25_R02_RAW_ATTACHMENT];
		fbk_._eFields[UdrFmt.E_24_R01_RAW_UDR] = PubMethod.Join(raw_, ",");
		return fbk_;
	}

	private void _produce(String topic, UdrFmt udr) {
		ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, _rndmId, udr.toGsonStrFull());
//		KeyedMessage<String, String> kafka_msg_ = new KeyedMessage<String, String>(topic, _rndmId, udr.toGsonStrFull());
		while (true) {
			try {
//				_producer.send(kafka_msg_);
				l.debug("发送消息,topic:{}. {}",topic,udr.toGsonStrFull());
				_producer.send(producerRecord);
				break;
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("send exception, %s, sleep 3 seconds then retry",
						udr == null ? "" : udr.toGsonStr());
				l.error("{} {}", _logTag, alm_, e);
				PubMethod.Sleep(3000);
			}
		}
	}

	private String _decideTopic(UdrFmt udr) {
		if (PubMethod.IsEmpty(udr._eFields[UdrFmt.E_02_A03_ERR_CODE])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V000_NML_UDR;
		}
		if (udr._uFields != null) {
			if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
				udr._uFields[UdrFmt.U_00_ERROR_CODE_01] = udr._eFields[UdrFmt.E_02_A03_ERR_CODE];
			} else if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
				udr._uFields[UdrFmt.S_00_ERROR_CODE_01] = udr._eFields[UdrFmt.E_02_A03_ERR_CODE];
			}
		}

		TopologyCfg cfg_ = TopologyCfg.GetInstance();
		if (udr._eFields[UdrFmt.E_02_A03_ERR_CODE].equals(UdrFmt.V000_NML_UDR)) {
			udr._gsonStr = udr.toGsonStr();
			return cfg_._kafkaTopicNml;
		} else {
			if (PubMethod.IsEmpty(udr._eFields[UdrFmt.E_15_E01_ERR_IDX])) {
				udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = "-1";
			}
			if (!PubMethod.IsEmpty(udr._eFields[UdrFmt.E_22_E08_ERR_REASON])) {
				Matcher m = _ExpCrlf.matcher(udr._eFields[UdrFmt.E_22_E08_ERR_REASON]);
				if (m.find()) {
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = m.replaceAll("");
				}
			}
			if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD && udr.isFeedback()) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] += "(FBK)";
			}
			return cfg_._kafkaTopicErr;
		}
	}
}
