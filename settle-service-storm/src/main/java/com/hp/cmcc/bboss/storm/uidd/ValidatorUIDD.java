package com.hp.cmcc.bboss.storm.uidd;

import com.hp.cmcc.bboss.pub.dto.BaseRspsMsg;
import com.hp.cmcc.bboss.pub.dto.RequestParam;
import com.hp.cmcc.bboss.pub.dto.ResponseMsg;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.config.ConstantInfo;
import org.slf4j.LoggerFactory;

public class ValidatorUIDD extends FunctorIDD {

	private static String remoteUrl = "";

	public ValidatorUIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
		String remoteHost = System.getProperty("remotehost");
		String remotePort = System.getProperty("remoteport");
		remoteUrl = "http://" + remoteHost + ":" + remotePort;
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		_logTag = log_tag;
		String[] fields_ = udr._eFields[UdrFmt.E_24_R01_RAW_UDR].split(UdrFmt.CSV_NONE_ESC_PATTERN, -1);
		if (!_splitUIDD(udr, fields_))
			return;
		if (!_validateErrorCodeUIDD(udr))
			return;
		if (!_validateTimeDurUIDD(udr))
			return;
		if (!_validateDupTimeUIDD(udr))
			return;
		if (!_validateOtherUIDD(udr))
			return;
	}

	private boolean _splitUIDD(UdrFmt udr, String[] fields){

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setFields(fields);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = splitSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();
		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
//			l.info("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}

	public BaseRspsMsg<ResponseMsg> splitSIDD(RequestParam requestParam) {

//		l.info("--------------- _splitUIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String[] fields = requestParam.getFields();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();

		if (fields.length < UdrFmt.U_FIELD_MIN_56) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V099_FMT_ERR;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = Integer.toString(fields.length);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("insufficent fields %d, should ge %d", fields.length,
					UdrFmt.U_FIELD_MIN_56);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);

			responseMsg.setResult(false);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		udr._uFields = new String[UdrFmt.U_FIELD_CNT_57];
		for (int i = 0; i < UdrFmt.U_FIELD_MIN_56; i++) {
			if (fields[i] != null) {
				udr._uFields[i] = fields[i].trim();
			}
		}
		int attachment_idx_ = 0;
		int attachment_len_ = 0;
		for (int i = UdrFmt.U_FIELD_MIN_56; i < fields.length; i++) {
			if (fields[i] == null)
				continue;
			if (fields[i].length() > attachment_len_) {
				attachment_len_ = fields[i].length();
				attachment_idx_ = i;
			}
		}
		if (attachment_idx_ > 0) {
			udr._eFields[UdrFmt.E_25_R02_RAW_ATTACHMENT] = fields[attachment_idx_];
			l.trace("{} attachment idx:len {}:{}", logTag, attachment_idx_, attachment_len_);
		} else {
			l.trace("{} has no attachment", logTag);
		}
		udr._eFields[UdrFmt.E_17_E03_START_TM] = udr._uFields[UdrFmt.U_28_START_TIME_29];
		udr._eFields[UdrFmt.E_18_E04_EC_CODE] = udr._uFields[UdrFmt.U_23_EC_CODE_24];
		udr._eFields[UdrFmt.E_19_E05_PROV] = udr._uFields[UdrFmt.U_24_EC_PROV_CODE_25];
		udr._eFields[UdrFmt.E_20_E06_ACCT_DAY] = udr._uFields[UdrFmt.U_35_ACCT_DAY_36];
		udr._eFields[UdrFmt.E_21_E07_PP_FILE_ID] = udr._uFields[UdrFmt.U_34_PP_FILE_ID_35];


		responseMsg.setUdr(udr);
		responseMsg.setFields(fields);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}

	private boolean _validateErrorCodeUIDD(UdrFmt udr) {

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateErrorCodeSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}


	public BaseRspsMsg<ResponseMsg> validateErrorCodeSIDD(RequestParam requestParam) {
		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(true);

		if (UdrFmt.V000_NML_UDR.equals(udr._uFields[UdrFmt.U_00_ERROR_CODE_01]))
			return BaseRspsMsg.ok(responseMsg);
		if (PubMethod.IsBlank(udr._uFields[UdrFmt.U_00_ERROR_CODE_01]))
			return BaseRspsMsg.ok(responseMsg);
		udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = udr._uFields[UdrFmt.U_00_ERROR_CODE_01];
		udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_00_ERROR_CODE_01);
		udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_00_ERROR_CODE_01];
		udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ERROR_CODE_01 not %s or empty", UdrFmt.V000_NML_UDR);
//		l.info("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);

		responseMsg.setUdr(udr);
		responseMsg.setResult(false);
		return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
				responseMsg);
	}

	/**
	 * requestParam.getUdrExpireDays()
	 * requestParam.getUdrAheadMinutes()
	 * requestParam.getUdrDurationMax()
	 */
	private boolean _validateTimeDurUIDD(UdrFmt udr) {

		TopologyCfg cfg_ = TopologyCfg.GetInstance();
		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setUdrExpireDays(cfg_._udrExpireDays);
		requestParam.setUdrAheadMinutes(cfg_._udrAheadMinutes);
		requestParam.setUdrDurationMax(cfg_._udrDurationMax);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateTimeDurUIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();


		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}


	public BaseRspsMsg<ResponseMsg> validateTimeDurUIDD(RequestParam requestParam) {
//		l.info("--------------- validateTimeDurUIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_28_START_TIME_29])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F170_START_TIME_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_28_START_TIME_29);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "START_TIME_17 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		if (!ConstantInfo.patternTm14.matcher(udr._uFields[UdrFmt.U_28_START_TIME_29]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F170_START_TIME_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_28_START_TIME_29);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_28_START_TIME_29];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("START_TIME_17 %s not match %s",
					udr._uFields[UdrFmt.U_28_START_TIME_29], ConstantInfo.patternTm14.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		long stm14_ts_ = PubMethod.Str2Long(udr._uFields[UdrFmt.U_28_START_TIME_29], PubMethod.TimeStrFmt.Fmt14);
		String stm14_str_ = PubMethod.Long2Str(stm14_ts_, PubMethod.TimeStrFmt.Fmt14);
		if (!udr._uFields[UdrFmt.U_28_START_TIME_29].equals(stm14_str_)) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F171_START_TIME_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_28_START_TIME_29);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_28_START_TIME_29];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("START_TIME_17 %s ne %s",
					udr._uFields[UdrFmt.U_28_START_TIME_29], stm14_str_);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		long expire_ts_ = PubMethod.Str2Long(udr._eFields[UdrFmt.E_08_A09_RCV_TM], PubMethod.TimeStrFmt.Fmt14);
		//文件处理时间
		long ahead_ts_ = expire_ts_;
		expire_ts_ -= requestParam.getUdrExpireDays() * 86400L * 1000L;
//		l.info("stm14_ts_:{},expire_ts_:{}",stm14_ts_,expire_ts_);

		if (stm14_ts_ < expire_ts_) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F172_START_TIME_EXPIRE;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_28_START_TIME_29);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_28_START_TIME_29];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("START_TIME_17 %s expire %s",
					udr._uFields[UdrFmt.U_28_START_TIME_29], PubMethod.Long2Str(expire_ts_, PubMethod.TimeStrFmt.Fmt14));
//			l.info("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		ahead_ts_ += requestParam.getUdrAheadMinutes() * 60L * 1000L;
		if (stm14_ts_ > ahead_ts_) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F173_START_TIME_AHEAD;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_28_START_TIME_29);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_28_START_TIME_29];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("START_TIME_17 %s ahead %s",
					udr._uFields[UdrFmt.U_28_START_TIME_29], PubMethod.Long2Str(ahead_ts_, PubMethod.TimeStrFmt.Fmt14));
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (udr._uFields[UdrFmt.U_36_BIZ_TYPE_37].equals("CDN")) {
			udr._uFields[UdrFmt.U_29_END_TIME_30] = udr._uFields[UdrFmt.U_28_START_TIME_29];
			udr._uFields[UdrFmt.U_30_DURATION_31] = "0";
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_29_END_TIME_30])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F180_END_TIME_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_29_END_TIME_30);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "END_TIME_18 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		if (!ConstantInfo.patternTm14.matcher(udr._uFields[UdrFmt.U_29_END_TIME_30]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F180_END_TIME_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_29_END_TIME_30);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_29_END_TIME_30];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("END_TIME_18 %s not match %s",
					udr._uFields[UdrFmt.U_29_END_TIME_30], ConstantInfo.patternTm14.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		long etm14_ts_ = PubMethod.Str2Long(udr._uFields[UdrFmt.U_29_END_TIME_30], PubMethod.TimeStrFmt.Fmt14);
		String etm14_str_ = PubMethod.Long2Str(etm14_ts_, PubMethod.TimeStrFmt.Fmt14);
		if (!udr._uFields[UdrFmt.U_29_END_TIME_30].equals(etm14_str_)) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F181_END_TIME_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_29_END_TIME_30);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_29_END_TIME_30];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("END_TIME_18 %s ne %s",
					udr._uFields[UdrFmt.U_29_END_TIME_30], etm14_str_);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (udr._uFields[UdrFmt.U_29_END_TIME_30].compareTo(udr._uFields[UdrFmt.U_28_START_TIME_29]) < 0) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F184_END_TIME_LT_START;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_29_END_TIME_30);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_29_END_TIME_30];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("END_TIME_18 %s lt START_TIME_17 %s",
					udr._uFields[UdrFmt.U_29_END_TIME_30], udr._uFields[UdrFmt.U_28_START_TIME_29]);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_30_DURATION_31])) {
			l.trace("{} DURATION_19 is empty, no need biz", logTag);
			responseMsg.setUdr(udr);
			responseMsg.setResult(true);
			return BaseRspsMsg.ok(responseMsg);
		}

		if (!ConstantInfo.patternDigits.matcher(udr._uFields[UdrFmt.U_30_DURATION_31]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F190_DURATION_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_30_DURATION_31);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_30_DURATION_31];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("DURATION_19 %s not match %s",
					udr._uFields[UdrFmt.U_30_DURATION_31], ConstantInfo.patternDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		long dur_ = Long.parseLong(udr._uFields[UdrFmt.U_30_DURATION_31]);
		if (dur_ > requestParam.getUdrDurationMax()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F191_DURATION_TOO_LARGE;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_30_DURATION_31);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_30_DURATION_31];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("DURATION_19 %s gt %d, too large",
					udr._uFields[UdrFmt.U_30_DURATION_31], requestParam.getUdrDurationMax());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}

	/**
	 * requestParam.getUdrExpireDays()
	 * requestParam.getUdrAheadMinutes()
	 */
	private boolean _validateDupTimeUIDD(UdrFmt udr) {

		TopologyCfg cfg_ = TopologyCfg.GetInstance();
		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setUdrExpireDays(cfg_._udrExpireDays);
		requestParam.setUdrAheadMinutes(cfg_._udrAheadMinutes);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateDupTimeUIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();


		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}


	public BaseRspsMsg<ResponseMsg> validateDupTimeUIDD(RequestParam requestParam) {
//		l.info("---------------validateDupTimeUIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);


		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_40_DUP_TIME_41])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F290_DUP_TIME_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_40_DUP_TIME_41);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "DUP_TIME_29 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		if (udr._uFields[UdrFmt.U_40_DUP_TIME_41].equals(udr._uFields[UdrFmt.U_28_START_TIME_29])) {
			l.trace("{} DUP_TIME_29 eq START_TIME_17, no need re-validate", logTag);
			responseMsg.setUdr(udr);
			responseMsg.setResult(true);
			return BaseRspsMsg.ok(responseMsg);
		}
		if (!ConstantInfo.patternTm14.matcher(udr._uFields[UdrFmt.U_40_DUP_TIME_41]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F290_DUP_TIME_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_40_DUP_TIME_41);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_40_DUP_TIME_41];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("DUP_TIME_29 %s not match %s",
					udr._uFields[UdrFmt.U_40_DUP_TIME_41], ConstantInfo.patternTm14.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		long tm14_ts_ = PubMethod.Str2Long(udr._uFields[UdrFmt.U_40_DUP_TIME_41], PubMethod.TimeStrFmt.Fmt14);
		String tm14_str_ = PubMethod.Long2Str(tm14_ts_, PubMethod.TimeStrFmt.Fmt14);
		if (!udr._uFields[UdrFmt.U_40_DUP_TIME_41].equals(tm14_str_)) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F291_DUP_TIME_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_40_DUP_TIME_41);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_40_DUP_TIME_41];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("DUP_TIME_29 %s ne %s",
					udr._uFields[UdrFmt.U_40_DUP_TIME_41], tm14_str_);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}


		long expire_ts_ = PubMethod.Str2Long(udr._eFields[UdrFmt.E_08_A09_RCV_TM], PubMethod.TimeStrFmt.Fmt14);
		long ahead_ts_ = expire_ts_;
		expire_ts_ -= requestParam.getUdrExpireDays() * 86400L * 1000L;
		if (tm14_ts_ < expire_ts_) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F292_DUP_TIME_EXPIRE;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_40_DUP_TIME_41);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_40_DUP_TIME_41];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("DUP_TIME_29 %s expire %s",
					udr._uFields[UdrFmt.U_40_DUP_TIME_41], PubMethod.Long2Str(expire_ts_, PubMethod.TimeStrFmt.Fmt14));
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		ahead_ts_ += requestParam.getUdrAheadMinutes() * 60L * 1000L;
		if (tm14_ts_ > ahead_ts_) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F293_DUP_TIME_AHEAD;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_40_DUP_TIME_41);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_40_DUP_TIME_41];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("DUP_TIME_29 %s ahead %s",
					udr._uFields[UdrFmt.U_40_DUP_TIME_41], PubMethod.Long2Str(ahead_ts_, PubMethod.TimeStrFmt.Fmt14));
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}


	private boolean _validateOtherUIDD(UdrFmt udr) {

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = validateOtherSIDD(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return data.getResult();
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
		}

		return true;
	}

	public BaseRspsMsg<ResponseMsg> validateOtherSIDD(RequestParam requestParam){
//		l.info("---------------validateOtherUsIDD RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));
		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);


		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_31_VOLUME_UP_32])) {
			l.trace("{} VOLUME_UP_20 is empty, no need biz", logTag);
		} else if (!ConstantInfo.patternDigits.matcher(udr._uFields[UdrFmt.U_31_VOLUME_UP_32]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F200_VOLUME_UP_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_31_VOLUME_UP_32);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_31_VOLUME_UP_32];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("VOLUME_UP_20 %s not match %s",
					udr._uFields[UdrFmt.U_31_VOLUME_UP_32], ConstantInfo.patternDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33])) {
			l.trace("{} VOLUME_DOWN_21 is empty, no need biz", logTag);
		} else if (!ConstantInfo.patternDigits.matcher(udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F210_VOLUME_DOWN_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_32_VOLUME_DOWN_33);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("VOLUME_DOWN_21 %s not match %s",
					udr._uFields[UdrFmt.U_32_VOLUME_DOWN_33], ConstantInfo.patternDigits.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_35_ACCT_DAY_36])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F240_ACCT_DAY_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_35_ACCT_DAY_36);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "ACCT_DAY_24 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		if (!ConstantInfo.patternDt8.matcher(udr._uFields[UdrFmt.U_35_ACCT_DAY_36]).find()) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F240_ACCT_DAY_BAD_FMT;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_35_ACCT_DAY_36);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_35_ACCT_DAY_36];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ACCT_DAY_24 %s not match %s",
					udr._uFields[UdrFmt.U_35_ACCT_DAY_36], ConstantInfo.patternDt8.pattern());
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}
		long day_ts_ = PubMethod.Str2Long(udr._uFields[UdrFmt.U_35_ACCT_DAY_36], PubMethod.TimeStrFmt.Fmt8);
		String day_str_ = PubMethod.Long2Str(day_ts_, PubMethod.TimeStrFmt.Fmt8);
		if (!udr._uFields[UdrFmt.U_35_ACCT_DAY_36].equals(day_str_)) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F241_ACCT_DAY_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_35_ACCT_DAY_36);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_35_ACCT_DAY_36];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ACCT_DAY_24 %s ne %s",
					udr._uFields[UdrFmt.U_35_ACCT_DAY_36], day_str_);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		long now_ = System.currentTimeMillis();
		long min_ = now_ - (400L * 86400L * 1000L);
		long max_ = now_ + (100L * 86400L * 1000L);
		String acct_day_ear_ = PubMethod.Long2Str(min_, PubMethod.TimeStrFmt.Fmt8);
		String acct_day_lst_ = PubMethod.Long2Str(max_, PubMethod.TimeStrFmt.Fmt8);
		if (acct_day_ear_.compareTo(udr._uFields[UdrFmt.U_35_ACCT_DAY_36]) > 0
				|| acct_day_lst_.compareTo(udr._uFields[UdrFmt.U_35_ACCT_DAY_36]) < 0) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F241_ACCT_DAY_INVALID;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_35_ACCT_DAY_36);
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_35_ACCT_DAY_36];
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("ACCT_DAY_24 %s out of range [%s,%s]",
					udr._uFields[UdrFmt.U_35_ACCT_DAY_36], acct_day_ear_, acct_day_lst_);
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		String acnt_ym_ = udr._uFields[UdrFmt.U_35_ACCT_DAY_36].substring(0, 6);
		if (!udr._eFields[UdrFmt.E_30_X05_ACNT_YM].equals(acnt_ym_)) {
			// udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F242_ACCT_DAY_INCONSISTENT;
			// udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_35_ACCT_DAY_36);
			// udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = udr._uFields[UdrFmt.U_35_ACCT_DAY_36];
			// return false;
//			l.info("{} ACCT_DAY_24 [{}] inconsistent with acnt_ym [{}], pls be noticed", logTag,
//					udr._uFields[UdrFmt.U_35_ACCT_DAY_36], udr._eFields[UdrFmt.E_30_X05_ACNT_YM]);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_36_BIZ_TYPE_37])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F250_BIZ_TYPE_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_36_BIZ_TYPE_37);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "BIZ_TYPE_25 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_39_ACCUMULATION_KEY_40])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F280_ACCUMULATION_KEY_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_39_ACCUMULATION_KEY_40);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "ACCUMULATION_KEY_28 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		if (PubMethod.IsEmpty(udr._uFields[UdrFmt.U_34_PP_FILE_ID_35])) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.F220_PP_FILE_ID_EMPTY;
			udr._eFields[UdrFmt.E_15_E01_ERR_IDX] = Integer.toString(UdrFmt.U_34_PP_FILE_ID_35);
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "PP_FILE_ID_23 is empty";
			l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setFields(null);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}
}
