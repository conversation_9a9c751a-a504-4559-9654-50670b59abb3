package com.hp.cmcc.bboss.storm.config;

public interface SourceConfig {
	
	//数据来源
	public String FILE_SOURCE_AR = "AR";//账务
	public String FILE_SOURCE_BL = "BL";//计费
	public String FILE_SOURCE_BA = "BA";//经分
	public String FILE_SOURCE_CC = "CC";//内容计费
	public String FILE_SOURCE_ESP = "ESP"; //Esp
	//数据来源
	public String DATA_SOURCE_RECV = "1"; // 应收
	public String DATA_SOURCE_PAID = "2"; // 实收
	public String DATA_SOURCE_CPR = "5"; // CP应收 
	public String DATA_SOURCE_CPP = "6"; // CP实收
	public String DATA_SOURCE_AGENT = "7"; // 代理商
	public String DATA_SOURCE_BA_ = "8"; // 经分
	public String DATA_SOURCE_CC_ = "8";// 内容计费
	
}
