package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.mdb.MdbAgt;
import com.hp.cmcc.bboss.pub.mdb.MdbAgt2;
import com.hp.cmcc.bboss.pub.mdb.MdbCli;
import com.hp.cmcc.bboss.pub.mdb.MdbCli2;
import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import org.apache.storm.spout.SpoutOutputCollector;
import org.apache.storm.task.TopologyContext;
import org.apache.storm.topology.OutputFieldsDeclarer;
import org.apache.storm.topology.base.BaseRichSpout;
import org.apache.storm.tuple.Fields;
import org.apache.storm.tuple.Values;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisSentinelPool;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class KafkaSpout extends BaseRichSpout {
	private static final long serialVersionUID = 6495476661460360446L;
	private Logger l = null;
	private SpoutOutputCollector _collector;

	private int workPort;
	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields(TopologyCfg.OUTPUT_FIELD_DFT));
	}

	@Override
	public void open(@SuppressWarnings("rawtypes") Map conf, TopologyContext context, SpoutOutputCollector collector) {
		l = LoggerFactory.getLogger(this.getClass());
		workPort = context.getThisWorkerPort();
		TopologyCtx.Init(workPort);
		_collector = collector;
		l.debug("spout opened");
	}

	@Override
	public void nextTuple() {
		try {
			UdrFmtMsg msg_ = TopologyCtx._BlockingQueue.poll(200, TimeUnit.MILLISECONDS);

			if (msg_ == null){
				return;
			}
			_chkDup(msg_);
			String msg_id_ = null;
			if (msg_._udrFmtList.isEmpty()) {
				msg_id_ = PubMethod._JvmPid + ":" + PubMethod.NextTsSeq();
			} else {
				if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
					msg_id_ = msg_._udrFmtList.get(0).getBizlogUid();
					for (UdrFmt udr_ : msg_._udrFmtList) {
						TopologyCtx.BizlogTimestamp(true, udr_, MdbConst.BIZLOG_HK_UDR_SPOUT, null);
					}
				}
			}
			_collector.emit(new Values(msg_), msg_id_);
			l.trace("tuple {} emitted, sz {}", msg_._rndmId, msg_._udrFmtList.size());
		} catch (Exception e) {
			l.error("exception, sleep 1 seconds", e);
			PubMethod.Sleep(1000);
		}
	}

	private void _chkDup(UdrFmtMsg msg) {
		if (TopologyCfg.GetInstance().redisClusterMode()) {
			try {
				MdbCli2 mdbCli2 = MdbAgt2.GetDupchkInstance();
				JedisCluster jedisCluster = mdbCli2.getJedisCluster();
				String key_ = "R:" + msg._rndmId;
				String val_ = PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23);
				long cnt_ = jedisCluster.setnx(key_, val_);
				if (cnt_ <= 0) {
					l.warn("tuple {} dup, regard as not dup and ignore", msg._rndmId);
					msg._udrFmtList.clear();
				} else {
					jedisCluster.expire(key_, TopologyCfg.GetInstance()._spoutDupchkExpire);
				}
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("tuple %s jedis exception, regard as not dup and ignore", msg._rndmId);
				l.warn(alm_, e);
			}
		}else {
			MdbCli cli_ = MdbAgt.GetDupchkInstance();
			JedisSentinelPool pool_ = null;
			Jedis jedis_ = null;
			try {
				pool_ = cli_.getPool();
				jedis_ = pool_.getResource();
				String key_ = "R:" + msg._rndmId;
				String val_ = PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt23);
				long cnt_ = jedis_.setnx(key_, val_);
				if (cnt_ <= 0) {
					l.warn("tuple {} dup, regard as not dup and ignore", msg._rndmId);
					msg._udrFmtList.clear();
				} else {
					jedis_.expire(key_, TopologyCfg.GetInstance()._spoutDupchkExpire);
				}
			} catch (Exception e) {
				String alm_ = PubMethod.FmtArgs("tuple %s jedis exception, regard as not dup and ignore", msg._rndmId);
				l.warn(alm_, e);
				cli_.returnBrokenResource(null, pool_, jedis_);
				jedis_ = null;
			} finally {
				cli_.returnResource(pool_, jedis_);
			}
		}

	}

	@Override
	public void activate() {
		super.activate();
		l.info("spout activated");
	}

	@Override
	public void deactivate() {
		super.deactivate();
		l.info("spout deactivated");
	}

	@Override
	public void ack(Object msgId) {
		super.ack(msgId);
		//SttlTopologyCtx.BizlogTimestamp(msgId.toString(), MdbConst.BIZLOG_HK_UDR_ACK);
//		l.info("ack " + msgId);
	}

	@Override
	public void fail(Object msgId) {
		super.fail(msgId);
		//SttlTopologyCtx.BizlogTimestamp(msgId.toString(), MdbConst.BIZLOG_HK_UDR_FAIL);
//		l.info("fail " + msgId);
	}
}
