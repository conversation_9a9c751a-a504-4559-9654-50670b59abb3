package com.hp.cmcc.bboss.storm.sidd;

import cn.hutool.json.JSONUtil;
import com.hp.cmcc.bboss.pub.dto.BaseRspsMsg;
import com.hp.cmcc.bboss.pub.dto.RequestParam;
import com.hp.cmcc.bboss.pub.dto.ResponseMsg;
import com.hp.cmcc.bboss.pub.edb.Edb;
import com.hp.cmcc.bboss.pub.edb.entity.*;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.util.PubMethod;
import com.hp.cmcc.bboss.storm.FunctorIDD;
import com.hp.cmcc.bboss.storm.TopologyCfg;
import com.hp.cmcc.bboss.storm.TopologyCtx;
import com.hp.cmcc.bboss.storm.util.ExtractStl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

public class GuiderSIDD extends FunctorIDD {

	private static String remoteUrl = "";

	private Map<Long, EdbStlOfferRec> _offerMap; // K:_ruleId
	private Map<Long, EdbStlRuleRec> _ruleMap; // K:_ruleId
	private Map<Long, List<EdbStlRateRec>> _rateMap; // K:_ruleId
	private Map<Long, EdbStlObjectRec> _oObjMap; // K:rule_id
	private Map<Long, EdbStlObjectRec> _iObjMap; // K:_objectId;

	public GuiderSIDD() {
		super();
		l = LoggerFactory.getLogger(this.getClass());
		String remoteHost = System.getProperty("remotehost");
		String remotePort = System.getProperty("remoteport");
		remoteUrl = "http://" + remoteHost + ":" + remotePort;
		_offerMap = new HashMap<>();
		_ruleMap = new HashMap<>();
		_rateMap = new HashMap<>();
		_oObjMap = new HashMap<>();
		_iObjMap = new HashMap<>();
	}

	@Override
	public void execUdr(String log_tag, UdrFmt udr) throws Exception {
		l.debug("--------------------------start GuiderSIDD execUdr -------------------------------------");
		_logTag = log_tag;
		_clearMaps();
		if (udr.isErr())
			return;
		_guidingSIDD(udr);
	}

	private void _guidingSIDD(UdrFmt udr) throws Exception {
		/**1 offer表**/
		_guidOffer(udr);
		if (udr.isErr())
			return;

		/**2 规则表**/
		_guidRule(udr);
		if (udr.isErr())
			return;

		if (this.seekStlObjectlist(udr)) return;
//		for (EdbStlRuleRec stl_rule_ : _ruleMap.values()) {
//			/**3 结出 结算对象**/
//			_seekStlObject(udr, stl_rule_);
//			if (udr.isErr())
//				return;
//		}

		if (this.guidRate(udr)) return;
//		for (Long rule_id_ : _ruleMap.keySet()) {
//			/**4 结算方式**/
//			_guidRate(udr, rule_id_);
//			if (udr.isErr())
//				return;
//		}

		if (seekStlInObjectlist(udr)) return;
//		for (List<EdbStlRateRec> stl_rate_list_ : _rateMap.values()) {
//			/**5 结入对象**/
//			_seekStlInObjects(udr, stl_rate_list_);
//			if (udr.isErr())
//				return;
//		}

		/**6 **/
		_filterFeedbackOffer(udr);
		if (udr.isErr())
			return;

		/**7 **/
		_fillAuxMap(udr);
		/**8 **/
		_traceGuidingInfo(udr);
	}

	private boolean seekStlInObjectlist(UdrFmt udr) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/guiding/sidd/seekStlInObjects";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setRateMap(_rateMap);
		requestParam.setiObjMap(_iObjMap);

		TopologyCfg topologyCfg = TopologyCfg.GetInstance();

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = seekStlInObjects(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return true;
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
			_iObjMap = data.getiObjMap();
//			_iObjMap.putAll(data.getiObjMap());
		}
		return false;
	}


	public BaseRspsMsg<ResponseMsg> seekStlInObjects(RequestParam requestParam) throws Exception {
//		l.info("--------------- seekStlInObjects RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		Map<Long, List<EdbStlRateRec>> rateMap = requestParam.getRateMap();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		SeekStlInObjects seekStlInObjects = new SeekStlInObjects();
		seekStlInObjects.setLogTag(requestParam.getLogTag());
		seekStlInObjects.setiObjMap(requestParam.getiObjMap());

		for (List<EdbStlRateRec> stl_rate_list_ : rateMap.values()) {
			/**5 结入对象**/
			seekStlInObjects.seekStlInObjects(udr, stl_rate_list_);
			if (udr.isErr())
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setiObjMap(seekStlInObjects.getiObjMap());
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}
	/**
	 *
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/13 16:49
	 */
	public static class SeekStlInObjects{

		private static final String _Q_STL_OBJECT = "SELECT OBJECT_ID, OBJECT_NAME, OBJECT_TYPE, OWNER_NAME, FIELD_NAME, "
				+ "SEARCH_KEY, OBJECT_VALUE FROM OBJECT WHERE OBJECT_ID = ?";

		private String logTag;
		private Logger log;
		private Map<Long, EdbStlObjectRec> iObjMap = new HashMap<>();

		public Map<Long, EdbStlObjectRec> getiObjMap() {
			return iObjMap;
		}
		public SeekStlInObjects() {
			log = LoggerFactory.getLogger(this.getClass());
		}
		public void setiObjMap(Map<Long, EdbStlObjectRec> oObjMap) {
			this.iObjMap = oObjMap;
		}

		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		public void seekStlInObjects(UdrFmt udr, List<EdbStlRateRec> rate_list) throws Exception{
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_OBJECT);
				for (EdbStlRateRec stl_rate_ : rate_list) {
					if (iObjMap.containsKey(stl_rate_._inObjectId))
						continue;
					EdbStlObjectRec stl_object_ = null;
					pstmt_.setLong(1, stl_rate_._inObjectId);
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_object_ = ExtractStl.extractStlObject(rs_);
						break;
					}
					if (stl_object_ != null) {
						// l.trace("{} IN_OBJECT {} for RATE {} located, {}", _logTag, stl_rate_._inObjectId, stl_rate_._rateId,
						// stl_object_.toGsonStr());
						iObjMap.put(stl_object_._objectId, stl_object_);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S532_RATE_NO_COR_IN_OBJECT_CFG;
						udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RATE_ID(%d) in OBJ_ID(%d)", OfferElem.offerElem(udr),
								stl_rate_._rateId, stl_rate_._inObjectId);
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("IN_OBJECT %d for RATE %d no cfg",
								stl_rate_._inObjectId, stl_rate_._rateId);
						log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
								udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
						return;
					}
				}
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	/***
	* 
	* @author: zhanglei
	* @date: 2022/1/20 11:11
	* @param udr
	* @return: boolean
	* @exception:  
	* @update: 
	* @updatePerson: 
	*/
	private boolean guidRate(UdrFmt udr) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/guiding/sidd/guidRate";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setRuleMap(_ruleMap);
		TopologyCfg topologyCfg = TopologyCfg.GetInstance();

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = guidRate(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return true;
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
			_rateMap = data.getRateMap();
//			_rateMap.putAll(data.getRateMap());
		}
		return false;
	}


	public BaseRspsMsg<ResponseMsg> guidRate(RequestParam requestParam) throws Exception {
//		l.info("--------------- guidRate RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		Map<Long, EdbStlRuleRec> ruleMap = requestParam.getRuleMap();

		String logTag = requestParam.getLogTag();
		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		GuidRate guidRate = new GuidRate();
		guidRate.setLogTag(logTag);

		for (Long rule_id_ : ruleMap.keySet()) {
			/**4 结算方式**/
			guidRate.guidRate(udr, rule_id_);
			if (udr.isErr())
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		responseMsg.setRateMap(guidRate.rateMap);
		return BaseRspsMsg.ok(responseMsg);
	}

	public static class GuidRate{

		private static final String _Q_STL_RATE = "SELECT ID, RATE_ID, RULE_ID, IN_OBJECT_ID, RATE_CODE, RATE_TYPE, "
				+ "CALC_PRIORITY, ROUND_METHOD, EFF_DATE, EXP_DATE FROM RATE WHERE RULE_ID = ? "
				+ "ORDER BY CALC_PRIORITY ASC, EFF_DATE DESC, ID ASC";

		private String logTag;
		private Logger log;
		private Map<Long, List<EdbStlRateRec>> rateMap = new HashMap<>();

		public String getLogTag() {
			return logTag;
		}
		public GuidRate() {
			log = LoggerFactory.getLogger(this.getClass());
		}
		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		public Map<Long, List<EdbStlRateRec>> getRateMap() {
			return rateMap;
		}

		public void setRateMap(Map<Long, List<EdbStlRateRec>> rateMap) {
			this.rateMap = rateMap;
		}

		public void guidRate(UdrFmt udr, long rule_id) throws Exception {
			List<EdbStlRateRec> stl_rate_list_ = seekStlRate(udr, rule_id);
			if (udr.isErr())
				return;

			Set<Integer> rate_type_set_ = new HashSet<Integer>();
			for (EdbStlRateRec stl_rate_ : stl_rate_list_)
				rate_type_set_.add(stl_rate_._rateType);
			if (rate_type_set_.size() > 1) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID %d", OfferElem.offerElem(udr), rule_id);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S504_MULTI_RATE_TYPE_FOR_ONE_RULE;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE %d has multi RATE_TYPEs (%s)", rule_id,
						PubMethod.Collection2Str(rate_type_set_, ","));
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}
			rateMap.put(rule_id, stl_rate_list_);
		}


		private List<EdbStlRateRec> seekStlRate(UdrFmt udr, long rule_id) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				boolean has_rs_ = false;
				List<EdbStlRateRec> stl_rate_list_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_RATE);
				pstmt_.setLong(1, rule_id);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					EdbStlRateRec stl_rate_ = ExtractStl.extractStlRate(rs_);
					has_rs_ = true;
					if (!stl_rate_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
						log.trace("{} START_TIME_36 {} ineffective, skip rate {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
								stl_rate_.toGsonStr());
						stl_rate_ = null;
						continue;
					}
					if (stl_rate_list_ == null) {
						stl_rate_list_ = new ArrayList<EdbStlRateRec>();
					}
					stl_rate_list_.add(stl_rate_);
				}
				if (stl_rate_list_ != null) {
					// l.trace("{} RATE for RULE {} located, total {} elem", _logTag, rule_id, stl_rate_list_.size());
				} else {
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d)", OfferElem.offerElem(udr), rule_id);
					if (has_rs_) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S531_RATE_CFG_INEFFECTIVE;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RATE for RULE %d none effective", rule_id);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S530_NO_RATE_CFG;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RATE for RULE %d no cfg", rule_id);
					}
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				}
				return stl_rate_list_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}


	/***
	* 
	* @author: zhanglei
	* @date: 2022/1/19 17:12
	* @param udr
	* @return: boolean
	* @exception:  
	* @update: 
	* @updatePerson: 
	*/
	private boolean seekStlObjectlist(UdrFmt udr) throws Exception {

		String url = remoteUrl + "/api/v1/settle/storm/guiding/sidd/seekStlObjects";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setRuleMap(_ruleMap);
		TopologyCfg topologyCfg = TopologyCfg.GetInstance();

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);

		BaseRspsMsg<ResponseMsg> baseRspsMsg = seekStlObjects(requestParam);

		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return true;
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)) {
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
			_oObjMap = data.getoObjMap();
		}
		return false;
	}

	public BaseRspsMsg<ResponseMsg> seekStlObjects(RequestParam requestParam) throws Exception {
//		l.info("--------------- seekStlObject RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		Map<Long, EdbStlRuleRec> ruleMap = requestParam.getRuleMap();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		SeekStlObject seekStlObject = new SeekStlObject();
		seekStlObject.setLogTag(logTag);

		for (EdbStlRuleRec stl_rule_ : ruleMap.values()) {

			seekStlObject.seekStlObject(udr, stl_rule_);
			if (udr.isErr())
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
		}

		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		responseMsg.setoObjMap(seekStlObject.get_oObjMap());
		return BaseRspsMsg.ok(responseMsg);
	}

	/**
	 *
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/13 11:27
	 */
	public static class SeekStlObject{

		private static final String _Q_STL_OBJECT = "SELECT OBJECT_ID, OBJECT_NAME, OBJECT_TYPE, OWNER_NAME, FIELD_NAME, "
				+ "SEARCH_KEY, OBJECT_VALUE FROM OBJECT WHERE OBJECT_ID = ?";

		private String logTag;

		private Logger log;

		private Map<Long, EdbStlObjectRec> _oObjMap = new HashMap<>();

		public SeekStlObject() {
			log = LoggerFactory.getLogger(this.getClass());
		}

		public Map<Long, EdbStlObjectRec> get_oObjMap() {
			return _oObjMap;
		}

		public void set_oObjMap(Map<Long, EdbStlObjectRec> _oObjMap) {
			this._oObjMap = _oObjMap;
		}

		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		public void seekStlObject(UdrFmt udr, EdbStlRuleRec rule_rec) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				EdbStlObjectRec stl_object_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_OBJECT);
				pstmt_.setLong(1, rule_rec._objectId);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_object_ = ExtractStl.extractStlObject(rs_);
					break;
				}
				if (stl_object_ != null) {
					// l.trace("{} OBJECT for RULE {} located, {}", _logTag, rule_rec._ruleId, stl_object_.toGsonStr());
					_oObjMap.put(rule_rec._ruleId, stl_object_);
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S520_NO_OBJECT_CFG;
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d) out OBJ_ID(%d)", OfferElem.offerElem(udr),
							rule_rec._ruleId, rule_rec._objectId);
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s:%s:%s:%s:%d:%d",
							udr._uFields[UdrFmt.S_03_DATA_SOURCE_04], udr._uFields[UdrFmt.S_07_OFFER_CODE_08],
							udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09], udr._uFields[UdrFmt.S_15_ORDER_MODE_16], rule_rec._ruleId,
							rule_rec._objectId);
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("(out) OBJECT_ID %d for RULE %d no cfg",
							rule_rec._objectId, rule_rec._ruleId);
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				}
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	private void _traceGuidingInfo(UdrFmt udr) {
		List<String> offer_json_list_ = new ArrayList<>();
		for (EdbStlOfferRec offer_ : _offerMap.values())
			offer_json_list_.add(offer_.toGsonStr());

		List<String> rule_json_list_ = new ArrayList<>();
		for (EdbStlRuleRec rule_ : _ruleMap.values())
			rule_json_list_.add(rule_.toGsonStr());

		List<String> o_obj_json_list_ = new ArrayList<>();
		for (EdbStlObjectRec o_obj_ : _oObjMap.values())
			o_obj_json_list_.add(o_obj_.toGsonStr());

		List<String> rate_json_list_ = new ArrayList<>();
		for (List<EdbStlRateRec> rate_list_ : _rateMap.values())
			for (EdbStlRateRec rate_ : rate_list_)
				rate_json_list_.add(rate_.toGsonStr());

		List<String> i_obj_json_list_ = new ArrayList<>();
		for (EdbStlObjectRec i_obj_ : _iObjMap.values())
			i_obj_json_list_.add(i_obj_.toGsonStr());

		l.trace("{} OFFER({}) {}, RULE({}) {}, RATE({}) {}, O_OBJ({}) {}, I_OBJ({}) {}", _logTag, offer_json_list_.size(),
				PubMethod.Collection2Str(offer_json_list_, ","), rule_json_list_.size(),
				PubMethod.Collection2Str(rule_json_list_, ","), rate_json_list_.size(),
				PubMethod.Collection2Str(rate_json_list_, ","), o_obj_json_list_.size(),
				PubMethod.Collection2Str(o_obj_json_list_, ","), i_obj_json_list_.size(),
				PubMethod.Collection2Str(i_obj_json_list_, ","));
	}

	private void _fillAuxMap(UdrFmt udr) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/guiding/sidd/fillAuxMap";

		RequestParam requestParam = new RequestParam();
		requestParam.setUdr(udr);
		requestParam.setOfferMap(_offerMap);
		requestParam.setRuleMap(_ruleMap);
		requestParam.setRateMap(_rateMap);
		requestParam.setoObjMap(_oObjMap);
		requestParam.setiObjMap(_iObjMap);

		TopologyCfg topologyCfg = TopologyCfg.GetInstance();

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = fillAuxMap(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		udr._auxMap = data.getUdr()._auxMap;
	}


	public BaseRspsMsg<ResponseMsg> fillAuxMap(RequestParam requestParam) throws Exception{
//		l.info("--------------- fillAuxMap RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		Map<Long, EdbStlOfferRec> offerMap = requestParam.getOfferMap();
		Map<Long, EdbStlRuleRec> ruleMap = requestParam.getRuleMap();
		Map<Long, List<EdbStlRateRec>> rateMap = requestParam.getRateMap();
		Map<Long, EdbStlObjectRec> iObjMap = requestParam.getiObjMap();
		Map<Long, EdbStlObjectRec> oObjMap = requestParam.getoObjMap();

		if (udr._auxMap == null)
			udr._auxMap = new HashMap<>();

		List<EdbStlOfferRec> offer_list_ = new ArrayList<>(offerMap.values());
		udr._auxMap.put(UdrFmt.AUX_MAP_KEY_OFFER, offer_list_);

		for (EdbStlRuleRec rule_ : ruleMap.values())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RULE, rule_._ruleId), rule_);

		for (Map.Entry<Long, EdbStlObjectRec> ent_ : oObjMap.entrySet())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_O_OBJECT, ent_.getKey()), ent_.getValue());

		for (Map.Entry<Long, List<EdbStlRateRec>> ent_ : rateMap.entrySet())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_RATE, ent_.getKey()), ent_.getValue());

		for (EdbStlObjectRec i_obj_ : iObjMap.values())
			udr._auxMap.put(PubMethod.FmtArgs(UdrFmt.AUX_MAP_KEY_I_OBJECT, i_obj_._objectId), i_obj_);

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}

	private void _filterFeedbackOffer(UdrFmt udr) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/guiding/sidd/filterFeedbackOffer";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = filterFeedbackOffer(requestParam);
		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}
	}


	public BaseRspsMsg<ResponseMsg> filterFeedbackOffer(RequestParam requestParam) throws Exception{
//		l.info("--------------- filterFeedbackOffer RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		Map<Long, EdbStlOfferRec> offerMap = requestParam.getOfferMap();
		String logTag = requestParam.getLogTag();
		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);


		for (EdbStlOfferRec offer_ : offerMap.values()) {
			if (udr.isFeedback() && (EdbStlOfferRec.ROUTE_CODE_3_EC_RECEIVEABLE == offer_._routeCode
					|| EdbStlOfferRec.ROUTE_CODE_4_EC_RECEIVED == offer_._routeCode)) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFER.ID %d", OfferElem.offerElem(udr), offer_._id);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S503_INVALID_ROUTE_CODE_FOR_FEEDBACK_UDR;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("OFFER ID %d invalid ROUTE_CODE %d for feedback UDR",
						offer_._id, offer_._routeCode);
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
		}

		responseMsg.setUdr(udr);
		responseMsg.setOfferMap(offerMap);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}

	private void _guidRule(UdrFmt udr) throws Exception {
		String url = remoteUrl + "/api/v1/settle/storm/guiding/sidd/guidRule";

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);
		requestParam.setOfferMap(_offerMap);

		//BaseRspsMsg<ResponseMsg> baseRspsMsg = OkHttpClientUtil.callPost(url, requestParam);
		BaseRspsMsg<ResponseMsg> baseRspsMsg = guidRule(requestParam);

		ResponseMsg data = baseRspsMsg.getData();

		if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
			udr._eFields = data.getUdr()._eFields;
			l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			return;
		}

		if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
			udr._eFields = data.getUdr()._eFields;
			udr._uFields = data.getUdr()._uFields;
			_offerMap = data.getOfferMap();
			_ruleMap = data.getRuleMap();
		}

	}

	public BaseRspsMsg<ResponseMsg> guidRule(RequestParam requestParam) throws Exception {
//		l.info("--------------- guidRule RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();
		Map<Long, EdbStlOfferRec> offerMap = requestParam.getOfferMap();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

/*		EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		GuidRule guidRule = new GuidRule();
		guidRule.setLogTag(logTag);

		List<EdbStlOfferRec> offer_list_ = new ArrayList<>(offerMap.values());
		List<EdbStlRuleRec> rule_list_ = guidRule.seekStlRule(udr, offer_list_);

		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);

		guidRule.filterOfferRules(udr, rule_list_, offerMap);

		if (udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);

		guidRule.matchStlRuleItem(udr, offerMap);

		responseMsg.setUdr(udr);
		responseMsg.setResult(true);
		responseMsg.setOfferMap(offerMap);
		responseMsg.setRuleMap(guidRule.getRuleMap());
		return BaseRspsMsg.ok(responseMsg);
	}

	/**
	 * guidRule 业务类
	 * @author: zhanglei
	 * @version: 1.0
	 * @date: 2022/1/12 17:52
	 */
	public static class GuidRule{

		private static final String _Q_STL_RULE_ITEM = "SELECT ID, RULE_ID, CHARGE_ITEM, ITEM_NAME, EFF_DATE, EXP_DATE "
				+ "FROM RULE_ITEM WHERE RULE_ID = ? AND CHARGE_ITEM = ? ORDER BY EFF_DATE DESC";

		private static final String _Q_STL_RULE = "SELECT RULE_ID, RULE_NAME, OBJECT_ID, BALANCE, EFF_DATE, EXP_DATE "
				+ "FROM RULE WHERE RULE_ID = ?";

		private Map<Long, EdbStlRuleRec> ruleMap = new HashMap<>();

		private String logTag;

		private Logger log;

		public GuidRule() {
			log = LoggerFactory.getLogger(this.getClass());
		}

		public Map<Long, EdbStlRuleRec> getRuleMap() {
			return ruleMap;
		}

		public void setRuleMap(Map<Long, EdbStlRuleRec> ruleMap) {
			this.ruleMap = ruleMap;
		}

		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		public List<EdbStlRuleRec> seekStlRule(UdrFmt udr, List<EdbStlOfferRec> offer_list) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			List<EdbStlRuleRec> rule_list_ = null;
			try {
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_RULE);
				for (EdbStlOfferRec offer_ : offer_list) {
					EdbStlRuleRec stl_rule_ = null;
					pstmt_.setLong(1, offer_._ruleId);
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_rule_ = ExtractStl.extractStlRule(rs_);
						if (rule_list_ == null)
							rule_list_ = new ArrayList<EdbStlRuleRec>();
						rule_list_.add(stl_rule_);
					}
					if (stl_rule_ == null) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S510_NO_RULE_CFG;
						udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_ID(%d)", OfferElem.offerElem(udr), offer_._ruleId);
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("OFFER %d RULE_ID %d not exists", offer_._id,
								offer_._ruleId);
						log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
								udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
						return null;
					}
				}
				return rule_list_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}

		public void filterOfferRules(UdrFmt udr, List<EdbStlRuleRec> rule_list, Map<Long, EdbStlOfferRec> offerMap) {
			List<Long> offer_id_list_ = new ArrayList<>();
			for (EdbStlOfferRec offer_ : offerMap.values())
				offer_id_list_.add(offer_._id);

			List<Long> rule_id_list_ = new ArrayList<>();
			for (EdbStlRuleRec rule_ : rule_list)
				rule_id_list_.add(rule_._ruleId);

			if (offer_id_list_.size() != rule_id_list_.size()) { // assert
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S511_RULE_CFG_INEFFECTIVE;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFERs(%s) RULEs(%s)", OfferElem.offerElem(udr),
						PubMethod.Collection2Str(offer_id_list_, ","), PubMethod.Collection2Str(rule_id_list_, ","));
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("INTERNAL ERROR, count of OFFER:RULE %d:%d ne",
						offer_id_list_.size(), rule_id_list_.size());
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return;
			}

			for (EdbStlRuleRec rule_ : rule_list) {
				if (!rule_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
					EdbStlOfferRec offer_ = offerMap.get(rule_._ruleId);
					log.trace("{} START_TIME_36 {} ineffective, skip RULE {}, skip OFFER {}", logTag,
							udr._uFields[UdrFmt.S_35_START_TIME_36], rule_.toGsonStr(), offer_.toGsonStr());
					offerMap.remove(rule_._ruleId);
				} else {
					ruleMap.put(rule_._ruleId, rule_);
				}
			}

			if (ruleMap.isEmpty()) {
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S511_RULE_CFG_INEFFECTIVE;
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s OFFERs(%s) RULEs(%s)", OfferElem.offerElem(udr),
						PubMethod.Collection2Str(offer_id_list_, ","), PubMethod.Collection2Str(rule_id_list_, ","));
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("%d RULEs but none effective", rule_id_list_.size());
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
		}

		public void matchStlRuleItem(UdrFmt udr, Map<Long, EdbStlOfferRec> offerMap) throws Exception {
			int cnt_nonexits_ = 0;
			int cnt_ineffective_ = 0;
			AtomicBoolean has_rs_ = new AtomicBoolean(false);
			List<Long> rule_id_list_ = new ArrayList<>(ruleMap.keySet());

			for (Long rule_id_ : rule_id_list_) {
				EdbStlRuleRec rule_ = ruleMap.get(rule_id_);
				EdbStlOfferRec offer_ = offerMap.get(rule_id_);
				has_rs_.set(false);
				EdbStlRuleItemRec stl_rule_item_ = this.matchOneStlRuleItem(udr, rule_, has_rs_, logTag);
				if (stl_rule_item_ == null) {
					if (has_rs_.get())
						++cnt_ineffective_;
					else
						++cnt_nonexits_;
					ruleMap.remove(rule_id_);
					offerMap.remove(rule_id_);
					if (offer_ != null && udr._uFields != null && udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18] != null) {
						log.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE {}, skip OFFER {}", logTag,
								udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], JSONUtil.toJsonStr(rule_), JSONUtil.toJsonStr(offer_));
					} else if (udr._uFields != null && udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18] != null) {
						log.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE null or skip OFFER null", logTag,
								udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
					}else {
						log.debug("{} udr._uFields is null , skip RULE null or skip OFFER null", logTag);
					}

				}
			}

			if (ruleMap.isEmpty()) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = PubMethod.FmtArgs("%s RULE_IDs(%s) CHARGE_ITEM(%s)", OfferElem.offerElem(udr),
						PubMethod.Collection2Str(rule_id_list_, ","), udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				if (cnt_nonexits_ == 0) {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S513_RULE_ITEM_INEFFECTIVE;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE_IDs(%s) RULE_ITEM none effective",
							PubMethod.Collection2Str(rule_id_list_, ","));
				} else {
					udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S512_NO_RULE_ITEM_CFG;
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("RULE_IDs(%s) RULE_ITEM nonexist:ineffective %d:%d",
							PubMethod.Collection2Str(rule_id_list_, ","), cnt_nonexits_, cnt_ineffective_);
				}
				log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON], udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
			}
		}

		private EdbStlRuleItemRec matchOneStlRuleItem(UdrFmt udr, EdbStlRuleRec stl_rule, AtomicBoolean has_rs, String logTag) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			try {
				EdbStlRuleItemRec stl_rule_item_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				/**
				 l.info("-----------------GuiderSIDD._matchOneStlRuleItem._Q_STL_RULE_ITEM = {}",
				 _Q_STL_RULE_ITEM);
				 l.info("-----------------GuiderSIDD._matchOneStlRuleItem.stl_rule._ruleId = {}",
				 stl_rule._ruleId);
				 l.info("-----------------GuiderSIDD._matchOneStlRuleItem.udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18] = {}",
				 udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				 **/
				pstmt_ = conn_.prepareStatement(_Q_STL_RULE_ITEM);
				pstmt_.setLong(1, stl_rule._ruleId);
				pstmt_.setString(2, udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18]);
				rs_ = pstmt_.executeQuery();
				while (rs_.next()) {
					stl_rule_item_ = ExtractStl.extractStlRuleItem(rs_);
					has_rs.set(true);
					if (!stl_rule_item_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
						log.trace("{} START_TIME_36 {} ineffective, skip rule_item {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
								stl_rule_item_.toGsonStr());
						stl_rule_item_ = null;
						continue;
					}
					break;
				}
				if (stl_rule_item_ == null) {
					pstmt_.setString(2, "-1");
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_rule_item_ = ExtractStl.extractStlRuleItem(rs_);
						has_rs.set(true);
						if (!stl_rule_item_.isEffective(udr._uFields[UdrFmt.S_35_START_TIME_36])) {
							log.trace("{} START_TIME_36 {} ineffective, skip rule_item {}", logTag,
									udr._uFields[UdrFmt.S_35_START_TIME_36], stl_rule_item_.toGsonStr());
							stl_rule_item_ = null;
							continue;
						}
						break;
					}
				}
				if (stl_rule_item_ == null) {
					// l.debug("{} CHARGE_ITEM {} no RULE_ITEM matches, skip RULE {}", _logTag,
					// udr._uFields[UdrFmt.S_17_CHARGE_ITEM_18], stl_rule.toGsonStr());
				}
				return stl_rule_item_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	private void _guidOffer(UdrFmt udr) throws Exception {

		RequestParam requestParam = new RequestParam();
		requestParam.setLogTag(_logTag);
		requestParam.setUdr(udr);

        try {

			BaseRspsMsg<ResponseMsg> baseRspsMsg = guidOffer(requestParam);
            ResponseMsg data = baseRspsMsg.getData();

            if (baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00001_FAILE)){
				udr._eFields = data.getUdr()._eFields;
                l.warn("{} {}, {} reject", _logTag, baseRspsMsg.getBizDesc(), udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
                return;
            }

            if(baseRspsMsg.getBizCode().equals(BaseRspsMsg.BIZ_CODE_00000_SUCCESS)){
                udr._eFields = data.getUdr()._eFields;
                udr._uFields = data.getUdr()._uFields;
                _offerMap = data.getOfferMap();

            }
        } catch (IOException e) {
            l.error("GuiderSIDD._guidOffer error {}-------", _logTag,e);
        }
	}

	public BaseRspsMsg<ResponseMsg> guidOffer(RequestParam requestParam) throws Exception {

//		l.info("--------------- guidOffer RequestParam requestParam = {}", GsonUtil.toJsonString(requestParam));

		UdrFmt udr = requestParam.getUdr();
		String logTag = requestParam.getLogTag();

		ResponseMsg responseMsg = new ResponseMsg();
		responseMsg.setUdr(udr);
		responseMsg.setResult(false);

		/*EdbShpmCtx._InitEdbShpmPool(requestParam.getShpmBaseDir(),
				requestParam.getShpmCacheSize());*/

		GuidOffer guidOffer = new GuidOffer();
		Map<Long, EdbStlOfferRec> offerMap = guidOffer.getOfferMap();
		guidOffer.setLogTag(logTag);
		List<EdbStlOfferRec> offer_list_ = guidOffer.seekStlOffer(udr);



		if (offer_list_ == null || udr.isErr())
			return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
					udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
					responseMsg);
		List<Long> offer_id_list_ = new ArrayList<>();
		List<Long> rule_id_list_ = new ArrayList<>();
		for (EdbStlOfferRec offer_ : offer_list_) {
			offer_id_list_.add(offer_._id);
			rule_id_list_.add(offer_._ruleId);
		}

		for (EdbStlOfferRec offer_ : offer_list_) {
			if (offerMap.containsKey(offer_._ruleId)) {
				udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = OfferElem.offerElem(udr);
				udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S502_MULTI_OFFER_RULE_ID_DUP;
				udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("matched %d OFFERs(%s) has dup RULE_ID(%s)",
						offer_list_.size(), PubMethod.Collection2Str(offer_id_list_, ","),
						PubMethod.Collection2Str(rule_id_list_, ","));
				l.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				return BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE,
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
						responseMsg);
			}
			offerMap.put(offer_._ruleId, offer_);
		}

		responseMsg.setUdr(udr);
		responseMsg.setOfferMap(offerMap);
		responseMsg.setResult(true);
		return BaseRspsMsg.ok(responseMsg);
	}

	public static class GuidOffer{

		private static final String _Q_STL_OFFER = "SELECT ID, DATA_SOURCE, OFFER_CODE, PRODUCT_CODE, ORDER_MODE, RULE_ID, "
				+ "ROUTE_CODE, DEST_SOURCE, EFF_DATE, EXP_DATE FROM OFFER WHERE DATA_SOURCE = ? AND OFFER_CODE = ? "
				+ "AND PRODUCT_CODE = ? AND ORDER_MODE = ? ORDER BY EFF_DATE DESC";

		private Map<Long, EdbStlOfferRec> offerMap = new HashMap<>();

		private String logTag;
		private Logger log;
		public GuidOffer() {
			log = LoggerFactory.getLogger(this.getClass());
		}
		public Map<Long, EdbStlOfferRec> getOfferMap() {
			return offerMap;
		}

		public void setOfferMap(Map<Long, EdbStlOfferRec> offerMap) {
			this.offerMap = offerMap;
		}

		public String getLogTag() {
			return logTag;
		}

		public void setLogTag(String logTag) {
			this.logTag = logTag;
		}

		/***
		 *
		 * @author: zhanglei
		 * @date: 2022/1/12 17:29
		 * @param udr
		 * @return: java.util.List<com.hp.cmcc.bboss.pub.edb.entity.EdbStlOfferRec>
		 * @exception:
		 * @update:
		 * @updatePerson:
		 */
		private List<EdbStlOfferRec> seekStlOffer(UdrFmt udr) throws Exception {
			String shpm_ver_ = udr._eFields[UdrFmt.E_26_X01_SHPARM_VER];
			Edb edb_ = null;
			PreparedStatement pstmt_ = null;
			ResultSet rs_ = null;
			List<EdbStlOfferRec> offer_list_ = null;
			try {
				String tm14_ = udr._uFields[UdrFmt.S_35_START_TIME_36];
				int raw_offer_cnt_ = 0;
				boolean has_rs_ = false;
				EdbStlOfferRec stl_offer_ = null;
				edb_ = TopologyCtx._EdbShpmPool.borrowObject(shpm_ver_);
				Connection conn_ = edb_.getConnection();
				pstmt_ = conn_.prepareStatement(_Q_STL_OFFER);
				pstmt_.setInt(1, Integer.parseInt(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]));
				pstmt_.setString(2, udr._uFields[UdrFmt.S_07_OFFER_CODE_08]);
				pstmt_.setString(3, udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09]);
				pstmt_.setString(4, udr._uFields[UdrFmt.S_15_ORDER_MODE_16]);
				if (!PubMethod.IsEmpty(udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09])) {
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_offer_ = ExtractStl.extractStlOffer(rs_);
						++raw_offer_cnt_;
						has_rs_ = true;
						if (!stl_offer_.isEffective(tm14_)) {
							log.trace("{} START_TIME_36 {} ineffective, skip offer {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
									stl_offer_.toGsonStr());
							continue;
						}
						if (offer_list_ == null)
							offer_list_ = new ArrayList<EdbStlOfferRec>();
						offer_list_.add(stl_offer_);
					}
				}

				if (offer_list_ == null || offer_list_.isEmpty()) {
					pstmt_.setString(3, "-1");
					rs_ = pstmt_.executeQuery();
					while (rs_.next()) {
						stl_offer_ = ExtractStl.extractStlOffer(rs_);
						++raw_offer_cnt_;
						has_rs_ = true;
						if (!stl_offer_.isEffective(tm14_)) {
							log.trace("{} START_TIME_36 {} ineffective, skip offer {}", logTag, udr._uFields[UdrFmt.S_35_START_TIME_36],
									stl_offer_.toGsonStr());
							continue;
						}
						if (offer_list_ == null)
							offer_list_ = new ArrayList<EdbStlOfferRec>();
						offer_list_.add(stl_offer_);
					}
				}

				if (offer_list_ != null && !offer_list_.isEmpty()) {
					// l.trace("{} {} OFFERs located", _logTag, offer_list_.size());
				} else {
					udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = OfferElem.offerElem(udr);
					if (has_rs_) {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S501_OFFER_CFG_INEFFECTIVE;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = PubMethod.FmtArgs("%d OFFERs but none effective", raw_offer_cnt_);
					} else {
						udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.S500_NO_OFFER_CFG;
						udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = "no OFFER matches";
					}
					log.warn("{} {}, {} reject", logTag, udr._eFields[UdrFmt.E_22_E08_ERR_REASON],
							udr._eFields[UdrFmt.E_02_A03_ERR_CODE]);
				}
				return offer_list_;
			} finally {
				Edb.Close(rs_, pstmt_, null);
				if (edb_ != null) {
					TopologyCtx._EdbShpmPool.returnObject(shpm_ver_, edb_);
				}
			}
		}
	}

	public static class OfferElem{
		public static String offerElem(UdrFmt udr) {
			StringBuilder sb_ = new StringBuilder();
			sb_.append(udr._uFields[UdrFmt.S_03_DATA_SOURCE_04] == null ? "" : udr._uFields[UdrFmt.S_03_DATA_SOURCE_04]);
			sb_.append(':');
			sb_.append(udr._uFields[UdrFmt.S_07_OFFER_CODE_08] == null ? "" : udr._uFields[UdrFmt.S_07_OFFER_CODE_08]);
			sb_.append(':');
			sb_.append(udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09] == null ? "" : udr._uFields[UdrFmt.S_08_PRODUCT_CODE_09]);
			sb_.append(':');
			sb_.append(udr._uFields[UdrFmt.S_15_ORDER_MODE_16] == null ? "" : udr._uFields[UdrFmt.S_15_ORDER_MODE_16]);
			return sb_.substring(0);
		}
	}


	private void _clearMaps() {
		_offerMap.clear();
		_ruleMap.clear();
		_rateMap.clear();
		_oObjMap.clear();
		_iObjMap.clear();
	}
}
