package com.hp.cmcc.bboss.storm;

import com.hp.cmcc.bboss.pub.mdb.MdbConst;
import com.hp.cmcc.bboss.pub.odb.OdbSystemParam;
import com.hp.cmcc.bboss.pub.udr.UdrFmt;
import com.hp.cmcc.bboss.pub.udr.UdrFmtMsg;
import com.hp.cmcc.bboss.storm.sidd.*;
import com.hp.cmcc.bboss.storm.uidd.RaterUIDDNew;
import org.apache.storm.task.OutputCollector;
import org.apache.storm.task.TopologyContext;
import org.apache.storm.topology.OutputFieldsDeclarer;
import org.apache.storm.topology.base.BaseRichBolt;
import org.apache.storm.tuple.Fields;
import org.apache.storm.tuple.Tuple;
import org.apache.storm.tuple.Values;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class RatingBolt extends BaseRichBolt {
	private static final long serialVersionUID = 6019048979728685935L;
	private Logger l = null;
	private OutputCollector _collector;
	private String _rndmId;
	private String _logTag;
	private FunctorIDD _rater;

	@Override
	public void prepare(@SuppressWarnings("rawtypes") Map stormConf, TopologyContext context, OutputCollector collector) {
		l = LoggerFactory.getLogger(this.getClass());
		TopologyCtx.Init(context.getThisWorkerPort());
		_collector = collector;
		_rndmId = "";
		if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_1_UIDD) {
			_rater = new RaterUIDDNew();
		} else if (OdbSystemParam.GetInstance()._subSystemType == OdbSystemParam.SUB_SYSTEM_TYPE_2_SIDD) {
			//TODO 测试代码，排查问题，后期删除
			if("Ref".equals(TopologyCfg.GetInstance().validationImpl)){
				_rater = new RaterSIDDRef();
			}else if("New".equals(TopologyCfg.GetInstance().validationImpl)){
				_rater = new RaterSIDDNew();
			}else {
				_rater = new RaterSIDD();
			}
		} else {
			throw new RuntimeException("ukn SUB_SYSTEM_TYPE " + OdbSystemParam.GetInstance()._subSystemType);
		}
	}

	@Override
	public void execute(Tuple input) {
		UdrFmtMsg msg_ = null;
		try {
			msg_ = (UdrFmtMsg) input.getValue(0);
			_rndmId = msg_._rndmId;
			for (UdrFmt udr_ : msg_._udrFmtList) {
				_execUdr(udr_);
			}
		} catch (Exception e) {
			l.info("exception, {}, {}", _rndmId, msg_ == null ? "null" : msg_.toGsonStr(), e);
		} finally {
			try{
				_collector.emit(new Values(msg_));
				_collector.ack(input);
			}catch (Exception e){
				l.error("RatingBolt error", e);
			}

		}
	}

	@Override
	public void declareOutputFields(OutputFieldsDeclarer declarer) {
		declarer.declare(new Fields(TopologyCfg.OUTPUT_FIELD_DFT));
	}

	private void _execUdr(UdrFmt udr) {
		try {
			if (udr.isErr())
				return;
			_logTag = _rndmId + ", " + udr.getBizlogUid();
			_rater.execUdr(_logTag, udr);
			if (!udr.isErr())
				udr.setProcFlag(UdrFmt.PROC_FLAG_RATING);
		} catch (Exception e) {
			udr._eFields[UdrFmt.E_02_A03_ERR_CODE] = UdrFmt.V084_EXP_RATING;
			udr._eFields[UdrFmt.E_16_E02_ERR_VAL] = "storm rating exception";
			udr._eFields[UdrFmt.E_22_E08_ERR_REASON] = e.getMessage();
			l.error("rating处理异常,话单：{}. logTag: {} err_code: {}, err_val: {}",
					udr.toGsonStr(), _logTag, udr._eFields[UdrFmt.E_02_A03_ERR_CODE], udr._eFields[UdrFmt.E_16_E02_ERR_VAL], e);
		} finally {
			if (TopologyCfg.GetInstance()._bizlogFlags.contains(UdrFmt.PROC_FLAG_RATING)) {
				String aux_info_ = null;
				if (!udr.isErr()) {
					aux_info_ = (udr._eFields[UdrFmt.E_27_X02_BIZRCY_CHARGE] == null ? ""
							: udr._eFields[UdrFmt.E_27_X02_BIZRCY_CHARGE]) + "|"
							+ (udr._eFields[UdrFmt.E_28_X03_BIZRCY_ACUMLT] == null ? ""
									: udr._eFields[UdrFmt.E_28_X03_BIZRCY_ACUMLT]);
				}
				TopologyCtx.BizlogTimestamp(true, udr, MdbConst.BIZLOG_HK_UDR_RATING, aux_info_);
			}
		}
	}
}
