<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="60 seconds">
	<appender name="ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>./data/storm/data/logs/settle-service-storm/settle-service-storm.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>./data/storm/data/logs/settle-service-storm/settle-service-storm.%d{yyyyMMdd}.%i.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>500MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyMMddHHmmss.SSS}|%X{jvmPid:-}|%-5level|%X{module:-}|%X{instNm:-}|%X{almCd:-0}|%t|%c{1}|%L|%m%n</pattern>
		</encoder>
	</appender>

	<appender name="SOCKET" class="ch.qos.logback.classic.net.SocketAppender">
		<remoteHost>localhost</remoteHost>
		<port>12688</port>
		<reconnectionDelay>5000</reconnectionDelay>
		<queueSize>1000</queueSize>
		<includeCallerData>true</includeCallerData>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>WARN</level>
		</filter>
	</appender>

	<root level="INFO">
		<appender-ref ref="ROLLING"/>
		<appender-ref ref="SOCKET"/>
	</root>

	<logger name="kafka" level="warn" />
	<logger name="com.hp.cmcc.bboss" level="trace" />
</configuration>

