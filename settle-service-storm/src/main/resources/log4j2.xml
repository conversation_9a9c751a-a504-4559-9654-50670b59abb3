<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">

    <properties>
        <property name="pattern">%d{yyyy-MM-dd HH:mm:ss.SSS} %c{1.} %t [%p] %L %msg%n</property>
        <property name="patternNoTime">%msg%n</property>
        <property name="patternMetrics">%d %-8r %m%n</property>

        <property name="LOG_HOME">logs</property>
        <property name="FILE_NAME">worker</property>
    </properties>

    <Appenders>
        <Console name="LogToConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] [%-5level] %class{36} %L : %msg%xEx%n"/>
        </Console>
        <Console name="LogToConsole2" target="SYSTEM_OUT">
            <PatternLayout>
                <pattern>${pattern}</pattern>
            </PatternLayout>
        </Console>

        <RollingFile name="RollingFile" fileName="${LOG_HOME}/${FILE_NAME}.log"
                     filePattern="${LOG_HOME}/$${date:yyyy-MM}/${FILE_NAME}-%d{yyyy-MM-dd-HH}-%i.log.gz">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %class{36} %L : %msg%xEx%n"/>
            <Policies>
                <!--6小时回滚一次-->
                <TimeBasedTriggeringPolicy interval="6" />
                <!--日志达到250MB回滚一次-->
                <SizeBasedTriggeringPolicy size="250MB" />
            </Policies>
            <!--当文件回滚了120次后-->
            <DefaultRolloverStrategy max="120">
                <!--删除文件-->
                <Delete basePath="${LOG_HOME}" maxDepth="2">
                    <IfFileName glob="*/*.log.gz">
                        <IfLastModified age="P30D">
                            <IfAny>
                                <IfAccumulatedFileSize exceeds="100 GB" />
                                <IfAccumulatedFileCount exceeds="24" />
                            </IfAny>
                        </IfLastModified>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>

    <Loggers>
        <Root level="info">
            <AppenderRef ref="LogToConsole"/>
            <AppenderRef ref="RollingFile"/>
        </Root>
        <Logger name="com.hp.cmcc.bboss.storm" level="debug" additivity="false">
            <AppenderRef ref="LogToConsole"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
    </Loggers>
</Configuration>