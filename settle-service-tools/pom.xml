<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.hp.cmcc.bboss</groupId>
		<artifactId>settle-service</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>settle-service-tools</artifactId>
	<name>settle-service-tools</name>
	<version>1.0.0</version>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>12.2.0.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

	<build>
		<!--<finalName>settle-tools</finalName>-->
	</build>
</project>
