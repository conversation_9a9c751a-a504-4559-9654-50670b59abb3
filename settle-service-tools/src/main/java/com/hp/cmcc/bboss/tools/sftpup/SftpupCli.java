package com.hp.cmcc.bboss.tools.sftpup;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;

import static com.hp.cmcc.bboss.tools.ut.Stdout.*;

public class SftpupCli {
	public JSch _jsch;
	public Session _session;
	public ChannelSftp _sftp;

	public SftpupCli() {
		_jsch = new JSch();
	}

	public void login(SftpupCfgRec cfg_rec) throws JSchException {
		this.close();
		_session = _jsch.getSession(cfg_rec._userNm, cfg_rec._hostAddr, cfg_rec._port);
		_session.setConfig("StrictHostKeyChecking", "no");
		_session.setPassword(cfg_rec._passwd);
		_session.connect();
		P(DBG, "session %s@%s:%d connect ok", cfg_rec._userNm, cfg_rec._hostAddr, cfg_rec._port);
		_sftp = (ChannelSftp) _session.openChannel("sftp");
		_sftp.connect();
	}

	public void close() {
		if (_sftp != null) {
			_sftp.disconnect();
			_sftp = null;
		}
		if (_session != null) {
			_session.disconnect();
			_session = null;
		}
	}
}
