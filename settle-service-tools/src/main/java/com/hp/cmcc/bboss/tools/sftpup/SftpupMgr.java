package com.hp.cmcc.bboss.tools.sftpup;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.hp.cmcc.bboss.tools.ut.PubMethod;
import com.hp.cmcc.bboss.tools.ut.Stdout;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpATTRS;
import com.jcraft.jsch.SftpException;

import static com.hp.cmcc.bboss.tools.ut.Stdout.*;

public class SftpupMgr {
	private List<SftpupCfgRec> _cfgList;
	private String _ymdHM;

	SftpupMgr() {
		_cfgList = new ArrayList<SftpupCfgRec>();
	}

	public boolean parseCfg(String cfg_fnm) throws IOException {
		boolean r = true;
		_cfgList.clear();
		BufferedReader br_ = new BufferedReader(new FileReader(cfg_fnm));
		String line_;
		int lnum_ = 0;
		while ((line_ = br_.readLine()) != null) {
			++lnum_;
			if (PubMethod.IsBlank(line_))
				continue;
			if (line_.startsWith("#"))
				continue;
			String[] fields_ = line_.split("\\]\\|\\[", -1);
			if (fields_.length != 7) {
				P(WRN, "[%s] line %d, has %d fields while expecting 7", cfg_fnm, lnum_, fields_.length);
				r = false;
			}
			SftpupCfgRec rec_ = new SftpupCfgRec();
			rec_._hostAddr = fields_[0];
			rec_._port = Integer.parseInt(fields_[1]);
			rec_._userNm = fields_[2];
			rec_._passwd = fields_[3];
			rec_._locFnm = fields_[4];
			rec_._rmtDir = fields_[5];
			rec_._rmtBakDir = fields_[6];
			File f = new File(rec_._locFnm);
			if (!f.isFile()) {
				P(WRN, "[%s] line %d, loc_fnm [%s] does not exists", cfg_fnm, lnum_, rec_._locFnm);
				r = false;
				continue;
			}
			_cfgList.add(rec_);
		}
		br_.close();
		P(DBG, "[%s] %d cfg items parsed", cfg_fnm, _cfgList.size());
		return r;
	}

	public void run() throws Exception {
		_ymdHM = PubMethod.Long2Str(System.currentTimeMillis(), PubMethod.TimeStrFmt.Fmt14).substring(0, 12);
		P(TRC, "ymdHM is [%s]", _ymdHM);
		int cnt_ = 0;
		for (SftpupCfgRec cfg_rec_ : _cfgList) {
			P(TRC, "[%s] begin, %d of %d", cfg_rec_._hostAddr, ++cnt_, _cfgList.size());
			_runOneCfg(cfg_rec_);
		}
	}

	private void _runOneCfg(SftpupCfgRec cfg_rec) throws Exception {
		SftpupCli cli_ = new SftpupCli();
		cli_.login(cfg_rec);
		cli_._sftp.cd(cfg_rec._rmtBakDir);
		P(TRC, "[%s] rmt_bak_dir [%s] validated", cfg_rec._hostAddr, cfg_rec._rmtBakDir);
		cli_._sftp.cd(cfg_rec._rmtDir);
		P(TRC, "[%s] cd rmt_dir [%s] ok", cfg_rec._hostAddr, cfg_rec._rmtDir);
		File f = new File(cfg_rec._locFnm);
		SftpATTRS attr_ = null;
		try {
			attr_ = cli_._sftp.stat(f.getName());
		} catch (SftpException e) {
			if (e.getMessage().contains("No such file")) {
				P(DBG, "[%s] [%s] not exists, no need backup", cfg_rec._hostAddr, f.getName());
				attr_ = null;
			}
		}
		if (attr_ != null) {
			String rmt_bak_ = cfg_rec._rmtBakDir + "/" + f.getName() + "." + _ymdHM;
			cli_._sftp.rename(f.getName(), rmt_bak_);
			P(DBG, "[%s] backup to [%s] done", cfg_rec._hostAddr, rmt_bak_);
		}
		cli_._sftp.put(f.getAbsolutePath(), f.getName(), ChannelSftp.OVERWRITE);
		attr_ = cli_._sftp.stat(f.getName());
		if (f.length() != attr_.getSize()) {
			String alm_ = Stdout.FmtArgs("[%s] size ne loc:rmt %d:%d", cfg_rec._hostAddr, f.length(), attr_.getSize());
			P(WRN, "%s", alm_);
			throw new RuntimeException(alm_);
		}
		P(INF, "[%s] [%s] upgrade ok, %d bytes", cfg_rec._hostAddr, f.getName(), attr_.getSize());
		cli_.close();
	}
}
