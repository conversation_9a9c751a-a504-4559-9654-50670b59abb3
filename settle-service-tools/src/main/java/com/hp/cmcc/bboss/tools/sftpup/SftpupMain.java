package com.hp.cmcc.bboss.tools.sftpup;

import static com.hp.cmcc.bboss.tools.ut.Stdout.*;

public class SftpupMain {
	public static void main(String[] args) throws Exception {
		try {
			SftpupCmdline cmdline_ = SftpupCmdline.GetInstance();
			cmdline_.init(args);
			P(INF, "cfg_fnm is [%s]", cmdline_._cfgFnm);
			SftpupMgr mgr_ = new SftpupMgr();
			if (!mgr_.parseCfg(cmdline_._cfgFnm)) {
				P(ERO, "parse cfg_fnm [%s] error", cmdline_._cfgFnm);
				System.exit(1);
			}
			mgr_.run();
			System.exit(0);
		} catch (Exception e) {
			P(ERO, e, "encounter exception, exit");
			System.exit(1);
		}
	}
}
