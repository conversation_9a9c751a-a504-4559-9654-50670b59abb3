package com.hp.cmcc.bboss.tools.ut;

import static com.hp.cmcc.bboss.tools.ut.Stdout.*;

public class PasswdMain {
	public static void main(String[] args) {
		String mode_ = null;
		String str_ = null;
		Getopt g = new Getopt("passwd", args, ":m:s:");
		int c;
		while ((c = g.getopt()) != -1) {
			switch (c) {
			case 'm':
				mode_ = g.getOptarg();
				break;
			case 's':
				str_ = g.getOptarg();
				break;
			case '?':
				break;
			default:
				P(WRN, "getopt() returned [%c]", c);
				break;
			}
		}

		if (mode_ == null || str_ == null)
			_Usage(1);
		if (mode_.equals("e")) {
			String base64_ = LoginPasswd.PasswdEncryptAES(str_);
			if (base64_ == null) {
				System.exit(2);
			} else {
				System.out.println(base64_);
			}
		} else if (mode_.equals("d")) {
			String plain_ = LoginPasswd.PasswdDecryptAES(str_);
			if (plain_ == null) {
				System.exit(3);
			} else {
				System.out.println(plain_);
			}
		} else {
			_Usage(1);
		}
	}

	private static void _Usage(int jvm_exit_code) {
		String newline_ = String.format("%n");
		StringBuilder sb_ = new StringBuilder();
		sb_.append("Usage: -m [e|d] -s str");
		sb_.append(newline_);
		sb_.append("eg   : -m e -s secrete");
		sb_.append(newline_);
		sb_.append("eg   : -m d -s 1a4i7R+kAyHYlJ3EWjQ/Ww==");
		sb_.append(newline_);
		System.out.printf("%s", sb_.substring(0));

		if (jvm_exit_code >= 0) {
			System.exit(jvm_exit_code);
		}
	}
}
