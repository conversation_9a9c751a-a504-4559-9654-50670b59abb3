package com.hp.cmcc.bboss.tools.sftpup;

import com.hp.cmcc.bboss.tools.ut.Getopt;
import com.hp.cmcc.bboss.tools.ut.PubMethod;
import com.hp.cmcc.bboss.tools.ut.Stdout;

public class SftpupCmdline {
	private static SftpupCmdline _Instance = null;
	public String _cfgFnm;
	public int _verboseLevel = 3;

	static {
		_Instance = new SftpupCmdline();
	}

	public static SftpupCmdline GetInstance() {
		return _Instance;
	}

	public void init(String[] args) {
		Getopt g = new Getopt("sftpup", args, ":c:v:");
		int c;
		while ((c = g.getopt()) != -1) {
			switch (c) {
			case 'c':
				_cfgFnm = g.getOptarg();
				break;
			case 'v':
				_verboseLevel = Integer.parseInt(g.getOptarg());
				if (_verboseLevel < 0 || _verboseLevel > 4)
					_verboseLevel = 4;
				break;
			case '?':
				break; // getopt() already printed an error
			default:
				Stdout.P(Stdout.WRN, "getopt() returned [%c]", c);
				break;
			}
		}

		if (PubMethod.IsEmpty(_cfgFnm)) {
			_usage(1);
		}
		Stdout._DftLevel = _verboseLevel;
	}

	private void _usage(int jvm_exit_code) {
		String newline_ = String.format("%n");
		StringBuilder sb_ = new StringBuilder();
		sb_.append("Usage: -c cfg_file [-v verbose]");
		sb_.append(newline_);
		sb_.append("eg   : -c sftpup.cfg");
		sb_.append(newline_);
		sb_.append("     : -v : default 3, 0:ERO, 1:WRN, 2:INF, 3:DBG, 4:TRC");
		sb_.append(newline_);
		System.out.print(sb_.substring(0));

		if (jvm_exit_code >= 0) {
			System.exit(jvm_exit_code);
		}
	}
}
