package com.hp.cmcc.bboss.tools.sqluld;

import static com.hp.cmcc.bboss.tools.ut.Stdout.*;

import java.nio.charset.Charset;

import com.hp.cmcc.bboss.tools.ut.PubMethod;
import com.hp.cmcc.bboss.tools.ut.Getopt;
import com.hp.cmcc.bboss.tools.ut.Stdout;

public class SqluldCmdline {
	private static SqluldCmdline _Instance = null;
	public String _loginStr;
	public String _loginK_I; // key and instance in '$DB_LOGIN_FILE'
	public String _querySql;
	public String _outputFnm;
	public String _delimiter;
	public int _wrkNum = 2;
	public int _verboseLevel = 3;
	public String _charset;
	public boolean _trimChar;

	static {
		_Instance = new SqluldCmdline();
	}

	public SqluldCmdline() {
		_delimiter = "|";
	}

	public static SqluldCmdline GetInstance() {
		return _Instance;
	}

	public void init(String[] args) {
		Getopt g = new Getopt("sqluld", args, ":l:L:q:o:d:c:w:v:t");
		int c;
		while ((c = g.getopt()) != -1) {
			switch (c) {
			case 'l':
				_loginStr = g.getOptarg();
				break;
			case 'L':
				_loginK_I = g.getOptarg();
				break;
			case 'q':
				_querySql = g.getOptarg();
				break;
			case 'o':
				_outputFnm = g.getOptarg();
				break;
			case 'd':
				_delimiter = g.getOptarg();
				break;
			case 'c':
				_charset = g.getOptarg();
				break;
			case 'w':
				_wrkNum = Integer.parseInt(g.getOptarg());
				if (_wrkNum <= 0)
					_wrkNum = 1;
				else if (_wrkNum > 4)
					_wrkNum = 4;
				break;
			case 'v':
				_verboseLevel = Integer.parseInt(g.getOptarg());
				if (_verboseLevel < 0 || _verboseLevel > 4)
					_verboseLevel = 4;
				break;
			case 't':
				_trimChar = true;
			case '?':
				break; // getopt() already printed an error
			default:
				Stdout.P(Stdout.WRN, "getopt() returned [%c]", c);
				break;
			}
		}

		if (PubMethod.IsEmpty(_loginStr) && PubMethod.IsEmpty(_loginK_I)) {
			_usage(1);
		}
		if (PubMethod.IsEmpty(_querySql) || PubMethod.IsEmpty(_outputFnm)) {
			_usage(1);
		}

		if (PubMethod.IsEmpty(_charset)) {
			Charset dft_charset_ = Charset.defaultCharset();
			_charset = dft_charset_.name();
		}
		Stdout._DftLevel = _verboseLevel;
	}

	public void print() {
		P(DBG, "login_str [%s]", _loginStr);
		P(DBG, "login_k:i [%s]", _loginK_I);
		P(DBG, "query_sql [%s]", _querySql);
		P(DBG, "output_fnm [%s]", _outputFnm);
		P(DBG, "delimiter [%s]", _delimiter);
		P(DBG, "charset [%s]", _charset);
		P(DBG, "wrk_num %d", _wrkNum);
		P(DBG, "verbose %d", _verboseLevel);
		P(DBG, "trim %s", _trimChar ? "true" : "false");
	}

	private void _usage(int jvm_exit_code) {
		String newline_ = String.format("%n");
		StringBuilder sb_ = new StringBuilder();
		sb_.append("Usage: -l login_str -q query_sql -o output_fnm [-d delimiter] [-c charset] [-w wrk_num] [-v verbose] [-t]");
		sb_.append(newline_);
		sb_.append("Usage: -L login_k:i -q query_sql -o output_fnm [-d delimiter] [-c charset] [-w wrk_num] [-v verbose] [-t]");
		sb_.append(newline_);
		sb_.append("eg   : -l usr/passwd@sid:127.0.0.1:1521 -q \"select * from table_name\" -o uld.bcp");
		sb_.append(newline_);
		sb_.append("eg   : -L billing:udr -q \"select * from table_name\" -o uld.bcp");
		sb_.append(newline_);
		sb_.append("     : -d : default delimiter is pipe char '|'");
		sb_.append(newline_);
		sb_.append("     : -c : default using JVM default encoding, support GB18030/UTF-8 etc.");
		sb_.append(newline_);
		sb_.append("     : -w : default 2, worker thread number, should between 1 and 4");
		sb_.append(newline_);
		sb_.append("     : -v : default 3, 0:ERO, 1:WRN, 2:INF, 3:DBG, 4:TRC");
		sb_.append(newline_);
		sb_.append("     : -t : default no trim for CHAR type");
		sb_.append(newline_);
		System.out.print(sb_.substring(0));

		if (jvm_exit_code >= 0) {
			System.exit(jvm_exit_code);
		}
	}
}
