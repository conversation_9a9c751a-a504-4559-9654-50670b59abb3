package com.hp.cmcc.bboss.tools.sqluld;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import com.hp.cmcc.bboss.tools.ut.LoginPasswd;
import com.hp.cmcc.bboss.tools.ut.PubMethod;

import static com.hp.cmcc.bboss.tools.ut.Stdout.*;

public class SqluldMgr {
	public int _sqlCnt;
	private BlockingQueue<SqluldTuple>[] _upQueues;
	private SqluldCmdline _cmdline;
	private Connection _conn;
	private ResultSetMetaData _meta;
	private ResultSet _rs;
	private Statement _stmt;
	private int[] _columnTypes;

	public SqluldMgr(BlockingQueue<SqluldTuple>[] up_queues) {
		_upQueues = up_queues;
		_cmdline = SqluldCmdline.GetInstance();
	}

	public int run() {
		boolean connect_ok_ = false;
		try {
			if (!_initConn()) {
				P(WRN, "call _initConn() error");
				return 1;
			} else {
				connect_ok_ = true;
			}
		} catch (Throwable e) { // for 'java.lang.NoClassDefFoundError'
			P(WRN, e, "call _initConn error");
			return 1;
		} finally {
			if (!connect_ok_) {
				_emitEOF();
			}
		}

		try {
			_exec();
		} catch (Exception e) {
			P(WRN, e, "call _exec() exception");
			return 1;
		} finally {
			_emitEOF();
		}

		return 0;
	}

	public void closeResource() {
		_closeResource(_rs, _stmt, _conn);
		_rs = null;
		_stmt = null;
		_conn = null;
	}

	private boolean _initConn() {
		String login_str_ = null;
		if (!PubMethod.IsEmpty(_cmdline._loginK_I)) {
			String[] k_i_ = _cmdline._loginK_I.split(":");
			if (k_i_.length != 2) {
				P(WRN, "login_k:i [%s] split by ':' has %d fields while expecting 2", _cmdline._loginK_I, k_i_.length);
				return false;
			}
			login_str_ = LoginPasswd.DecryptLogin(k_i_[0], k_i_[1]);
		} else {
			login_str_ = _cmdline._loginStr;
		}
		if (PubMethod.IsEmpty(login_str_)) {
			P(WRN, "parse login_str failed");
			return false;
		}

		String[] fields_ = login_str_.split("[/@:]");
		if (fields_.length < 3) {
			P(WRN, "login_str [%s] bad fmt", login_str_);
			return false;
		}
		String db_user_ = fields_[0];
		String db_passwd_ = fields_[1];
		String db_sid_ = fields_[2];
		String db_ip_ = "127.0.0.1";
		if (fields_.length > 3)
			db_ip_ = fields_[3];
		int db_port_ = 1521;
		if (fields_.length > 4) {
			long port_ = PubMethod.A2L(fields_[4]);
			if (port_ <= 0) {
				P(WRN, "parse db_port of [%s] failed", login_str_);
				return false;
			} else {
				db_port_ = (int) port_;
			}
		}

		String jdbc_url_ = String.format("**************************", db_ip_, db_port_, db_sid_);
		try {
			Class.forName("oracle.jdbc.driver.OracleDriver");
			_conn = DriverManager.getConnection(jdbc_url_, db_user_, db_passwd_);
		} catch (Exception e) {
			P(WRN, e, "init db connection failed");
			return false;
		}
		P(INF, "init db connection [%s] db user [%s] ok", jdbc_url_, db_user_);
		return true;
	}

	private void _exec() throws SQLException, InterruptedException {
		_stmt = _conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
		_stmt.setFetchSize(1000);
		_rs = _stmt.executeQuery(_cmdline._querySql);
		P(INF, "query executed, fetchSize=%d, maxRows=%d", _stmt.getFetchSize(), _stmt.getMaxRows());
		_meta = _rs.getMetaData();
		_printMeta();
		int column_cnt_ = _columnTypes.length - 1;
		SqluldTuple tuple_;
		while (_rs.next()) {
			tuple_ = new SqluldTuple(column_cnt_);
			for (int i = 1; i <= column_cnt_; i++) {
				_fillTuple(tuple_, _rs, i);
			}
			int idx_ = _sqlCnt % _upQueues.length;
			_upQueues[idx_].offer(tuple_, 86400, TimeUnit.SECONDS);
			_sqlCnt++;
		}
	}

	private void _emitEOF() {
		SqluldTuple tuple_;
		for (int i = 0; i < _upQueues.length; ++i) {
			tuple_ = new SqluldTuple(0);
			tuple_._idx = -1;
			_upQueues[i].add(tuple_);
		}
		P(INF, "%d EOF tuple emitted, _sqlCnt=%d", _upQueues.length, _sqlCnt);
	}

	private void _printMeta() throws SQLException {
		int column_cnt_ = _meta.getColumnCount();
		_columnTypes = new int[column_cnt_ + 1];
		for (int i = 1; i <= column_cnt_; i++) {
			_columnTypes[i] = _meta.getColumnType(i);
			P(DBG, "column %3d, [%s] [%s:%d:%d] [%d] [%s]", i, _meta.getColumnName(i), _meta.getColumnTypeName(i),
					_meta.getPrecision(i), _meta.getScale(i), _meta.getColumnType(i), _meta.getColumnClassName(i));
		}
		SqluldWrkRunnable._ColumnTypes = _columnTypes;
	}

	private void _fillTuple(SqluldTuple tuple, ResultSet rs, int idx) throws SQLException {
		switch (_columnTypes[idx]) {
		case 2: // NUMBER, java.math.BigDecimal, (tuple._cells[idx] = rs.getBigDecimal(idx);)
			tuple._bytes[idx] = rs.getBytes(idx);
			break;
		case 12: // VARCHAR/VARCHAR2
		case 1: // CHAR
		case 91: // DATE
		case 93: // TIMESTAMP
		case -8: // ROWID
			tuple._bytes[idx] = rs.getBytes(idx);
			break;
		case 2005: // CLOB
			tuple._cells[idx] = rs.getClob(idx);
			break;
		case 2004: // BLOB, do nothing, always regard as NULL
			break;
		default:
			byte[] bytes_ = rs.getBytes(idx);
			if (bytes_ != null) {
				StringBuilder sb_ = new StringBuilder();
				sb_.append("[");
				int i = 0;
				for (byte b : bytes_) {
					if (i == 0) {
						sb_.append(String.format("%02x", b));
					} else {
						sb_.append(String.format(" %02x", b));
					}
					++i;
				}
				sb_.append("]");
				P(WRN, "_columnTypes[%d] is %d, sample guts: %s", idx, _columnTypes[idx], sb_.substring(0));
				throw new SQLException("unsupported column type " + _columnTypes[idx]);
			}
			break;
		}
	}

	private void _closeResource(ResultSet rs, Statement stmt, Connection conn) {
		if (rs != null) {
			try {
				rs.close();
			} catch (Exception e) {
				P(WRN, e, "close ResultSet exception, pls ignore");
			}
		}
		if (stmt != null) {
			try {
				stmt.close();
			} catch (Exception e) {
				P(WRN, e, "close Statement exception, pls ignore");
			}
		}
		if (conn != null) {
			try {
				conn.close();
			} catch (Exception e) {
				P(WRN, e, "close Connection exception, pls ignore");
			}
		}
	}
}
